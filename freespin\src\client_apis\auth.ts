"use server";

import { cookies } from "next/headers";
import { redirect } from "next/navigation";

interface ILogin {
  email: string;
  password: string;
}

interface IResult {
  success: boolean;
  message: string;
  data?: {
    token: string;
    user: {
      id: string;
      email: string;
      firstName?: string;
      lastName?: string;
      username?: string;
      role?: string;
    };
  };
}

export async function loginUser(body: ILogin): Promise<IResult> {
  try {
    // Use absolute URL for server-side fetch
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";

    const response = await fetch(`${baseUrl}/api/auth/login`, {
      method: "POST",
      body: JSON.stringify(body),
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        success: false,
        message: errorData.error || "Login failed",
      };
    }

    const result: IResult = await response.json();
    console.log("Login result:", result);

    if (result.success && result.data) {
      const cookieStore = await cookies();

      // Set session token (httpOnly for security)
      cookieStore.set("session-token", result.data.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 60 * 60 * 24 * 7, // 7 days
        path: "/",
      });

      // Set user info (accessible to client-side)
      if (result.data.user) {
        const userInfo = JSON.stringify({
          id: result.data.user.id,
          email: result.data.user.email,
          name:
            `${result.data.user.firstName || ""} ${
              result.data.user.lastName || ""
            }`.trim() || result.data.user.username,
          username: result.data.user.username,
          role: result.data.user.role,
        });

        // Set server-side cookie
        cookieStore.set("user-info", userInfo, {
          httpOnly: false, // Accessible to client-side
          secure: process.env.NODE_ENV === "production",
          sameSite: "lax",
          maxAge: 60 * 60 * 24 * 7, // 7 days
          path: "/",
        });

        // Also set client-side cookie for immediate access
        if (typeof document !== "undefined") {
          const maxAge = 60 * 60 * 24 * 7; // 7 days in seconds
          const expires = new Date(Date.now() + maxAge * 1000).toUTCString();
          document.cookie = `user-info=${encodeURIComponent(
            userInfo
          )}; expires=${expires}; path=/; SameSite=lax`;
        }
      }
    }

    return result;
  } catch (error) {
    console.error("Login error:", error);
    return {
      success: false,
      message: "Network error. Please check your connection and try again.",
    };
  }
}

export async function logoutUser() {
  try {
    const cookieStore = await cookies();

    // Clear session token
    cookieStore.delete("session-token");

    // Clear user info
    cookieStore.delete("user-info");

    // Optionally call logout API endpoint
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";

    try {
      await fetch(`${baseUrl}/api/auth/logout`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });
    } catch (error) {
      // Ignore API errors for logout, cookies are already cleared
      console.warn("Logout API call failed:", error);
    }

    return { success: true, message: "Logged out successfully" };
  } catch (error) {
    console.error("Logout error:", error);
    return { success: false, message: "Logout failed" };
  }
}

export async function registerUser(body: {
  email: string;
  password: string;
  username: string;
  firstName?: string;
  lastName?: string;
}): Promise<IResult> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000";

    const response = await fetch(`${baseUrl}/api/auth/register`, {
      method: "POST",
      body: JSON.stringify(body),
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        success: false,
        message: errorData.error || "Registration failed",
      };
    }

    const result: IResult = await response.json();

    // Auto-login after successful registration
    if (result.success && result.data) {
      const cookieStore = await cookies();

      cookieStore.set("session-token", result.data.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 60 * 60 * 24 * 7,
        path: "/",
      });

      if (result.data.user) {
        cookieStore.set(
          "user-info",
          JSON.stringify({
            id: result.data.user.id,
            email: result.data.user.email,
            name:
              `${result.data.user.firstName || ""} ${
                result.data.user.lastName || ""
              }`.trim() || result.data.user.username,
            username: result.data.user.username,
            role: result.data.user.role,
          }),
          {
            httpOnly: false,
            secure: process.env.NODE_ENV === "production",
            sameSite: "lax",
            maxAge: 60 * 60 * 24 * 7,
            path: "/",
          }
        );
      }
    }

    return result;
  } catch (error) {
    console.error("Registration error:", error);
    return {
      success: false,
      message: "Network error. Please check your connection and try again.",
    };
  }
}

// Helper function to get current user from cookies
export async function getCurrentUser() {
  try {
    const cookieStore = await cookies();
    const userInfo = cookieStore.get("user-info");
    const sessionToken = cookieStore.get("session-token");

    if (!userInfo || !sessionToken) {
      return null;
    }

    return JSON.parse(userInfo.value);
  } catch (error) {
    console.error("Error getting current user:", error);
    return null;
  }
}

// Server action for form-based login (with redirect)
export async function loginAction(formData: FormData) {
  const email = formData.get("email") as string;
  const password = formData.get("password") as string;

  if (!email || !password) {
    return {
      success: false,
      message: "Email and password are required",
    };
  }

  const result = await loginUser({ email, password });

  if (result.success) {
    redirect("/dashboard/blogs"); // Redirect after successful login
  }

  return result;
}
