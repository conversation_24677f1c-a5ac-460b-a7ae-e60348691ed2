// Removed unused Metadata import
// import {  } from "next/font/google";

// ;
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>_Mon<PERSON>,
  Gloock,
  Lora,
  Plus_Jakarta_Sans,
  Protest_Strike,
  Saira_Stencil_One,
} from "next/font/google";
import "slick-carousel/slick/slick-theme.css";
import "slick-carousel/slick/slick.css";
import "./globals.css"; // Updated path
import { ToastProvider } from "./shared/ToastProvider";
import GoogleAnalytics from "@/components/GoogleAnalytics/GoogleAnalytics";

const plusJakarta = Plus_Jakarta_Sans({
  subsets: ["latin"],
  weight: ["400", "600"],
  variable: "--font-jakarta",
});

const lora = Lora({
  subsets: ["latin"],
  weight: ["400", "700"],
  variable: "--font-lora",
});

const saira = Saira_Stencil_One({
  subsets: ["latin"],
  weight: ["400"],
  variable: "--font-saira",
});

const gloock = Gloock({
  subsets: ["latin"],
  weight: ["400"],
  variable: "--font-gloock",
});

const protest = Protest_Strike({
  subsets: ["latin"],
  weight: ["400"],
  variable: "--font-protest",
});

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// Readonly<{
// children: React.ReactNode;
// }>
export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="th">
      <head>
        <meta name="language" content="Thai" />
        <meta httpEquiv="content-language" content="th" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${plusJakarta.variable} ${lora.variable} ${saira.variable} ${gloock.variable} ${protest.variable} antialiased`}
      >
        {children}
        <ToastProvider />
        <GoogleAnalytics />
      </body>
    </html>
  );
}
