{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|sitemap.xml|robots.txt|.*\\.).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|.*\\.).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "xDeZu0aFLjbH1YhdahZtJ", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "FrNNN+HBdWeLONyYct2yC9KZ/WQp8bWC8ijse9tJFWk=", "__NEXT_PREVIEW_MODE_ID": "e2eaae50ce31ef4b3b7a1d5c4b5063fe", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e7644e4246d88c6adc2a1a9d562640634b6e18b7ce9db8945ea42f23e79f9a28", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6ecb6d568d317f2ceb1970c58b7a2bd765090883438faaf8180898fc7eebcad8"}}}, "functions": {}, "sortedMiddleware": ["/"]}