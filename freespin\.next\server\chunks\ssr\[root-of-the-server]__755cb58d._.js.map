{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/client_apis/auth.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { cookies } from \"next/headers\";\r\nimport { redirect } from \"next/navigation\";\r\n\r\ninterface ILogin {\r\n  email: string;\r\n  password: string;\r\n}\r\n\r\ninterface IResult {\r\n  success: boolean;\r\n  message: string;\r\n  data?: {\r\n    token: string;\r\n    user: {\r\n      id: string;\r\n      email: string;\r\n      firstName?: string;\r\n      lastName?: string;\r\n      username?: string;\r\n      role?: string;\r\n    };\r\n  };\r\n}\r\n\r\nexport async function loginUser(body: ILogin): Promise<IResult> {\r\n  try {\r\n    // Use absolute URL for server-side fetch\r\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:3000\";\r\n\r\n    const response = await fetch(`${baseUrl}/api/auth/login`, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(body),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json();\r\n      return {\r\n        success: false,\r\n        message: errorData.error || \"Login failed\",\r\n      };\r\n    }\r\n\r\n    const result: IResult = await response.json();\r\n    console.log(\"Login result:\", result);\r\n\r\n    if (result.success && result.data) {\r\n      const cookieStore = await cookies();\r\n\r\n      // Set session token (httpOnly for security)\r\n      cookieStore.set(\"session-token\", result.data.token, {\r\n        httpOnly: true,\r\n        secure: process.env.NODE_ENV === \"production\",\r\n        sameSite: \"lax\",\r\n        maxAge: 60 * 60 * 24 * 7, // 7 days\r\n        path: \"/\",\r\n      });\r\n\r\n      // Set user info (accessible to client-side)\r\n      if (result.data.user) {\r\n        const userInfo = JSON.stringify({\r\n          id: result.data.user.id,\r\n          email: result.data.user.email,\r\n          name:\r\n            `${result.data.user.firstName || \"\"} ${\r\n              result.data.user.lastName || \"\"\r\n            }`.trim() || result.data.user.username,\r\n          username: result.data.user.username,\r\n          role: result.data.user.role,\r\n        });\r\n\r\n        // Set server-side cookie\r\n        cookieStore.set(\"user-info\", userInfo, {\r\n          httpOnly: false, // Accessible to client-side\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          sameSite: \"lax\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n\r\n        // Also set client-side cookie for immediate access\r\n        if (typeof document !== \"undefined\") {\r\n          const maxAge = 60 * 60 * 24 * 7; // 7 days in seconds\r\n          const expires = new Date(Date.now() + maxAge * 1000).toUTCString();\r\n          document.cookie = `user-info=${encodeURIComponent(\r\n            userInfo\r\n          )}; expires=${expires}; path=/; SameSite=lax`;\r\n        }\r\n      }\r\n    }\r\n\r\n    return result;\r\n  } catch (error) {\r\n    console.error(\"Login error:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Network error. Please check your connection and try again.\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logoutUser() {\r\n  try {\r\n    const cookieStore = await cookies();\r\n\r\n    // Clear session token\r\n    cookieStore.delete(\"session-token\");\r\n\r\n    // Clear user info\r\n    cookieStore.delete(\"user-info\");\r\n\r\n    // Optionally call logout API endpoint\r\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:3000\";\r\n\r\n    try {\r\n      await fetch(`${baseUrl}/api/auth/logout`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      });\r\n    } catch (error) {\r\n      // Ignore API errors for logout, cookies are already cleared\r\n      console.warn(\"Logout API call failed:\", error);\r\n    }\r\n\r\n    return { success: true, message: \"Logged out successfully\" };\r\n  } catch (error) {\r\n    console.error(\"Logout error:\", error);\r\n    return { success: false, message: \"Logout failed\" };\r\n  }\r\n}\r\n\r\nexport async function registerUser(body: {\r\n  email: string;\r\n  password: string;\r\n  username: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n}): Promise<IResult> {\r\n  try {\r\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:3000\";\r\n\r\n    const response = await fetch(`${baseUrl}/api/auth/register`, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(body),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json();\r\n      return {\r\n        success: false,\r\n        message: errorData.error || \"Registration failed\",\r\n      };\r\n    }\r\n\r\n    const result: IResult = await response.json();\r\n\r\n    // Auto-login after successful registration\r\n    if (result.success && result.data) {\r\n      const cookieStore = await cookies();\r\n\r\n      cookieStore.set(\"session-token\", result.data.token, {\r\n        httpOnly: true,\r\n        secure: process.env.NODE_ENV === \"production\",\r\n        sameSite: \"lax\",\r\n        maxAge: 60 * 60 * 24 * 7,\r\n        path: \"/\",\r\n      });\r\n\r\n      if (result.data.user) {\r\n        cookieStore.set(\r\n          \"user-info\",\r\n          JSON.stringify({\r\n            id: result.data.user.id,\r\n            email: result.data.user.email,\r\n            name:\r\n              `${result.data.user.firstName || \"\"} ${\r\n                result.data.user.lastName || \"\"\r\n              }`.trim() || result.data.user.username,\r\n            username: result.data.user.username,\r\n            role: result.data.user.role,\r\n          }),\r\n          {\r\n            httpOnly: false,\r\n            secure: process.env.NODE_ENV === \"production\",\r\n            sameSite: \"lax\",\r\n            maxAge: 60 * 60 * 24 * 7,\r\n            path: \"/\",\r\n          }\r\n        );\r\n      }\r\n    }\r\n\r\n    return result;\r\n  } catch (error) {\r\n    console.error(\"Registration error:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Network error. Please check your connection and try again.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Helper function to get current user from cookies\r\nexport async function getCurrentUser() {\r\n  try {\r\n    const cookieStore = await cookies();\r\n    const userInfo = cookieStore.get(\"user-info\");\r\n    const sessionToken = cookieStore.get(\"session-token\");\r\n\r\n    if (!userInfo || !sessionToken) {\r\n      return null;\r\n    }\r\n\r\n    return JSON.parse(userInfo.value);\r\n  } catch (error) {\r\n    console.error(\"Error getting current user:\", error);\r\n    return null;\r\n  }\r\n}\r\n\r\n// Server action for form-based login (with redirect)\r\nexport async function loginAction(formData: FormData) {\r\n  const email = formData.get(\"email\") as string;\r\n  const password = formData.get(\"password\") as string;\r\n\r\n  if (!email || !password) {\r\n    return {\r\n      success: false,\r\n      message: \"Email and password are required\",\r\n    };\r\n  }\r\n\r\n  const result = await loginUser({ email, password });\r\n\r\n  if (result.success) {\r\n    redirect(\"/dashboard/blogs\"); // Redirect after successful login\r\n  }\r\n\r\n  return result;\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA0BsB,YAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/auth/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { z } from \"zod\";\r\nimport {\r\n  Eye,\r\n  EyeOff,\r\n  User,\r\n  // Mail, // Commented out - used for register\r\n  Lock,\r\n  // UserPlus, // Commented out - used for register\r\n  LogIn,\r\n  // Github, // Commented out - used for register\r\n  // Globe, // Commented out - used for register\r\n} from \"lucide-react\";\r\nimport { loginUser } from \"@/client_apis/auth\";\r\nimport { toast } from \"react-toastify\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\n// Zod validation schemas\r\nconst loginSchema = z.object({\r\n  email: z.string().min(1, \"Email or username is required\"),\r\n  password: z.string().min(6, \"Password must be at least 6 characters\"),\r\n  // rememberMe: z.boolean().optional(),\r\n});\r\n\r\n// Register schema (commented out)\r\n// const registerSchema = z\r\n//   .object({\r\n//     username: z\r\n//       .string()\r\n//       .min(3, \"Username must be at least 3 characters\")\r\n//       .max(30, \"Username cannot exceed 30 characters\")\r\n//       .regex(\r\n//         /^[a-zA-Z0-9_]+$/,\r\n//         \"Username can only contain letters, numbers, and underscores\"\r\n//       ),\r\n//     email: z.string().email(\"Please enter a valid email address\").toLowerCase(),\r\n//     password: z\r\n//       .string()\r\n//       .min(6, \"Password must be at least 6 characters\")\r\n//       .regex(\r\n//         /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/,\r\n//         \"Password must contain at least one uppercase letter, one lowercase letter, and one number\"\r\n//       ),\r\n//     confirmPassword: z.string(),\r\n//     firstName: z\r\n//       .string()\r\n//       .max(50, \"First name cannot exceed 50 characters\")\r\n//       .optional()\r\n//       .or(z.literal(\"\")),\r\n//     lastName: z\r\n//       .string()\r\n//       .max(50, \"Last name cannot exceed 50 characters\")\r\n//       .optional()\r\n//       .or(z.literal(\"\")),\r\n//     agreeToTerms: z\r\n//       .boolean()\r\n//       .refine(\r\n//         (val) => val === true,\r\n//         \"You must agree to the terms and conditions\"\r\n//       ),\r\n//   })\r\n//   .refine((data) => data.password === data.confirmPassword, {\r\n//     message: \"Passwords don't match\",\r\n//     path: [\"confirmPassword\"],\r\n//   });\r\n\r\ntype LoginFormData = {\r\n  email: string;\r\n  password: string;\r\n  // rememberMe?: boolean;\r\n};\r\n\r\n// Register form data type (commented out)\r\n// type RegisterFormData = z.infer<typeof registerSchema>;\r\n\r\n// Reusable UI Components\r\nconst Button = ({\r\n  children,\r\n  onClick,\r\n  variant = \"primary\",\r\n  size = \"md\",\r\n  type = \"button\",\r\n  disabled = false,\r\n  className = \"\",\r\n  fullWidth = false,\r\n}: {\r\n  children: React.ReactNode;\r\n  onClick?: () => void;\r\n  variant?: \"primary\" | \"secondary\" | \"outline\" | \"ghost\";\r\n  size?: \"sm\" | \"md\" | \"lg\";\r\n  type?: \"button\" | \"submit\";\r\n  disabled?: boolean;\r\n  className?: string;\r\n  fullWidth?: boolean;\r\n}) => {\r\n  const baseClasses =\r\n    \"inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2\";\r\n\r\n  const variants = {\r\n    primary:\r\n      \"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm\",\r\n    secondary: \"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500\",\r\n    outline:\r\n      \"border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-blue-500\",\r\n    ghost: \"text-gray-700 hover:bg-gray-100 focus:ring-gray-500\",\r\n  };\r\n\r\n  const sizes = {\r\n    sm: \"px-3 py-2 text-sm\",\r\n    md: \"px-4 py-2.5 text-sm\",\r\n    lg: \"px-6 py-3 text-base\",\r\n  };\r\n\r\n  return (\r\n    <button\r\n      type={type}\r\n      onClick={onClick}\r\n      disabled={disabled}\r\n      className={`\r\n        ${baseClasses} \r\n        ${variants[variant]} \r\n        ${sizes[size]} \r\n        ${fullWidth ? \"w-full\" : \"\"} \r\n        ${disabled ? \"opacity-50 cursor-not-allowed\" : \"\"} \r\n        ${className}\r\n      `}\r\n    >\r\n      {children}\r\n    </button>\r\n  );\r\n};\r\n\r\nconst Input = ({\r\n  label,\r\n  type = \"text\",\r\n  placeholder,\r\n  error,\r\n  icon: Icon,\r\n  showPasswordToggle = false,\r\n  className = \"\",\r\n  ...props\r\n}: {\r\n  label?: string;\r\n  type?: string;\r\n  placeholder?: string;\r\n  error?: string;\r\n  icon?: React.ComponentType<{ className?: string }>;\r\n  showPasswordToggle?: boolean;\r\n  className?: string;\r\n  [key: string]: any;\r\n}) => {\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const inputType = showPasswordToggle\r\n    ? showPassword\r\n      ? \"text\"\r\n      : \"password\"\r\n    : type;\r\n\r\n  return (\r\n    <div className={`space-y-1 ${className}`}>\r\n      {label && (\r\n        <label className=\"block text-sm font-medium text-gray-700\">\r\n          {label}\r\n        </label>\r\n      )}\r\n      <div className=\"relative\">\r\n        {Icon && (\r\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n            <Icon className=\"h-5 w-5 text-gray-400\" />\r\n          </div>\r\n        )}\r\n        <input\r\n          type={inputType}\r\n          placeholder={placeholder}\r\n          className={`\r\n            block w-full rounded-lg border border-gray-300 px-3 py-2.5 text-sm \r\n            placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500\r\n            ${Icon ? \"pl-10\" : \"\"} \r\n            ${showPasswordToggle ? \"pr-10\" : \"\"} \r\n            ${\r\n              error\r\n                ? \"border-red-300 focus:border-red-500 focus:ring-red-500\"\r\n                : \"\"\r\n            }\r\n          `}\r\n          {...props}\r\n        />\r\n        {showPasswordToggle && (\r\n          <button\r\n            type=\"button\"\r\n            className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\r\n            onClick={() => setShowPassword(!showPassword)}\r\n          >\r\n            {showPassword ? (\r\n              <EyeOff className=\"h-5 w-5 text-gray-400 hover:text-gray-600\" />\r\n            ) : (\r\n              <Eye className=\"h-5 w-5 text-gray-400 hover:text-gray-600\" />\r\n            )}\r\n          </button>\r\n        )}\r\n      </div>\r\n      {error && <p className=\"text-sm text-red-600\">{error}</p>}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Checkbox = ({\r\n  label,\r\n  error,\r\n  ...props\r\n}: {\r\n  label: string;\r\n  error?: string;\r\n  [key: string]: any;\r\n}) => (\r\n  <div className=\"space-y-1\">\r\n    <div className=\"flex items-center\">\r\n      <input\r\n        type=\"checkbox\"\r\n        className=\"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\r\n        {...props}\r\n      />\r\n      <label className=\"ml-2 block text-sm text-gray-700\">{label}</label>\r\n    </div>\r\n    {error && <p className=\"text-sm text-red-600\">{error}</p>}\r\n  </div>\r\n);\r\n\r\n// Login Component\r\nconst LoginPage = () => {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const router = useRouter();\r\n\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    formState: { errors },\r\n  } = useForm<LoginFormData>({\r\n    resolver: zodResolver(loginSchema),\r\n  });\r\n\r\n  const onSubmit = async (data: LoginFormData) => {\r\n    setIsLoading(true);\r\n    try {\r\n      const res = await loginUser(data);\r\n\r\n      if (!res.success) {\r\n        toast.error(res.message);\r\n        return;\r\n      }\r\n      toast.success(res.message);\r\n\r\n      // Redirect to dashboard after successful login\r\n      router.push(\"/dashboard/blogs\");\r\n    } catch (error) {\r\n      console.error(\"Login error:\", error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\r\n      <div className=\"max-w-md w-full space-y-8\">\r\n        <div className=\"text-center\">\r\n          <div className=\"mx-auto h-12 w-12 bg-gray-800 rounded-full flex items-center justify-center\">\r\n            <LogIn className=\"h-6 w-6 text-white\" />\r\n          </div>\r\n          <h2 className=\"mt-6 text-3xl font-bold text-gray-900\">\r\n            Sign in to your account\r\n          </h2>\r\n          <p className=\"mt-2 text-sm text-gray-600\">\r\n            Welcome back! Please sign in to your account.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"bg-white py-8 px-6 shadow-sm rounded-lg border\">\r\n          <div className=\"space-y-6\">\r\n            <Input\r\n              label=\"Email or Username\"\r\n              type=\"text\"\r\n              placeholder=\"Enter your email or username\"\r\n              icon={User}\r\n              error={errors.email?.message}\r\n              {...register(\"email\")}\r\n            />\r\n\r\n            <Input\r\n              label=\"Password\"\r\n              type=\"password\"\r\n              placeholder=\"Enter your password\"\r\n              icon={Lock}\r\n              showPasswordToggle\r\n              error={errors.password?.message}\r\n              {...register(\"password\")}\r\n            />\r\n\r\n            {/* <div className=\"flex items-center justify-between\">\r\n              <Checkbox label=\"Remember me\" {...register(\"rememberMe\")} />\r\n              <button className=\"text-sm text-blue-600 hover:text-blue-500\">\r\n                Forgot your password?\r\n              </button>\r\n            </div> */}\r\n\r\n            <Button\r\n              type=\"submit\"\r\n              className=\"bg-gray-800 hover:bg-gray-700 cursor-pointer\"\r\n              size=\"lg\"\r\n              fullWidth\r\n              disabled={isLoading}\r\n              onClick={handleSubmit(onSubmit)}\r\n            >\r\n              {isLoading ? \"Signing in...\" : \"Sign in\"}\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n// Register Component (commented out)\r\n// interface IResponse {\r\n//   success: boolean;\r\n//   message: string;\r\n// }\r\n// const RegisterPage = ({ onSwitchToLogin }: { onSwitchToLogin: () => void }) => {\r\n//   const [isLoading, setIsLoading] = useState(false);\r\n\r\n//   const {\r\n//     register,\r\n//     handleSubmit,\r\n//     formState: { errors },\r\n//     watch,\r\n//     reset,\r\n//   } = useForm<RegisterFormData>({\r\n//     resolver: zodResolver(registerSchema),\r\n//   });\r\n\r\n//   const onSubmit = async (data: RegisterFormData) => {\r\n//     setIsLoading(true);\r\n//     try {\r\n//       const { confirmPassword, agreeToTerms, ...submitData } = data;\r\n\r\n//       const res = await fetch(\"/api/auth/register\", {\r\n//         method: \"POST\",\r\n//         headers: {\r\n//           \"Content-Type\": \"application/json\",\r\n//         },\r\n//         body: JSON.stringify({ ...submitData, role: \"admin\" }),\r\n//       });\r\n\r\n//       const { success, message }: IResponse = await res.json();\r\n\r\n//       if (!success) {\r\n//         toast.error(message);\r\n//         return;\r\n//       }\r\n\r\n//       toast.success(message);\r\n//       reset();\r\n//     } catch (error) {\r\n//       console.error(\"Registration error:\", error);\r\n//     } finally {\r\n//       setIsLoading(false);\r\n//     }\r\n//   };\r\n\r\n//   return (\r\n//     <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\r\n//       <div className=\"max-w-md w-full space-y-8\">\r\n//         <div className=\"text-center\">\r\n//           <div className=\"mx-auto h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center\">\r\n//             <UserPlus className=\"h-6 w-6 text-white\" />\r\n//           </div>\r\n//           <h2 className=\"mt-6 text-3xl font-bold text-gray-900\">\r\n//             Create your account\r\n//           </h2>\r\n//           <p className=\"mt-2 text-sm text-gray-600\">\r\n//             Already have an account?{\" \"}\r\n//             <button\r\n//               onClick={onSwitchToLogin}\r\n//               className=\"font-medium text-blue-600 hover:text-blue-500\"\r\n//             >\r\n//               Sign in here\r\n//             </button>\r\n//           </p>\r\n//         </div>\r\n\r\n//         <div className=\"bg-white py-8 px-6 shadow-sm rounded-lg border\">\r\n//           <div className=\"space-y-6\">\r\n//             <Input\r\n//               label=\"Username\"\r\n//               type=\"text\"\r\n//               placeholder=\"Choose a unique username\"\r\n//               icon={User}\r\n//               error={errors.username?.message}\r\n//               {...register(\"username\")}\r\n//             />\r\n\r\n//             <Input\r\n//               label=\"Email Address\"\r\n//               type=\"email\"\r\n//               placeholder=\"Enter your email address\"\r\n//               icon={Mail}\r\n//               error={errors.email?.message}\r\n//               {...register(\"email\")}\r\n//             />\r\n\r\n//             <div className=\"grid grid-cols-2 gap-4\">\r\n//               <Input\r\n//                 label=\"First Name\"\r\n//                 type=\"text\"\r\n//                 placeholder=\"First name\"\r\n//                 error={errors.firstName?.message}\r\n//                 {...register(\"firstName\")}\r\n//               />\r\n//               <Input\r\n//                 label=\"Last Name\"\r\n//                 type=\"text\"\r\n//                 placeholder=\"Last name\"\r\n//                 error={errors.lastName?.message}\r\n//                 {...register(\"lastName\")}\r\n//               />\r\n//             </div>\r\n\r\n//             <Input\r\n//               label=\"Password\"\r\n//               type=\"password\"\r\n//               placeholder=\"Create a strong password\"\r\n//               icon={Lock}\r\n//               showPasswordToggle\r\n//               error={errors.password?.message}\r\n//               {...register(\"password\")}\r\n//             />\r\n\r\n//             <Input\r\n//               label=\"Confirm Password\"\r\n//               type=\"password\"\r\n//               placeholder=\"Confirm your password\"\r\n//               icon={Lock}\r\n//               showPasswordToggle\r\n//               error={errors.confirmPassword?.message}\r\n//               {...register(\"confirmPassword\")}\r\n//             />\r\n\r\n//             <Checkbox\r\n//               label=\"I agree to the Terms of Service and Privacy Policy\"\r\n//               error={errors.agreeToTerms?.message}\r\n//               {...register(\"agreeToTerms\")}\r\n//             />\r\n\r\n//             <Button\r\n//               type=\"submit\"\r\n//               variant=\"primary\"\r\n//               size=\"lg\"\r\n//               fullWidth\r\n//               disabled={isLoading}\r\n//               onClick={handleSubmit(onSubmit)}\r\n//             >\r\n//               {isLoading ? \"Creating account...\" : \"Create account\"}\r\n//             </Button>\r\n//           </div>\r\n//         </div>\r\n//       </div>\r\n//     </div>\r\n//   );\r\n// };\r\n\r\n// Main Auth Component (register functionality commented out)\r\nexport default function AuthPages() {\r\n  // const [currentPage, setCurrentPage] = useState<\"login\" | \"register\">(\"login\");\r\n\r\n  return (\r\n    <>\r\n      <LoginPage />\r\n      {/* Register page commented out */}\r\n      {/* {currentPage === \"login\" ? (\r\n        <LoginPage onSwitchToRegister={() => setCurrentPage(\"register\")} />\r\n      ) : (\r\n        <RegisterPage onSwitchToLogin={() => setCurrentPage(\"login\")} />\r\n      )} */}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AAlBA;;;;;;;;;;AAoBA,yBAAyB;AACzB,MAAM,cAAc,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAE9B;AAkDA,0CAA0C;AAC1C,0DAA0D;AAE1D,yBAAyB;AACzB,MAAM,SAAS,CAAC,EACd,QAAQ,EACR,OAAO,EACP,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,OAAO,QAAQ,EACf,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,YAAY,KAAK,EAUlB;IACC,MAAM,cACJ;IAEF,MAAM,WAAW;QACf,SACE;QACF,WAAW;QACX,SACE;QACF,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW,CAAC;QACV,EAAE,YAAY;QACd,EAAE,QAAQ,CAAC,QAAQ,CAAC;QACpB,EAAE,KAAK,CAAC,KAAK,CAAC;QACd,EAAE,YAAY,WAAW,GAAG;QAC5B,EAAE,WAAW,kCAAkC,GAAG;QAClD,EAAE,UAAU;MACd,CAAC;kBAEA;;;;;;AAGP;AAEA,MAAM,QAAQ,CAAC,EACb,KAAK,EACL,OAAO,MAAM,EACb,WAAW,EACX,KAAK,EACL,MAAM,IAAI,EACV,qBAAqB,KAAK,EAC1B,YAAY,EAAE,EACd,GAAG,OAUJ;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,YAAY,qBACd,eACE,SACA,aACF;IAEJ,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;YACrC,uBACC,8OAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,8OAAC;gBAAI,WAAU;;oBACZ,sBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;;;;;;;;;;;kCAGpB,8OAAC;wBACC,MAAM;wBACN,aAAa;wBACb,WAAW,CAAC;;;YAGV,EAAE,OAAO,UAAU,GAAG;YACtB,EAAE,qBAAqB,UAAU,GAAG;YACpC,EACE,QACI,2DACA,GACL;UACH,CAAC;wBACA,GAAG,KAAK;;;;;;oBAEV,oCACC,8OAAC;wBACC,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,gBAAgB,CAAC;kCAE/B,6BACC,8OAAC,0MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;iDAElB,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAKtB,uBAAS,8OAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;;AAGrD;AAEA,MAAM,WAAW,CAAC,EAChB,KAAK,EACL,KAAK,EACL,GAAG,OAKJ,iBACC,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,MAAK;wBACL,WAAU;wBACT,GAAG,KAAK;;;;;;kCAEX,8OAAC;wBAAM,WAAU;kCAAoC;;;;;;;;;;;;YAEtD,uBAAS,8OAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;;AAInD,kBAAkB;AAClB,MAAM,YAAY;IAChB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAiB;QACzB,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,IAAI;YACF,MAAM,MAAM,MAAM,CAAA,GAAA,0JAAA,CAAA,YAAS,AAAD,EAAE;YAE5B,IAAI,CAAC,IAAI,OAAO,EAAE;gBAChB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,IAAI,OAAO;gBACvB;YACF;YACA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,IAAI,OAAO;YAEzB,+CAA+C;YAC/C,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;QAChC,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,wMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,aAAY;gCACZ,MAAM,kMAAA,CAAA,OAAI;gCACV,OAAO,OAAO,KAAK,EAAE;gCACpB,GAAG,SAAS,QAAQ;;;;;;0CAGvB,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,aAAY;gCACZ,MAAM,kMAAA,CAAA,OAAI;gCACV,kBAAkB;gCAClB,OAAO,OAAO,QAAQ,EAAE;gCACvB,GAAG,SAAS,WAAW;;;;;;0CAU1B,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,MAAK;gCACL,SAAS;gCACT,UAAU;gCACV,SAAS,aAAa;0CAErB,YAAY,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C;AAsJe,SAAS;IACtB,iFAAiF;IAEjF,qBACE;kBACE,cAAA,8OAAC;;;;;;AASP", "debugId": null}}]}