"use client";

import { Card, CardContent } from "@/components/ui/card";
import Image from "next/image";
import { motion } from "framer-motion";
import { useState } from "react";
;

const DestinationCard: React.FC<{ titleKey: string; contentKey: string }> = ({
  titleKey,
  contentKey,
}) => {
  
  const [isHovered, setIsHovered] = useState(false);

  return (
    <Card
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className="relative overflow-hidden min-h-[265px] mx-2 border-[#310267] text-white bg-gradient-to-br from-[#310267] to-[#A945F1]"
    >
      {/* Background red circle */}
      <motion.div
        className="absolute z-0 top-1/2 right-0 bg-[#A945F1] rounded-full"
        initial={{ scale: 0, x: "50%", y: "-50%" }}
        animate={
          isHovered
            ? { scale: 5, transition: { duration: 0.6, ease: "easeInOut" } }
            : { scale: 0, transition: { duration: 0.4, ease: "easeInOut" } }
        }
        style={{ width: 200, height: 200 }}
      />

      {/* Content above the background */}
      <CardContent className="relative z-10">
        <Image
          width={60}
          height={60}
          src="/homepage/choose.png"
          alt="destination"
        />
        <h3 className="font-semibold text-xl mb-1">{titleKey}</h3>
        <p className="text-sm">{contentKey}</p>
      </CardContent>
    </Card>
  );
};

export default DestinationCard;
