// Twelveth

"use client";
// import React, { useRef, useEffect, useState } from "react";
// import { motion } from "framer-motion";
// import { twMerge } from "tailwind-merge";

// interface TextHoverEffectProps {
//   text: string;
//   duration?: number;
//   maxWidth?: number;
//   lineHeight?: number;
// }

// export const TextHoverEffect: React.FC<TextHoverEffectProps> = ({
//   text,
//   duration = 0,
//   maxWidth = 700,
//   lineHeight = 60,
// }) => {
//   const svgRef = useRef<SVGSVGElement>(null);
//   const [cursor, setCursor] = useState({ x: 0, y: 0 });
//   const [hovered, setHovered] = useState(false);
//   const [maskPosition, setMaskPosition] = useState({
//     cx: "50%",
//     cy: "50%",
//     r: "20%",
//   });
//   const [wrappedLines, setWrappedLines] = useState<string[]>([]);

//   // Function to wrap text
//   const wrapText = (text: string, maxWidth: number) => {
//     const words = text.split(" ");
//     const lines: string[] = [];
//     let currentLine = "";

//     // Create a temporary SVG text element to measure text width
//     const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
//     const textElement = document.createElementNS(
//       "http://www.w3.org/2000/svg",
//       "text"
//     );
//     textElement.setAttribute("class", "text-6xl font-bold");
//     textElement.style.fontSize = "48px";
//     textElement.style.fontWeight = "bold";
//     svg.appendChild(textElement);
//     document.body.appendChild(svg);

//     words.forEach((word) => {
//       const testLine = currentLine ? `${currentLine} ${word}` : word;
//       textElement.textContent = testLine;
//       const textWidth = textElement.getBBox().width;

//       if (textWidth > maxWidth && currentLine) {
//         lines.push(currentLine);
//         currentLine = word;
//       } else {
//         currentLine = testLine;
//       }
//     });

//     if (currentLine) {
//       lines.push(currentLine);
//     }

//     document.body.removeChild(svg);
//     return lines;
//   };

//   useEffect(() => {
//     const lines = wrapText(text, maxWidth);
//     setWrappedLines(lines);
//   }, [text, maxWidth]);

//   useEffect(() => {
//     if (svgRef.current) {
//       const svgRect = svgRef.current.getBoundingClientRect();
//       const cxPercentage = (cursor.x / svgRect.width) * 100;
//       const cyPercentage = (cursor.y / svgRect.height) * 100;

//       // Calculate dynamic radius to ensure full coverage at edges
//       const distanceFromEdgeX = Math.min(cursor.x, svgRect.width - cursor.x);
//       const distanceFromEdgeY = Math.min(cursor.y, svgRect.height - cursor.y);
//       const maxDistance = Math.max(distanceFromEdgeX, distanceFromEdgeY);
//       const radius = Math.max(120, maxDistance + 50); // Minimum 120px, or distance to edge + 50px
//       const radiusPercentage =
//         (radius / Math.max(svgRect.width, svgRect.height)) * 100;

//       setMaskPosition({
//         cx: `${cxPercentage}%`,
//         cy: `${cyPercentage}%`,
//         r: `${Math.min(radiusPercentage, 40)}%`, // Cap at 40% to avoid too large spotlight
//       });
//     }
//   }, [cursor]);

//   // Calculate total height needed
//   const totalHeight = wrappedLines.length * lineHeight + 40;
//   const startY = 50;

//   return (
//     <motion.svg
//       ref={svgRef}
//       width="100%"
//       height={totalHeight}
//       viewBox={`0 0 800 ${totalHeight}`}
//       xmlns="http://www.w3.org/2000/svg"
//       onMouseEnter={() => setHovered(true)}
//       onMouseLeave={() => setHovered(false)}
//       onMouseMove={(e) => {
//         const rect = e.currentTarget.getBoundingClientRect();
//         const newX = e.clientX - rect.left;
//         const newY = e.clientY - rect.top;
//         setCursor({ x: newX, y: newY });

//         // Update mask position immediately with dynamic radius
//         const cxPercentage = (newX / rect.width) * 100;
//         const cyPercentage = (newY / rect.height) * 100;

//         const distanceFromEdgeX = Math.min(newX, rect.width - newX);
//         const distanceFromEdgeY = Math.min(newY, rect.height - newY);
//         const maxDistance = Math.max(distanceFromEdgeX, distanceFromEdgeY);
//         const radius = Math.max(120, maxDistance + 50);
//         const radiusPercentage =
//           (radius / Math.max(rect.width, rect.height)) * 100;

//         setMaskPosition({
//           cx: `${cxPercentage}%`,
//           cy: `${cyPercentage}%`,
//           r: `${Math.min(radiusPercentage, 40)}%`,
//         });
//       }}
//       className={twMerge("select-none bg-black")}
//     >
//       <defs>
//         <radialGradient
//           id="shineMask"
//           gradientUnits="userSpaceOnUse"
//           r="20%"
//           cx={maskPosition.cx}
//           cy={maskPosition.cy}
//         >
//           <stop offset="0%" stopColor="white" stopOpacity="1" />
//           <stop offset="50%" stopColor="white" stopOpacity="0.8" />
//           <stop offset="100%" stopColor="white" stopOpacity="0" />
//         </radialGradient>
//         <mask id="textMask">
//           <rect x="0" y="0" width="100%" height="100%" fill="black" />
//           <rect x="0" y="0" width="100%" height="100%" fill="url(#shineMask)" />
//         </mask>
//       </defs>

//       {/* Background text (darker) */}
//       {wrappedLines.map((line, index) => (
//         <text
//           key={`bg-${index}`}
//           x="50%"
//           y={startY + index * lineHeight}
//           textAnchor="middle"
//           dominantBaseline="middle"
//           className="fill-gray-600 text-6xl font-bold"
//         >
//           {line}
//         </text>
//       ))}

//       {/* Spotlight effect text */}
//       {wrappedLines.map((line, index) => (
//         <text
//           key={`shine-${index}`}
//           x="50%"
//           y={startY + index * lineHeight}
//           textAnchor="middle"
//           dominantBaseline="middle"
//           fill="white"
//           mask="url(#textMask)"
//           className="text-6xl font-bold"
//         >
//           {line}
//         </text>
//       ))}
//     </motion.svg>
//   );
// };

// "use client";
// import React, { useRef, useEffect, useState } from "react";
// import { motion } from "framer-motion";
// import { twMerge } from "tailwind-merge";

// interface TextHoverEffectProps {
//   text: string;
//   duration?: number;
//   maxWidth?: number;
//   lineHeight?: number;
// }

// export const TextHoverEffect: React.FC<TextHoverEffectProps> = ({
//   text,
//   duration = 0,
//   maxWidth = 700,
//   lineHeight = 60,
// }) => {
//   const svgRef = useRef<SVGSVGElement>(null);
//   const [cursor, setCursor] = useState({ x: 0, y: 0 });
//   const [hovered, setHovered] = useState(false);
//   const [maskPosition, setMaskPosition] = useState({
//     cx: "50%",
//     cy: "50%",
//     r: "20%",
//   });
//   const [wrappedLines, setWrappedLines] = useState<string[]>([]);

//   // Function to wrap text
//   const wrapText = (text: string, maxWidth: number) => {
//     const words = text.split(" ");
//     const lines: string[] = [];
//     let currentLine = "";

//     // Create a temporary SVG text element to measure text width
//     const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
//     const textElement = document.createElementNS(
//       "http://www.w3.org/2000/svg",
//       "text"
//     );
//     textElement.setAttribute("class", "text-6xl font-bold");
//     textElement.style.fontSize = "48px";
//     textElement.style.fontWeight = "bold";
//     svg.appendChild(textElement);
//     document.body.appendChild(svg);

//     words.forEach((word) => {
//       const testLine = currentLine ? `${currentLine} ${word}` : word;
//       textElement.textContent = testLine;
//       const textWidth = textElement.getBBox().width;

//       if (textWidth > maxWidth && currentLine) {
//         lines.push(currentLine);
//         currentLine = word;
//       } else {
//         currentLine = testLine;
//       }
//     });

//     if (currentLine) {
//       lines.push(currentLine);
//     }

//     document.body.removeChild(svg);
//     return lines;
//   };

//   useEffect(() => {
//     const lines = wrapText(text, maxWidth);
//     setWrappedLines(lines);
//   }, [text, maxWidth]);

//   useEffect(() => {
//     if (svgRef.current) {
//       const svgRect = svgRef.current.getBoundingClientRect();
//       const cxPercentage = (cursor.x / svgRect.width) * 100;
//       const cyPercentage = (cursor.y / svgRect.height) * 100;

//       // Calculate dynamic radius to ensure full coverage at edges
//       const distanceFromEdgeX = Math.min(cursor.x, svgRect.width - cursor.x);
//       const distanceFromEdgeY = Math.min(cursor.y, svgRect.height - cursor.y);
//       const maxDistance = Math.max(distanceFromEdgeX, distanceFromEdgeY);
//       const radius = Math.max(120, maxDistance + 50); // Minimum 120px, or distance to edge + 50px
//       const radiusPercentage =
//         (radius / Math.max(svgRect.width, svgRect.height)) * 100;

//       setMaskPosition({
//         cx: `${cxPercentage}%`,
//         cy: `${cyPercentage}%`,
//         r: `${Math.min(radiusPercentage, 40)}%`, // Cap at 40% to avoid too large spotlight
//       });
//     }
//   }, [cursor]);

//   // Calculate total height needed
//   const totalHeight = wrappedLines.length * lineHeight + 40;
//   const startY = 50;

//   return (
//     <motion.svg
//       ref={svgRef}
//       width="100%"
//       height={totalHeight}
//       viewBox={`0 0 800 ${totalHeight}`}
//       xmlns="http://www.w3.org/2000/svg"
//       onMouseEnter={() => setHovered(true)}
//       onMouseLeave={() => setHovered(false)}
//       onMouseMove={(e) => {
//         const rect = e.currentTarget.getBoundingClientRect();
//         const newX = e.clientX - rect.left;
//         const newY = e.clientY - rect.top;
//         setCursor({ x: newX, y: newY });

//         // Update mask position immediately with dynamic radius
//         const cxPercentage = (newX / rect.width) * 100;
//         const cyPercentage = (newY / rect.height) * 100;

//         const distanceFromEdgeX = Math.min(newX, rect.width - newX);
//         const distanceFromEdgeY = Math.min(newY, rect.height - newY);
//         const maxDistance = Math.max(distanceFromEdgeX, distanceFromEdgeY);
//         const radius = Math.max(120, maxDistance + 50);
//         const radiusPercentage =
//           (radius / Math.max(rect.width, rect.height)) * 100;

//         setMaskPosition({
//           cx: `${cxPercentage}%`,
//           cy: `${cyPercentage}%`,
//           r: `${Math.min(radiusPercentage, 40)}%`,
//         });
//       }}
//       className={twMerge("select-none bg-black")}
//     >
//       <defs>
//         <radialGradient
//           id="shineMask"
//           gradientUnits="userSpaceOnUse"
//           r="20%"
//           cx={maskPosition.cx}
//           cy={maskPosition.cy}
//         >
//           <stop offset="0%" stopColor="white" stopOpacity="1" />
//           <stop offset="40%" stopColor="white" stopOpacity="0.95" />
//           <stop offset="60%" stopColor="white" stopOpacity="0.5" />
//           <stop offset="75%" stopColor="white" stopOpacity="0.2" />
//           <stop offset="85%" stopColor="white" stopOpacity="0.1" />
//           <stop offset="100%" stopColor="white" stopOpacity="0.05" />
//         </radialGradient>

//         <mask id="textMask">
//           <rect x="0" y="0" width="100%" height="100%" fill="black" />
//           <rect x="0" y="0" width="100%" height="100%" fill="url(#shineMask)" />
//         </mask>
//       </defs>
//       <circle
//         cx={maskPosition.cx}
//         cy={maskPosition.cy}
//         r={maskPosition.r}
//         fill="none"
//         stroke="white"
//         strokeWidth="2"
//         opacity={hovered ? 0.15 : 0}
//       />

//       {/* Background text (darker) */}
//       {wrappedLines.map((line, index) => (
//         <text
//           key={`bg-${index}`}
//           x="50%"
//           y={startY + index * lineHeight}
//           textAnchor="middle"
//           dominantBaseline="middle"
//           className="fill-gray-600 text-6xl font-bold"
//         >
//           {line}
//         </text>
//       ))}

//       {/* Spotlight effect text */}
//       {wrappedLines.map((line, index) => (
//         <text
//           key={`shine-${index}`}
//           x="50%"
//           y={startY + index * lineHeight}
//           textAnchor="middle"
//           dominantBaseline="middle"
//           fill="white"
//           mask="url(#textMask)"
//           className="text-6xl font-bold"
//         >
//           {line}
//         </text>
//       ))}
//     </motion.svg>
//   );
// };

// "use client";
// import React, { useRef, useEffect, useState } from "react";
// import { motion } from "framer-motion";
// import { twMerge } from "tailwind-merge";

// interface TextHoverEffectProps {
//   text: string;
//   duration?: number;
//   maxWidth?: number;
//   lineHeight?: number;
// }

// export const TextHoverEffect: React.FC<TextHoverEffectProps> = ({
//   text,
//   duration = 0,
//   maxWidth = 700,
//   lineHeight = 60,
// }) => {
//   const svgRef = useRef<SVGSVGElement>(null);
//   const [cursor, setCursor] = useState({ x: 0, y: 0 });
//   const [hovered, setHovered] = useState(false);
//   const [maskPosition, setMaskPosition] = useState({
//     cx: "50%",
//     cy: "50%",
//   });
//   const [maskRadiusPx, setMaskRadiusPx] = useState(120); // in pixels
//   const [wrappedLines, setWrappedLines] = useState<string[]>([]);

//   // Text wrapping logic
//   const wrapText = (text: string, maxWidth: number) => {
//     const words = text.split(" ");
//     const lines: string[] = [];
//     let currentLine = "";

//     const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
//     const textElement = document.createElementNS(
//       "http://www.w3.org/2000/svg",
//       "text"
//     );
//     textElement.setAttribute("class", "text-6xl font-bold");
//     textElement.style.fontSize = "48px";
//     textElement.style.fontWeight = "bold";
//     svg.appendChild(textElement);
//     document.body.appendChild(svg);

//     words.forEach((word) => {
//       const testLine = currentLine ? `${currentLine} ${word}` : word;
//       textElement.textContent = testLine;
//       const textWidth = textElement.getBBox().width;

//       if (textWidth > maxWidth && currentLine) {
//         lines.push(currentLine);
//         currentLine = word;
//       } else {
//         currentLine = testLine;
//       }
//     });

//     if (currentLine) {
//       lines.push(currentLine);
//     }

//     document.body.removeChild(svg);
//     return lines;
//   };

//   useEffect(() => {
//     const lines = wrapText(text, maxWidth);
//     setWrappedLines(lines);
//   }, [text, maxWidth]);

//   const updateMask = (x: number, y: number, width: number, height: number) => {
//     const cxPercentage = (x / width) * 100;
//     const cyPercentage = (y / height) * 100;

//     const distanceX = Math.min(x, width - x);
//     const distanceY = Math.min(y, height - y);
//     const maxDistance = Math.max(distanceX, distanceY);
//     const radius = Math.max(100, maxDistance + 50); // in px

//     setMaskRadiusPx(radius);
//     setMaskPosition({
//       cx: `${cxPercentage}%`,
//       cy: `${cyPercentage}%`,
//     });
//   };

//   // Recalculate on cursor update
//   useEffect(() => {
//     if (svgRef.current) {
//       const rect = svgRef.current.getBoundingClientRect();
//       updateMask(cursor.x, cursor.y, rect.width, rect.height);
//     }
//   }, [cursor]);

//   const totalHeight = wrappedLines.length * lineHeight + 40;
//   const startY = 50;

//   return (
//     <motion.svg
//       ref={svgRef}
//       width="100%"
//       height={totalHeight}
//       viewBox={`0 0 800 ${totalHeight}`}
//       xmlns="http://www.w3.org/2000/svg"
//       onMouseEnter={() => setHovered(true)}
//       onMouseLeave={() => setHovered(false)}
//       onMouseMove={(e) => {
//         const rect = e.currentTarget.getBoundingClientRect();
//         const newX = e.clientX - rect.left;
//         const newY = e.clientY - rect.top;
//         setCursor({ x: newX, y: newY });
//         updateMask(newX, newY, rect.width, rect.height);
//       }}
//       className={twMerge("select-none bg-black")}
//     >
//       <defs>
//         <radialGradient
//           id="shineMask"
//           gradientUnits="userSpaceOnUse"
//           r={(maskRadiusPx / 800) * 100 + "%"} // scale r into percentage
//           cx={maskPosition.cx}
//           cy={maskPosition.cy}
//         >
//           <stop offset="0%" stopColor="white" stopOpacity="1" />
//           <stop offset="40%" stopColor="white" stopOpacity="0.95" />
//           <stop offset="60%" stopColor="white" stopOpacity="0.5" />
//           <stop offset="75%" stopColor="white" stopOpacity="0.2" />
//           <stop offset="85%" stopColor="white" stopOpacity="0.1" />
//           <stop offset="100%" stopColor="white" stopOpacity="0.05" />
//         </radialGradient>

//         <mask id="textMask">
//           <rect x="0" y="0" width="100%" height="100%" fill="black" />
//           <rect x="0" y="0" width="100%" height="100%" fill="url(#shineMask)" />
//         </mask>
//       </defs>

//       {/* Circle outline matches actual spotlight */}
//       <circle
//         cx={maskPosition.cx}
//         cy={maskPosition.cy}
//         r={maskRadiusPx}
//         fill="none"
//         stroke="white"
//         strokeWidth="2"
//         opacity={hovered ? 0.15 : 0}
//         pointerEvents="none"
//       />

//       {/* Background Text */}
//       {wrappedLines.map((line, index) => (
//         <text
//           key={`bg-${index}`}
//           x="50%"
//           y={startY + index * lineHeight}
//           textAnchor="middle"
//           dominantBaseline="middle"
//           className="fill-gray-600 text-6xl font-bold"
//         >
//           {line}
//         </text>
//       ))}

//       {/* Foreground Text with Spotlight */}
//       {wrappedLines.map((line, index) => (
//         <text
//           key={`shine-${index}`}
//           x="50%"
//           y={startY + index * lineHeight}
//           textAnchor="middle"
//           dominantBaseline="middle"
//           fill="white"
//           mask="url(#textMask)"
//           className="text-6xl font-bold"
//         >
//           {line}
//         </text>
//       ))}
//     </motion.svg>
//   );
// };

// "use client";
// import React, { useRef, useEffect, useState } from "react";
// import { motion } from "framer-motion";
// import { twMerge } from "tailwind-merge";

// interface TextHoverEffectProps {
//   text: string;
//   duration?: number;
//   maxWidth?: number;
//   lineHeight?: number;
//   spotlightRadiusPx?: number;
// }

// export const TextHoverEffect: React.FC<TextHoverEffectProps> = ({
//   text,
//   duration = 0,
//   maxWidth = 1024,
//   lineHeight = 60,
//   spotlightRadiusPx = 150, // Fixed radius
// }) => {
//   const svgRef = useRef<SVGSVGElement>(null);
//   const [cursor, setCursor] = useState({ x: 0, y: 0 });
//   const [hovered, setHovered] = useState(false);
//   const [maskPosition, setMaskPosition] = useState({
//     cx: "50%",
//     cy: "50%",
//   });
//   const [wrappedLines, setWrappedLines] = useState<string[]>([]);

//   // Wrap long text based on width
//   const wrapText = (text: string, maxWidth: number) => {
//     const words = text.split(" ");
//     const lines: string[] = [];
//     let currentLine = "";

//     const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
//     const textElement = document.createElementNS(
//       "http://www.w3.org/2000/svg",
//       "text"
//     );
//     textElement.setAttribute("class", "text-6xl font-bold");
//     textElement.style.fontSize = "48px";
//     textElement.style.fontWeight = "bold";
//     svg.appendChild(textElement);
//     document.body.appendChild(svg);

//     words.forEach((word) => {
//       const testLine = currentLine ? `${currentLine} ${word}` : word;
//       textElement.textContent = testLine;
//       const textWidth = textElement.getBBox().width;

//       if (textWidth > maxWidth && currentLine) {
//         lines.push(currentLine);
//         currentLine = word;
//       } else {
//         currentLine = testLine;
//       }
//     });

//     if (currentLine) {
//       lines.push(currentLine);
//     }

//     document.body.removeChild(svg);
//     return lines;
//   };

//   useEffect(() => {
//     const lines = wrapText(text, maxWidth);
//     setWrappedLines(lines);
//   }, [text, maxWidth]);

//   useEffect(() => {
//     if (svgRef.current) {
//       const rect = svgRef.current.getBoundingClientRect();
//       const cxPercentage = (cursor.x / rect.width) * 100;
//       const cyPercentage = (cursor.y / rect.height) * 100;

//       setMaskPosition({
//         cx: `${cxPercentage}%`,
//         cy: `${cyPercentage}%`,
//       });
//     }
//   }, [cursor]);

//   const totalHeight = wrappedLines.length * lineHeight + 40;
//   const startY = 50;

//   return (
//     <motion.svg
//       ref={svgRef}
//       width="100%"
//       height={totalHeight}
//       viewBox={`0 0 800 ${totalHeight}`}
//       xmlns="http://www.w3.org/2000/svg"
//       onMouseEnter={() => setHovered(true)}
//       onMouseLeave={() => setHovered(false)}
//       onMouseMove={(e) => {
//         const rect = e.currentTarget.getBoundingClientRect();
//         const newX = e.clientX - rect.left;
//         const newY = e.clientY - rect.top;
//         setCursor({ x: newX, y: newY });

//         const cxPercentage = (newX / rect.width) * 100;
//         const cyPercentage = (newY / rect.height) * 100;

//         setMaskPosition({
//           cx: `${cxPercentage}%`,
//           cy: `${cyPercentage}%`,
//         });
//       }}
//       className={twMerge("select-none bg-black")}
//     >
//       <defs>
//         <radialGradient
//           id="shineMask"
//           gradientUnits="userSpaceOnUse"
//           r={(spotlightRadiusPx / 800) * 100 + "%"}
//           cx={maskPosition.cx}
//           cy={maskPosition.cy}
//         >
//           <stop offset="0%" stopColor="white" stopOpacity="1" />
//           <stop offset="40%" stopColor="white" stopOpacity="0.95" />
//           <stop offset="60%" stopColor="white" stopOpacity="0.5" />
//           <stop offset="75%" stopColor="white" stopOpacity="0.2" />
//           <stop offset="85%" stopColor="white" stopOpacity="0.1" />
//           <stop offset="100%" stopColor="white" stopOpacity="0.05" />
//         </radialGradient>

//         <mask id="textMask">
//           <rect x="0" y="0" width="100%" height="100%" fill="black" />
//           <rect x="0" y="0" width="100%" height="100%" fill="url(#shineMask)" />
//         </mask>
//       </defs>

//       {/* Circle outline to match spotlight */}
//       {/* <circle
//         cx={maskPosition.cx}
//         cy={maskPosition.cy}
//         r={spotlightRadiusPx}
//         fill="none"
//         stroke="white"
//         strokeWidth="2"
//         opacity={hovered ? 0.15 : 0}
//         pointerEvents="none"
//       /> */}

//       {/* Background text */}
//       {wrappedLines.map((line, index) => (
//         <text
//           key={`bg-${index}`}
//           x="50%"
//           y={startY + index * lineHeight}
//           textAnchor="middle"
//           dominantBaseline="middle"
//           className="fill-gray-600 jakarta text-4xl font-bold"
//         >
//           {line}
//         </text>
//       ))}

//       {/* Foreground text with spotlight */}
//       {wrappedLines.map((line, index) => (
//         <text
//           key={`shine-${index}`}
//           x="50%"
//           y={startY + index * lineHeight}
//           textAnchor="middle"
//           dominantBaseline="middle"
//           fill="white"
//           mask="url(#textMask)"
//           className="text-4xl jakarta font-bold"
//         >
//           {line}
//         </text>
//       ))}
//     </motion.svg>
//
import React, { useRef, useEffect, useState } from "react";
import { motion } from "framer-motion";
import { twMerge } from "tailwind-merge";
interface TextHoverEffectProps {
  text: string;
  duration?: number;
  maxWidth?: number;
  lineHeight?: number;
  spotlightRadiusPx?: number;
}

export const TextHoverEffect: React.FC<TextHoverEffectProps> = ({
  text,
  duration = 0,
  maxWidth = 1024,
  lineHeight = 60,
  spotlightRadiusPx = 150,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [cursor, setCursor] = useState({ x: 0, y: 0 });
  const [hovered, setHovered] = useState(false);
  const [maskPosition, setMaskPosition] = useState({
    cx: "50%",
    cy: "50%",
  });
  const [wrappedLines, setWrappedLines] = useState<string[]>([]);

  // Wrap long text based on width
  const wrapText = (text: string, maxWidth: number) => {
    const words = text.split(" ");
    const lines: string[] = [];
    let currentLine = "";

    const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
    const textElement = document.createElementNS(
      "http://www.w3.org/2000/svg",
      "text"
    );
    textElement.setAttribute("class", "text-6xl font-bold");
    textElement.style.fontSize = "48px";
    textElement.style.fontWeight = "bold";
    svg.appendChild(textElement);
    document.body.appendChild(svg);

    words.forEach((word) => {
      const testLine = currentLine ? `${currentLine} ${word}` : word;
      textElement.textContent = testLine;
      const textWidth = textElement.getBBox().width;

      if (textWidth > maxWidth && currentLine) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        currentLine = testLine;
      }
    });

    if (currentLine) {
      lines.push(currentLine);
    }

    document.body.removeChild(svg);
    return lines;
  };

  useEffect(() => {
    const lines = wrapText(text, maxWidth);
    setWrappedLines(lines);
  }, [text, maxWidth]);

  // Remove this useEffect to prevent infinite loop
  // The mask position is now updated directly in onMouseMove

  const totalHeight = wrappedLines.length * lineHeight + 40;
  const startY = 50;

  return (
    <motion.svg
      ref={svgRef}
      width="100%"
      height={totalHeight}
      viewBox={`0 0 800 ${totalHeight}`}
      xmlns="http://www.w3.org/2000/svg"
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      onMouseMove={(e) => {
        const rect = e.currentTarget.getBoundingClientRect();
        const newX = e.clientX - rect.left;
        const newY = e.clientY - rect.top;

        // Only update if position actually changed significantly
        if (Math.abs(newX - cursor.x) > 5 || Math.abs(newY - cursor.y) > 5) {
          setCursor({ x: newX, y: newY });

          const cxPercentage = (newX / rect.width) * 100;
          const cyPercentage = (newY / rect.height) * 100;

          setMaskPosition({
            cx: `${cxPercentage}%`,
            cy: `${cyPercentage}%`,
          });
        }
      }}
      className={twMerge("select-none bg-black")}
    >
      <defs>
        <radialGradient
          id="shineMask"
          gradientUnits="userSpaceOnUse"
          r={(spotlightRadiusPx / 800) * 100 + "%"}
          cx={maskPosition.cx}
          cy={maskPosition.cy}
        >
          <stop offset="0%" stopColor="white" stopOpacity="1" />
          <stop offset="40%" stopColor="white" stopOpacity="0.95" />
          <stop offset="60%" stopColor="white" stopOpacity="0.5" />
          <stop offset="75%" stopColor="white" stopOpacity="0.2" />
          <stop offset="85%" stopColor="white" stopOpacity="0.1" />
          <stop offset="100%" stopColor="white" stopOpacity="0.05" />
        </radialGradient>

        <mask id="textMask">
          <rect x="0" y="0" width="100%" height="100%" fill="black" />
          <rect x="0" y="0" width="100%" height="100%" fill="url(#shineMask)" />
        </mask>
      </defs>

      {/* Background text (always visible) */}
      {wrappedLines.map((line, index) => (
        <text
          key={`bg-${index}`}
          x="50%"
          y={startY + index * lineHeight}
          textAnchor="middle"
          dominantBaseline="middle"
          className="fill-gray-600 jakarta text-4xl font-bold"
        >
          {line}
        </text>
      ))}

      {/* Foreground text (only on hover) */}
      {hovered &&
        wrappedLines.map((line, index) => (
          <text
            key={`shine-${index}`}
            x="50%"
            y={startY + index * lineHeight}
            textAnchor="middle"
            dominantBaseline="middle"
            fill="white"
            mask="url(#textMask)"
            className="text-4xl jakarta font-bold"
          >
            {line}
          </text>
        ))}
    </motion.svg>
  );
};
