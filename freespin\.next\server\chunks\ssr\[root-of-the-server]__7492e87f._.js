module.exports = {

"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"0083a550d7711b950e0380eaf183245f5ff600cd2d":"logoutUser","00f4c1f708bce729d9b8d919d949572c63e67c3019":"getCurrentUser","40854177558e038788b8501074df3232f423f42673":"registerUser","40c18b8283e746cbd2fd864a875e466648111e74da":"loginUser","40f9b4ed2c9a1c8918e6e97e967f70e121e1c693f5":"loginAction"},"",""] */ __turbopack_context__.s({
    "getCurrentUser": (()=>getCurrentUser),
    "loginAction": (()=>loginAction),
    "loginUser": (()=>loginUser),
    "logoutUser": (()=>logoutUser),
    "registerUser": (()=>registerUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function loginUser(body) {
    try {
        // Use absolute URL for server-side fetch
        const baseUrl = ("TURBOPACK compile-time value", "https://freespin168.asia") || "http://localhost:3000";
        const response = await fetch(`${baseUrl}/api/auth/login`, {
            method: "POST",
            body: JSON.stringify(body),
            headers: {
                "Content-Type": "application/json"
            }
        });
        if (!response.ok) {
            const errorData = await response.json();
            return {
                success: false,
                message: errorData.error || "Login failed"
            };
        }
        const result = await response.json();
        console.log("Login result:", result);
        if (result.success && result.data) {
            const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
            // Set session token (httpOnly for security)
            cookieStore.set("session-token", result.data.token, {
                httpOnly: true,
                secure: ("TURBOPACK compile-time value", "development") === "production",
                sameSite: "lax",
                maxAge: 60 * 60 * 24 * 7,
                path: "/"
            });
            // Set user info (accessible to client-side)
            if (result.data.user) {
                const userInfo = JSON.stringify({
                    id: result.data.user.id,
                    email: result.data.user.email,
                    name: `${result.data.user.firstName || ""} ${result.data.user.lastName || ""}`.trim() || result.data.user.username,
                    username: result.data.user.username,
                    role: result.data.user.role
                });
                // Set server-side cookie
                cookieStore.set("user-info", userInfo, {
                    httpOnly: false,
                    secure: ("TURBOPACK compile-time value", "development") === "production",
                    sameSite: "lax",
                    maxAge: 60 * 60 * 24 * 7,
                    path: "/"
                });
                // Also set client-side cookie for immediate access
                if (typeof document !== "undefined") {
                    const maxAge = 60 * 60 * 24 * 7; // 7 days in seconds
                    const expires = new Date(Date.now() + maxAge * 1000).toUTCString();
                    document.cookie = `user-info=${encodeURIComponent(userInfo)}; expires=${expires}; path=/; SameSite=lax`;
                }
            }
        }
        return result;
    } catch (error) {
        console.error("Login error:", error);
        return {
            success: false,
            message: "Network error. Please check your connection and try again."
        };
    }
}
async function logoutUser() {
    try {
        const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
        // Clear session token
        cookieStore.delete("session-token");
        // Clear user info
        cookieStore.delete("user-info");
        // Optionally call logout API endpoint
        const baseUrl = ("TURBOPACK compile-time value", "https://freespin168.asia") || "http://localhost:3000";
        try {
            await fetch(`${baseUrl}/api/auth/logout`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                }
            });
        } catch (error) {
            // Ignore API errors for logout, cookies are already cleared
            console.warn("Logout API call failed:", error);
        }
        return {
            success: true,
            message: "Logged out successfully"
        };
    } catch (error) {
        console.error("Logout error:", error);
        return {
            success: false,
            message: "Logout failed"
        };
    }
}
async function registerUser(body) {
    try {
        const baseUrl = ("TURBOPACK compile-time value", "https://freespin168.asia") || "http://localhost:3000";
        const response = await fetch(`${baseUrl}/api/auth/register`, {
            method: "POST",
            body: JSON.stringify(body),
            headers: {
                "Content-Type": "application/json"
            }
        });
        if (!response.ok) {
            const errorData = await response.json();
            return {
                success: false,
                message: errorData.error || "Registration failed"
            };
        }
        const result = await response.json();
        // Auto-login after successful registration
        if (result.success && result.data) {
            const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
            cookieStore.set("session-token", result.data.token, {
                httpOnly: true,
                secure: ("TURBOPACK compile-time value", "development") === "production",
                sameSite: "lax",
                maxAge: 60 * 60 * 24 * 7,
                path: "/"
            });
            if (result.data.user) {
                cookieStore.set("user-info", JSON.stringify({
                    id: result.data.user.id,
                    email: result.data.user.email,
                    name: `${result.data.user.firstName || ""} ${result.data.user.lastName || ""}`.trim() || result.data.user.username,
                    username: result.data.user.username,
                    role: result.data.user.role
                }), {
                    httpOnly: false,
                    secure: ("TURBOPACK compile-time value", "development") === "production",
                    sameSite: "lax",
                    maxAge: 60 * 60 * 24 * 7,
                    path: "/"
                });
            }
        }
        return result;
    } catch (error) {
        console.error("Registration error:", error);
        return {
            success: false,
            message: "Network error. Please check your connection and try again."
        };
    }
}
async function getCurrentUser() {
    try {
        const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
        const userInfo = cookieStore.get("user-info");
        const sessionToken = cookieStore.get("session-token");
        if (!userInfo || !sessionToken) {
            return null;
        }
        return JSON.parse(userInfo.value);
    } catch (error) {
        console.error("Error getting current user:", error);
        return null;
    }
}
async function loginAction(formData) {
    const email = formData.get("email");
    const password = formData.get("password");
    if (!email || !password) {
        return {
            success: false,
            message: "Email and password are required"
        };
    }
    const result = await loginUser({
        email,
        password
    });
    if (result.success) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])("/dashboard/blogs"); // Redirect after successful login
    }
    return result;
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    loginUser,
    logoutUser,
    registerUser,
    getCurrentUser,
    loginAction
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(loginUser, "40c18b8283e746cbd2fd864a875e466648111e74da", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(logoutUser, "0083a550d7711b950e0380eaf183245f5ff600cd2d", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(registerUser, "40854177558e038788b8501074df3232f423f42673", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getCurrentUser, "00f4c1f708bce729d9b8d919d949572c63e67c3019", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(loginAction, "40f9b4ed2c9a1c8918e6e97e967f70e121e1c693f5", null);
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
    throw new Error("Please define the MONGODB_URI environment variable inside .env.local");
}
/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */ // @ts-expect-error //global can have type any
let cached = global?.mongoose;
if (!cached) {
    // @ts-expect-error //global mongoose can have type any
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function connectDB() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts).then((mongoose)=>{
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = connectDB;
}}),
"[project]/src/models/Post.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const PostSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    // Blog Post fields
    title: {
        type: String,
        required: [
            true,
            "Post title is required"
        ],
        trim: true,
        maxlength: [
            1000,
            "Title cannot exceed 200 characters"
        ]
    },
    slug: {
        type: String,
        trim: true,
        lowercase: true
    },
    canonicalUrl: {
        type: String,
        trim: true,
        validate: {
            validator: (v)=>{
                if (!v) return true; // Allow empty string
                return /^https?:\/\/.+/.test(v);
            },
            message: "Canonical URL must be a valid URL"
        }
    },
    existingUrl: {
        type: Boolean,
        default: false
    },
    content: {
        type: String,
        required: [
            true,
            "Post content is required"
        ]
    },
    excerpt: {
        type: String,
        trim: true,
        maxlength: [
            1000,
            "Excerpt cannot exceed 300 characters"
        ]
    },
    description: {
        type: String
    },
    author: {
        type: String,
        trim: true
    },
    isBlog: {
        type: Boolean,
        default: true
    },
    categories: [
        {
            type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Schema.Types.ObjectId,
            ref: "Category"
        }
    ],
    tags: [
        {
            type: String,
            trim: true,
            lowercase: true
        }
    ],
    // SEO fields
    metaTitle: {
        type: String,
        required: [
            true,
            "Meta title is required"
        ],
        maxlength: [
            1000,
            "Meta title cannot exceed 60 characters"
        ]
    },
    metaDescription: {
        type: String,
        required: [
            true,
            "Meta description is required"
        ],
        maxlength: [
            1000,
            "Meta description cannot exceed 160 characters"
        ]
    },
    metaKeywords: {
        type: String,
        required: [
            true,
            "Meta keywords are required"
        ],
        maxlength: [
            1000,
            "Meta keywords cannot exceed 200 characters"
        ]
    },
    // Banner fields
    banner: {
        type: String,
        required: [
            true,
            "Banner image is required"
        ],
        trim: true
    },
    // Additional blog functionality fields
    status: {
        type: String,
        enum: [
            "draft",
            "published",
            "archived"
        ],
        default: "draft"
    },
    isPublished: {
        type: Boolean,
        default: false
    },
    publishedAt: {
        type: Date,
        default: null
    },
    views: {
        type: Number,
        default: 0
    },
    readTime: {
        type: Number,
        default: 1
    },
    isTopNews: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: true
});
// Pre-save middleware
PostSchema.pre("save", function(next) {
    // Generate slug from title if not provided
    if (this.isModified("title") && !this.slug) {
        this.slug = this.title.toLowerCase().replace(/[^a-z0-9]+/g, "-").replace(/^-+|-+$/g, "");
    }
    // Calculate read time (average 200 words per minute)
    if (this.isModified("content")) {
        const wordCount = (this.content || "").split(/\s+/).length;
        this.readTime = Math.ceil(wordCount / 200);
    }
    // Set published date when status changes to published
    if (this.isModified("status") && this.status === "published" && !this.publishedAt) {
        this.publishedAt = new Date();
        this.isPublished = true;
    }
    next();
});
// Create indexes for better query performance
PostSchema.index({
    categories: 1
});
PostSchema.index({
    tags: 1
});
PostSchema.index({
    status: 1
});
PostSchema.index({
    isPublished: 1
});
PostSchema.index({
    publishedAt: -1
});
PostSchema.index({
    "banner.title": 1
});
PostSchema.index({
    title: "text",
    content: "text",
    metaTitle: "text",
    metaDescription: "text"
}); // Text search index
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Post || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model("Post", PostSchema);
}}),
"[project]/src/models/Category.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const CategorySchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    name: {
        type: String,
        required: [
            true,
            "Category name is required"
        ],
        unique: true,
        trim: true,
        maxlength: [
            50,
            "Category name cannot exceed 50 characters"
        ]
    },
    description: {
        type: String,
        maxlength: [
            200,
            "Description cannot exceed 200 characters"
        ]
    },
    color: {
        type: String,
        default: "#6366f1",
        match: [
            /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
            "Please enter a valid hex color"
        ]
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});
CategorySchema.index({
    isActive: 1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Category || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model("Category", CategorySchema);
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/string_decoder [external] (string_decoder, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("string_decoder", () => require("string_decoder"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/timers [external] (timers, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("timers", () => require("timers"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/node:fs [external] (node:fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:fs", () => require("node:fs"));

module.exports = mod;
}}),
"[externals]/node:fs [external] (node:fs, cjs) <export promises as fsp>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "fsp": (()=>__TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$fs__$5b$external$5d$__$28$node$3a$fs$2c$__cjs$29$__["promises"])
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$fs__$5b$external$5d$__$28$node$3a$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:fs [external] (node:fs, cjs)");
}}),
"[project]/src/lib/minio.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BUCKET_NAME": (()=>BUCKET_NAME),
    "MinioService": (()=>MinioService),
    "minioClient": (()=>minioClient),
    "minioConfig": (()=>minioConfig)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$minio$2f$dist$2f$esm$2f$minio$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/minio/dist/esm/minio.mjs [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$minio$2f$dist$2f$esm$2f$minio$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/minio/dist/esm/minio.mjs [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/stream [external] (stream, cjs)");
;
;
// MinIO configuration
const minioConfig = {
    endPoint: process.env.MINIO_ENDPOINT?.replace(/^https?:\/\//, "") || "localhost",
    port: process.env.MINIO_PORT ? Number.parseInt(process.env.MINIO_PORT) : 9000,
    useSSL: process.env.MINIO_USE_SSL === "true",
    accessKey: process.env.MINIO_ACCESS_KEY || "3uiq5emitjasdfghyjui",
    secretKey: process.env.MINIO_SECRET_KEY || "TYo1ruP1PqbOx3fONapGwawKQGqCDQsdfadsdfgh"
};
const BUCKET_NAME = process.env.MINIO_BUCKET_NAME || "spinfree";
// Create MinIO client
const minioClient = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$minio$2f$dist$2f$esm$2f$minio$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Client"](minioConfig);
// Initialize bucket if it doesn't exist
const initializeBucket = async ()=>{
    try {
        console.log("Checking MinIO connection...");
        console.log("MinIO config:", {
            endPoint: minioConfig.endPoint,
            port: minioConfig.port,
            useSSL: minioConfig.useSSL,
            bucket: BUCKET_NAME
        });
        const bucketExists = await minioClient.bucketExists(BUCKET_NAME);
        if (!bucketExists) {
            await minioClient.makeBucket(BUCKET_NAME, "us-east-1");
            console.log(`Bucket '${BUCKET_NAME}' created successfully`);
            // Set bucket policy to allow public read access
            const policy = {
                Version: "2012-10-17",
                Statement: [
                    {
                        Effect: "Allow",
                        Principal: {
                            AWS: [
                                "*"
                            ]
                        },
                        Action: [
                            "s3:GetObject"
                        ],
                        Resource: [
                            `arn:aws:s3:::${BUCKET_NAME}/*`
                        ]
                    }
                ]
            };
            await minioClient.setBucketPolicy(BUCKET_NAME, JSON.stringify(policy));
            console.log(`Bucket policy set for '${BUCKET_NAME}'`);
        } else {
            console.log(`Bucket '${BUCKET_NAME}' already exists`);
        }
    } catch (error) {
        console.error("Error initializing MinIO bucket:", error);
    // Don't throw error, allow application to continue
    }
};
// File type detection
const detectImageType = (buffer)=>{
    // Check for PNG
    if (buffer[0] === 0x89 && buffer[1] === 0x50 && buffer[2] === 0x4e && buffer[3] === 0x47) {
        return "image/png";
    }
    // Check for JPEG
    if (buffer[0] === 0xff && buffer[1] === 0xd8 && buffer[2] === 0xff) {
        return "image/jpeg";
    }
    // Check for SVG
    const possibleSvg = buffer.toString("ascii", 0, 100).toLowerCase();
    if (possibleSvg.includes("<svg") || possibleSvg.includes("<?xml")) {
        return "image/svg+xml";
    }
    return null;
};
class MinioService {
    static async uploadFile(fileBuffer, fileName, contentType, folder = "posts") {
        try {
            console.log("Starting file upload to MinIO...");
            // Check MinIO connection first
            try {
                await minioClient.bucketExists(BUCKET_NAME);
                console.log("MinIO connection verified");
            } catch (connectionError) {
                console.error("MinIO connection failed:", connectionError);
                throw new Error("MinIO service is unavailable. Please try again later.");
            }
            // Initialize bucket if needed
            await initializeBucket();
            // Detect actual file type if it's octet-stream
            let mimeType = contentType;
            if (contentType === "application/octet-stream") {
                const detectedType = detectImageType(fileBuffer);
                if (!detectedType) {
                    throw new Error("Invalid or unsupported image format");
                }
                mimeType = detectedType;
            }
            // Generate unique filename with date structure
            const date = new Date();
            const fileExtension = mimeType === "image/svg+xml" ? "svg" : mimeType === "image/png" ? "png" : "jpg";
            const uniqueFileName = `${Date.now()}-${fileName.replace(/\.[^/.]+$/, "")}.${fileExtension}`;
            const relativePath = [
                folder,
                date.getFullYear().toString(),
                (date.getMonth() + 1).toString().padStart(2, "0"),
                uniqueFileName
            ].join("/");
            console.log("Uploading to path:", relativePath);
            // Create readable stream from buffer
            const fileStream = new __TURBOPACK__imported__module__$5b$externals$5d2f$stream__$5b$external$5d$__$28$stream$2c$__cjs$29$__["Readable"]();
            fileStream.push(fileBuffer);
            fileStream.push(null);
            // Upload to MinIO
            await minioClient.putObject(BUCKET_NAME, relativePath, fileStream, fileBuffer.length, {
                "Content-Type": mimeType
            });
            // Generate URL through our API proxy instead of direct MinIO URL
            const baseUrl = ("TURBOPACK compile-time value", "https://freespin168.asia") || "http://localhost:3001";
            const fileUrl = `${baseUrl}/api/uploads/${relativePath}`;
            console.log(`File uploaded to MinIO successfully!`);
            console.log(`- MinIO path: ${relativePath}`);
            console.log(`- Proxy URL: ${fileUrl}`);
            console.log(`- Base URL: ${baseUrl}`);
            return fileUrl;
        } catch (error) {
            console.error("Error uploading file to MinIO:", error);
            console.error("MinIO config:", {
                endPoint: minioConfig.endPoint,
                port: minioConfig.port,
                useSSL: minioConfig.useSSL,
                bucket: BUCKET_NAME
            });
            console.error("Upload details:", {
                fileName,
                contentType,
                folder,
                bufferSize: fileBuffer.length
            });
            // Fallback: return a placeholder URL instead of throwing
            const fallbackUrl = `/uploads/${folder}/${Date.now()}-${fileName}`;
            console.log("Upload failed, using fallback URL:", fallbackUrl);
            return fallbackUrl;
        }
    }
    static async deleteFile(fileUrl) {
        try {
            // Extract object name from URL
            const url = new URL(fileUrl);
            const objectName = url.pathname.substring(url.pathname.indexOf("/", 1) + 1);
            await minioClient.removeObject(BUCKET_NAME, objectName);
            console.log(`File deleted from MinIO: ${objectName}`);
        } catch (error) {
            console.error("Error deleting file from MinIO:", error);
            throw new Error("Failed to delete file");
        }
    }
    static async listFiles(folder = "posts", maxKeys = 100) {
        try {
            const files = [];
            const stream = minioClient.listObjects(BUCKET_NAME, `${folder}/`, true);
            return new Promise((resolve, reject)=>{
                stream.on("data", (obj)=>{
                    if (files.length < maxKeys) {
                        const protocol = minioConfig.useSSL ? "https" : "http";
                        const port = minioConfig.port !== (minioConfig.useSSL ? 443 : 80) ? `:${minioConfig.port}` : "";
                        const fileUrl = `${protocol}://${minioConfig.endPoint}${port}/${BUCKET_NAME}/${obj.name}`;
                        files.push({
                            name: obj.name,
                            size: obj.size,
                            lastModified: obj.lastModified,
                            url: fileUrl,
                            fileName: obj.name?.split("/").pop() || "",
                            folder: obj.name?.split("/").slice(0, -1).join("/") || ""
                        });
                    }
                });
                stream.on("end", ()=>resolve(files));
                stream.on("error", (err)=>reject(err));
            });
        } catch (error) {
            console.error("Error listing files:", error);
            throw new Error("Failed to list files");
        }
    }
    static async getPresignedUrl(objectName, expiry = 3600) {
        try {
            return await minioClient.presignedGetObject(BUCKET_NAME, objectName, expiry);
        } catch (error) {
            console.error("Error generating presigned URL:", error);
            throw new Error("Failed to generate presigned URL");
        }
    }
    static async getFileStream(objectName) {
        try {
            const stream = await minioClient.getObject(BUCKET_NAME, objectName);
            const chunks = [];
            return new Promise((resolve, reject)=>{
                stream.on("data", (chunk)=>chunks.push(chunk));
                stream.on("end", ()=>resolve(Buffer.concat(chunks)));
                stream.on("error", reject);
            });
        } catch (error) {
            console.error("Error getting file stream from MinIO:", error);
            throw new Error("Failed to get file from MinIO");
        }
    }
}
// Initialize bucket on module load (but don't block)
initializeBucket().catch((err)=>{
    console.error("Critical error during MinIO initialization:", err);
});
;
}}),
"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00aaf3229ad66ce8c0e05f422008f0d7c0448605e4":"getCategoriesAction","409d162b3b4f864115692081373dbef024bdcff963":"createCategoryAction","607c10ea8628475a8cbfb06ec6fe9153598d2e744a":"updatePostStatusAction","60e82a0f1ab060d924d48393e65f78bcf87b8015d6":"createPostAction","70ca012623c33fa3ea1bb858d64dc26cce9ffda769":"updatePostAction"},"",""] */ __turbopack_context__.s({
    "createCategoryAction": (()=>createCategoryAction),
    "createPostAction": (()=>createPostAction),
    "getCategoriesAction": (()=>getCategoriesAction),
    "updatePostAction": (()=>updatePostAction),
    "updatePostStatusAction": (()=>updatePostStatusAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Post$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Post.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Category$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Category.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$minio$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/minio.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";
async function updatePostStatusAction(postId, status) {
    try {
        // Get cookies directly
        const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
        const authToken = cookieStore.get("auth-token")?.value;
        const userInfoCookie = cookieStore.get("user-info")?.value;
        let userInfo = null;
        // Try both auth methods
        if (authToken) {
            try {
                userInfo = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].verify(authToken, JWT_SECRET);
            } catch (error) {
                console.log("Token verification failed:", error);
            }
        }
        // Fallback to user-info cookie
        if (!userInfo && userInfoCookie) {
            try {
                const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));
                if (parsedUserInfo.role === "admin") {
                    userInfo = parsedUserInfo;
                }
            } catch (error) {
                console.log("Failed to parse user-info cookie:", error);
            }
        }
        if (!userInfo) {
            return {
                success: false,
                error: "Authentication required"
            };
        }
        // Connect to database
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])();
        // Find the post first
        const post = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Post$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].findById(postId);
        if (!post) {
            return {
                success: false,
                error: "Post not found"
            };
        }
        // Update the status and save to trigger middleware
        post.status = status;
        // Manually handle the published state logic
        if (status === "published") {
            post.isPublished = true;
            if (!post.publishedAt) {
                post.publishedAt = new Date();
            }
        } else {
            post.isPublished = false;
        }
        const updatedPost = await post.save();
        // Revalidate the public posts cache to show updated posts immediately
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])("public-posts");
        // Also revalidate the blogs page to show updated posts
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/en/blogs");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/th/blogs");
        // Serialize the post data for client components
        const serializedPost = {
            _id: updatedPost._id.toString(),
            title: updatedPost.title,
            status: updatedPost.status,
            isPublished: updatedPost.isPublished,
            publishedAt: updatedPost.publishedAt?.toISOString(),
            createdAt: updatedPost.createdAt?.toISOString(),
            updatedAt: updatedPost.updatedAt?.toISOString()
        };
        return {
            success: true,
            data: {
                post: serializedPost
            }
        };
    } catch (error) {
        console.error("Error updating post status:", error);
        return {
            success: false,
            error: "Internal server error"
        };
    }
}
async function getCategoriesAction() {
    try {
        // Get cookies directly
        const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
        console.log("=== CATEGORIES ACTION DEBUG ===");
        console.log("All cookies:", cookieStore.getAll());
        // Try both auth methods: httpOnly token and user-info cookie
        const authToken = cookieStore.get("auth-token")?.value;
        const userInfoCookie = cookieStore.get("user-info")?.value;
        console.log("Auth token:", authToken ? "Found" : "Not found");
        console.log("User info cookie:", userInfoCookie ? "Found" : "Not found");
        let userInfo = null;
        // Method 1: Try httpOnly token
        if (authToken) {
            try {
                const decoded = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].verify(authToken, JWT_SECRET);
                console.log("Token verified successfully for user:", decoded.userId);
                userInfo = decoded;
            } catch (error) {
                console.log("Token verification failed:", error);
            }
        }
        // Method 2: Try user-info cookie (fallback)
        if (!userInfo && userInfoCookie) {
            try {
                const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));
                console.log("User info from cookie:", parsedUserInfo);
                // Verify user is admin
                if (parsedUserInfo.role === "admin") {
                    userInfo = parsedUserInfo;
                    console.log("Admin access granted via user-info cookie");
                } else {
                    return {
                        success: false,
                        error: "Admin access required"
                    };
                }
            } catch (error) {
                console.log("Failed to parse user-info cookie:", error);
            }
        }
        if (!userInfo) {
            return {
                success: false,
                error: "No valid authentication found"
            };
        }
        // Connect to database
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])();
        // Get all categories
        const categories = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Category$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].find({
            isActive: true
        }).sort({
            name: 1
        }).lean();
        // Convert MongoDB documents to plain objects for client components
        const serializedCategories = categories.map((category)=>({
                _id: category._id.toString(),
                name: category.name,
                description: category.description || "",
                color: category.color || "#6366f1",
                isActive: category.isActive,
                createdAt: category.createdAt?.toISOString() || new Date().toISOString(),
                updatedAt: category.updatedAt?.toISOString() || new Date().toISOString()
            }));
        return {
            success: true,
            data: {
                categories: serializedCategories
            }
        };
    } catch (error) {
        console.error("Error fetching categories:", error);
        return {
            success: false,
            error: "Internal server error"
        };
    }
}
async function createCategoryAction(data) {
    try {
        // Get cookies directly
        const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
        const authToken = cookieStore.get("auth-token")?.value;
        const userInfoCookie = cookieStore.get("user-info")?.value;
        let userInfo = null;
        // Try both auth methods
        if (authToken) {
            try {
                userInfo = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].verify(authToken, JWT_SECRET);
            } catch (error) {
                console.log("Token verification failed:", error);
            }
        }
        // Fallback to user-info cookie
        if (!userInfo && userInfoCookie) {
            try {
                const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));
                if (parsedUserInfo.role === "admin") {
                    userInfo = parsedUserInfo;
                }
            } catch (error) {
                console.log("Failed to parse user-info cookie:", error);
            }
        }
        if (!userInfo) {
            return {
                success: false,
                error: "Authentication required"
            };
        }
        // Connect to database
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])();
        // Check if category already exists (case-insensitive)
        const existingCategory = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Category$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].findOne({
            name: {
                $regex: new RegExp(`^${data.name}$`, "i")
            }
        });
        if (existingCategory) {
            return {
                success: false,
                error: "Category already exists"
            };
        }
        // Create new category
        const category = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Category$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"](data);
        await category.save();
        // Serialize the category for client components
        const serializedCategory = {
            _id: category._id.toString(),
            name: category.name,
            description: category.description || "",
            color: category.color || "#6366f1",
            isActive: category.isActive,
            createdAt: category.createdAt?.toISOString() || new Date().toISOString(),
            updatedAt: category.updatedAt?.toISOString() || new Date().toISOString()
        };
        return {
            success: true,
            message: "Category created successfully",
            data: {
                category: serializedCategory
            }
        };
    } catch (error) {
        console.error("Error creating category:", error);
        return {
            success: false,
            error: "Internal server error"
        };
    }
}
async function createPostAction(postData, bannerImageBase64) {
    console.log("hello");
    try {
        // Get cookies directly
        const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
        const authToken = cookieStore.get("auth-token")?.value;
        const userInfoCookie = cookieStore.get("user-info")?.value;
        let userInfo = null;
        // Try both auth methods
        if (authToken) {
            try {
                userInfo = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].verify(authToken, JWT_SECRET);
            } catch (error) {
                console.log("Token verification failed:", error);
            }
        }
        // Fallback to user-info cookie
        if (!userInfo && userInfoCookie) {
            try {
                const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));
                if (parsedUserInfo.role === "admin") {
                    userInfo = parsedUserInfo;
                }
            } catch (error) {
                console.log("Failed to parse user-info cookie:", error);
            }
        }
        if (!userInfo) {
            return {
                success: false,
                error: "Authentication required"
            };
        }
        // Connect to database
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])();
        // Handle banner image upload if provided
        let bannerUrl = "";
        if (bannerImageBase64) {
            try {
                // Extract content type and base64 data
                const matches = bannerImageBase64.match(/^data:(.+);base64,(.+)$/);
                if (!matches || matches.length !== 3) {
                    throw new Error("Invalid base64 image format");
                }
                const contentType = matches[1]; // e.g., "image/jpeg", "image/png"
                const base64Data = matches[2];
                const imageBuffer = Buffer.from(base64Data, "base64");
                // Generate filename with correct extension
                const timestamp = Date.now();
                const fileExtension = contentType.split("/")[1] || "jpg";
                const filename = `banner-${timestamp}.${fileExtension}`;
                console.log("Uploading banner image:", {
                    contentType,
                    filename,
                    bufferSize: imageBuffer.length
                });
                // Upload to MinIO
                bannerUrl = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$minio$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MinioService"].uploadFile(imageBuffer, filename, contentType, "banners" // folder
                );
                console.log("Banner uploaded successfully:", bannerUrl);
            } catch (error) {
                console.error("Error uploading banner:", error);
            // Continue without banner if upload fails
            }
        }
        // Create the post
        const postToCreate = {
            ...postData,
            banner: bannerUrl || postData.banner || "",
            author: userInfo.userId || userInfo._id,
            status: "draft",
            isBlog: true,
            isPublished: false
        };
        const post = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Post$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"](postToCreate);
        await post.save();
        // Serialize the post for client components
        const serializedPost = {
            _id: post._id.toString(),
            title: post.title,
            slug: post.slug,
            excerpt: post.excerpt,
            content: post.content,
            banner: post.banner,
            status: post.status,
            isPublished: post.isPublished,
            publishedAt: post.publishedAt?.toISOString(),
            createdAt: post.createdAt?.toISOString(),
            updatedAt: post.updatedAt?.toISOString()
        };
        return {
            success: true,
            message: "Post created successfully",
            data: {
                post: serializedPost
            }
        };
    } catch (error) {
        console.error("Error creating post:", error);
        return {
            success: false,
            error: "Internal server error"
        };
    }
}
async function updatePostAction(postId, postData, bannerImageBase64) {
    try {
        // Get cookies directly
        const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cookies"])();
        const authToken = cookieStore.get("auth-token")?.value;
        const userInfoCookie = cookieStore.get("user-info")?.value;
        let userInfo = null;
        // Try both auth methods
        if (authToken) {
            try {
                userInfo = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].verify(authToken, JWT_SECRET);
            } catch (error) {
                console.log("Token verification failed:", error);
            }
        }
        // Fallback to user-info cookie
        if (!userInfo && userInfoCookie) {
            try {
                const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));
                if (parsedUserInfo.role === "admin") {
                    userInfo = parsedUserInfo;
                }
            } catch (error) {
                console.log("Failed to parse user-info cookie:", error);
            }
        }
        if (!userInfo) {
            return {
                success: false,
                error: "Authentication required"
            };
        }
        // Connect to database
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])();
        // Find the post
        const post = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Post$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].findOne({
            slug: postId
        });
        if (!post) {
            return {
                success: false,
                error: "Post not found"
            };
        }
        // Handle banner image upload if provided
        let bannerUrl = postData.banner?.image || post.banner?.image || post.banner || "";
        if (bannerImageBase64) {
            try {
                // Extract content type and base64 data
                const matches = bannerImageBase64.match(/^data:(.+);base64,(.+)$/);
                if (!matches || matches.length !== 3) {
                    throw new Error("Invalid base64 image format");
                }
                const contentType = matches[1];
                const base64Data = matches[2];
                const imageBuffer = Buffer.from(base64Data, "base64");
                // Generate filename with correct extension
                const timestamp = Date.now();
                const fileExtension = contentType.split("/")[1] || "jpg";
                const filename = `banner-${timestamp}.${fileExtension}`;
                console.log("Uploading banner image:", {
                    contentType,
                    filename,
                    bufferSize: imageBuffer.length
                });
                // Upload to MinIO
                bannerUrl = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$minio$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MinioService"].uploadFile(imageBuffer, filename, contentType, "banners");
                console.log("Banner uploaded successfully:", bannerUrl);
            } catch (error) {
                console.error("Error uploading banner:", error);
            // Continue without banner if upload fails
            }
        }
        // Clean the postData to avoid circular references
        const cleanPostData = {
            title: postData.title,
            slug: postData.slug,
            content: postData.content,
            categories: postData.categories,
            tags: postData.tags,
            status: postData.status,
            isPublished: postData.isPublished,
            publishedAt: postData.publishedAt,
            metaTitle: postData.metaTitle,
            metaDescription: postData.metaDescription,
            metaKeywords: postData.metaKeywords
        };
        const updateData = {
            ...cleanPostData,
            banner: bannerUrl,
            seoTitle: postData.metaTitle,
            seoDescription: postData.metaDescription,
            metaKeywords: postData.metaKeywords,
            author: userInfo.userId || userInfo._id
        };
        // Update the post
        const updatedPost = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Post$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].findOneAndUpdate({
            slug: postId
        }, updateData, {
            new: true,
            runValidators: true
        }).populate("categories", "name description color").lean(); // Add .lean() to get plain objects
        if (!updatedPost) {
            return {
                success: false,
                error: "Failed to update post"
            };
        }
        // Serialize the post for client components (ensure no circular references)
        const serializedPost = {
            _id: updatedPost._id.toString(),
            title: updatedPost.title,
            slug: updatedPost.slug,
            content: updatedPost.content,
            banner: updatedPost.banner,
            categories: Array.isArray(updatedPost.categories) ? updatedPost.categories.map((cat)=>({
                    _id: cat._id?.toString(),
                    name: cat.name,
                    description: cat.description,
                    color: cat.color
                })) : [],
            tags: updatedPost.tags || [],
            seoTitle: updatedPost.seoTitle,
            seoDescription: updatedPost.seoDescription,
            metaKeywords: updatedPost.metaKeywords,
            status: updatedPost.status,
            isPublished: updatedPost.isPublished,
            publishedAt: updatedPost.publishedAt?.toISOString?.() || updatedPost.publishedAt,
            createdAt: updatedPost.createdAt?.toISOString?.() || updatedPost.createdAt,
            updatedAt: updatedPost.updatedAt?.toISOString?.() || updatedPost.updatedAt
        };
        // Revalidate caches
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidateTag"])("public-posts");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/en/blogs");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/th/blogs");
        return {
            success: true,
            message: "Post updated successfully",
            data: {
                post: serializedPost
            }
        };
    } catch (error) {
        console.error("Error updating post:", error);
        return {
            success: false,
            error: "Internal server error"
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    updatePostStatusAction,
    getCategoriesAction,
    createCategoryAction,
    createPostAction,
    updatePostAction
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updatePostStatusAction, "607c10ea8628475a8cbfb06ec6fe9153598d2e744a", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getCategoriesAction, "00aaf3229ad66ce8c0e05f422008f0d7c0448605e4", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(createCategoryAction, "409d162b3b4f864115692081373dbef024bdcff963", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(createPostAction, "60e82a0f1ab060d924d48393e65f78bcf87b8015d6", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(updatePostAction, "70ca012623c33fa3ea1bb858d64dc26cce9ffda769", null);
}}),
"[project]/.next-internal/server/app/dashboard/blogs/add/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$client_apis$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)");
;
;
;
}}),
"[project]/.next-internal/server/app/dashboard/blogs/add/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$client_apis$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$blogs$2f$add$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$client_apis$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/dashboard/blogs/add/page/actions.js { ACTIONS_MODULE0 => "[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/dashboard/blogs/add/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "0083a550d7711b950e0380eaf183245f5ff600cd2d": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$client_apis$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["logoutUser"]),
    "00aaf3229ad66ce8c0e05f422008f0d7c0448605e4": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCategoriesAction"]),
    "60e82a0f1ab060d924d48393e65f78bcf87b8015d6": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createPostAction"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$client_apis$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$blogs$2f$add$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$client_apis$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/dashboard/blogs/add/page/actions.js { ACTIONS_MODULE0 => "[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/dashboard/blogs/add/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "0083a550d7711b950e0380eaf183245f5ff600cd2d": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$blogs$2f$add$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$client_apis$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["0083a550d7711b950e0380eaf183245f5ff600cd2d"]),
    "00aaf3229ad66ce8c0e05f422008f0d7c0448605e4": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$blogs$2f$add$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$client_apis$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["00aaf3229ad66ce8c0e05f422008f0d7c0448605e4"]),
    "60e82a0f1ab060d924d48393e65f78bcf87b8015d6": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$blogs$2f$add$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$client_apis$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["60e82a0f1ab060d924d48393e65f78bcf87b8015d6"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$blogs$2f$add$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$client_apis$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/dashboard/blogs/add/page/actions.js { ACTIONS_MODULE0 => "[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f$dashboard$2f$blogs$2f$add$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$src$2f$client_apis$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/dashboard/blogs/add/page/actions.js { ACTIONS_MODULE0 => "[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/dashboard/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/dashboard/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MultiSelect": (()=>MultiSelect),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const MultiSelect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call MultiSelect() from the server but MultiSelect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx <module evaluation>", "MultiSelect");
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx <module evaluation>", "default");
}}),
"[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MultiSelect": (()=>MultiSelect),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const MultiSelect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call MultiSelect() from the server but MultiSelect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx", "MultiSelect");
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx", "default");
}}),
"[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$components$2f$BlogPostEditor$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$components$2f$BlogPostEditor$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$components$2f$BlogPostEditor$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/app/dashboard/blogs/add/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$components$2f$BlogPostEditor$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx [app-rsc] (ecmascript)");
;
;
const page = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$components$2f$BlogPostEditor$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
        fileName: "[project]/src/app/dashboard/blogs/add/page.tsx",
        lineNumber: 5,
        columnNumber: 10
    }, this);
};
const __TURBOPACK__default__export__ = page;
}}),
"[project]/src/app/dashboard/blogs/add/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/dashboard/blogs/add/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__7492e87f._.js.map