import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import connectDB from "@/lib/mongodb";
import Category from "@/models/Category";
import Post from "@/models/Post";
import {
  withAdmin,
  withLogging,
  type AuthenticatedRequest,
} from "@/lib/middleware";

const updateCategorySchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .max(50, "Name too long")
    .trim()
    .optional(),
  description: z.string().max(200, "Description too long").optional(),
  color: z
    .string()
    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, "Invalid color format")
    .optional(),
  isActive: z.boolean().optional(),
});

// GET /api/categories/[id] - Get a specific category (Public)
// export async function GET(
//   request: NextRequest,
//   context: { params: { id: string } }
// ) {
//   const { id } = context.params;
//   console.log("🔥 GET CATEGORY ROUTE HIT - ID:", id);

//   try {
//     await connectDB();

//     if (!id.match(/^[0-9a-fA-F]{24}$/)) {
//       return NextResponse.json(
//         { success: false, error: "Invalid category ID format" },
//         { status: 400 }
//       );
//     }

//     const category = await Category.findById(id).lean();

//     if (!category) {
//       return NextResponse.json(
//         { success: false, error: "Category not found" },
//         { status: 404 }
//       );
//     }

//     const postCount = await Post.countDocuments({
//       category: id,
//       status: "published",
//     });

//     return NextResponse.json({
//       success: true,
//       data: {
//         category: {
//           ...category,
//           postCount,
//         },
//       },
//     });
//   } catch (error) {
//     console.error("💥 Error fetching category:", error);
//     return NextResponse.json(
//       { success: false, error: "Failed to fetch category" },
//       { status: 500 }
//     );
//   }
// }

// PUT /api/categories/[id] - Update a category (Admin only)
// export const PUT = withLogging(
//   withAdmin(
//     async (
//       request: AuthenticatedRequest,
//       { params }: { params: { id: string } }
//     ) => {
//       console.log("🔥 PUT CATEGORY ROUTE HIT - ID:", params.id);

//       try {
//         await connectDB();

//         // Validate ObjectId format
//         if (!params.id.match(/^[0-9a-fA-F]{24}$/)) {
//           return NextResponse.json(
//             { success: false, error: "Invalid category ID format" },
//             { status: 400 }
//           );
//         }

//         const body = await request.json();
//         const validatedData = updateCategorySchema.parse(body);

//         // Check if category exists
//         const existingCategory = await Category.findById(params.id);
//         if (!existingCategory) {
//           console.log("❌ Category not found for update:", params.id);
//           return NextResponse.json(
//             { success: false, error: "Category not found" },
//             { status: 404 }
//           );
//         }

//         // If updating name, check for duplicates
//         if (
//           validatedData.name &&
//           validatedData.name !== existingCategory.name
//         ) {
//           const duplicateCategory = await Category.findOne({
//             name: { $regex: new RegExp(`^${validatedData.name}$`, "i") },
//             _id: { $ne: params.id },
//           });

//           if (duplicateCategory) {
//             return NextResponse.json(
//               { success: false, error: "Category name already exists" },
//               { status: 400 }
//             );
//           }
//         }

//         // Update category
//         const updatedCategory = await Category.findByIdAndUpdate(
//           params.id,
//           validatedData,
//           {
//             new: true,
//             runValidators: true,
//           }
//         ).lean();

//         console.log("✅ Updated category:", updatedCategory?.name);

//         return NextResponse.json({
//           success: true,
//           message: "Category updated successfully",
//           data: { category: updatedCategory },
//           updatedBy: request.user!.username,
//         });
//       } catch (error: any) {
//         if (error instanceof z.ZodError) {
//           return NextResponse.json(
//             {
//               success: false,
//               error: "Validation failed",
//               details: error.errors,
//             },
//             { status: 400 }
//           );
//         }

//         // Handle MongoDB duplicate key errors
//         if (error.code === 11000 && error.keyPattern?.name) {
//           return NextResponse.json(
//             { success: false, error: "Category name already exists" },
//             { status: 400 }
//           );
//         }

//         console.error("💥 Error updating category:", error);
//         return NextResponse.json(
//           { success: false, error: "Failed to update category" },
//           { status: 500 }
//         );
//       }
//     }
//   )
// );

// DELETE /api/categories/[id] - Delete a category (Admin only)
export const DELETE = withLogging(
  withAdmin(
    async (
      request: AuthenticatedRequest,
      { params }: { params: { id: string } }
    ) => {
      console.log("🔥 DELETE CATEGORY ROUTE HIT - ID:", params.id);
      console.log("🔥 Request method:", request.method);
      console.log("🔥 Request URL:", request.url);

      try {
        await connectDB();

        // Validate ObjectId format
        if (!params.id.match(/^[0-9a-fA-F]{24}$/)) {
          console.log("❌ Invalid ObjectId format:", params.id);
          return NextResponse.json(
            { success: false, error: "Invalid category ID format" },
            { status: 400 }
          );
        }

        // Check if category exists
        const category = await Category.findById(params.id);
        if (!category) {
          console.log("❌ Category not found for deletion:", params.id);
          return NextResponse.json(
            { success: false, error: "Category not found" },
            { status: 404 }
          );
        }

        console.log("✅ Found category to delete:", category.name);

        // Check if category has posts
        const postCount = await Post.countDocuments({ category: params.id });
        if (postCount > 0) {
          console.log("❌ Category has posts, cannot delete:", postCount);
          return NextResponse.json(
            {
              success: false,
              error: `Cannot delete category. It has ${postCount} associated posts. Please reassign or delete the posts first.`,
            },
            { status: 400 }
          );
        }

        // Delete category
        await Category.findByIdAndDelete(params.id);

        console.log("✅ Successfully deleted category:", category.name);

        return NextResponse.json({
          success: true,
          message: "Category deleted successfully",
          deletedBy: request.user!.username,
        });
      } catch (error) {
        console.error("💥 Error deleting category:", error);
        return NextResponse.json(
          { success: false, error: "Failed to delete category" },
          { status: 500 }
        );
      }
    }
  )
);
