import React from "react";
import clsx from "clsx";
import { MagicMarquee } from "@/components/magicui/marquee";

const FreeSpinText = () => {
  const texts = [
    { text: "FreeSpin168", className: `from-[#3159C6] to-[#9A6D6D]` },
    {
      text: "▪",
      className: `to-[#3159C6] from-[#9A6D6D] text-lg flex items-end`,
    },
    { text: "FreeSpin168", className: `to-[#3159C6] from-[#9A6D6D]` },
    {
      text: "▪",
      className: `to-[#3159C6] from-[#9A6D6D] text-lg flex items-end`,
    },
    { text: "FreeSpin168", className: `from-[#3159C6] to-[#9A6D6D]` },
    {
      text: "▪",
      className: `to-[#3159C6] from-[#9A6D6D] text-lg flex items-end`,
    },
    { text: "<PERSON>Spin168", className: `to-[#3159C6] from-[#9A6D6D]` },
    {
      text: "▪",
      className: `to-[#3159C6] from-[#9A6D6D] text-lg flex items-end`,
    },
  ];
  return (
    <MagicMarquee className="[--duration:30s] ">
      {texts.map((item, index) => (
        <span
          key={index}
          className={clsx(
            "bg-clip-text text-transparent text-6xl font-bold bg-gradient-to-r",
            item.className
          )}
        >
          {item.text}
        </span>
      ))}
    </MagicMarquee>
  );
};

export default FreeSpinText;
