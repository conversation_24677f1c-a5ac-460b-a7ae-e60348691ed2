/** @type {import('next-sitemap').IConfig} */
module.exports = {
  // siteUrl: process.env.SITE_URL || "https://freespin168.asia",
  siteUrl: "https://www.freespin168.asia",
  generateRobotsTxt: true,
  generateIndexSitemap: false,
  exclude: [
    "/api/*",
    "/dashboard/*",
    "/auth/*",
    "/unauthorized",
    "/server-sitemap.xml",
    "/*/dashboard/*",
    "/*/auth/*",
    "/*/unauthorized",
  ],
  // Default transformation function
  transform: async (config, path) => {
    // Remove locale prefix for cleaner URLs in sitemap
    const cleanPath = path.replace(/^\/(en|th)/, "") || "/";

    return {
      loc: cleanPath,
      changefreq: "daily",
      priority: cleanPath === "/" ? 1.0 : 0.7,
      lastmod: new Date().toISOString(),
    };
  },
  additionalPaths: async (config) => {
    const paths = [
      "/",
      "/aboutus",
      "/blogs",
      "/all-bets",
      "/pretty-game",
      "/pragmatic-play",
      "/wm-casino",
      "/big-game",
      "/on-gaming-lobby",
      "/sexy",
      "/sa",
      "/dream-gaming",
      "/evolution-lobby",
      "/sv388",
      "/pg-games",
      "/sbo-bet",
      "/lalika",
      "/jili-games",
      "/home",
    ];

    return paths.map((path) => ({
      loc: path,
      changefreq: "daily",
      priority: path === "/" ? 1.0 : 0.7,
      lastmod: new Date().toISOString(),
    }));
  },
  robotsTxtOptions: {
    policies: [
      {
        userAgent: "Googlebot",
        allow: "/",
      },
      {
        userAgent: "*",
        allow: "/",
        disallow: ["/api/", "/dashboard/", "/auth/", "/unauthorized"],
      },
    ],
  },
};
