{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/categories/[id]", "regex": "^/api/categories/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/categories/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/posts/[id]", "regex": "^/api/posts/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/posts/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/posts/[id]/status", "regex": "^/api/posts/([^/]+?)/status(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/posts/(?<nxtPid>[^/]+?)/status(?:/)?$"}, {"page": "/api/posts/[id]/toggle-top-news", "regex": "^/api/posts/([^/]+?)/toggle\\-top\\-news(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/posts/(?<nxtPid>[^/]+?)/toggle\\-top\\-news(?:/)?$"}, {"page": "/api/uploads/[...path]", "regex": "^/api/uploads/(.+?)(?:/)?$", "routeKeys": {"nxtPpath": "nxtPpath"}, "namedRegex": "^/api/uploads/(?<nxtPpath>.+?)(?:/)?$"}, {"page": "/blogs/[id]", "regex": "^/blogs/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/blogs/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/dashboard/blogs/edit/[id]", "regex": "^/dashboard/blogs/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/dashboard/blogs/edit/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/aboutus", "regex": "^/aboutus(?:/)?$", "routeKeys": {}, "namedRegex": "^/aboutus(?:/)?$"}, {"page": "/all-bets", "regex": "^/all\\-bets(?:/)?$", "routeKeys": {}, "namedRegex": "^/all\\-bets(?:/)?$"}, {"page": "/auth", "regex": "^/auth(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth(?:/)?$"}, {"page": "/big-game", "regex": "^/big\\-game(?:/)?$", "routeKeys": {}, "namedRegex": "^/big\\-game(?:/)?$"}, {"page": "/blogs", "regex": "^/blogs(?:/)?$", "routeKeys": {}, "namedRegex": "^/blogs(?:/)?$"}, {"page": "/dashboard/blogs", "regex": "^/dashboard/blogs(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/blogs(?:/)?$"}, {"page": "/dashboard/blogs/add", "regex": "^/dashboard/blogs/add(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/blogs/add(?:/)?$"}, {"page": "/dashboard/blogs/categories", "regex": "^/dashboard/blogs/categories(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/blogs/categories(?:/)?$"}, {"page": "/dream-gaming", "regex": "^/dream\\-gaming(?:/)?$", "routeKeys": {}, "namedRegex": "^/dream\\-gaming(?:/)?$"}, {"page": "/evolution-lobby", "regex": "^/evolution\\-lobby(?:/)?$", "routeKeys": {}, "namedRegex": "^/evolution\\-lobby(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/home", "regex": "^/home(?:/)?$", "routeKeys": {}, "namedRegex": "^/home(?:/)?$"}, {"page": "/jili-games", "regex": "^/jili\\-games(?:/)?$", "routeKeys": {}, "namedRegex": "^/jili\\-games(?:/)?$"}, {"page": "/lalika", "regex": "^/lalika(?:/)?$", "routeKeys": {}, "namedRegex": "^/lalika(?:/)?$"}, {"page": "/on-gaming-lobby", "regex": "^/on\\-gaming\\-lobby(?:/)?$", "routeKeys": {}, "namedRegex": "^/on\\-gaming\\-lobby(?:/)?$"}, {"page": "/pg-games", "regex": "^/pg\\-games(?:/)?$", "routeKeys": {}, "namedRegex": "^/pg\\-games(?:/)?$"}, {"page": "/pragmatic-play", "regex": "^/pragmatic\\-play(?:/)?$", "routeKeys": {}, "namedRegex": "^/pragmatic\\-play(?:/)?$"}, {"page": "/pretty-game", "regex": "^/pretty\\-game(?:/)?$", "routeKeys": {}, "namedRegex": "^/pretty\\-game(?:/)?$"}, {"page": "/sa", "regex": "^/sa(?:/)?$", "routeKeys": {}, "namedRegex": "^/sa(?:/)?$"}, {"page": "/sbo-bet", "regex": "^/sbo\\-bet(?:/)?$", "routeKeys": {}, "namedRegex": "^/sbo\\-bet(?:/)?$"}, {"page": "/sexy", "regex": "^/sexy(?:/)?$", "routeKeys": {}, "namedRegex": "^/sexy(?:/)?$"}, {"page": "/sv388", "regex": "^/sv388(?:/)?$", "routeKeys": {}, "namedRegex": "^/sv388(?:/)?$"}, {"page": "/unauthorized", "regex": "^/unauthorized(?:/)?$", "routeKeys": {}, "namedRegex": "^/unauthorized(?:/)?$"}, {"page": "/wm-casino", "regex": "^/wm\\-casino(?:/)?$", "routeKeys": {}, "namedRegex": "^/wm\\-casino(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}