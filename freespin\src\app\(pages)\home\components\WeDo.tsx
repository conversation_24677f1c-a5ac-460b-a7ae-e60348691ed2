"use client";
import React, { useEffect, useRef, useState } from "react";
import { motion } from "framer-motion";

const SpotlightText = ({ text }: { text?: string }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const finalText = text || "Hover me";

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;
      const rect = containerRef.current.getBoundingClientRect();
      setMousePos({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    };

    const container = containerRef.current;
    container?.addEventListener("mousemove", handleMouseMove);
    return () => container?.removeEventListener("mousemove", handleMouseMove);
  }, []);

  return (
    <div
      ref={containerRef}
      className="h-screen bg-black flex justify-center items-center select-none"
    >
      <div className="flex space-x-1 text-5xl font-bold font-sans relative">
        {finalText.split("").map((char, idx) => (
          <Character key={idx} char={char} mousePos={mousePos} />
        ))}
      </div>
    </div>
  );
};

const Character = ({
  char,
  mousePos,
  radius = 100,
}: {
  char: string;
  mousePos: { x: number; y: number };
  radius?: number;
}) => {
  const ref = useRef<HTMLSpanElement>(null);
  const [distance, setDistance] = useState(Infinity);

  useEffect(() => {
    if (!ref.current) return;

    const rect = ref.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    const dist = Math.hypot(
      centerX - (rect.left + mousePos.x),
      centerY - (rect.top + mousePos.y)
    );

    setDistance(dist);
  }, [mousePos]);

  const isInRange = distance < radius;

  return (
    <motion.span
      ref={ref}
      animate={{ color: isInRange ? "#ffffff" : "#4b5563" }} // Tailwind gray-700: #4b5563
      transition={{ duration: 0.15 }}
    >
      {char}
    </motion.span>
  );
};

export default SpotlightText;
