{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/client_apis/auth.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { cookies } from \"next/headers\";\r\nimport { redirect } from \"next/navigation\";\r\n\r\ninterface ILogin {\r\n  email: string;\r\n  password: string;\r\n}\r\n\r\ninterface IResult {\r\n  success: boolean;\r\n  message: string;\r\n  data?: {\r\n    token: string;\r\n    user: {\r\n      id: string;\r\n      email: string;\r\n      firstName?: string;\r\n      lastName?: string;\r\n      username?: string;\r\n      role?: string;\r\n    };\r\n  };\r\n}\r\n\r\nexport async function loginUser(body: ILogin): Promise<IResult> {\r\n  try {\r\n    // Use absolute URL for server-side fetch\r\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:3000\";\r\n\r\n    const response = await fetch(`${baseUrl}/api/auth/login`, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(body),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json();\r\n      return {\r\n        success: false,\r\n        message: errorData.error || \"Login failed\",\r\n      };\r\n    }\r\n\r\n    const result: IResult = await response.json();\r\n    console.log(\"Login result:\", result);\r\n\r\n    if (result.success && result.data) {\r\n      const cookieStore = await cookies();\r\n\r\n      // Set session token (httpOnly for security)\r\n      cookieStore.set(\"session-token\", result.data.token, {\r\n        httpOnly: true,\r\n        secure: process.env.NODE_ENV === \"production\",\r\n        sameSite: \"lax\",\r\n        maxAge: 60 * 60 * 24 * 7, // 7 days\r\n        path: \"/\",\r\n      });\r\n\r\n      // Set user info (accessible to client-side)\r\n      if (result.data.user) {\r\n        const userInfo = JSON.stringify({\r\n          id: result.data.user.id,\r\n          email: result.data.user.email,\r\n          name:\r\n            `${result.data.user.firstName || \"\"} ${\r\n              result.data.user.lastName || \"\"\r\n            }`.trim() || result.data.user.username,\r\n          username: result.data.user.username,\r\n          role: result.data.user.role,\r\n        });\r\n\r\n        // Set server-side cookie\r\n        cookieStore.set(\"user-info\", userInfo, {\r\n          httpOnly: false, // Accessible to client-side\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          sameSite: \"lax\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n\r\n        // Also set client-side cookie for immediate access\r\n        if (typeof document !== \"undefined\") {\r\n          const maxAge = 60 * 60 * 24 * 7; // 7 days in seconds\r\n          const expires = new Date(Date.now() + maxAge * 1000).toUTCString();\r\n          document.cookie = `user-info=${encodeURIComponent(\r\n            userInfo\r\n          )}; expires=${expires}; path=/; SameSite=lax`;\r\n        }\r\n      }\r\n    }\r\n\r\n    return result;\r\n  } catch (error) {\r\n    console.error(\"Login error:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Network error. Please check your connection and try again.\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logoutUser() {\r\n  try {\r\n    const cookieStore = await cookies();\r\n\r\n    // Clear session token\r\n    cookieStore.delete(\"session-token\");\r\n\r\n    // Clear user info\r\n    cookieStore.delete(\"user-info\");\r\n\r\n    // Optionally call logout API endpoint\r\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:3000\";\r\n\r\n    try {\r\n      await fetch(`${baseUrl}/api/auth/logout`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      });\r\n    } catch (error) {\r\n      // Ignore API errors for logout, cookies are already cleared\r\n      console.warn(\"Logout API call failed:\", error);\r\n    }\r\n\r\n    return { success: true, message: \"Logged out successfully\" };\r\n  } catch (error) {\r\n    console.error(\"Logout error:\", error);\r\n    return { success: false, message: \"Logout failed\" };\r\n  }\r\n}\r\n\r\nexport async function registerUser(body: {\r\n  email: string;\r\n  password: string;\r\n  username: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n}): Promise<IResult> {\r\n  try {\r\n    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:3000\";\r\n\r\n    const response = await fetch(`${baseUrl}/api/auth/register`, {\r\n      method: \"POST\",\r\n      body: JSON.stringify(body),\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json();\r\n      return {\r\n        success: false,\r\n        message: errorData.error || \"Registration failed\",\r\n      };\r\n    }\r\n\r\n    const result: IResult = await response.json();\r\n\r\n    // Auto-login after successful registration\r\n    if (result.success && result.data) {\r\n      const cookieStore = await cookies();\r\n\r\n      cookieStore.set(\"session-token\", result.data.token, {\r\n        httpOnly: true,\r\n        secure: process.env.NODE_ENV === \"production\",\r\n        sameSite: \"lax\",\r\n        maxAge: 60 * 60 * 24 * 7,\r\n        path: \"/\",\r\n      });\r\n\r\n      if (result.data.user) {\r\n        cookieStore.set(\r\n          \"user-info\",\r\n          JSON.stringify({\r\n            id: result.data.user.id,\r\n            email: result.data.user.email,\r\n            name:\r\n              `${result.data.user.firstName || \"\"} ${\r\n                result.data.user.lastName || \"\"\r\n              }`.trim() || result.data.user.username,\r\n            username: result.data.user.username,\r\n            role: result.data.user.role,\r\n          }),\r\n          {\r\n            httpOnly: false,\r\n            secure: process.env.NODE_ENV === \"production\",\r\n            sameSite: \"lax\",\r\n            maxAge: 60 * 60 * 24 * 7,\r\n            path: \"/\",\r\n          }\r\n        );\r\n      }\r\n    }\r\n\r\n    return result;\r\n  } catch (error) {\r\n    console.error(\"Registration error:\", error);\r\n    return {\r\n      success: false,\r\n      message: \"Network error. Please check your connection and try again.\",\r\n    };\r\n  }\r\n}\r\n\r\n// Helper function to get current user from cookies\r\nexport async function getCurrentUser() {\r\n  try {\r\n    const cookieStore = await cookies();\r\n    const userInfo = cookieStore.get(\"user-info\");\r\n    const sessionToken = cookieStore.get(\"session-token\");\r\n\r\n    if (!userInfo || !sessionToken) {\r\n      return null;\r\n    }\r\n\r\n    return JSON.parse(userInfo.value);\r\n  } catch (error) {\r\n    console.error(\"Error getting current user:\", error);\r\n    return null;\r\n  }\r\n}\r\n\r\n// Server action for form-based login (with redirect)\r\nexport async function loginAction(formData: FormData) {\r\n  const email = formData.get(\"email\") as string;\r\n  const password = formData.get(\"password\") as string;\r\n\r\n  if (!email || !password) {\r\n    return {\r\n      success: false,\r\n      message: \"Email and password are required\",\r\n    };\r\n  }\r\n\r\n  const result = await loginUser({ email, password });\r\n\r\n  if (result.success) {\r\n    redirect(\"/dashboard/blogs\"); // Redirect after successful login\r\n  }\r\n\r\n  return result;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAAA;;;;;;AAuBO,eAAe,UAAU,IAAY;IAC1C,IAAI;QACF,yCAAyC;QACzC,MAAM,UAAU,gEAAoC;QAEpD,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,eAAe,CAAC,EAAE;YACxD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;YACrB,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,OAAO;gBACL,SAAS;gBACT,SAAS,UAAU,KAAK,IAAI;YAC9B;QACF;QAEA,MAAM,SAAkB,MAAM,SAAS,IAAI;QAC3C,QAAQ,GAAG,CAAC,iBAAiB;QAE7B,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;YACjC,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;YAEhC,4CAA4C;YAC5C,YAAY,GAAG,CAAC,iBAAiB,OAAO,IAAI,CAAC,KAAK,EAAE;gBAClD,UAAU;gBACV,QAAQ,oDAAyB;gBACjC,UAAU;gBACV,QAAQ,KAAK,KAAK,KAAK;gBACvB,MAAM;YACR;YAEA,4CAA4C;YAC5C,IAAI,OAAO,IAAI,CAAC,IAAI,EAAE;gBACpB,MAAM,WAAW,KAAK,SAAS,CAAC;oBAC9B,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;oBACvB,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;oBAC7B,MACE,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,GAAG,CAAC,EACnC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAC7B,CAAC,IAAI,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ;oBACxC,UAAU,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ;oBACnC,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;gBAC7B;gBAEA,yBAAyB;gBACzB,YAAY,GAAG,CAAC,aAAa,UAAU;oBACrC,UAAU;oBACV,QAAQ,oDAAyB;oBACjC,UAAU;oBACV,QAAQ,KAAK,KAAK,KAAK;oBACvB,MAAM;gBACR;gBAEA,mDAAmD;gBACnD,IAAI,OAAO,aAAa,aAAa;oBACnC,MAAM,SAAS,KAAK,KAAK,KAAK,GAAG,oBAAoB;oBACrD,MAAM,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,MAAM,WAAW;oBAChE,SAAS,MAAM,GAAG,CAAC,UAAU,EAAE,mBAC7B,UACA,UAAU,EAAE,QAAQ,sBAAsB,CAAC;gBAC/C;YACF;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;QAEhC,sBAAsB;QACtB,YAAY,MAAM,CAAC;QAEnB,kBAAkB;QAClB,YAAY,MAAM,CAAC;QAEnB,sCAAsC;QACtC,MAAM,UAAU,gEAAoC;QAEpD,IAAI;YACF,MAAM,MAAM,GAAG,QAAQ,gBAAgB,CAAC,EAAE;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;QACF,EAAE,OAAO,OAAO;YACd,4DAA4D;YAC5D,QAAQ,IAAI,CAAC,2BAA2B;QAC1C;QAEA,OAAO;YAAE,SAAS;YAAM,SAAS;QAA0B;IAC7D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO;YAAE,SAAS;YAAO,SAAS;QAAgB;IACpD;AACF;AAEO,eAAe,aAAa,IAMlC;IACC,IAAI;QACF,MAAM,UAAU,gEAAoC;QAEpD,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,kBAAkB,CAAC,EAAE;YAC3D,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;YACrB,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,OAAO;gBACL,SAAS;gBACT,SAAS,UAAU,KAAK,IAAI;YAC9B;QACF;QAEA,MAAM,SAAkB,MAAM,SAAS,IAAI;QAE3C,2CAA2C;QAC3C,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;YACjC,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;YAEhC,YAAY,GAAG,CAAC,iBAAiB,OAAO,IAAI,CAAC,KAAK,EAAE;gBAClD,UAAU;gBACV,QAAQ,oDAAyB;gBACjC,UAAU;gBACV,QAAQ,KAAK,KAAK,KAAK;gBACvB,MAAM;YACR;YAEA,IAAI,OAAO,IAAI,CAAC,IAAI,EAAE;gBACpB,YAAY,GAAG,CACb,aACA,KAAK,SAAS,CAAC;oBACb,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;oBACvB,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;oBAC7B,MACE,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,GAAG,CAAC,EACnC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAC7B,CAAC,IAAI,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ;oBACxC,UAAU,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ;oBACnC,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;gBAC7B,IACA;oBACE,UAAU;oBACV,QAAQ,oDAAyB;oBACjC,UAAU;oBACV,QAAQ,KAAK,KAAK,KAAK;oBACvB,MAAM;gBACR;YAEJ;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;QAChC,MAAM,WAAW,YAAY,GAAG,CAAC;QACjC,MAAM,eAAe,YAAY,GAAG,CAAC;QAErC,IAAI,CAAC,YAAY,CAAC,cAAc;YAC9B,OAAO;QACT;QAEA,OAAO,KAAK,KAAK,CAAC,SAAS,KAAK;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF;AAGO,eAAe,YAAY,QAAkB;IAClD,MAAM,QAAQ,SAAS,GAAG,CAAC;IAC3B,MAAM,WAAW,SAAS,GAAG,CAAC;IAE9B,IAAI,CAAC,SAAS,CAAC,UAAU;QACvB,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;IAEA,MAAM,SAAS,MAAM,UAAU;QAAE;QAAO;IAAS;IAEjD,IAAI,OAAO,OAAO,EAAE;QAClB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE,qBAAqB,kCAAkC;IAClE;IAEA,OAAO;AACT;;;IA9NsB;IA+EA;IAgCA;IA2EA;IAkBA;;AA5MA,+OAAA;AA+EA,+OAAA;AAgCA,+OAAA;AA2EA,+OAAA;AAkBA,+OAAA", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/client_apis/api-wrapper.ts"], "sourcesContent": ["import { cookies } from \"next/headers\";\r\n\r\ninterface FetchOptions extends RequestInit {\r\n  baseUrl?: string;\r\n  nextOptions?: {\r\n    tags?: string[];\r\n    revalidate?: number | false;\r\n  };\r\n  includeCookies?: boolean;\r\n  customCookies?: Record<string, string>;\r\n}\r\n\r\ninterface ApiError {\r\n  status: number;\r\n  message: string;\r\n  errors?: Record<string, string[]>;\r\n}\r\n\r\n// Enhanced base URL handling - use local server for development, production URL for production\r\nconst BASE_URL: string =\r\n  typeof window !== \"undefined\"\r\n    ? window.location.origin\r\n    : process.env.NODE_ENV === \"development\"\r\n    ? \"http://localhost:3000\"\r\n    : \"https://www.freespin168.asia\";\r\n\r\nexport async function customFetch<T>(\r\n  endpoint: string,\r\n  options: FetchOptions = {}\r\n): Promise<T | ApiError> {\r\n  const {\r\n    baseUrl = BASE_URL,\r\n    nextOptions,\r\n    headers,\r\n    includeCookies = true,\r\n    customCookies,\r\n    ...rest\r\n  } = options;\r\n\r\n  const url = `${baseUrl}${endpoint}`;\r\n  console.log(url);\r\n\r\n  // Prepare headers\r\n  const fetchHeaders: HeadersInit = {\r\n    \"Content-Type\": \"application/json\",\r\n    ...headers,\r\n  };\r\n\r\n  // Add cookies if we're in a server context and includeCookies is true\r\n  if (includeCookies) {\r\n    try {\r\n      const cookieStore = await cookies();\r\n      const sessionToken = cookieStore.get(\"session-token\");\r\n\r\n      if (sessionToken) {\r\n        // Add Authorization header with Bearer token\r\n        (fetchHeaders as Record<string, string>)[\r\n          \"Authorization\"\r\n        ] = `Bearer ${sessionToken.value}`;\r\n      }\r\n\r\n      // Add custom cookies if provided\r\n      if (customCookies) {\r\n        const cookieString = Object.entries(customCookies)\r\n          .map(([key, value]) => `${key}=${value}`)\r\n          .join(\"; \");\r\n\r\n        (fetchHeaders as Record<string, string>).Cookie = cookieString;\r\n      }\r\n\r\n      // Or include all cookies as Cookie header (if your API expects it)\r\n      const allCookies = cookieStore.getAll();\r\n      if (allCookies.length > 0) {\r\n        const cookieString = allCookies\r\n          .map((cookie) => `${cookie.name}=${cookie.value}`)\r\n          .join(\"; \");\r\n\r\n        // Only set if not already set by customCookies\r\n        if (!customCookies) {\r\n          (fetchHeaders as Record<string, string>)[\"Cookie\"] = cookieString;\r\n        }\r\n      }\r\n    } catch (error) {\r\n      // We're probably in a client context or cookies are not available\r\n      console.warn(\"Cookies not available in this context:\", error);\r\n    }\r\n  }\r\n\r\n  const fetchOptions: RequestInit = {\r\n    ...rest,\r\n    headers: fetchHeaders,\r\n    next: {\r\n      tags: nextOptions?.tags || [],\r\n      revalidate: nextOptions?.revalidate,\r\n    },\r\n  };\r\n\r\n  try {\r\n    const res = await fetch(url, fetchOptions);\r\n    console.log(\"res\", res);\r\n\r\n    if (!res.ok) {\r\n      // Extract error message from response\r\n      let errorMessage = \"An error occurred\";\r\n      let errorData: any = {};\r\n\r\n      try {\r\n        errorData = await res.json();\r\n        errorMessage =\r\n          errorData.message ||\r\n          errorData.error ||\r\n          `HTTP ${res.status}: ${res.statusText}`;\r\n      } catch {\r\n        // If JSON parsing fails, use status text\r\n        errorMessage = `HTTP ${res.status}: ${res.statusText}`;\r\n      }\r\n\r\n      // Return structured error object\r\n      return {\r\n        status: res.status,\r\n        message: errorMessage,\r\n        errors: errorData.errors || undefined,\r\n      } as ApiError;\r\n    }\r\n\r\n    return res.json() as Promise<T>;\r\n  } catch (error) {\r\n    // Handle network errors\r\n    return {\r\n      status: 0,\r\n      message:\r\n        error instanceof Error ? error.message : \"Network error occurred\",\r\n    } as ApiError;\r\n  }\r\n}\r\n\r\n// Helper function to check if response is an error\r\nexport function isApiError(response: any): response is ApiError {\r\n  return (\r\n    response &&\r\n    typeof response.status === \"number\" &&\r\n    typeof response.message === \"string\"\r\n  );\r\n}\r\n\r\n// Helper function for authenticated requests\r\nexport async function authenticatedFetch<T>(\r\n  endpoint: string,\r\n  options: Omit<FetchOptions, \"includeCookies\"> = {}\r\n): Promise<T | ApiError> {\r\n  return customFetch<T>(endpoint, {\r\n    ...options,\r\n    includeCookies: true,\r\n  });\r\n}\r\n\r\n// Helper function for public requests (no cookies)\r\nexport async function publicFetch<T>(\r\n  endpoint: string,\r\n  options: Omit<FetchOptions, \"includeCookies\"> = {}\r\n): Promise<T | ApiError> {\r\n  return customFetch<T>(endpoint, {\r\n    ...options,\r\n    includeCookies: false,\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;;AAkBA,+FAA+F;AAC/F,MAAM,WACJ,6EAEI,uCACA;AAGC,eAAe,YACpB,QAAgB,EAChB,UAAwB,CAAC,CAAC;IAE1B,MAAM,EACJ,UAAU,QAAQ,EAClB,WAAW,EACX,OAAO,EACP,iBAAiB,IAAI,EACrB,aAAa,EACb,GAAG,MACJ,GAAG;IAEJ,MAAM,MAAM,GAAG,UAAU,UAAU;IACnC,QAAQ,GAAG,CAAC;IAEZ,kBAAkB;IAClB,MAAM,eAA4B;QAChC,gBAAgB;QAChB,GAAG,OAAO;IACZ;IAEA,sEAAsE;IACtE,IAAI,gBAAgB;QAClB,IAAI;YACF,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;YAChC,MAAM,eAAe,YAAY,GAAG,CAAC;YAErC,IAAI,cAAc;gBAChB,6CAA6C;gBAC5C,YAAuC,CACtC,gBACD,GAAG,CAAC,OAAO,EAAE,aAAa,KAAK,EAAE;YACpC;YAEA,iCAAiC;YACjC,IAAI,eAAe;gBACjB,MAAM,eAAe,OAAO,OAAO,CAAC,eACjC,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,GAAG,IAAI,CAAC,EAAE,OAAO,EACvC,IAAI,CAAC;gBAEP,aAAwC,MAAM,GAAG;YACpD;YAEA,mEAAmE;YACnE,MAAM,aAAa,YAAY,MAAM;YACrC,IAAI,WAAW,MAAM,GAAG,GAAG;gBACzB,MAAM,eAAe,WAClB,GAAG,CAAC,CAAC,SAAW,GAAG,OAAO,IAAI,CAAC,CAAC,EAAE,OAAO,KAAK,EAAE,EAChD,IAAI,CAAC;gBAER,+CAA+C;gBAC/C,IAAI,CAAC,eAAe;oBACjB,YAAuC,CAAC,SAAS,GAAG;gBACvD;YACF;QACF,EAAE,OAAO,OAAO;YACd,kEAAkE;YAClE,QAAQ,IAAI,CAAC,0CAA0C;QACzD;IACF;IAEA,MAAM,eAA4B;QAChC,GAAG,IAAI;QACP,SAAS;QACT,MAAM;YACJ,MAAM,aAAa,QAAQ,EAAE;YAC7B,YAAY,aAAa;QAC3B;IACF;IAEA,IAAI;QACF,MAAM,MAAM,MAAM,MAAM,KAAK;QAC7B,QAAQ,GAAG,CAAC,OAAO;QAEnB,IAAI,CAAC,IAAI,EAAE,EAAE;YACX,sCAAsC;YACtC,IAAI,eAAe;YACnB,IAAI,YAAiB,CAAC;YAEtB,IAAI;gBACF,YAAY,MAAM,IAAI,IAAI;gBAC1B,eACE,UAAU,OAAO,IACjB,UAAU,KAAK,IACf,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,UAAU,EAAE;YAC3C,EAAE,OAAM;gBACN,yCAAyC;gBACzC,eAAe,CAAC,KAAK,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,UAAU,EAAE;YACxD;YAEA,iCAAiC;YACjC,OAAO;gBACL,QAAQ,IAAI,MAAM;gBAClB,SAAS;gBACT,QAAQ,UAAU,MAAM,IAAI;YAC9B;QACF;QAEA,OAAO,IAAI,IAAI;IACjB,EAAE,OAAO,OAAO;QACd,wBAAwB;QACxB,OAAO;YACL,QAAQ;YACR,SACE,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC7C;IACF;AACF;AAGO,SAAS,WAAW,QAAa;IACtC,OACE,YACA,OAAO,SAAS,MAAM,KAAK,YAC3B,OAAO,SAAS,OAAO,KAAK;AAEhC;AAGO,eAAe,mBACpB,QAAgB,EAChB,UAAgD,CAAC,CAAC;IAElD,OAAO,YAAe,UAAU;QAC9B,GAAG,OAAO;QACV,gBAAgB;IAClB;AACF;AAGO,eAAe,YACpB,QAAgB,EAChB,UAAgD,CAAC,CAAC;IAElD,OAAO,YAAe,UAAU;QAC9B,GAAG,OAAO;QACV,gBAAgB;IAClB;AACF", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/client_apis/api-config.ts"], "sourcesContent": ["import { revalidateTag } from \"next/cache\";\r\nimport { authenticatedFetch, publicFetch, isApiError } from \"./api-wrapper\";\r\n\r\ninterface IProps {\r\n  endpoint: string;\r\n  tags: string[];\r\n  queryParams?: Record<string, string>;\r\n  revalidate?: number | false;\r\n  requireAuth?: boolean;\r\n}\r\n\r\nexport interface ApiResult<T> {\r\n  success: boolean;\r\n  data?: T;\r\n  message: string;\r\n  status?: number;\r\n}\r\n\r\nconst buildQuery = (params?: Record<string, string>) =>\r\n  params ? \"?\" + new URLSearchParams(params).toString() : \"\";\r\n\r\nexport const createApiConfig = <T, CreateInput = any, UpdateInput = any>({\r\n  endpoint,\r\n  tags,\r\n  queryParams,\r\n  revalidate,\r\n  requireAuth = true, // Default to requiring auth\r\n}: IProps) => {\r\n  // Choose the appropriate fetch function based on auth requirement\r\n  const fetchFn = requireAuth ? authenticatedFetch : publicFetch;\r\n\r\n  return {\r\n    async getAll(): Promise<ApiResult<T[]>> {\r\n      const response = await fetchFn<ApiResult<T[]>>(\r\n        `${endpoint}${buildQuery(queryParams)}`,\r\n        {\r\n          nextOptions: {\r\n            tags: [endpoint, ...tags],\r\n            revalidate,\r\n          },\r\n        }\r\n      );\r\n\r\n      if (isApiError(response)) {\r\n        return {\r\n          success: false,\r\n          message: response.message,\r\n          status: response.status,\r\n        };\r\n      }\r\n\r\n      return response;\r\n    },\r\n\r\n    async getById(id: string): Promise<ApiResult<T>> {\r\n      const response = await fetchFn<ApiResult<T>>(`${endpoint}/${id}`, {\r\n        nextOptions: {\r\n          tags: [`${endpoint}-${id}`, ...tags],\r\n        },\r\n      });\r\n\r\n      if (isApiError(response)) {\r\n        return {\r\n          success: false,\r\n          message: response.message,\r\n          status: response.status,\r\n        };\r\n      }\r\n\r\n      return response;\r\n    },\r\n\r\n    async createData(body: CreateInput): Promise<ApiResult<T>> {\r\n      const headers =\r\n        body instanceof FormData\r\n          ? { \"Content-Type\": \"multipart/form-data\" }\r\n          : { \"Content-Type\": \"application/json\" };\r\n\r\n      const response = await fetchFn<ApiResult<T>>(endpoint, {\r\n        method: \"POST\",\r\n        body: body instanceof FormData ? body : JSON.stringify(body),\r\n        headers,\r\n      });\r\n\r\n      if (isApiError(response)) {\r\n        return {\r\n          success: false,\r\n          message: response.message,\r\n          status: response.status,\r\n        };\r\n      }\r\n\r\n      // Revalidate cache tags\r\n      tags.forEach((tag) => revalidateTag(tag));\r\n      revalidateTag(endpoint);\r\n\r\n      return response;\r\n    },\r\n\r\n    async updateData(id: string, body: UpdateInput): Promise<ApiResult<T>> {\r\n      const headers =\r\n        body instanceof FormData\r\n          ? undefined\r\n          : { \"Content-Type\": \"application/json\" };\r\n\r\n      const response = await fetchFn<ApiResult<T>>(`${endpoint}/${id}`, {\r\n        method: \"PATCH\",\r\n        body: body instanceof FormData ? body : JSON.stringify(body),\r\n        headers,\r\n      });\r\n\r\n      if (isApiError(response)) {\r\n        return {\r\n          success: false,\r\n          message: response.message,\r\n          status: response.status,\r\n        };\r\n      }\r\n\r\n      // Revalidate specific item and collection\r\n      tags.forEach((tag) => revalidateTag(tag));\r\n      revalidateTag(endpoint);\r\n      revalidateTag(`${endpoint}-${id}`);\r\n\r\n      return response;\r\n    },\r\n\r\n    async deleteDataById(id: string): Promise<ApiResult<null>> {\r\n      const response = await fetchFn<ApiResult<null>>(`${endpoint}/${id}`, {\r\n        method: \"DELETE\",\r\n      });\r\n\r\n      if (isApiError(response)) {\r\n        return {\r\n          success: false,\r\n          message: response.message,\r\n          status: response.status,\r\n        };\r\n      }\r\n\r\n      // Revalidate cache tags\r\n      tags.forEach((tag) => revalidateTag(tag));\r\n      revalidateTag(endpoint);\r\n      revalidateTag(`${endpoint}-${id}`);\r\n\r\n      return response;\r\n    },\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAiBA,MAAM,aAAa,CAAC,SAClB,SAAS,MAAM,IAAI,gBAAgB,QAAQ,QAAQ,KAAK;AAEnD,MAAM,kBAAkB,CAA0C,EACvE,QAAQ,EACR,IAAI,EACJ,WAAW,EACX,UAAU,EACV,cAAc,IAAI,EACX;IACP,kEAAkE;IAClE,MAAM,UAAU,cAAc,oIAAA,CAAA,qBAAkB,GAAG,oIAAA,CAAA,cAAW;IAE9D,OAAO;QACL,MAAM;YACJ,MAAM,WAAW,MAAM,QACrB,GAAG,WAAW,WAAW,cAAc,EACvC;gBACE,aAAa;oBACX,MAAM;wBAAC;2BAAa;qBAAK;oBACzB;gBACF;YACF;YAGF,IAAI,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD,EAAE,WAAW;gBACxB,OAAO;oBACL,SAAS;oBACT,SAAS,SAAS,OAAO;oBACzB,QAAQ,SAAS,MAAM;gBACzB;YACF;YAEA,OAAO;QACT;QAEA,MAAM,SAAQ,EAAU;YACtB,MAAM,WAAW,MAAM,QAAsB,GAAG,SAAS,CAAC,EAAE,IAAI,EAAE;gBAChE,aAAa;oBACX,MAAM;wBAAC,GAAG,SAAS,CAAC,EAAE,IAAI;2BAAK;qBAAK;gBACtC;YACF;YAEA,IAAI,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD,EAAE,WAAW;gBACxB,OAAO;oBACL,SAAS;oBACT,SAAS,SAAS,OAAO;oBACzB,QAAQ,SAAS,MAAM;gBACzB;YACF;YAEA,OAAO;QACT;QAEA,MAAM,YAAW,IAAiB;YAChC,MAAM,UACJ,gBAAgB,WACZ;gBAAE,gBAAgB;YAAsB,IACxC;gBAAE,gBAAgB;YAAmB;YAE3C,MAAM,WAAW,MAAM,QAAsB,UAAU;gBACrD,QAAQ;gBACR,MAAM,gBAAgB,WAAW,OAAO,KAAK,SAAS,CAAC;gBACvD;YACF;YAEA,IAAI,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD,EAAE,WAAW;gBACxB,OAAO;oBACL,SAAS;oBACT,SAAS,SAAS,OAAO;oBACzB,QAAQ,SAAS,MAAM;gBACzB;YACF;YAEA,wBAAwB;YACxB,KAAK,OAAO,CAAC,CAAC,MAAQ,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE;YACpC,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE;YAEd,OAAO;QACT;QAEA,MAAM,YAAW,EAAU,EAAE,IAAiB;YAC5C,MAAM,UACJ,gBAAgB,WACZ,YACA;gBAAE,gBAAgB;YAAmB;YAE3C,MAAM,WAAW,MAAM,QAAsB,GAAG,SAAS,CAAC,EAAE,IAAI,EAAE;gBAChE,QAAQ;gBACR,MAAM,gBAAgB,WAAW,OAAO,KAAK,SAAS,CAAC;gBACvD;YACF;YAEA,IAAI,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD,EAAE,WAAW;gBACxB,OAAO;oBACL,SAAS;oBACT,SAAS,SAAS,OAAO;oBACzB,QAAQ,SAAS,MAAM;gBACzB;YACF;YAEA,0CAA0C;YAC1C,KAAK,OAAO,CAAC,CAAC,MAAQ,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE;YACpC,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE;YACd,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,SAAS,CAAC,EAAE,IAAI;YAEjC,OAAO;QACT;QAEA,MAAM,gBAAe,EAAU;YAC7B,MAAM,WAAW,MAAM,QAAyB,GAAG,SAAS,CAAC,EAAE,IAAI,EAAE;gBACnE,QAAQ;YACV;YAEA,IAAI,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD,EAAE,WAAW;gBACxB,OAAO;oBACL,SAAS;oBACT,SAAS,SAAS,OAAO;oBACzB,QAAQ,SAAS,MAAM;gBACzB;YACF;YAEA,wBAAwB;YACxB,KAAK,OAAO,CAAC,CAAC,MAAQ,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE;YACpC,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE;YACd,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,SAAS,CAAC,EAAE,IAAI;YAEjC,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/client_apis/api/blog.ts"], "sourcesContent": ["\"use server\";\r\nimport { createApiConfig } from \"../api-config\";\r\n\r\nconst blog = createApiConfig({\r\n  endpoint: \"/api/categories\",\r\n  tags: [\"categories\"],\r\n  requireAuth: true, // Login doesn't require existing auth\r\n});\r\n\r\n// Public categories function for frontend use (no auth required)\r\nexport async function getPublicCategories() {\r\n  const baseUrl =\r\n    typeof window !== \"undefined\"\r\n      ? window.location.origin\r\n      : process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:3001\";\r\n\r\n  try {\r\n    const response = await fetch(`${baseUrl}/api/categories`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      credentials: \"include\",\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      throw new Error(\r\n        `HTTP error! status: ${response.status}, body: ${errorText}`\r\n      );\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error(\"Error fetching categories:\", error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Legacy function - now redirects to server action for admin use\r\nexport async function getBlogsCategories() {\r\n  // This function is deprecated for admin use\r\n  // Use getCategoriesAction server action instead for authenticated admin access\r\n  return getPublicCategories();\r\n}\r\n\r\nexport async function createBlogCategory(data: any) {\r\n  return await blog.createData(data);\r\n}\r\n\r\nexport async function deleteBlogCategory(id: any) {\r\n  return await blog.deleteDataById(id);\r\n}\r\n\r\nconst posts = createApiConfig({\r\n  endpoint: \"/api/posts\",\r\n  tags: [\"posts\"],\r\n  requireAuth: true,\r\n});\r\n\r\nexport async function getPosts() {\r\n  return await posts.getAll();\r\n}\r\nexport async function createPost(data: any) {\r\n  return await posts.createData(data);\r\n}\r\nexport async function deletePost(id: any) {\r\n  return await posts.deleteDataById(id);\r\n}\r\n\r\nexport async function updatePostStatus(\r\n  id: string,\r\n  status: \"draft\" | \"published\" | \"archived\"\r\n) {\r\n  try {\r\n    const baseUrl =\r\n      typeof window !== \"undefined\"\r\n        ? window.location.origin\r\n        : process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:3001\";\r\n\r\n    // Headers for the request\r\n    const headers: Record<string, string> = {\r\n      \"Content-Type\": \"application/json\",\r\n    };\r\n\r\n    const response = await fetch(`${baseUrl}/api/posts/${id}/status`, {\r\n      method: \"PATCH\",\r\n      headers,\r\n      credentials: \"include\",\r\n      body: JSON.stringify({ status }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      throw new Error(\r\n        `HTTP error! status: ${response.status}, body: ${errorText}`\r\n      );\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error(\"Error updating post status:\", error);\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error ? error.message : \"Failed to update post status\",\r\n    };\r\n  }\r\n}\r\n\r\n// Public posts API (no auth required)\r\nexport async function getPublicPosts(params?: {\r\n  page?: number;\r\n  limit?: number;\r\n  category?: string;\r\n  tag?: string;\r\n  search?: string;\r\n}) {\r\n  const queryParams = new URLSearchParams();\r\n  if (params?.page) queryParams.append(\"page\", params.page.toString());\r\n  if (params?.limit) queryParams.append(\"limit\", params.limit.toString());\r\n  if (params?.category) queryParams.append(\"category\", params.category);\r\n  if (params?.tag) queryParams.append(\"tag\", params.tag);\r\n  if (params?.search) queryParams.append(\"search\", params.search);\r\n\r\n  const queryString = queryParams.toString();\r\n  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:3001\";\r\n  const endpoint = queryString\r\n    ? `${baseUrl}/api/public/posts?${queryString}`\r\n    : `${baseUrl}/api/public/posts`;\r\n\r\n  try {\r\n    const response = await fetch(endpoint, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      next: {\r\n        tags: [\"public-posts\"],\r\n        revalidate: 300, // Cache for 5 minutes\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`HTTP error! status: ${response.status}`);\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error(\"Error fetching public posts:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Failed to fetch posts\",\r\n    };\r\n  }\r\n}\r\n\r\n// Blog post interface for public consumption\r\nexport interface PublicBlogPost {\r\n  _id: string;\r\n  title: string;\r\n  description: string;\r\n  banner: string;\r\n  categories: string[];\r\n  tags: string[];\r\n  slug: string;\r\n  publishedAt: string;\r\n  createdAt: string;\r\n  readTime: number;\r\n  views: number;\r\n  metaTitle: string;\r\n  metaDescription: string;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AACA;;;;;AAEA,MAAM,OAAO,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD,EAAE;IAC3B,UAAU;IACV,MAAM;QAAC;KAAa;IACpB,aAAa;AACf;AAGO,eAAe;IACpB,MAAM,UACJ,6EAEI,gEAAoC;IAE1C,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,eAAe,CAAC,EAAE;YACxD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,aAAa;QACf;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MACR,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,QAAQ,EAAE,WAAW;QAEhE;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAGO,eAAe;IACpB,4CAA4C;IAC5C,+EAA+E;IAC/E,OAAO;AACT;AAEO,eAAe,mBAAmB,IAAS;IAChD,OAAO,MAAM,KAAK,UAAU,CAAC;AAC/B;AAEO,eAAe,mBAAmB,EAAO;IAC9C,OAAO,MAAM,KAAK,cAAc,CAAC;AACnC;AAEA,MAAM,QAAQ,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD,EAAE;IAC5B,UAAU;IACV,MAAM;QAAC;KAAQ;IACf,aAAa;AACf;AAEO,eAAe;IACpB,OAAO,MAAM,MAAM,MAAM;AAC3B;AACO,eAAe,WAAW,IAAS;IACxC,OAAO,MAAM,MAAM,UAAU,CAAC;AAChC;AACO,eAAe,WAAW,EAAO;IACtC,OAAO,MAAM,MAAM,cAAc,CAAC;AACpC;AAEO,eAAe,iBACpB,EAAU,EACV,MAA0C;IAE1C,IAAI;QACF,MAAM,UACJ,6EAEI,gEAAoC;QAE1C,0BAA0B;QAC1B,MAAM,UAAkC;YACtC,gBAAgB;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,WAAW,EAAE,GAAG,OAAO,CAAC,EAAE;YAChE,QAAQ;YACR;YACA,aAAa;YACb,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAO;QAChC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MACR,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,QAAQ,EAAE,WAAW;QAEhE;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YACL,SAAS;YACT,OACE,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC7C;IACF;AACF;AAGO,eAAe,eAAe,MAMpC;IACC,MAAM,cAAc,IAAI;IACxB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;IACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;IACpE,IAAI,QAAQ,UAAU,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;IACpE,IAAI,QAAQ,KAAK,YAAY,MAAM,CAAC,OAAO,OAAO,GAAG;IACrD,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;IAE9D,MAAM,cAAc,YAAY,QAAQ;IACxC,MAAM,UAAU,gEAAoC;IACpD,MAAM,WAAW,cACb,GAAG,QAAQ,kBAAkB,EAAE,aAAa,GAC5C,GAAG,QAAQ,iBAAiB,CAAC;IAEjC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,UAAU;YACrC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM;gBACJ,MAAM;oBAAC;iBAAe;gBACtB,YAAY;YACd;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;;;IAjJsB;IA8BA;IAMA;IAIA;IAUA;IAGA;IAGA;IAIA;IAyCA;;AArGA,+OAAA;AA8BA,+OAAA;AAMA,+OAAA;AAIA,+OAAA;AAUA,+OAAA;AAGA,+OAAA;AAGA,+OAAA;AAIA,+OAAA;AAyCA,+OAAA", "debugId": null}}, {"offset": {"line": 619, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from \"mongoose\";\r\n\r\nconst MONGODB_URI = process.env.MONGODB_URI!;\r\n\r\nif (!MONGODB_URI) {\r\n  throw new Error(\r\n    \"Please define the MONGODB_URI environment variable inside .env.local\"\r\n  );\r\n}\r\n\r\n/**\r\n * Global is used here to maintain a cached connection across hot reloads\r\n * in development. This prevents connections growing exponentially\r\n * during API Route usage.\r\n */\r\n// @ts-expect-error //global can have type any\r\nlet cached = global?.mongoose;\r\n\r\nif (!cached) {\r\n  // @ts-expect-error //global mongoose can have type any\r\n  cached = global.mongoose = { conn: null, promise: null };\r\n}\r\n\r\nasync function connectDB() {\r\n  if (cached.conn) {\r\n    return cached.conn;\r\n  }\r\n\r\n  if (!cached.promise) {\r\n    const opts = {\r\n      bufferCommands: false,\r\n    };\r\n\r\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\r\n      return mongoose;\r\n    });\r\n  }\r\n\r\n  try {\r\n    cached.conn = await cached.promise;\r\n  } catch (e) {\r\n    cached.promise = null;\r\n    throw e;\r\n  }\r\n\r\n  return cached.conn;\r\n}\r\n\r\nexport default connectDB;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MACR;AAEJ;AAEA;;;;CAIC,GACD,8CAA8C;AAC9C,IAAI,SAAS,QAAQ;AAErB,IAAI,CAAC,QAAQ;IACX,uDAAuD;IACvD,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 668, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/models/Post.ts"], "sourcesContent": ["import mongoose, { type Document, Mongoose, Schema, Types } from \"mongoose\";\r\n\r\nexport interface IPost extends Document {\r\n  // Blog Post fields\r\n  title: string;\r\n  slug?: string;\r\n  canonicalUrl?: string;\r\n  existingUrl: boolean;\r\n  content: string; // Main content field\r\n  excerpt?: string; // Short description/excerpt\r\n  description: string; // For backward compatibility\r\n  isBlog: boolean;\r\n  categories: Types.ObjectId[];\r\n  tags: string[];\r\n  author?: string; // Author reference\r\n\r\n  // SEO fields\r\n  metaTitle: string;\r\n  metaDescription: string;\r\n  metaKeywords: string;\r\n\r\n  // FAQ fields\r\n  // faqs: {\r\n  //   question: string;\r\n  //   answer: string;\r\n  //   index: number;\r\n  // }[];\r\n\r\n  // Banner fields\r\n  banner: string;\r\n\r\n  // Additional blog functionality fields\r\n  status: \"draft\" | \"published\" | \"archived\";\r\n  isPublished: boolean;\r\n  publishedAt?: Date;\r\n  views: number;\r\n  readTime: number;\r\n  isTopNews: boolean;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\nconst PostSchema = new Schema<IPost>(\r\n  {\r\n    // Blog Post fields\r\n    title: {\r\n      type: String,\r\n      required: [true, \"Post title is required\"],\r\n      trim: true,\r\n      maxlength: [1000, \"Title cannot exceed 200 characters\"],\r\n    },\r\n\r\n    slug: {\r\n      type: String,\r\n      trim: true,\r\n      lowercase: true,\r\n    },\r\n\r\n    canonicalUrl: {\r\n      type: String,\r\n      trim: true,\r\n      validate: {\r\n        validator: (v: string) => {\r\n          if (!v) return true; // Allow empty string\r\n          return /^https?:\\/\\/.+/.test(v);\r\n        },\r\n        message: \"Canonical URL must be a valid URL\",\r\n      },\r\n    },\r\n    existingUrl: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n\r\n    content: {\r\n      type: String,\r\n      required: [true, \"Post content is required\"],\r\n    },\r\n\r\n    excerpt: {\r\n      type: String,\r\n      trim: true,\r\n      maxlength: [1000, \"Excerpt cannot exceed 300 characters\"],\r\n    },\r\n\r\n    description: {\r\n      type: String,\r\n      // Not required anymore since we have content field\r\n    },\r\n\r\n    author: {\r\n      type: String,\r\n      trim: true,\r\n    },\r\n    isBlog: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    categories: [\r\n      {\r\n        type: mongoose.Schema.Types.ObjectId,\r\n        ref: \"Category\",\r\n      },\r\n    ],\r\n    tags: [\r\n      {\r\n        type: String,\r\n        trim: true,\r\n        lowercase: true,\r\n      },\r\n    ],\r\n\r\n    // SEO fields\r\n    metaTitle: {\r\n      type: String,\r\n      required: [true, \"Meta title is required\"],\r\n      maxlength: [1000, \"Meta title cannot exceed 60 characters\"],\r\n    },\r\n    metaDescription: {\r\n      type: String,\r\n      required: [true, \"Meta description is required\"],\r\n      maxlength: [1000, \"Meta description cannot exceed 160 characters\"],\r\n    },\r\n    metaKeywords: {\r\n      type: String,\r\n      required: [true, \"Meta keywords are required\"],\r\n      maxlength: [1000, \"Meta keywords cannot exceed 200 characters\"],\r\n    },\r\n\r\n    // Banner fields\r\n    banner: {\r\n      type: String,\r\n      required: [true, \"Banner image is required\"],\r\n      trim: true,\r\n    },\r\n\r\n    // Additional blog functionality fields\r\n    status: {\r\n      type: String,\r\n      enum: [\"draft\", \"published\", \"archived\"],\r\n      default: \"draft\",\r\n    },\r\n    isPublished: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    publishedAt: {\r\n      type: Date,\r\n      default: null,\r\n    },\r\n    views: {\r\n      type: Number,\r\n      default: 0,\r\n    },\r\n    readTime: {\r\n      type: Number,\r\n      default: 1,\r\n    },\r\n    isTopNews: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  {\r\n    timestamps: true,\r\n  }\r\n);\r\n\r\n// Pre-save middleware\r\nPostSchema.pre(\"save\", function (next) {\r\n  // Generate slug from title if not provided\r\n  if (this.isModified(\"title\") && !this.slug) {\r\n    this.slug = this.title\r\n      .toLowerCase()\r\n      .replace(/[^a-z0-9]+/g, \"-\")\r\n      .replace(/^-+|-+$/g, \"\");\r\n  }\r\n\r\n  // Calculate read time (average 200 words per minute)\r\n  if (this.isModified(\"content\")) {\r\n    const wordCount = (this.content || \"\").split(/\\s+/).length;\r\n    this.readTime = Math.ceil(wordCount / 200);\r\n  }\r\n\r\n  // Set published date when status changes to published\r\n  if (\r\n    this.isModified(\"status\") &&\r\n    this.status === \"published\" &&\r\n    !this.publishedAt\r\n  ) {\r\n    this.publishedAt = new Date();\r\n    this.isPublished = true;\r\n  }\r\n\r\n  next();\r\n});\r\n\r\n// Create indexes for better query performance\r\nPostSchema.index({ categories: 1 });\r\nPostSchema.index({ tags: 1 });\r\nPostSchema.index({ status: 1 });\r\nPostSchema.index({ isPublished: 1 });\r\nPostSchema.index({ publishedAt: -1 });\r\nPostSchema.index({ \"banner.title\": 1 });\r\nPostSchema.index({\r\n  title: \"text\",\r\n  content: \"text\",\r\n  metaTitle: \"text\",\r\n  metaDescription: \"text\",\r\n}); // Text search index\r\n\r\nexport default mongoose.models.Post ||\r\n  mongoose.model<IPost>(\"Post\", PostSchema);\r\n"], "names": [], "mappings": ";;;AAAA;;AA0CA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAC3B;IACE,mBAAmB;IACnB,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,MAAM;QACN,WAAW;YAAC;YAAM;SAAqC;IACzD;IAEA,MAAM;QACJ,MAAM;QACN,MAAM;QACN,WAAW;IACb;IAEA,cAAc;QACZ,MAAM;QACN,MAAM;QACN,UAAU;YACR,WAAW,CAAC;gBACV,IAAI,CAAC,GAAG,OAAO,MAAM,qBAAqB;gBAC1C,OAAO,iBAAiB,IAAI,CAAC;YAC/B;YACA,SAAS;QACX;IACF;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IAEA,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;IAC9C;IAEA,SAAS;QACP,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAAuC;IAC3D;IAEA,aAAa;QACX,MAAM;IAER;IAEA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,YAAY;QACV;YACE,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;YACpC,KAAK;QACP;KACD;IACD,MAAM;QACJ;YACE,MAAM;YACN,MAAM;YACN,WAAW;QACb;KACD;IAED,aAAa;IACb,WAAW;QACT,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,WAAW;YAAC;YAAM;SAAyC;IAC7D;IACA,iBAAiB;QACf,MAAM;QACN,UAAU;YAAC;YAAM;SAA+B;QAChD,WAAW;YAAC;YAAM;SAAgD;IACpE;IACA,cAAc;QACZ,MAAM;QACN,UAAU;YAAC;YAAM;SAA6B;QAC9C,WAAW;YAAC;YAAM;SAA6C;IACjE;IAEA,gBAAgB;IAChB,QAAQ;QACN,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,MAAM;IACR;IAEA,uCAAuC;IACvC,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAS;YAAa;SAAW;QACxC,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IACA,OAAO;QACL,MAAM;QACN,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,SAAS;IACX;AACF,GACA;IACE,YAAY;AACd;AAGF,sBAAsB;AACtB,WAAW,GAAG,CAAC,QAAQ,SAAU,IAAI;IACnC,2CAA2C;IAC3C,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE;QAC1C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CACnB,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;IACzB;IAEA,qDAAqD;IACrD,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY;QAC9B,MAAM,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,EAAE,KAAK,CAAC,OAAO,MAAM;QAC1D,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,YAAY;IACxC;IAEA,sDAAsD;IACtD,IACE,IAAI,CAAC,UAAU,CAAC,aAChB,IAAI,CAAC,MAAM,KAAK,eAChB,CAAC,IAAI,CAAC,WAAW,EACjB;QACA,IAAI,CAAC,WAAW,GAAG,IAAI;QACvB,IAAI,CAAC,WAAW,GAAG;IACrB;IAEA;AACF;AAEA,8CAA8C;AAC9C,WAAW,KAAK,CAAC;IAAE,YAAY;AAAE;AACjC,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC7B,WAAW,KAAK,CAAC;IAAE,aAAa;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,aAAa,CAAC;AAAE;AACnC,WAAW,KAAK,CAAC;IAAE,gBAAgB;AAAE;AACrC,WAAW,KAAK,CAAC;IACf,OAAO;IACP,SAAS;IACT,WAAW;IACX,iBAAiB;AACnB,IAAI,oBAAoB;uCAET,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IACjC,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 872, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/models/Category.ts"], "sourcesContent": ["import mongoose, { type Document, Schema } from \"mongoose\";\r\n\r\nexport interface ICategory extends Document {\r\n  name: string;\r\n  description?: string;\r\n  color?: string;\r\n  isActive: boolean;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\nconst CategorySchema = new Schema<ICategory>(\r\n  {\r\n    name: {\r\n      type: String,\r\n      required: [true, \"Category name is required\"],\r\n      unique: true,\r\n      trim: true,\r\n      maxlength: [50, \"Category name cannot exceed 50 characters\"],\r\n    },\r\n    description: {\r\n      type: String,\r\n      maxlength: [200, \"Description cannot exceed 200 characters\"],\r\n    },\r\n    color: {\r\n      type: String,\r\n      default: \"#6366f1\",\r\n      match: [\r\n        /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,\r\n        \"Please enter a valid hex color\",\r\n      ],\r\n    },\r\n    isActive: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n  },\r\n  {\r\n    timestamps: true,\r\n  }\r\n);\r\n\r\nCategorySchema.index({ isActive: 1 });\r\n\r\nexport default mongoose.models.Category ||\r\n  mongoose.model<ICategory>(\"Category\", CategorySchema);\r\n"], "names": [], "mappings": ";;;AAAA;;AAWA,MAAM,iBAAiB,IAAI,yGAAA,CAAA,SAAM,CAC/B;IACE,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAA4B;QAC7C,QAAQ;QACR,MAAM;QACN,WAAW;YAAC;YAAI;SAA4C;IAC9D;IACA,aAAa;QACX,MAAM;QACN,WAAW;YAAC;YAAK;SAA2C;IAC9D;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,OAAO;YACL;YACA;SACD;IACH;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;AACF,GACA;IACE,YAAY;AACd;AAGF,eAAe,KAAK,CAAC;IAAE,UAAU;AAAE;uCAEpB,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,QAAQ,IACrC,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAY,YAAY", "debugId": null}}, {"offset": {"line": 1021, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/lib/minio.ts"], "sourcesContent": ["import { Client } from \"minio\";\r\nimport { Readable } from \"stream\";\r\n\r\n// MinIO configuration\r\nconst minioConfig = {\r\n  endPoint:\r\n    process.env.MINIO_ENDPOINT?.replace(/^https?:\\/\\//, \"\") || \"localhost\",\r\n  port: process.env.MINIO_PORT ? Number.parseInt(process.env.MINIO_PORT) : 9000,\r\n  useSSL: process.env.MINIO_USE_SSL === \"true\",\r\n  accessKey: process.env.MINIO_ACCESS_KEY || \"3uiq5emitjasdfghyjui\",\r\n  secretKey:\r\n    process.env.MINIO_SECRET_KEY || \"TYo1ruP1PqbOx3fONapGwawKQGqCDQsdfadsdfgh\",\r\n};\r\n\r\nconst BUCKET_NAME = process.env.MINIO_BUCKET_NAME || \"spinfree\";\r\n\r\n// Create MinIO client\r\nconst minioClient = new Client(minioConfig);\r\n\r\n// Initialize bucket if it doesn't exist\r\nconst initializeBucket = async (): Promise<void> => {\r\n  try {\r\n    console.log(\"Checking MinIO connection...\");\r\n    console.log(\"MinIO config:\", {\r\n      endPoint: minioConfig.endPoint,\r\n      port: minioConfig.port,\r\n      useSSL: minioConfig.useSSL,\r\n      bucket: BUCKET_NAME,\r\n    });\r\n\r\n    const bucketExists = await minioClient.bucketExists(BUCKET_NAME);\r\n\r\n    if (!bucketExists) {\r\n      await minioClient.makeBucket(BUCKET_NAME, \"us-east-1\");\r\n      console.log(`Bucket '${BUCKET_NAME}' created successfully`);\r\n\r\n      // Set bucket policy to allow public read access\r\n      const policy = {\r\n        Version: \"2012-10-17\",\r\n        Statement: [\r\n          {\r\n            Effect: \"Allow\",\r\n            Principal: { AWS: [\"*\"] },\r\n            Action: [\"s3:GetObject\"],\r\n            Resource: [`arn:aws:s3:::${BUCKET_NAME}/*`],\r\n          },\r\n        ],\r\n      };\r\n\r\n      await minioClient.setBucketPolicy(BUCKET_NAME, JSON.stringify(policy));\r\n      console.log(`Bucket policy set for '${BUCKET_NAME}'`);\r\n    } else {\r\n      console.log(`Bucket '${BUCKET_NAME}' already exists`);\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error initializing MinIO bucket:\", error);\r\n    // Don't throw error, allow application to continue\r\n  }\r\n};\r\n\r\n// File type detection\r\nconst detectImageType = (buffer: Buffer): string | null => {\r\n  // Check for PNG\r\n  if (\r\n    buffer[0] === 0x89 &&\r\n    buffer[1] === 0x50 &&\r\n    buffer[2] === 0x4e &&\r\n    buffer[3] === 0x47\r\n  ) {\r\n    return \"image/png\";\r\n  }\r\n  // Check for JPEG\r\n  if (buffer[0] === 0xff && buffer[1] === 0xd8 && buffer[2] === 0xff) {\r\n    return \"image/jpeg\";\r\n  }\r\n  // Check for SVG\r\n  const possibleSvg = buffer.toString(\"ascii\", 0, 100).toLowerCase();\r\n  if (possibleSvg.includes(\"<svg\") || possibleSvg.includes(\"<?xml\")) {\r\n    return \"image/svg+xml\";\r\n  }\r\n  return null;\r\n};\r\n\r\nexport class MinioService {\r\n  static async uploadFile(\r\n    fileBuffer: Buffer,\r\n    fileName: string,\r\n    contentType: string,\r\n    folder = \"posts\"\r\n  ): Promise<string> {\r\n    try {\r\n      console.log(\"Starting file upload to MinIO...\");\r\n\r\n      // Check MinIO connection first\r\n      try {\r\n        await minioClient.bucketExists(BUCKET_NAME);\r\n        console.log(\"MinIO connection verified\");\r\n      } catch (connectionError) {\r\n        console.error(\"MinIO connection failed:\", connectionError);\r\n        throw new Error(\r\n          \"MinIO service is unavailable. Please try again later.\"\r\n        );\r\n      }\r\n\r\n      // Initialize bucket if needed\r\n      await initializeBucket();\r\n\r\n      // Detect actual file type if it's octet-stream\r\n      let mimeType = contentType;\r\n      if (contentType === \"application/octet-stream\") {\r\n        const detectedType = detectImageType(fileBuffer);\r\n        if (!detectedType) {\r\n          throw new Error(\"Invalid or unsupported image format\");\r\n        }\r\n        mimeType = detectedType;\r\n      }\r\n\r\n      // Generate unique filename with date structure\r\n      const date = new Date();\r\n      const fileExtension =\r\n        mimeType === \"image/svg+xml\"\r\n          ? \"svg\"\r\n          : mimeType === \"image/png\"\r\n          ? \"png\"\r\n          : \"jpg\";\r\n      const uniqueFileName = `${Date.now()}-${fileName.replace(\r\n        /\\.[^/.]+$/,\r\n        \"\"\r\n      )}.${fileExtension}`;\r\n\r\n      const relativePath = [\r\n        folder,\r\n        date.getFullYear().toString(),\r\n        (date.getMonth() + 1).toString().padStart(2, \"0\"),\r\n        uniqueFileName,\r\n      ].join(\"/\");\r\n\r\n      console.log(\"Uploading to path:\", relativePath);\r\n\r\n      // Create readable stream from buffer\r\n      const fileStream = new Readable();\r\n      fileStream.push(fileBuffer);\r\n      fileStream.push(null);\r\n\r\n      // Upload to MinIO\r\n      await minioClient.putObject(\r\n        BUCKET_NAME,\r\n        relativePath,\r\n        fileStream,\r\n        fileBuffer.length,\r\n        {\r\n          \"Content-Type\": mimeType,\r\n        }\r\n      );\r\n\r\n      // Generate URL through our API proxy instead of direct MinIO URL\r\n      const baseUrl =\r\n        process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:3001\";\r\n      const fileUrl = `${baseUrl}/api/uploads/${relativePath}`;\r\n\r\n      console.log(`File uploaded to MinIO successfully!`);\r\n      console.log(`- MinIO path: ${relativePath}`);\r\n      console.log(`- Proxy URL: ${fileUrl}`);\r\n      console.log(`- Base URL: ${baseUrl}`);\r\n      return fileUrl;\r\n    } catch (error) {\r\n      console.error(\"Error uploading file to MinIO:\", error);\r\n      console.error(\"MinIO config:\", {\r\n        endPoint: minioConfig.endPoint,\r\n        port: minioConfig.port,\r\n        useSSL: minioConfig.useSSL,\r\n        bucket: BUCKET_NAME,\r\n      });\r\n      console.error(\"Upload details:\", {\r\n        fileName,\r\n        contentType,\r\n        folder,\r\n        bufferSize: fileBuffer.length,\r\n      });\r\n\r\n      // Fallback: return a placeholder URL instead of throwing\r\n      const fallbackUrl = `/uploads/${folder}/${Date.now()}-${fileName}`;\r\n      console.log(\"Upload failed, using fallback URL:\", fallbackUrl);\r\n      return fallbackUrl;\r\n    }\r\n  }\r\n\r\n  static async deleteFile(fileUrl: string): Promise<void> {\r\n    try {\r\n      // Extract object name from URL\r\n      const url = new URL(fileUrl);\r\n      const objectName = url.pathname.substring(\r\n        url.pathname.indexOf(\"/\", 1) + 1\r\n      );\r\n\r\n      await minioClient.removeObject(BUCKET_NAME, objectName);\r\n      console.log(`File deleted from MinIO: ${objectName}`);\r\n    } catch (error) {\r\n      console.error(\"Error deleting file from MinIO:\", error);\r\n      throw new Error(\"Failed to delete file\");\r\n    }\r\n  }\r\n\r\n  static async listFiles(folder = \"posts\", maxKeys = 100): Promise<any[]> {\r\n    try {\r\n      const files: any[] = [];\r\n      const stream = minioClient.listObjects(BUCKET_NAME, `${folder}/`, true);\r\n\r\n      return new Promise((resolve, reject) => {\r\n        stream.on(\"data\", (obj) => {\r\n          if (files.length < maxKeys) {\r\n            const protocol = minioConfig.useSSL ? \"https\" : \"http\";\r\n            const port =\r\n              minioConfig.port !== (minioConfig.useSSL ? 443 : 80)\r\n                ? `:${minioConfig.port}`\r\n                : \"\";\r\n            const fileUrl = `${protocol}://${minioConfig.endPoint}${port}/${BUCKET_NAME}/${obj.name}`;\r\n\r\n            files.push({\r\n              name: obj.name,\r\n              size: obj.size,\r\n              lastModified: obj.lastModified,\r\n              url: fileUrl,\r\n              fileName: obj.name?.split(\"/\").pop() || \"\",\r\n              folder: obj.name?.split(\"/\").slice(0, -1).join(\"/\") || \"\",\r\n            });\r\n          }\r\n        });\r\n\r\n        stream.on(\"end\", () => resolve(files));\r\n        stream.on(\"error\", (err) => reject(err));\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Error listing files:\", error);\r\n      throw new Error(\"Failed to list files\");\r\n    }\r\n  }\r\n\r\n  static async getPresignedUrl(\r\n    objectName: string,\r\n    expiry = 3600\r\n  ): Promise<string> {\r\n    try {\r\n      return await minioClient.presignedGetObject(\r\n        BUCKET_NAME,\r\n        objectName,\r\n        expiry\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Error generating presigned URL:\", error);\r\n      throw new Error(\"Failed to generate presigned URL\");\r\n    }\r\n  }\r\n\r\n  static async getFileStream(objectName: string): Promise<Buffer> {\r\n    try {\r\n      const stream = await minioClient.getObject(BUCKET_NAME, objectName);\r\n      const chunks: Buffer[] = [];\r\n\r\n      return new Promise((resolve, reject) => {\r\n        stream.on(\"data\", (chunk) => chunks.push(chunk));\r\n        stream.on(\"end\", () => resolve(Buffer.concat(chunks)));\r\n        stream.on(\"error\", reject);\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Error getting file stream from MinIO:\", error);\r\n      throw new Error(\"Failed to get file from MinIO\");\r\n    }\r\n  }\r\n}\r\n\r\n// Initialize bucket on module load (but don't block)\r\ninitializeBucket().catch((err) => {\r\n  console.error(\"Critical error during MinIO initialization:\", err);\r\n});\r\n\r\nexport { minioClient, minioConfig, BUCKET_NAME };\r\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;;;AAEA,sBAAsB;AACtB,MAAM,cAAc;IAClB,UACE,QAAQ,GAAG,CAAC,cAAc,EAAE,QAAQ,gBAAgB,OAAO;IAC7D,MAAM,QAAQ,GAAG,CAAC,UAAU,GAAG,OAAO,QAAQ,CAAC,QAAQ,GAAG,CAAC,UAAU,IAAI;IACzE,QAAQ,QAAQ,GAAG,CAAC,aAAa,KAAK;IACtC,WAAW,QAAQ,GAAG,CAAC,gBAAgB,IAAI;IAC3C,WACE,QAAQ,GAAG,CAAC,gBAAgB,IAAI;AACpC;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,iBAAiB,IAAI;AAErD,sBAAsB;AACtB,MAAM,cAAc,IAAI,8JAAA,CAAA,SAAM,CAAC;AAE/B,wCAAwC;AACxC,MAAM,mBAAmB;IACvB,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,iBAAiB;YAC3B,UAAU,YAAY,QAAQ;YAC9B,MAAM,YAAY,IAAI;YACtB,QAAQ,YAAY,MAAM;YAC1B,QAAQ;QACV;QAEA,MAAM,eAAe,MAAM,YAAY,YAAY,CAAC;QAEpD,IAAI,CAAC,cAAc;YACjB,MAAM,YAAY,UAAU,CAAC,aAAa;YAC1C,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,sBAAsB,CAAC;YAE1D,gDAAgD;YAChD,MAAM,SAAS;gBACb,SAAS;gBACT,WAAW;oBACT;wBACE,QAAQ;wBACR,WAAW;4BAAE,KAAK;gCAAC;6BAAI;wBAAC;wBACxB,QAAQ;4BAAC;yBAAe;wBACxB,UAAU;4BAAC,CAAC,aAAa,EAAE,YAAY,EAAE,CAAC;yBAAC;oBAC7C;iBACD;YACH;YAEA,MAAM,YAAY,eAAe,CAAC,aAAa,KAAK,SAAS,CAAC;YAC9D,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC;QACtD,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,gBAAgB,CAAC;QACtD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;IAClD,mDAAmD;IACrD;AACF;AAEA,sBAAsB;AACtB,MAAM,kBAAkB,CAAC;IACvB,gBAAgB;IAChB,IACE,MAAM,CAAC,EAAE,KAAK,QACd,MAAM,CAAC,EAAE,KAAK,QACd,MAAM,CAAC,EAAE,KAAK,QACd,MAAM,CAAC,EAAE,KAAK,MACd;QACA,OAAO;IACT;IACA,iBAAiB;IACjB,IAAI,MAAM,CAAC,EAAE,KAAK,QAAQ,MAAM,CAAC,EAAE,KAAK,QAAQ,MAAM,CAAC,EAAE,KAAK,MAAM;QAClE,OAAO;IACT;IACA,gBAAgB;IAChB,MAAM,cAAc,OAAO,QAAQ,CAAC,SAAS,GAAG,KAAK,WAAW;IAChE,IAAI,YAAY,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC,UAAU;QACjE,OAAO;IACT;IACA,OAAO;AACT;AAEO,MAAM;IACX,aAAa,WACX,UAAkB,EAClB,QAAgB,EAChB,WAAmB,EACnB,SAAS,OAAO,EACC;QACjB,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,+BAA+B;YAC/B,IAAI;gBACF,MAAM,YAAY,YAAY,CAAC;gBAC/B,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,iBAAiB;gBACxB,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,MAAM,IAAI,MACR;YAEJ;YAEA,8BAA8B;YAC9B,MAAM;YAEN,+CAA+C;YAC/C,IAAI,WAAW;YACf,IAAI,gBAAgB,4BAA4B;gBAC9C,MAAM,eAAe,gBAAgB;gBACrC,IAAI,CAAC,cAAc;oBACjB,MAAM,IAAI,MAAM;gBAClB;gBACA,WAAW;YACb;YAEA,+CAA+C;YAC/C,MAAM,OAAO,IAAI;YACjB,MAAM,gBACJ,aAAa,kBACT,QACA,aAAa,cACb,QACA;YACN,MAAM,iBAAiB,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,SAAS,OAAO,CACtD,aACA,IACA,CAAC,EAAE,eAAe;YAEpB,MAAM,eAAe;gBACnB;gBACA,KAAK,WAAW,GAAG,QAAQ;gBAC3B,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;gBAC7C;aACD,CAAC,IAAI,CAAC;YAEP,QAAQ,GAAG,CAAC,sBAAsB;YAElC,qCAAqC;YACrC,MAAM,aAAa,IAAI,qGAAA,CAAA,WAAQ;YAC/B,WAAW,IAAI,CAAC;YAChB,WAAW,IAAI,CAAC;YAEhB,kBAAkB;YAClB,MAAM,YAAY,SAAS,CACzB,aACA,cACA,YACA,WAAW,MAAM,EACjB;gBACE,gBAAgB;YAClB;YAGF,iEAAiE;YACjE,MAAM,UACJ,gEAAoC;YACtC,MAAM,UAAU,GAAG,QAAQ,aAAa,EAAE,cAAc;YAExD,QAAQ,GAAG,CAAC,CAAC,oCAAoC,CAAC;YAClD,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc;YAC3C,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,SAAS;YACrC,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,SAAS;YACpC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,QAAQ,KAAK,CAAC,iBAAiB;gBAC7B,UAAU,YAAY,QAAQ;gBAC9B,MAAM,YAAY,IAAI;gBACtB,QAAQ,YAAY,MAAM;gBAC1B,QAAQ;YACV;YACA,QAAQ,KAAK,CAAC,mBAAmB;gBAC/B;gBACA;gBACA;gBACA,YAAY,WAAW,MAAM;YAC/B;YAEA,yDAAyD;YACzD,MAAM,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,UAAU;YAClE,QAAQ,GAAG,CAAC,sCAAsC;YAClD,OAAO;QACT;IACF;IAEA,aAAa,WAAW,OAAe,EAAiB;QACtD,IAAI;YACF,+BAA+B;YAC/B,MAAM,MAAM,IAAI,IAAI;YACpB,MAAM,aAAa,IAAI,QAAQ,CAAC,SAAS,CACvC,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,KAAK;YAGjC,MAAM,YAAY,YAAY,CAAC,aAAa;YAC5C,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,YAAY;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,UAAU,SAAS,OAAO,EAAE,UAAU,GAAG,EAAkB;QACtE,IAAI;YACF,MAAM,QAAe,EAAE;YACvB,MAAM,SAAS,YAAY,WAAW,CAAC,aAAa,GAAG,OAAO,CAAC,CAAC,EAAE;YAElE,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC3B,OAAO,EAAE,CAAC,QAAQ,CAAC;oBACjB,IAAI,MAAM,MAAM,GAAG,SAAS;wBAC1B,MAAM,WAAW,YAAY,MAAM,GAAG,UAAU;wBAChD,MAAM,OACJ,YAAY,IAAI,KAAK,CAAC,YAAY,MAAM,GAAG,MAAM,EAAE,IAC/C,CAAC,CAAC,EAAE,YAAY,IAAI,EAAE,GACtB;wBACN,MAAM,UAAU,GAAG,SAAS,GAAG,EAAE,YAAY,QAAQ,GAAG,KAAK,CAAC,EAAE,YAAY,CAAC,EAAE,IAAI,IAAI,EAAE;wBAEzF,MAAM,IAAI,CAAC;4BACT,MAAM,IAAI,IAAI;4BACd,MAAM,IAAI,IAAI;4BACd,cAAc,IAAI,YAAY;4BAC9B,KAAK;4BACL,UAAU,IAAI,IAAI,EAAE,MAAM,KAAK,SAAS;4BACxC,QAAQ,IAAI,IAAI,EAAE,MAAM,KAAK,MAAM,GAAG,CAAC,GAAG,KAAK,QAAQ;wBACzD;oBACF;gBACF;gBAEA,OAAO,EAAE,CAAC,OAAO,IAAM,QAAQ;gBAC/B,OAAO,EAAE,CAAC,SAAS,CAAC,MAAQ,OAAO;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,gBACX,UAAkB,EAClB,SAAS,IAAI,EACI;QACjB,IAAI;YACF,OAAO,MAAM,YAAY,kBAAkB,CACzC,aACA,YACA;QAEJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,cAAc,UAAkB,EAAmB;QAC9D,IAAI;YACF,MAAM,SAAS,MAAM,YAAY,SAAS,CAAC,aAAa;YACxD,MAAM,SAAmB,EAAE;YAE3B,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC3B,OAAO,EAAE,CAAC,QAAQ,CAAC,QAAU,OAAO,IAAI,CAAC;gBACzC,OAAO,EAAE,CAAC,OAAO,IAAM,QAAQ,OAAO,MAAM,CAAC;gBAC7C,OAAO,EAAE,CAAC,SAAS;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAEA,qDAAqD;AACrD,mBAAmB,KAAK,CAAC,CAAC;IACxB,QAAQ,KAAK,CAAC,+CAA+C;AAC/D", "debugId": null}}, {"offset": {"line": 1248, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/dashboard/blogs/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { cookies } from \"next/headers\";\r\nimport { revalidateTag, revalidatePath } from \"next/cache\";\r\nimport connectDB from \"@/lib/mongodb\";\r\nimport Post from \"@/models/Post\";\r\nimport Category from \"@/models/Category\";\r\nimport { MinioService } from \"@/lib/minio\";\r\nimport jwt from \"jsonwebtoken\";\r\n\r\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key\";\r\n\r\nexport async function updatePostStatusAction(postId: string, status: string) {\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    let userInfo = null;\r\n\r\n    // Try both auth methods\r\n    if (authToken) {\r\n      try {\r\n        userInfo = jwt.verify(authToken, JWT_SECRET) as any;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Fallback to user-info cookie\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"Authentication required\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Find the post first\r\n    const post = await Post.findById(postId);\r\n\r\n    if (!post) {\r\n      return { success: false, error: \"Post not found\" };\r\n    }\r\n\r\n    // Update the status and save to trigger middleware\r\n    post.status = status as \"draft\" | \"published\" | \"archived\";\r\n\r\n    // Manually handle the published state logic\r\n    if (status === \"published\") {\r\n      post.isPublished = true;\r\n      if (!post.publishedAt) {\r\n        post.publishedAt = new Date();\r\n      }\r\n    } else {\r\n      post.isPublished = false;\r\n    }\r\n\r\n    const updatedPost = await post.save();\r\n\r\n    // Revalidate the public posts cache to show updated posts immediately\r\n    revalidateTag(\"public-posts\");\r\n\r\n    // Also revalidate the blogs page to show updated posts\r\n    revalidatePath(\"/en/blogs\");\r\n    revalidatePath(\"/th/blogs\");\r\n\r\n    // Serialize the post data for client components\r\n    const serializedPost = {\r\n      _id: updatedPost._id.toString(),\r\n      title: updatedPost.title,\r\n      status: updatedPost.status,\r\n      isPublished: updatedPost.isPublished,\r\n      publishedAt: updatedPost.publishedAt?.toISOString(),\r\n      createdAt: updatedPost.createdAt?.toISOString(),\r\n      updatedAt: updatedPost.updatedAt?.toISOString(),\r\n    };\r\n\r\n    return {\r\n      success: true,\r\n      data: { post: serializedPost },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error updating post status:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getCategoriesAction() {\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n\r\n    console.log(\"=== CATEGORIES ACTION DEBUG ===\");\r\n    console.log(\"All cookies:\", cookieStore.getAll());\r\n\r\n    // Try both auth methods: httpOnly token and user-info cookie\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    console.log(\"Auth token:\", authToken ? \"Found\" : \"Not found\");\r\n    console.log(\"User info cookie:\", userInfoCookie ? \"Found\" : \"Not found\");\r\n\r\n    let userInfo = null;\r\n\r\n    // Method 1: Try httpOnly token\r\n    if (authToken) {\r\n      try {\r\n        const decoded = jwt.verify(authToken, JWT_SECRET) as any;\r\n        console.log(\"Token verified successfully for user:\", decoded.userId);\r\n        userInfo = decoded;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Method 2: Try user-info cookie (fallback)\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        console.log(\"User info from cookie:\", parsedUserInfo);\r\n\r\n        // Verify user is admin\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n          console.log(\"Admin access granted via user-info cookie\");\r\n        } else {\r\n          return { success: false, error: \"Admin access required\" };\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"No valid authentication found\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Get all categories\r\n    const categories = await Category.find({ isActive: true })\r\n      .sort({ name: 1 })\r\n      .lean();\r\n\r\n    // Convert MongoDB documents to plain objects for client components\r\n    const serializedCategories = categories.map((category: any) => ({\r\n      _id: category._id.toString(), // Convert ObjectId to string\r\n      name: category.name,\r\n      description: category.description || \"\",\r\n      color: category.color || \"#6366f1\",\r\n      isActive: category.isActive,\r\n      createdAt: category.createdAt?.toISOString() || new Date().toISOString(),\r\n      updatedAt: category.updatedAt?.toISOString() || new Date().toISOString(),\r\n    }));\r\n\r\n    return {\r\n      success: true,\r\n      data: { categories: serializedCategories },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching categories:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createCategoryAction(data: {\r\n  name: string;\r\n  description: string;\r\n}) {\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    let userInfo = null;\r\n\r\n    // Try both auth methods\r\n    if (authToken) {\r\n      try {\r\n        userInfo = jwt.verify(authToken, JWT_SECRET) as any;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Fallback to user-info cookie\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"Authentication required\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Check if category already exists (case-insensitive)\r\n    const existingCategory = await Category.findOne({\r\n      name: { $regex: new RegExp(`^${data.name}$`, \"i\") },\r\n    });\r\n\r\n    if (existingCategory) {\r\n      return { success: false, error: \"Category already exists\" };\r\n    }\r\n\r\n    // Create new category\r\n    const category = new Category(data);\r\n    await category.save();\r\n\r\n    // Serialize the category for client components\r\n    const serializedCategory = {\r\n      _id: category._id.toString(),\r\n      name: category.name,\r\n      description: category.description || \"\",\r\n      color: category.color || \"#6366f1\",\r\n      isActive: category.isActive,\r\n      createdAt: category.createdAt?.toISOString() || new Date().toISOString(),\r\n      updatedAt: category.updatedAt?.toISOString() || new Date().toISOString(),\r\n    };\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Category created successfully\",\r\n      data: { category: serializedCategory },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error creating category:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createPostAction(\r\n  postData: any,\r\n  bannerImageBase64?: string\r\n) {\r\n  console.log(\"hello\");\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    let userInfo = null;\r\n\r\n    // Try both auth methods\r\n    if (authToken) {\r\n      try {\r\n        userInfo = jwt.verify(authToken, JWT_SECRET) as any;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Fallback to user-info cookie\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"Authentication required\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Handle banner image upload if provided\r\n    let bannerUrl = \"\";\r\n    if (bannerImageBase64) {\r\n      try {\r\n        // Extract content type and base64 data\r\n        const matches = bannerImageBase64.match(/^data:(.+);base64,(.+)$/);\r\n        if (!matches || matches.length !== 3) {\r\n          throw new Error(\"Invalid base64 image format\");\r\n        }\r\n\r\n        const contentType = matches[1]; // e.g., \"image/jpeg\", \"image/png\"\r\n        const base64Data = matches[2];\r\n        const imageBuffer = Buffer.from(base64Data, \"base64\");\r\n\r\n        // Generate filename with correct extension\r\n        const timestamp = Date.now();\r\n        const fileExtension = contentType.split(\"/\")[1] || \"jpg\";\r\n        const filename = `banner-${timestamp}.${fileExtension}`;\r\n\r\n        console.log(\"Uploading banner image:\", {\r\n          contentType,\r\n          filename,\r\n          bufferSize: imageBuffer.length,\r\n        });\r\n\r\n        // Upload to MinIO\r\n        bannerUrl = await MinioService.uploadFile(\r\n          imageBuffer,\r\n          filename,\r\n          contentType, // Use detected content type\r\n          \"banners\" // folder\r\n        );\r\n        console.log(\"Banner uploaded successfully:\", bannerUrl);\r\n      } catch (error) {\r\n        console.error(\"Error uploading banner:\", error);\r\n        // Continue without banner if upload fails\r\n      }\r\n    }\r\n\r\n    // Create the post\r\n    const postToCreate = {\r\n      ...postData,\r\n      banner: bannerUrl || postData.banner || \"\",\r\n      author: userInfo.userId || userInfo._id,\r\n      status: \"draft\", // Default to draft\r\n      isBlog: true,\r\n      isPublished: false,\r\n    };\r\n\r\n    const post = new Post(postToCreate);\r\n    await post.save();\r\n\r\n    // Serialize the post for client components\r\n    const serializedPost = {\r\n      _id: post._id.toString(),\r\n      title: post.title,\r\n      slug: post.slug,\r\n      excerpt: post.excerpt,\r\n      content: post.content,\r\n      banner: post.banner,\r\n      status: post.status,\r\n      isPublished: post.isPublished,\r\n      publishedAt: post.publishedAt?.toISOString(),\r\n      createdAt: post.createdAt?.toISOString(),\r\n      updatedAt: post.updatedAt?.toISOString(),\r\n    };\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Post created successfully\",\r\n      data: { post: serializedPost },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error creating post:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePostAction(\r\n  postId: string,\r\n  postData: any,\r\n  bannerImageBase64?: string\r\n) {\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    let userInfo = null;\r\n\r\n    // Try both auth methods\r\n    if (authToken) {\r\n      try {\r\n        userInfo = jwt.verify(authToken, JWT_SECRET) as any;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Fallback to user-info cookie\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"Authentication required\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Find the post\r\n    const post = await Post.findOne({ slug: postId });\r\n    if (!post) {\r\n      return { success: false, error: \"Post not found\" };\r\n    }\r\n\r\n    // Handle banner image upload if provided\r\n    let bannerUrl =\r\n      postData.banner?.image || post.banner?.image || post.banner || \"\";\r\n    if (bannerImageBase64) {\r\n      try {\r\n        // Extract content type and base64 data\r\n        const matches = bannerImageBase64.match(/^data:(.+);base64,(.+)$/);\r\n        if (!matches || matches.length !== 3) {\r\n          throw new Error(\"Invalid base64 image format\");\r\n        }\r\n\r\n        const contentType = matches[1];\r\n        const base64Data = matches[2];\r\n        const imageBuffer = Buffer.from(base64Data, \"base64\");\r\n\r\n        // Generate filename with correct extension\r\n        const timestamp = Date.now();\r\n        const fileExtension = contentType.split(\"/\")[1] || \"jpg\";\r\n        const filename = `banner-${timestamp}.${fileExtension}`;\r\n\r\n        console.log(\"Uploading banner image:\", {\r\n          contentType,\r\n          filename,\r\n          bufferSize: imageBuffer.length,\r\n        });\r\n\r\n        // Upload to MinIO\r\n        bannerUrl = await MinioService.uploadFile(\r\n          imageBuffer,\r\n          filename,\r\n          contentType,\r\n          \"banners\"\r\n        );\r\n        console.log(\"Banner uploaded successfully:\", bannerUrl);\r\n      } catch (error) {\r\n        console.error(\"Error uploading banner:\", error);\r\n        // Continue without banner if upload fails\r\n      }\r\n    }\r\n\r\n    // Clean the postData to avoid circular references\r\n    const cleanPostData = {\r\n      title: postData.title,\r\n      slug: postData.slug, // Include slug field for updates\r\n      content: postData.content,\r\n      categories: postData.categories,\r\n      tags: postData.tags,\r\n      status: postData.status,\r\n      isPublished: postData.isPublished,\r\n      publishedAt: postData.publishedAt,\r\n      metaTitle: postData.metaTitle,\r\n      metaDescription: postData.metaDescription,\r\n      metaKeywords: postData.metaKeywords,\r\n    };\r\n\r\n    const updateData = {\r\n      ...cleanPostData,\r\n      banner: bannerUrl, // Store as string, not object\r\n      seoTitle: postData.metaTitle,\r\n      seoDescription: postData.metaDescription,\r\n      metaKeywords: postData.metaKeywords,\r\n      author: userInfo.userId || userInfo._id,\r\n    };\r\n\r\n    // Update the post\r\n    const updatedPost = (await Post.findOneAndUpdate(\r\n      { slug: postId },\r\n      updateData,\r\n      {\r\n        new: true,\r\n        runValidators: true,\r\n      }\r\n    )\r\n      .populate(\"categories\", \"name description color\")\r\n      .lean()) as any; // Add .lean() to get plain objects\r\n\r\n    if (!updatedPost) {\r\n      return { success: false, error: \"Failed to update post\" };\r\n    }\r\n\r\n    // Serialize the post for client components (ensure no circular references)\r\n    const serializedPost = {\r\n      _id: updatedPost._id.toString(),\r\n      title: updatedPost.title,\r\n      slug: updatedPost.slug,\r\n      content: updatedPost.content,\r\n      banner: updatedPost.banner,\r\n      categories: Array.isArray(updatedPost.categories)\r\n        ? updatedPost.categories.map((cat: any) => ({\r\n            _id: cat._id?.toString(),\r\n            name: cat.name,\r\n            description: cat.description,\r\n            color: cat.color,\r\n          }))\r\n        : [],\r\n      tags: updatedPost.tags || [],\r\n      seoTitle: updatedPost.seoTitle,\r\n      seoDescription: updatedPost.seoDescription,\r\n      metaKeywords: updatedPost.metaKeywords,\r\n      status: updatedPost.status,\r\n      isPublished: updatedPost.isPublished,\r\n      publishedAt:\r\n        updatedPost.publishedAt?.toISOString?.() || updatedPost.publishedAt,\r\n      createdAt:\r\n        updatedPost.createdAt?.toISOString?.() || updatedPost.createdAt,\r\n      updatedAt:\r\n        updatedPost.updatedAt?.toISOString?.() || updatedPost.updatedAt,\r\n    };\r\n\r\n    // Revalidate caches\r\n    revalidateTag(\"public-posts\");\r\n    revalidatePath(\"/en/blogs\");\r\n    revalidatePath(\"/th/blogs\");\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Post updated successfully\",\r\n      data: { post: serializedPost },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error updating post:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAEtC,eAAe,uBAAuB,MAAc,EAAE,MAAc;IACzE,IAAI;QACF,uBAAuB;QACvB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;QAChC,MAAM,YAAY,YAAY,GAAG,CAAC,eAAe;QACjD,MAAM,iBAAiB,YAAY,GAAG,CAAC,cAAc;QAErD,IAAI,WAAW;QAEf,wBAAwB;QACxB,IAAI,WAAW;YACb,IAAI;gBACF,WAAW,qIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,WAAW;YACnC,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,8BAA8B;YAC5C;QACF;QAEA,+BAA+B;QAC/B,IAAI,CAAC,YAAY,gBAAgB;YAC/B,IAAI;gBACF,MAAM,iBAAiB,KAAK,KAAK,CAAC,mBAAmB;gBACrD,IAAI,eAAe,IAAI,KAAK,SAAS;oBACnC,WAAW;gBACb;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,qCAAqC;YACnD;QACF;QAEA,IAAI,CAAC,UAAU;YACb,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA0B;QAC5D;QAEA,sBAAsB;QACtB,MAAM,CAAA,GAAA,qHAAA,CAAA,UAAS,AAAD;QAEd,sBAAsB;QACtB,MAAM,OAAO,MAAM,qHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;QAEjC,IAAI,CAAC,MAAM;YACT,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAiB;QACnD;QAEA,mDAAmD;QACnD,KAAK,MAAM,GAAG;QAEd,4CAA4C;QAC5C,IAAI,WAAW,aAAa;YAC1B,KAAK,WAAW,GAAG;YACnB,IAAI,CAAC,KAAK,WAAW,EAAE;gBACrB,KAAK,WAAW,GAAG,IAAI;YACzB;QACF,OAAO;YACL,KAAK,WAAW,GAAG;QACrB;QAEA,MAAM,cAAc,MAAM,KAAK,IAAI;QAEnC,sEAAsE;QACtE,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE;QAEd,uDAAuD;QACvD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,gDAAgD;QAChD,MAAM,iBAAiB;YACrB,KAAK,YAAY,GAAG,CAAC,QAAQ;YAC7B,OAAO,YAAY,KAAK;YACxB,QAAQ,YAAY,MAAM;YAC1B,aAAa,YAAY,WAAW;YACpC,aAAa,YAAY,WAAW,EAAE;YACtC,WAAW,YAAY,SAAS,EAAE;YAClC,WAAW,YAAY,SAAS,EAAE;QACpC;QAEA,OAAO;YACL,SAAS;YACT,MAAM;gBAAE,MAAM;YAAe;QAC/B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAEO,eAAe;IACpB,IAAI;QACF,uBAAuB;QACvB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;QAEhC,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,gBAAgB,YAAY,MAAM;QAE9C,6DAA6D;QAC7D,MAAM,YAAY,YAAY,GAAG,CAAC,eAAe;QACjD,MAAM,iBAAiB,YAAY,GAAG,CAAC,cAAc;QAErD,QAAQ,GAAG,CAAC,eAAe,YAAY,UAAU;QACjD,QAAQ,GAAG,CAAC,qBAAqB,iBAAiB,UAAU;QAE5D,IAAI,WAAW;QAEf,+BAA+B;QAC/B,IAAI,WAAW;YACb,IAAI;gBACF,MAAM,UAAU,qIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,WAAW;gBACtC,QAAQ,GAAG,CAAC,yCAAyC,QAAQ,MAAM;gBACnE,WAAW;YACb,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,8BAA8B;YAC5C;QACF;QAEA,4CAA4C;QAC5C,IAAI,CAAC,YAAY,gBAAgB;YAC/B,IAAI;gBACF,MAAM,iBAAiB,KAAK,KAAK,CAAC,mBAAmB;gBACrD,QAAQ,GAAG,CAAC,0BAA0B;gBAEtC,uBAAuB;gBACvB,IAAI,eAAe,IAAI,KAAK,SAAS;oBACnC,WAAW;oBACX,QAAQ,GAAG,CAAC;gBACd,OAAO;oBACL,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAAwB;gBAC1D;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,qCAAqC;YACnD;QACF;QAEA,IAAI,CAAC,UAAU;YACb,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAgC;QAClE;QAEA,sBAAsB;QACtB,MAAM,CAAA,GAAA,qHAAA,CAAA,UAAS,AAAD;QAEd,qBAAqB;QACrB,MAAM,aAAa,MAAM,yHAAA,CAAA,UAAQ,CAAC,IAAI,CAAC;YAAE,UAAU;QAAK,GACrD,IAAI,CAAC;YAAE,MAAM;QAAE,GACf,IAAI;QAEP,mEAAmE;QACnE,MAAM,uBAAuB,WAAW,GAAG,CAAC,CAAC,WAAkB,CAAC;gBAC9D,KAAK,SAAS,GAAG,CAAC,QAAQ;gBAC1B,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW,IAAI;gBACrC,OAAO,SAAS,KAAK,IAAI;gBACzB,UAAU,SAAS,QAAQ;gBAC3B,WAAW,SAAS,SAAS,EAAE,iBAAiB,IAAI,OAAO,WAAW;gBACtE,WAAW,SAAS,SAAS,EAAE,iBAAiB,IAAI,OAAO,WAAW;YACxE,CAAC;QAED,OAAO;YACL,SAAS;YACT,MAAM;gBAAE,YAAY;YAAqB;QAC3C;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAEO,eAAe,qBAAqB,IAG1C;IACC,IAAI;QACF,uBAAuB;QACvB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;QAChC,MAAM,YAAY,YAAY,GAAG,CAAC,eAAe;QACjD,MAAM,iBAAiB,YAAY,GAAG,CAAC,cAAc;QAErD,IAAI,WAAW;QAEf,wBAAwB;QACxB,IAAI,WAAW;YACb,IAAI;gBACF,WAAW,qIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,WAAW;YACnC,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,8BAA8B;YAC5C;QACF;QAEA,+BAA+B;QAC/B,IAAI,CAAC,YAAY,gBAAgB;YAC/B,IAAI;gBACF,MAAM,iBAAiB,KAAK,KAAK,CAAC,mBAAmB;gBACrD,IAAI,eAAe,IAAI,KAAK,SAAS;oBACnC,WAAW;gBACb;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,qCAAqC;YACnD;QACF;QAEA,IAAI,CAAC,UAAU;YACb,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA0B;QAC5D;QAEA,sBAAsB;QACtB,MAAM,CAAA,GAAA,qHAAA,CAAA,UAAS,AAAD;QAEd,sDAAsD;QACtD,MAAM,mBAAmB,MAAM,yHAAA,CAAA,UAAQ,CAAC,OAAO,CAAC;YAC9C,MAAM;gBAAE,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE;YAAK;QACpD;QAEA,IAAI,kBAAkB;YACpB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA0B;QAC5D;QAEA,sBAAsB;QACtB,MAAM,WAAW,IAAI,yHAAA,CAAA,UAAQ,CAAC;QAC9B,MAAM,SAAS,IAAI;QAEnB,+CAA+C;QAC/C,MAAM,qBAAqB;YACzB,KAAK,SAAS,GAAG,CAAC,QAAQ;YAC1B,MAAM,SAAS,IAAI;YACnB,aAAa,SAAS,WAAW,IAAI;YACrC,OAAO,SAAS,KAAK,IAAI;YACzB,UAAU,SAAS,QAAQ;YAC3B,WAAW,SAAS,SAAS,EAAE,iBAAiB,IAAI,OAAO,WAAW;YACtE,WAAW,SAAS,SAAS,EAAE,iBAAiB,IAAI,OAAO,WAAW;QACxE;QAEA,OAAO;YACL,SAAS;YACT,SAAS;YACT,MAAM;gBAAE,UAAU;YAAmB;QACvC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAEO,eAAe,iBACpB,QAAa,EACb,iBAA0B;IAE1B,QAAQ,GAAG,CAAC;IACZ,IAAI;QACF,uBAAuB;QACvB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;QAChC,MAAM,YAAY,YAAY,GAAG,CAAC,eAAe;QACjD,MAAM,iBAAiB,YAAY,GAAG,CAAC,cAAc;QAErD,IAAI,WAAW;QAEf,wBAAwB;QACxB,IAAI,WAAW;YACb,IAAI;gBACF,WAAW,qIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,WAAW;YACnC,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,8BAA8B;YAC5C;QACF;QAEA,+BAA+B;QAC/B,IAAI,CAAC,YAAY,gBAAgB;YAC/B,IAAI;gBACF,MAAM,iBAAiB,KAAK,KAAK,CAAC,mBAAmB;gBACrD,IAAI,eAAe,IAAI,KAAK,SAAS;oBACnC,WAAW;gBACb;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,qCAAqC;YACnD;QACF;QAEA,IAAI,CAAC,UAAU;YACb,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA0B;QAC5D;QAEA,sBAAsB;QACtB,MAAM,CAAA,GAAA,qHAAA,CAAA,UAAS,AAAD;QAEd,yCAAyC;QACzC,IAAI,YAAY;QAChB,IAAI,mBAAmB;YACrB,IAAI;gBACF,uCAAuC;gBACvC,MAAM,UAAU,kBAAkB,KAAK,CAAC;gBACxC,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;oBACpC,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,cAAc,OAAO,CAAC,EAAE,EAAE,kCAAkC;gBAClE,MAAM,aAAa,OAAO,CAAC,EAAE;gBAC7B,MAAM,cAAc,OAAO,IAAI,CAAC,YAAY;gBAE5C,2CAA2C;gBAC3C,MAAM,YAAY,KAAK,GAAG;gBAC1B,MAAM,gBAAgB,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;gBACnD,MAAM,WAAW,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,eAAe;gBAEvD,QAAQ,GAAG,CAAC,2BAA2B;oBACrC;oBACA;oBACA,YAAY,YAAY,MAAM;gBAChC;gBAEA,kBAAkB;gBAClB,YAAY,MAAM,mHAAA,CAAA,eAAY,CAAC,UAAU,CACvC,aACA,UACA,aACA,UAAU,SAAS;;gBAErB,QAAQ,GAAG,CAAC,iCAAiC;YAC/C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,0CAA0C;YAC5C;QACF;QAEA,kBAAkB;QAClB,MAAM,eAAe;YACnB,GAAG,QAAQ;YACX,QAAQ,aAAa,SAAS,MAAM,IAAI;YACxC,QAAQ,SAAS,MAAM,IAAI,SAAS,GAAG;YACvC,QAAQ;YACR,QAAQ;YACR,aAAa;QACf;QAEA,MAAM,OAAO,IAAI,qHAAA,CAAA,UAAI,CAAC;QACtB,MAAM,KAAK,IAAI;QAEf,2CAA2C;QAC3C,MAAM,iBAAiB;YACrB,KAAK,KAAK,GAAG,CAAC,QAAQ;YACtB,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,SAAS,KAAK,OAAO;YACrB,SAAS,KAAK,OAAO;YACrB,QAAQ,KAAK,MAAM;YACnB,QAAQ,KAAK,MAAM;YACnB,aAAa,KAAK,WAAW;YAC7B,aAAa,KAAK,WAAW,EAAE;YAC/B,WAAW,KAAK,SAAS,EAAE;YAC3B,WAAW,KAAK,SAAS,EAAE;QAC7B;QAEA,OAAO;YACL,SAAS;YACT,SAAS;YACT,MAAM;gBAAE,MAAM;YAAe;QAC/B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAEO,eAAe,iBACpB,MAAc,EACd,QAAa,EACb,iBAA0B;IAE1B,IAAI;QACF,uBAAuB;QACvB,MAAM,cAAc,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;QAChC,MAAM,YAAY,YAAY,GAAG,CAAC,eAAe;QACjD,MAAM,iBAAiB,YAAY,GAAG,CAAC,cAAc;QAErD,IAAI,WAAW;QAEf,wBAAwB;QACxB,IAAI,WAAW;YACb,IAAI;gBACF,WAAW,qIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,WAAW;YACnC,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,8BAA8B;YAC5C;QACF;QAEA,+BAA+B;QAC/B,IAAI,CAAC,YAAY,gBAAgB;YAC/B,IAAI;gBACF,MAAM,iBAAiB,KAAK,KAAK,CAAC,mBAAmB;gBACrD,IAAI,eAAe,IAAI,KAAK,SAAS;oBACnC,WAAW;gBACb;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,qCAAqC;YACnD;QACF;QAEA,IAAI,CAAC,UAAU;YACb,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA0B;QAC5D;QAEA,sBAAsB;QACtB,MAAM,CAAA,GAAA,qHAAA,CAAA,UAAS,AAAD;QAEd,gBAAgB;QAChB,MAAM,OAAO,MAAM,qHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,MAAM;QAAO;QAC/C,IAAI,CAAC,MAAM;YACT,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAiB;QACnD;QAEA,yCAAyC;QACzC,IAAI,YACF,SAAS,MAAM,EAAE,SAAS,KAAK,MAAM,EAAE,SAAS,KAAK,MAAM,IAAI;QACjE,IAAI,mBAAmB;YACrB,IAAI;gBACF,uCAAuC;gBACvC,MAAM,UAAU,kBAAkB,KAAK,CAAC;gBACxC,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;oBACpC,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,cAAc,OAAO,CAAC,EAAE;gBAC9B,MAAM,aAAa,OAAO,CAAC,EAAE;gBAC7B,MAAM,cAAc,OAAO,IAAI,CAAC,YAAY;gBAE5C,2CAA2C;gBAC3C,MAAM,YAAY,KAAK,GAAG;gBAC1B,MAAM,gBAAgB,YAAY,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;gBACnD,MAAM,WAAW,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,eAAe;gBAEvD,QAAQ,GAAG,CAAC,2BAA2B;oBACrC;oBACA;oBACA,YAAY,YAAY,MAAM;gBAChC;gBAEA,kBAAkB;gBAClB,YAAY,MAAM,mHAAA,CAAA,eAAY,CAAC,UAAU,CACvC,aACA,UACA,aACA;gBAEF,QAAQ,GAAG,CAAC,iCAAiC;YAC/C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,0CAA0C;YAC5C;QACF;QAEA,kDAAkD;QAClD,MAAM,gBAAgB;YACpB,OAAO,SAAS,KAAK;YACrB,MAAM,SAAS,IAAI;YACnB,SAAS,SAAS,OAAO;YACzB,YAAY,SAAS,UAAU;YAC/B,MAAM,SAAS,IAAI;YACnB,QAAQ,SAAS,MAAM;YACvB,aAAa,SAAS,WAAW;YACjC,aAAa,SAAS,WAAW;YACjC,WAAW,SAAS,SAAS;YAC7B,iBAAiB,SAAS,eAAe;YACzC,cAAc,SAAS,YAAY;QACrC;QAEA,MAAM,aAAa;YACjB,GAAG,aAAa;YAChB,QAAQ;YACR,UAAU,SAAS,SAAS;YAC5B,gBAAgB,SAAS,eAAe;YACxC,cAAc,SAAS,YAAY;YACnC,QAAQ,SAAS,MAAM,IAAI,SAAS,GAAG;QACzC;QAEA,kBAAkB;QAClB,MAAM,cAAe,MAAM,qHAAA,CAAA,UAAI,CAAC,gBAAgB,CAC9C;YAAE,MAAM;QAAO,GACf,YACA;YACE,KAAK;YACL,eAAe;QACjB,GAEC,QAAQ,CAAC,cAAc,0BACvB,IAAI,IAAY,mCAAmC;QAEtD,IAAI,CAAC,aAAa;YAChB,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAwB;QAC1D;QAEA,2EAA2E;QAC3E,MAAM,iBAAiB;YACrB,KAAK,YAAY,GAAG,CAAC,QAAQ;YAC7B,OAAO,YAAY,KAAK;YACxB,MAAM,YAAY,IAAI;YACtB,SAAS,YAAY,OAAO;YAC5B,QAAQ,YAAY,MAAM;YAC1B,YAAY,MAAM,OAAO,CAAC,YAAY,UAAU,IAC5C,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,MAAa,CAAC;oBACxC,KAAK,IAAI,GAAG,EAAE;oBACd,MAAM,IAAI,IAAI;oBACd,aAAa,IAAI,WAAW;oBAC5B,OAAO,IAAI,KAAK;gBAClB,CAAC,KACD,EAAE;YACN,MAAM,YAAY,IAAI,IAAI,EAAE;YAC5B,UAAU,YAAY,QAAQ;YAC9B,gBAAgB,YAAY,cAAc;YAC1C,cAAc,YAAY,YAAY;YACtC,QAAQ,YAAY,MAAM;YAC1B,aAAa,YAAY,WAAW;YACpC,aACE,YAAY,WAAW,EAAE,mBAAmB,YAAY,WAAW;YACrE,WACE,YAAY,SAAS,EAAE,mBAAmB,YAAY,SAAS;YACjE,WACE,YAAY,SAAS,EAAE,mBAAmB,YAAY,SAAS;QACnE;QAEA,oBAAoB;QACpB,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE;QACd,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QACf,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YACL,SAAS;YACT,SAAS;YACT,MAAM;gBAAE,MAAM;YAAe;QAC/B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;;;IAjiBsB;IA0FA;IAkFA;IA8EA;IA0HA;;AApXA,+OAAA;AA0FA,+OAAA;AAkFA,+OAAA;AA8EA,+OAAA;AA0HA,+OAAA", "debugId": null}}, {"offset": {"line": 1785, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/.next-internal/server/app/dashboard/blogs/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {logoutUser as '0083a550d7711b950e0380eaf183245f5ff600cd2d'} from 'ACTIONS_MODULE0'\nexport {getPosts as '00218888a992dbe0f0f59c40eb1d6da396b1f8bef9'} from 'ACTIONS_MODULE1'\nexport {getPublicCategories as '007ad306e150b2c52c5f587da7ab326f3381065acb'} from 'ACTIONS_MODULE1'\nexport {getBlogsCategories as '00aeb9467f3b81d270edac5fc42d30e46753429f0b'} from 'ACTIONS_MODULE1'\nexport {deletePost as '405237e0d0b1c045da7fe8974a3c8d45bf183207cb'} from 'ACTIONS_MODULE1'\nexport {createBlogCategory as '4090ffc861781390c33bd2a02d2bf879db9096df8f'} from 'ACTIONS_MODULE1'\nexport {deleteBlogCategory as '40ed9ac3739b2b25e2a2e38921fe3a197384a45c9a'} from 'ACTIONS_MODULE1'\nexport {getPublicPosts as '40ef6a94fd31e53e44bb335ccf220a53fa42e6ef7f'} from 'ACTIONS_MODULE1'\nexport {createPost as '40efe57005355e473756f81a95740baf067a03c492'} from 'ACTIONS_MODULE1'\nexport {updatePostStatus as '60a97fb412b406df9fa77ff920f3eb804ebce20bc1'} from 'ACTIONS_MODULE1'\nexport {updatePostStatusAction as '607c10ea8628475a8cbfb06ec6fe9153598d2e744a'} from 'ACTIONS_MODULE2'\n"], "names": [], "mappings": ";AAAA;AACA;AASA", "debugId": null}}, {"offset": {"line": 1879, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/dashboard/blogs/components/BlogManagement.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/dashboard/blogs/components/BlogManagement.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/dashboard/blogs/components/BlogManagement.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyT,GACtV,uFACA", "debugId": null}}, {"offset": {"line": 1893, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/dashboard/blogs/components/BlogManagement.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/dashboard/blogs/components/BlogManagement.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/dashboard/blogs/components/BlogManagement.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA", "debugId": null}}, {"offset": {"line": 1907, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1917, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/dashboard/blogs/page.tsx"], "sourcesContent": ["import { getPosts } from \"@/client_apis/api/blog\";\r\nimport BlogManagement, { BlogPost } from \"./components/BlogManagement\";\r\nimport get from \"lodash/get\";\r\nexport default async function AdminPage() {\r\n  const pos = await getPosts();\r\n  const postsData = get(pos, \"data.posts\", []);\r\n  return (\r\n    <div className=\"container mx-auto px-4 py-8\">\r\n      <BlogManagement posts={postsData as Array<BlogPost>} />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AACe,eAAe;IAC5B,MAAM,MAAM,MAAM,CAAA,GAAA,iIAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,YAAY,CAAA,GAAA,6HAAA,CAAA,UAAG,AAAD,EAAE,KAAK,cAAc,EAAE;IAC3C,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,iKAAA,CAAA,UAAc;YAAC,OAAO;;;;;;;;;;;AAG7B", "debugId": null}}]}