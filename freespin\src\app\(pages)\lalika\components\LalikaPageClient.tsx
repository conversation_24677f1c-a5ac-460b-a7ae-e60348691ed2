"use client";
import Banner from "@/app/shared/Banner";
import React from "react";
import Hero from "../../sexy/components/Hero";
import GamingScreenShot from "@/app/shared/GamingScreenShot";
import IrresistibleComponent from "@/app/shared/IrresistibleComponent";
// ;

const LalikaPageClient = () => {
  // 

  const obj = {
    t1: "สปินเข้าโชคลาภ: Lalika Betclub",
    text1:
      "Lalika Betclub บน Freespin168 นำเสนอประสบการณ์การเล่นเกมออนไลน์น่าตื่นเต้นและสมบูรณ์ เพลิดเพลินเกม ลเลอร์สด และโบนัส ้และเริ่มความร่ำรวย!",
    t2: "ทำไมต้อง Lalika Betclub",
    text2:
      "Lalika Betclub โดดเด่นในฐานะหมายปลายทางการเล่นเกมออนไลน์น้นนำด้วยเกมหลากหลาย อินเทอร์เฟซใช้งานง่าย และการค้าตลอด 24 ชั่วโมง เราให้ความปลอดภัยและความเป็นธรรม ทำให้เป็นมั่นใจในการเล่น",
    imageUrl: "/lalika/mbappe.png",
  };

  const irresistibleTextData = [
    {
      titleText: "ลเลอร์สดในเวลา",
      text: "เพลิดเพลินความตื่นเต้นของการเล่นลเลอร์สดในสภาพแวดล้อมสมบูรณ์ Lalika Betclub นำเสนอประสบการณ์สดทำให้สึกอยู่ใน",
    },
    {
      titleText: "เกมที่รองรับมือถือ",
      text: "เพลิดเพลินประสบการณ์การเล่นเกมราบรืennesบนหน้าจอทุกขนาด ไม่ว่าอยู่ไหน Lalika Betclub ออกแบบตอบสนองขนาดหน้าจอ ทำให้เล่นเกมโปรดได้เวลา",
    },
    {
      titleText: "เกมคลาสกหลากหลาย",
      text: "ตั้งแต่สล็อตน่าตื่นเต้นไปเกมต่าง ๆ Lalika Betclub มอบเกมเมียมหลากหลายด้วยการนำเสนอน่าสนใจ",
    },
    {
      titleText: "สภาพแวดล้อมการเล่นเกม",
      text: "สภาพแวดล้อมการเล่นเกมด้วยการง่งเน้นความธรรมอย่างแข็งแกร่งและความเมียมเล่น",
    },
  ];

  return (
    <div className="bg-black">
      <Banner
        secondTitleText="เกมส์"
        secondText="เกม Lalika Betclub"
        url="/banner2.png"
      />
      <div className="container mx-auto px-3 md:px-[4rem]">
        <Hero {...obj} />
        <GamingScreenShot
          header="ภาพหน้าจอ Lalika Betclub"
          imgList={["/lalika/sc1.png", "/lalika/sc2.png"]}
          className="gap-4 grid grid-cols-1 sm:grid-cols-2"
        />
        <IrresistibleComponent
          topHeading="5 เหตุผลที่ต้านทานไม่ได้ในการเล่น Lalika Betclub"
          src="/casino.png"
          altImage="lalika betclub"
          textData={irresistibleTextData}
        />
      </div>
    </div>
  );
};

export default LalikaPageClient;
