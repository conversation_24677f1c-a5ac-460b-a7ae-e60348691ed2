{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/dashboard/blogs/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { cookies } from \"next/headers\";\r\nimport { revalidateTag, revalidatePath } from \"next/cache\";\r\nimport connectDB from \"@/lib/mongodb\";\r\nimport Post from \"@/models/Post\";\r\nimport Category from \"@/models/Category\";\r\nimport { MinioService } from \"@/lib/minio\";\r\nimport jwt from \"jsonwebtoken\";\r\n\r\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key\";\r\n\r\nexport async function updatePostStatusAction(postId: string, status: string) {\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    let userInfo = null;\r\n\r\n    // Try both auth methods\r\n    if (authToken) {\r\n      try {\r\n        userInfo = jwt.verify(authToken, JWT_SECRET) as any;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Fallback to user-info cookie\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"Authentication required\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Find the post first\r\n    const post = await Post.findById(postId);\r\n\r\n    if (!post) {\r\n      return { success: false, error: \"Post not found\" };\r\n    }\r\n\r\n    // Update the status and save to trigger middleware\r\n    post.status = status as \"draft\" | \"published\" | \"archived\";\r\n\r\n    // Manually handle the published state logic\r\n    if (status === \"published\") {\r\n      post.isPublished = true;\r\n      if (!post.publishedAt) {\r\n        post.publishedAt = new Date();\r\n      }\r\n    } else {\r\n      post.isPublished = false;\r\n    }\r\n\r\n    const updatedPost = await post.save();\r\n\r\n    // Revalidate the public posts cache to show updated posts immediately\r\n    revalidateTag(\"public-posts\");\r\n\r\n    // Also revalidate the blogs page to show updated posts\r\n    revalidatePath(\"/en/blogs\");\r\n    revalidatePath(\"/th/blogs\");\r\n\r\n    // Serialize the post data for client components\r\n    const serializedPost = {\r\n      _id: updatedPost._id.toString(),\r\n      title: updatedPost.title,\r\n      status: updatedPost.status,\r\n      isPublished: updatedPost.isPublished,\r\n      publishedAt: updatedPost.publishedAt?.toISOString(),\r\n      createdAt: updatedPost.createdAt?.toISOString(),\r\n      updatedAt: updatedPost.updatedAt?.toISOString(),\r\n    };\r\n\r\n    return {\r\n      success: true,\r\n      data: { post: serializedPost },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error updating post status:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getCategoriesAction() {\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n\r\n    console.log(\"=== CATEGORIES ACTION DEBUG ===\");\r\n    console.log(\"All cookies:\", cookieStore.getAll());\r\n\r\n    // Try both auth methods: httpOnly token and user-info cookie\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    console.log(\"Auth token:\", authToken ? \"Found\" : \"Not found\");\r\n    console.log(\"User info cookie:\", userInfoCookie ? \"Found\" : \"Not found\");\r\n\r\n    let userInfo = null;\r\n\r\n    // Method 1: Try httpOnly token\r\n    if (authToken) {\r\n      try {\r\n        const decoded = jwt.verify(authToken, JWT_SECRET) as any;\r\n        console.log(\"Token verified successfully for user:\", decoded.userId);\r\n        userInfo = decoded;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Method 2: Try user-info cookie (fallback)\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        console.log(\"User info from cookie:\", parsedUserInfo);\r\n\r\n        // Verify user is admin\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n          console.log(\"Admin access granted via user-info cookie\");\r\n        } else {\r\n          return { success: false, error: \"Admin access required\" };\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"No valid authentication found\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Get all categories\r\n    const categories = await Category.find({ isActive: true })\r\n      .sort({ name: 1 })\r\n      .lean();\r\n\r\n    // Convert MongoDB documents to plain objects for client components\r\n    const serializedCategories = categories.map((category: any) => ({\r\n      _id: category._id.toString(), // Convert ObjectId to string\r\n      name: category.name,\r\n      description: category.description || \"\",\r\n      color: category.color || \"#6366f1\",\r\n      isActive: category.isActive,\r\n      createdAt: category.createdAt?.toISOString() || new Date().toISOString(),\r\n      updatedAt: category.updatedAt?.toISOString() || new Date().toISOString(),\r\n    }));\r\n\r\n    return {\r\n      success: true,\r\n      data: { categories: serializedCategories },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching categories:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createCategoryAction(data: {\r\n  name: string;\r\n  description: string;\r\n}) {\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    let userInfo = null;\r\n\r\n    // Try both auth methods\r\n    if (authToken) {\r\n      try {\r\n        userInfo = jwt.verify(authToken, JWT_SECRET) as any;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Fallback to user-info cookie\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"Authentication required\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Check if category already exists (case-insensitive)\r\n    const existingCategory = await Category.findOne({\r\n      name: { $regex: new RegExp(`^${data.name}$`, \"i\") },\r\n    });\r\n\r\n    if (existingCategory) {\r\n      return { success: false, error: \"Category already exists\" };\r\n    }\r\n\r\n    // Create new category\r\n    const category = new Category(data);\r\n    await category.save();\r\n\r\n    // Serialize the category for client components\r\n    const serializedCategory = {\r\n      _id: category._id.toString(),\r\n      name: category.name,\r\n      description: category.description || \"\",\r\n      color: category.color || \"#6366f1\",\r\n      isActive: category.isActive,\r\n      createdAt: category.createdAt?.toISOString() || new Date().toISOString(),\r\n      updatedAt: category.updatedAt?.toISOString() || new Date().toISOString(),\r\n    };\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Category created successfully\",\r\n      data: { category: serializedCategory },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error creating category:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createPostAction(\r\n  postData: any,\r\n  bannerImageBase64?: string\r\n) {\r\n  console.log(\"hello\");\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    let userInfo = null;\r\n\r\n    // Try both auth methods\r\n    if (authToken) {\r\n      try {\r\n        userInfo = jwt.verify(authToken, JWT_SECRET) as any;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Fallback to user-info cookie\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"Authentication required\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Handle banner image upload if provided\r\n    let bannerUrl = \"\";\r\n    if (bannerImageBase64) {\r\n      try {\r\n        // Extract content type and base64 data\r\n        const matches = bannerImageBase64.match(/^data:(.+);base64,(.+)$/);\r\n        if (!matches || matches.length !== 3) {\r\n          throw new Error(\"Invalid base64 image format\");\r\n        }\r\n\r\n        const contentType = matches[1]; // e.g., \"image/jpeg\", \"image/png\"\r\n        const base64Data = matches[2];\r\n        const imageBuffer = Buffer.from(base64Data, \"base64\");\r\n\r\n        // Generate filename with correct extension\r\n        const timestamp = Date.now();\r\n        const fileExtension = contentType.split(\"/\")[1] || \"jpg\";\r\n        const filename = `banner-${timestamp}.${fileExtension}`;\r\n\r\n        console.log(\"Uploading banner image:\", {\r\n          contentType,\r\n          filename,\r\n          bufferSize: imageBuffer.length,\r\n        });\r\n\r\n        // Upload to MinIO\r\n        bannerUrl = await MinioService.uploadFile(\r\n          imageBuffer,\r\n          filename,\r\n          contentType, // Use detected content type\r\n          \"banners\" // folder\r\n        );\r\n        console.log(\"Banner uploaded successfully:\", bannerUrl);\r\n      } catch (error) {\r\n        console.error(\"Error uploading banner:\", error);\r\n        // Continue without banner if upload fails\r\n      }\r\n    }\r\n\r\n    // Create the post\r\n    const postToCreate = {\r\n      ...postData,\r\n      banner: bannerUrl || postData.banner || \"\",\r\n      author: userInfo.userId || userInfo._id,\r\n      status: \"draft\", // Default to draft\r\n      isBlog: true,\r\n      isPublished: false,\r\n    };\r\n\r\n    const post = new Post(postToCreate);\r\n    await post.save();\r\n\r\n    // Serialize the post for client components\r\n    const serializedPost = {\r\n      _id: post._id.toString(),\r\n      title: post.title,\r\n      slug: post.slug,\r\n      excerpt: post.excerpt,\r\n      content: post.content,\r\n      banner: post.banner,\r\n      status: post.status,\r\n      isPublished: post.isPublished,\r\n      publishedAt: post.publishedAt?.toISOString(),\r\n      createdAt: post.createdAt?.toISOString(),\r\n      updatedAt: post.updatedAt?.toISOString(),\r\n    };\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Post created successfully\",\r\n      data: { post: serializedPost },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error creating post:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePostAction(\r\n  postId: string,\r\n  postData: any,\r\n  bannerImageBase64?: string\r\n) {\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    let userInfo = null;\r\n\r\n    // Try both auth methods\r\n    if (authToken) {\r\n      try {\r\n        userInfo = jwt.verify(authToken, JWT_SECRET) as any;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Fallback to user-info cookie\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"Authentication required\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Find the post\r\n    const post = await Post.findOne({ slug: postId });\r\n    if (!post) {\r\n      return { success: false, error: \"Post not found\" };\r\n    }\r\n\r\n    // Handle banner image upload if provided\r\n    let bannerUrl =\r\n      postData.banner?.image || post.banner?.image || post.banner || \"\";\r\n    if (bannerImageBase64) {\r\n      try {\r\n        // Extract content type and base64 data\r\n        const matches = bannerImageBase64.match(/^data:(.+);base64,(.+)$/);\r\n        if (!matches || matches.length !== 3) {\r\n          throw new Error(\"Invalid base64 image format\");\r\n        }\r\n\r\n        const contentType = matches[1];\r\n        const base64Data = matches[2];\r\n        const imageBuffer = Buffer.from(base64Data, \"base64\");\r\n\r\n        // Generate filename with correct extension\r\n        const timestamp = Date.now();\r\n        const fileExtension = contentType.split(\"/\")[1] || \"jpg\";\r\n        const filename = `banner-${timestamp}.${fileExtension}`;\r\n\r\n        console.log(\"Uploading banner image:\", {\r\n          contentType,\r\n          filename,\r\n          bufferSize: imageBuffer.length,\r\n        });\r\n\r\n        // Upload to MinIO\r\n        bannerUrl = await MinioService.uploadFile(\r\n          imageBuffer,\r\n          filename,\r\n          contentType,\r\n          \"banners\"\r\n        );\r\n        console.log(\"Banner uploaded successfully:\", bannerUrl);\r\n      } catch (error) {\r\n        console.error(\"Error uploading banner:\", error);\r\n        // Continue without banner if upload fails\r\n      }\r\n    }\r\n\r\n    // Clean the postData to avoid circular references\r\n    const cleanPostData = {\r\n      title: postData.title,\r\n      slug: postData.slug, // Include slug field for updates\r\n      content: postData.content,\r\n      categories: postData.categories,\r\n      tags: postData.tags,\r\n      status: postData.status,\r\n      isPublished: postData.isPublished,\r\n      publishedAt: postData.publishedAt,\r\n      metaTitle: postData.metaTitle,\r\n      metaDescription: postData.metaDescription,\r\n      metaKeywords: postData.metaKeywords,\r\n    };\r\n\r\n    const updateData = {\r\n      ...cleanPostData,\r\n      banner: bannerUrl, // Store as string, not object\r\n      seoTitle: postData.metaTitle,\r\n      seoDescription: postData.metaDescription,\r\n      metaKeywords: postData.metaKeywords,\r\n      author: userInfo.userId || userInfo._id,\r\n    };\r\n\r\n    // Update the post\r\n    const updatedPost = (await Post.findOneAndUpdate(\r\n      { slug: postId },\r\n      updateData,\r\n      {\r\n        new: true,\r\n        runValidators: true,\r\n      }\r\n    )\r\n      .populate(\"categories\", \"name description color\")\r\n      .lean()) as any; // Add .lean() to get plain objects\r\n\r\n    if (!updatedPost) {\r\n      return { success: false, error: \"Failed to update post\" };\r\n    }\r\n\r\n    // Serialize the post for client components (ensure no circular references)\r\n    const serializedPost = {\r\n      _id: updatedPost._id.toString(),\r\n      title: updatedPost.title,\r\n      slug: updatedPost.slug,\r\n      content: updatedPost.content,\r\n      banner: updatedPost.banner,\r\n      categories: Array.isArray(updatedPost.categories)\r\n        ? updatedPost.categories.map((cat: any) => ({\r\n            _id: cat._id?.toString(),\r\n            name: cat.name,\r\n            description: cat.description,\r\n            color: cat.color,\r\n          }))\r\n        : [],\r\n      tags: updatedPost.tags || [],\r\n      seoTitle: updatedPost.seoTitle,\r\n      seoDescription: updatedPost.seoDescription,\r\n      metaKeywords: updatedPost.metaKeywords,\r\n      status: updatedPost.status,\r\n      isPublished: updatedPost.isPublished,\r\n      publishedAt:\r\n        updatedPost.publishedAt?.toISOString?.() || updatedPost.publishedAt,\r\n      createdAt:\r\n        updatedPost.createdAt?.toISOString?.() || updatedPost.createdAt,\r\n      updatedAt:\r\n        updatedPost.updatedAt?.toISOString?.() || updatedPost.updatedAt,\r\n    };\r\n\r\n    // Revalidate caches\r\n    revalidateTag(\"public-posts\");\r\n    revalidatePath(\"/en/blogs\");\r\n    revalidatePath(\"/th/blogs\");\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Post updated successfully\",\r\n      data: { post: serializedPost },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error updating post:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAYsB,yBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/dashboard/blogs/components/BlogManagement.tsx"], "sourcesContent": ["\"use client\";\r\nimport { updatePostStatusAction } from \"../actions\";\r\nimport dayjs from \"dayjs\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardHeader } from \"@/components/ui/card\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport {\r\n  Edit,\r\n  Plus,\r\n  Search,\r\n  Trash2,\r\n  CheckCircle,\r\n  Clock,\r\n  Star,\r\n} from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useState } from \"react\";\r\nimport { toast } from \"react-toastify\";\r\nimport { get } from \"lodash\";\r\n\r\nexport interface BlogPost {\r\n  _id: string;\r\n  title: string;\r\n  categories: string[];\r\n  // categories: {\r\n  //   _id: string;\r\n  //   name: string;\r\n  //   description?: string;\r\n  // }[];\r\n\r\n  tags: string[];\r\n  createdAt?: Date | string | null;\r\n  status: \"published\" | \"draft\" | \"archived\";\r\n  slug?: string;\r\n  isTopNews?: boolean;\r\n}\r\n\r\nexport interface BlogManagementProps {\r\n  posts: BlogPost[];\r\n  onCreatePost?: () => void;\r\n  onPreviewPost?: (post: BlogPost) => void;\r\n  onEditPost?: (post: BlogPost) => void;\r\n  onDeletePost?: (post: BlogPost) => void;\r\n  onStatusChange?: (post: BlogPost, newStatus: BlogPost[\"status\"]) => void;\r\n  isLoading?: boolean;\r\n  className?: string;\r\n}\r\n\r\nconst statusFilters = [\r\n  { key: \"all\", label: \"All\" },\r\n  { key: \"published\", label: \"Published\" },\r\n  { key: \"draft\", label: \"Draft\" },\r\n  { key: \"archived\", label: \"Archived\" },\r\n] as const;\r\n\r\nconst statusColors = {\r\n  published: \"bg-green-100 text-green-800 hover:bg-green-200\",\r\n  draft: \"bg-yellow-100 text-yellow-800 hover:bg-yellow-200\",\r\n  archived: \"bg-gray-100 text-gray-800 hover:bg-gray-200\",\r\n} as const;\r\n\r\nexport default function BlogManagement({\r\n  posts,\r\n  isLoading = false,\r\n  className = \"\",\r\n}: BlogManagementProps) {\r\n  const router = useRouter();\r\n  const [activeFilter, setActiveFilter] = useState<string>(\"all\");\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n\r\n  const filteredPosts = posts.filter((post) => {\r\n    const matchesFilter =\r\n      activeFilter === \"all\" || post.status === activeFilter;\r\n    const matchesSearch =\r\n      searchQuery === \"\" ||\r\n      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n      post.categories.some((cat) =>\r\n        cat.toLowerCase().includes(searchQuery.toLowerCase())\r\n      ) ||\r\n      post.tags.some((tag) =>\r\n        tag.toLowerCase().includes(searchQuery.toLowerCase())\r\n      );\r\n\r\n    return matchesFilter && matchesSearch;\r\n  });\r\n\r\n  const formatDate = (date: Date | string | null | undefined) => {\r\n    if (!date) {\r\n      return \"Not published\";\r\n    }\r\n\r\n    try {\r\n      const dayjsDate = dayjs(date);\r\n      if (!dayjsDate.isValid()) {\r\n        return \"Invalid date\";\r\n      }\r\n\r\n      return dayjsDate.format(\"MMMM DD, YYYY\");\r\n    } catch (error) {\r\n      return \"Invalid date\";\r\n    }\r\n  };\r\n\r\n  const getStatusBadgeClass = (status: BlogPost[\"status\"]) => {\r\n    return `${statusColors[status]} border-0 font-medium`;\r\n  };\r\n\r\n  const deletePos = async (id: string) => {\r\n    try {\r\n      // Make direct API call with proper authentication\r\n      const response = await fetch(`/api/posts/${id}`, {\r\n        method: \"DELETE\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        credentials: \"include\", // Include cookies for authentication\r\n      });\r\n\r\n      const result = await response.json();\r\n\r\n      if (!response.ok || !result.success) {\r\n        toast.error(result.error || result.message || \"Failed to delete post\");\r\n        return;\r\n      }\r\n\r\n      toast.success(result.message || \"Post deleted successfully\");\r\n\r\n      // Refresh the page to update the list\r\n      window.location.reload();\r\n    } catch (error) {\r\n      console.error(\"Delete error:\", error);\r\n      toast.error(\"Failed to delete post\");\r\n    }\r\n  };\r\n\r\n  const togglePostStatus = async (post: BlogPost) => {\r\n    try {\r\n      console.log(\"Toggling post status for:\", post._id, \"from\", post.status);\r\n      const newStatus = post.status === \"published\" ? \"draft\" : \"published\";\r\n      console.log(\"New status will be:\", newStatus);\r\n\r\n      const res = await updatePostStatusAction(post._id, newStatus);\r\n      console.log(\"API response:\", res);\r\n\r\n      if (!res.success) {\r\n        toast.error(res.error || \"Failed to update post status\");\r\n        return;\r\n      }\r\n\r\n      toast.success(`Post ${newStatus} successfully`);\r\n\r\n      // Use Next.js router refresh instead of hard page reload\r\n      router.refresh();\r\n    } catch (error) {\r\n      console.error(\"Error toggling post status:\", error);\r\n      toast.error(\"Failed to update post status\");\r\n    }\r\n  };\r\n\r\n  const toggleTopNews = async (post: BlogPost) => {\r\n    try {\r\n      const response = await fetch(`/api/posts/${post._id}/toggle-top-news`, {\r\n        method: \"PATCH\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n      });\r\n\r\n      const result = await response.json();\r\n\r\n      if (!result.success) {\r\n        toast.error(result.message || \"Failed to toggle top news status\");\r\n        return;\r\n      }\r\n\r\n      toast.success(result.message);\r\n      router.refresh();\r\n    } catch (error) {\r\n      console.error(\"Error toggling top news:\", error);\r\n      toast.error(\"Failed to toggle top news status\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`space-y-6 ${className}`}>\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <h1 className=\"text-2xl font-bold text-gray-900\">Blog Management</h1>\r\n        <Button\r\n          onClick={() => router.push(\"/dashboard/blogs/add\")}\r\n          className=\"bg-black hover:bg-gray-800\"\r\n        >\r\n          <Plus className=\"h-4 w-4 mr-2\" />\r\n          Create Blog\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Filters and Search */}\r\n      <Card>\r\n        <CardHeader className=\"pb-4\">\r\n          <div className=\"flex items-center justify-between\">\r\n            {/* Status Filter Tabs */}\r\n            {/* <div className=\"flex items-center space-x-1\">\r\n              {statusFilters.map((filter) => (\r\n                <Button\r\n                  key={filter.key}\r\n                  variant={activeFilter === filter.key ? \"default\" : \"ghost\"}\r\n                  size=\"sm\"\r\n                  onClick={() => setActiveFilter(filter.key)}\r\n                  className={\r\n                    activeFilter === filter.key\r\n                      ? \"bg-gray-100 text-gray-900 hover:bg-gray-200\"\r\n                      : \"text-gray-600 hover:text-gray-900 hover:bg-gray-50\"\r\n                  }\r\n                >\r\n                  {filter.label}\r\n                </Button>\r\n              ))}\r\n            </div> */}\r\n\r\n            {/* Search */}\r\n            <div className=\"relative w-80\">\r\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\r\n              <Input\r\n                placeholder=\"Search...\"\r\n                value={searchQuery}\r\n                onChange={(e) => setSearchQuery(e.target.value)}\r\n                className=\"pl-10 bg-gray-50 border-gray-200 focus:bg-white\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </CardHeader>\r\n\r\n        <CardContent className=\"p-0\">\r\n          {/* Table */}\r\n          <div className=\"overflow-x-auto\">\r\n            <Table>\r\n              <TableHeader>\r\n                <TableRow className=\"border-gray-200\">\r\n                  <TableHead className=\"font-semibold text-gray-900\">\r\n                    Title\r\n                  </TableHead>\r\n                  <TableHead className=\"font-semibold text-gray-900\">\r\n                    Categories\r\n                  </TableHead>\r\n                  <TableHead className=\"font-semibold text-gray-900\">\r\n                    Tags\r\n                  </TableHead>\r\n                  <TableHead className=\"font-semibold text-gray-900\">\r\n                    Status\r\n                  </TableHead>\r\n                  <TableHead className=\"font-semibold text-gray-900\">\r\n                    Top News\r\n                  </TableHead>\r\n                  <TableHead className=\"font-semibold text-gray-900\">\r\n                    Published\r\n                  </TableHead>\r\n                  <TableHead className=\"font-semibold text-gray-900 text-right\">\r\n                    Actions\r\n                  </TableHead>\r\n                </TableRow>\r\n              </TableHeader>\r\n              <TableBody>\r\n                {isLoading ? (\r\n                  Array.from({ length: 3 }).map((_, index) => (\r\n                    <TableRow key={index}>\r\n                      <TableCell>\r\n                        <div className=\"h-4 bg-gray-200 rounded animate-pulse\"></div>\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        <div className=\"h-4 bg-gray-200 rounded animate-pulse w-16\"></div>\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        <div className=\"h-4 bg-gray-200 rounded animate-pulse w-16\"></div>\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        <div className=\"h-6 bg-gray-200 rounded animate-pulse w-16\"></div>\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        <div className=\"h-6 bg-gray-200 rounded animate-pulse w-16\"></div>\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        <div className=\"h-4 bg-gray-200 rounded animate-pulse w-20\"></div>\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        <div className=\"flex justify-end space-x-2\">\r\n                          <div className=\"h-8 w-8 bg-gray-200 rounded animate-pulse\"></div>\r\n                          <div className=\"h-8 w-8 bg-gray-200 rounded animate-pulse\"></div>\r\n                          <div className=\"h-8 w-8 bg-gray-200 rounded animate-pulse\"></div>\r\n                        </div>\r\n                      </TableCell>\r\n                    </TableRow>\r\n                  ))\r\n                ) : filteredPosts.length === 0 ? (\r\n                  <TableRow>\r\n                    <TableCell\r\n                      colSpan={8}\r\n                      className=\"text-center py-8 text-gray-500\"\r\n                    >\r\n                      {searchQuery\r\n                        ? \"No posts found matching your search.\"\r\n                        : \"No posts found.\"}\r\n                    </TableCell>\r\n                  </TableRow>\r\n                ) : (\r\n                  filteredPosts.map((post) => (\r\n                    <TableRow key={post._id} className=\"hover:bg-gray-50\">\r\n                      <TableCell className=\"font-medium max-w-xs\">\r\n                        <div className=\"truncate\" title={post.title}>\r\n                          {post.title}\r\n                        </div>\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        {post.categories.length > 0 ? (\r\n                          <div className=\"flex flex-wrap gap-1\">\r\n                            {post.categories\r\n                              .slice(0, 2)\r\n                              .map((category, index) => (\r\n                                <Badge\r\n                                  key={index}\r\n                                  variant=\"outline\"\r\n                                  className=\"text-xs\"\r\n                                >\r\n                                  {get(category, \"name\", \"N/A\")}\r\n                                </Badge>\r\n                              ))}\r\n                            {post.categories.length > 2 && (\r\n                              <Badge variant=\"outline\" className=\"text-xs\">\r\n                                +{post.categories.length - 2}\r\n                              </Badge>\r\n                            )}\r\n                          </div>\r\n                        ) : (\r\n                          <span className=\"text-gray-400\">-</span>\r\n                        )}\r\n                      </TableCell>\r\n\r\n                      <TableCell>\r\n                        {post.tags.length > 0 ? (\r\n                          <div className=\"flex flex-wrap gap-1\">\r\n                            {post.tags.slice(0, 2).map((tag, index) => (\r\n                              <Badge\r\n                                key={index}\r\n                                variant=\"secondary\"\r\n                                className=\"text-xs\"\r\n                              >\r\n                                {tag}\r\n                              </Badge>\r\n                            ))}\r\n                            {post.tags.length > 2 && (\r\n                              <Badge variant=\"secondary\" className=\"text-xs\">\r\n                                +{post.tags.length - 2}\r\n                              </Badge>\r\n                            )}\r\n                          </div>\r\n                        ) : (\r\n                          <span className=\"text-gray-400\">-</span>\r\n                        )}\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        <Badge className={getStatusBadgeClass(post.status)}>\r\n                          {post.status.charAt(0).toUpperCase() +\r\n                            post.status.slice(1)}\r\n                        </Badge>\r\n                      </TableCell>\r\n                      <TableCell>\r\n                        <div className=\"flex items-center\">\r\n                          <Star\r\n                            className={`h-4 w-4 ${\r\n                              post.isTopNews\r\n                                ? \"text-yellow-500 fill-yellow-500\"\r\n                                : \"text-gray-300\"\r\n                            }`}\r\n                          />\r\n                          <span className=\"ml-2 text-sm text-gray-600\">\r\n                            {post.isTopNews ? \"Yes\" : \"No\"}\r\n                          </span>\r\n                        </div>\r\n                      </TableCell>\r\n                      <TableCell className=\"text-gray-600\">\r\n                        {formatDate(post.createdAt)}\r\n                      </TableCell>\r\n\r\n                      <TableCell>\r\n                        <div className=\"flex items-center justify-end space-x-2\">\r\n                          {/* Top News Toggle Button */}\r\n                          <Button\r\n                            type=\"button\"\r\n                            variant=\"outline\"\r\n                            size=\"sm\"\r\n                            onClick={(e) => {\r\n                              e.preventDefault();\r\n                              e.stopPropagation();\r\n                              toggleTopNews(post);\r\n                            }}\r\n                            className={`h-8 px-3 ${\r\n                              post.isTopNews\r\n                                ? \"text-yellow-600 hover:text-yellow-700 hover:bg-yellow-50\"\r\n                                : \"text-gray-600 hover:text-gray-700 hover:bg-gray-50\"\r\n                            }`}\r\n                            title={\r\n                              post.isTopNews\r\n                                ? \"Remove from Top News\"\r\n                                : \"Mark as Top News\"\r\n                            }\r\n                          >\r\n                            <Star\r\n                              className={`h-4 w-4 mr-1 ${\r\n                                post.isTopNews ? \"fill-current\" : \"\"\r\n                              }`}\r\n                            />\r\n                            {post.isTopNews ? \"Remove\" : \"Top News\"}\r\n                          </Button>\r\n\r\n                          {/* Publish/Unpublish Button */}\r\n                          <Button\r\n                            type=\"button\"\r\n                            variant=\"outline\"\r\n                            size=\"sm\"\r\n                            onClick={(e) => {\r\n                              e.preventDefault();\r\n                              e.stopPropagation();\r\n                              togglePostStatus(post);\r\n                            }}\r\n                            className={`h-8 px-3 ${\r\n                              post.status === \"published\"\r\n                                ? \"text-orange-600 hover:text-orange-700 hover:bg-orange-50\"\r\n                                : \"text-green-600 hover:text-green-700 hover:bg-green-50\"\r\n                            }`}\r\n                          >\r\n                            {post.status === \"published\" ? (\r\n                              <>\r\n                                <Clock className=\"h-4 w-4 mr-1\" />\r\n                                Unpublish\r\n                              </>\r\n                            ) : (\r\n                              <>\r\n                                <CheckCircle className=\"h-4 w-4 mr-1\" />\r\n                                Publish\r\n                              </>\r\n                            )}\r\n                          </Button>\r\n                          {/* Edit Button */}\r\n                          <Button\r\n                            type=\"button\"\r\n                            variant=\"outline\"\r\n                            size=\"sm\"\r\n                            onClick={() =>\r\n                              router.push(`/dashboard/blogs/edit/${post.slug}`)\r\n                            }\r\n                            className=\"h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50\"\r\n                          >\r\n                            <Edit className=\"h-4 w-4\" />\r\n                          </Button>\r\n\r\n                          {/* Delete Button */}\r\n                          <Button\r\n                            type=\"button\"\r\n                            variant=\"outline\"\r\n                            size=\"sm\"\r\n                            onClick={(e) => {\r\n                              e.preventDefault();\r\n                              e.stopPropagation();\r\n                              deletePos(post._id);\r\n                            }}\r\n                            className=\"h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n                          >\r\n                            <Trash2 className=\"h-4 w-4\" />\r\n                          </Button>\r\n                        </div>\r\n                      </TableCell>\r\n                    </TableRow>\r\n                  ))\r\n                )}\r\n              </TableBody>\r\n            </Table>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AACA;;;AA3BA;;;;;;;;;;;;;AAyDA,MAAM,gBAAgB;IACpB;QAAE,KAAK;QAAO,OAAO;IAAM;IAC3B;QAAE,KAAK;QAAa,OAAO;IAAY;IACvC;QAAE,KAAK;QAAS,OAAO;IAAQ;IAC/B;QAAE,KAAK;QAAY,OAAO;IAAW;CACtC;AAED,MAAM,eAAe;IACnB,WAAW;IACX,OAAO;IACP,UAAU;AACZ;AAEe,SAAS,eAAe,EACrC,KAAK,EACL,YAAY,KAAK,EACjB,YAAY,EAAE,EACM;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAC;QAClC,MAAM,gBACJ,iBAAiB,SAAS,KAAK,MAAM,KAAK;QAC5C,MAAM,gBACJ,gBAAgB,MAChB,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACzD,KAAK,UAAU,CAAC,IAAI,CAAC,CAAC,MACpB,IAAI,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,QAEpD,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,MACd,IAAI,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAGtD,OAAO,iBAAiB;IAC1B;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QAEA,IAAI;YACF,MAAM,YAAY,CAAA,GAAA,wIAAA,CAAA,UAAK,AAAD,EAAE;YACxB,IAAI,CAAC,UAAU,OAAO,IAAI;gBACxB,OAAO;YACT;YAEA,OAAO,UAAU,MAAM,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,qBAAqB,CAAC;IACvD;IAEA,MAAM,YAAY,OAAO;QACvB,IAAI;YACF,kDAAkD;YAClD,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,IAAI,EAAE;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;YACf;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnC,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI,OAAO,OAAO,IAAI;gBAC9C;YACF;YAEA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO,IAAI;YAEhC,sCAAsC;YACtC,OAAO,QAAQ,CAAC,MAAM;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,QAAQ,GAAG,CAAC,6BAA6B,KAAK,GAAG,EAAE,QAAQ,KAAK,MAAM;YACtE,MAAM,YAAY,KAAK,MAAM,KAAK,cAAc,UAAU;YAC1D,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,MAAM,MAAM,MAAM,CAAA,GAAA,2KAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,GAAG,EAAE;YACnD,QAAQ,GAAG,CAAC,iBAAiB;YAE7B,IAAI,CAAC,IAAI,OAAO,EAAE;gBAChB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI;gBACzB;YACF;YAEA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,UAAU,aAAa,CAAC;YAE9C,yDAAyD;YACzD,OAAO,OAAO;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,gBAAgB,CAAC,EAAE;gBACrE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,OAAO,IAAI;gBAC9B;YACF;YAEA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,OAAO,OAAO;YAC5B,OAAO,OAAO;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC;4BAAI,WAAU;sCAqBb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAMlB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCAErB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;kDACJ,6LAAC,oIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;8DAClB,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAA8B;;;;;;8DAGnD,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAA8B;;;;;;8DAGnD,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAA8B;;;;;;8DAGnD,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAA8B;;;;;;8DAGnD,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAA8B;;;;;;8DAGnD,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAA8B;;;;;;8DAGnD,6LAAC,oIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAyC;;;;;;;;;;;;;;;;;kDAKlE,6LAAC,oIAAA,CAAA,YAAS;kDACP,YACC,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,6LAAC,oIAAA,CAAA,WAAQ;;kEACP,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;;;;;;;;;;kEAEjB,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;;;;;;;;;;kEAEjB,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;;;;;;;;;;kEAEjB,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;;;;;;;;;;kEAEjB,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;;;;;;;;;;kEAEjB,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;;;;;;;;;;kEAEjB,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;+CAvBN;;;;wDA4Bf,cAAc,MAAM,KAAK,kBAC3B,6LAAC,oIAAA,CAAA,WAAQ;sDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;gDACR,SAAS;gDACT,WAAU;0DAET,cACG,yCACA;;;;;;;;;;mDAIR,cAAc,GAAG,CAAC,CAAC,qBACjB,6LAAC,oIAAA,CAAA,WAAQ;gDAAgB,WAAU;;kEACjC,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,6LAAC;4DAAI,WAAU;4DAAW,OAAO,KAAK,KAAK;sEACxC,KAAK,KAAK;;;;;;;;;;;kEAGf,6LAAC,oIAAA,CAAA,YAAS;kEACP,KAAK,UAAU,CAAC,MAAM,GAAG,kBACxB,6LAAC;4DAAI,WAAU;;gEACZ,KAAK,UAAU,CACb,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,UAAU,sBACd,6LAAC,oIAAA,CAAA,QAAK;wEAEJ,SAAQ;wEACR,WAAU;kFAET,CAAA,GAAA,gIAAA,CAAA,UAAG,AAAD,EAAE,UAAU,QAAQ;uEAJlB;;;;;gEAOV,KAAK,UAAU,CAAC,MAAM,GAAG,mBACxB,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;;wEAAU;wEACzC,KAAK,UAAU,CAAC,MAAM,GAAG;;;;;;;;;;;;iFAKjC,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;kEAIpC,6LAAC,oIAAA,CAAA,YAAS;kEACP,KAAK,IAAI,CAAC,MAAM,GAAG,kBAClB,6LAAC;4DAAI,WAAU;;gEACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,6LAAC,oIAAA,CAAA,QAAK;wEAEJ,SAAQ;wEACR,WAAU;kFAET;uEAJI;;;;;gEAOR,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;;wEAAU;wEAC3C,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;iFAK3B,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;kEAGpC,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DAAC,WAAW,oBAAoB,KAAK,MAAM;sEAC9C,KAAK,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAChC,KAAK,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;kEAGxB,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEACH,WAAW,CAAC,QAAQ,EAClB,KAAK,SAAS,GACV,oCACA,iBACJ;;;;;;8EAEJ,6LAAC;oEAAK,WAAU;8EACb,KAAK,SAAS,GAAG,QAAQ;;;;;;;;;;;;;;;;;kEAIhC,6LAAC,oIAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,WAAW,KAAK,SAAS;;;;;;kEAG5B,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;8EAEb,6LAAC,qIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,CAAC;wEACR,EAAE,cAAc;wEAChB,EAAE,eAAe;wEACjB,cAAc;oEAChB;oEACA,WAAW,CAAC,SAAS,EACnB,KAAK,SAAS,GACV,6DACA,sDACJ;oEACF,OACE,KAAK,SAAS,GACV,yBACA;;sFAGN,6LAAC,qMAAA,CAAA,OAAI;4EACH,WAAW,CAAC,aAAa,EACvB,KAAK,SAAS,GAAG,iBAAiB,IAClC;;;;;;wEAEH,KAAK,SAAS,GAAG,WAAW;;;;;;;8EAI/B,6LAAC,qIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,CAAC;wEACR,EAAE,cAAc;wEAChB,EAAE,eAAe;wEACjB,iBAAiB;oEACnB;oEACA,WAAW,CAAC,SAAS,EACnB,KAAK,MAAM,KAAK,cACZ,6DACA,yDACJ;8EAED,KAAK,MAAM,KAAK,4BACf;;0FACE,6LAAC,uMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAAiB;;qGAIpC;;0FACE,6LAAC,8NAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;8EAM9C,6LAAC,qIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IACP,OAAO,IAAI,CAAC,CAAC,sBAAsB,EAAE,KAAK,IAAI,EAAE;oEAElD,WAAU;8EAEV,cAAA,6LAAC,8MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAIlB,6LAAC,qIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,CAAC;wEACR,EAAE,cAAc;wEAChB,EAAE,eAAe;wEACjB,UAAU,KAAK,GAAG;oEACpB;oEACA,WAAU;8EAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CAjKX,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+K3C;GApawB;;QAKP,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}]}