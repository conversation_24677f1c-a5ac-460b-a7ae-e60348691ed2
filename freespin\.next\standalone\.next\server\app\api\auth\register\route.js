(()=>{var e={};e.id=1612,e.ids=[1612],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17063:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(56037),a=t.n(s),n=t(85663);let i=new s.Schema({username:{type:String,required:[!0,"Username is required"],unique:!0,trim:!0,minlength:[3,"Username must be at least 3 characters"],maxlength:[30,"Username cannot exceed 30 characters"]},email:{type:String,required:[!0,"Email is required"],unique:!0,lowercase:!0,trim:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Please enter a valid email"]},password:{type:String,required:[!0,"Password is required"],minlength:[6,"Password must be at least 6 characters"],select:!1},firstName:{type:String,trim:!0,maxlength:[50,"First name cannot exceed 50 characters"]},lastName:{type:String,trim:!0,maxlength:[50,"Last name cannot exceed 50 characters"]},bio:{type:String,maxlength:[500,"Bio cannot exceed 500 characters"]},avatar:{type:String,default:null},role:{type:String,enum:["user","admin","moderator"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1}},{timestamps:!0});i.pre("save",async function(e){if(!this.isModified("password"))return e();try{let r=await n.Ay.genSalt(12);this.password=await n.Ay.hash(this.password,r),e()}catch(r){e(r)}}),i.methods.comparePassword=async function(e){return n.Ay.compare(e,this.password)};let o=a().models.User||a().model("User",i)},24144:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{POST:()=>d});var a=t(96559),n=t(48088),i=t(37719),o=t(32190),u=t(45697),l=t(75745),c=t(17063);let m=u.z.object({username:u.z.string().min(3).max(30),email:u.z.string().email(),password:u.z.string().min(6),firstName:u.z.string().optional(),lastName:u.z.string().optional(),role:u.z.string().optional()});async function d(e){try{await (0,l.A)();let r=await e.json(),t=m.parse(r),s=await c.A.findOne({$or:[{email:t.email},{username:t.username}]});if(s)return o.NextResponse.json({success:!1,error:s.email===t.email?"Email already registered":"Username already taken"},{status:400});let a=new c.A(t);await a.save();let n={id:a._id,username:a.username,email:a.email,firstName:a.firstName,lastName:a.lastName,role:a.role,createdAt:a.createdAt};return o.NextResponse.json({success:!0,message:"User registered successfully",data:{user:n}},{status:201})}catch(e){if(e instanceof u.z.ZodError)return o.NextResponse.json({success:!1,error:"Validation failed",details:e.errors},{status:400});return console.error("Registration error:",e),o.NextResponse.json({success:!1,error:"Registration failed"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\api\\auth\\register\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:f}=p;function w(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75745:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(56037),a=t.n(s);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let i=global?.mongoose;i||(i=global.mongoose={conn:null,promise:null});let o=async function(){if(i.conn)return i.conn;i.promise||(i.promise=a().connect(n,{bufferCommands:!1}).then(e=>e));try{i.conn=await i.promise}catch(e){throw i.promise=null,e}return i.conn}},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,5697,5663],()=>t(24144));module.exports=s})();