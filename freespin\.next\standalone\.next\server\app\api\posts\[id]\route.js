(()=>{var e={};e.id=619,e.ids=[619],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,s)=>{"use strict";s.d(t,{_:()=>n});var r=s(43205),o=s.n(r);let i=process.env.JWT_SECRET||"your-secret-key";async function n(e){try{console.log("=== AUTH DEBUG ==="),console.log("All cookies:",e.cookies.getAll()),console.log("Authorization header:",e.headers.get("authorization")),console.log("Cookie header:",e.headers.get("cookie"));let t=e.headers.get("authorization"),s=e.cookies.get("auth-token")?.value,r=e.cookies.get("session-token")?.value;console.log("Auth header token:",t?.replace("Bearer ","")),console.log("Auth token cookie:",s),console.log("Session token cookie:",r);let n=t?.replace("Bearer ","")||s||r;if(console.log("Final token:",n?"Found":"Not found"),console.log("=================="),!n)return{success:!1,error:"No authentication token provided"};let a=o().verify(n,i);return{success:!0,user:a}}catch(e){if(e instanceof o().JsonWebTokenError)return{success:!1,error:"Invalid authentication token"};return{success:!1,error:"Authentication failed"}}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55530:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(56037),o=s.n(r);let i=new r.Schema({title:{type:String,required:[!0,"Post title is required"],trim:!0,maxlength:[1e3,"Title cannot exceed 200 characters"]},slug:{type:String,trim:!0,lowercase:!0},canonicalUrl:{type:String,trim:!0,validate:{validator:e=>!e||/^https?:\/\/.+/.test(e),message:"Canonical URL must be a valid URL"}},existingUrl:{type:Boolean,default:!1},content:{type:String,required:[!0,"Post content is required"]},excerpt:{type:String,trim:!0,maxlength:[1e3,"Excerpt cannot exceed 300 characters"]},description:{type:String},author:{type:String,trim:!0},isBlog:{type:Boolean,default:!0},categories:[{type:o().Schema.Types.ObjectId,ref:"Category"}],tags:[{type:String,trim:!0,lowercase:!0}],metaTitle:{type:String,required:[!0,"Meta title is required"],maxlength:[1e3,"Meta title cannot exceed 60 characters"]},metaDescription:{type:String,required:[!0,"Meta description is required"],maxlength:[1e3,"Meta description cannot exceed 160 characters"]},metaKeywords:{type:String,required:[!0,"Meta keywords are required"],maxlength:[1e3,"Meta keywords cannot exceed 200 characters"]},banner:{type:String,required:[!0,"Banner image is required"],trim:!0},status:{type:String,enum:["draft","published","archived"],default:"draft"},isPublished:{type:Boolean,default:!1},publishedAt:{type:Date,default:null},views:{type:Number,default:0},readTime:{type:Number,default:1},isTopNews:{type:Boolean,default:!1}},{timestamps:!0});i.pre("save",function(e){if(this.isModified("title")&&!this.slug&&(this.slug=this.title.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,"")),this.isModified("content")){let e=(this.content||"").split(/\s+/).length;this.readTime=Math.ceil(e/200)}this.isModified("status")&&"published"===this.status&&!this.publishedAt&&(this.publishedAt=new Date,this.isPublished=!0),e()}),i.index({categories:1}),i.index({tags:1}),i.index({status:1}),i.index({isPublished:1}),i.index({publishedAt:-1}),i.index({"banner.title":1}),i.index({title:"text",content:"text",metaTitle:"text",metaDescription:"text"});let n=o().models.Post||o().model("Post",i)},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66663:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>k,routeModule:()=>y,serverHooks:()=>A,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>w});var r={};s.r(r),s.d(r,{DELETE:()=>x,GET:()=>m,PUT:()=>f,dynamic:()=>p,revalidate:()=>g});var o=s(96559),i=s(48088),n=s(37719),a=s(32190),c=s(45697),u=s(75745),l=s(55530),d=s(12909);let p="force-dynamic",g=0,h=c.z.object({title:c.z.string().min(1).max(200).optional(),content:c.z.string().min(1).optional(),excerpt:c.z.string().max(300).optional(),category:c.z.string().optional(),tags:c.z.array(c.z.string()).optional(),status:c.z.enum(["draft","published","archived"]).optional(),featuredImage:c.z.string().url().optional(),seoTitle:c.z.string().max(60).optional(),seoDescription:c.z.string().max(160).optional()});async function m(e,{params:t}){try{await (0,u.A)();let{id:e}=await t;console.log("id",e);let s=await l.A.findOne({slug:e}).populate("categories","name isActive color description").lean();if(!s)return a.NextResponse.json({success:!1,error:"Post not found"},{status:404});return await l.A.findOneAndUpdate({slug:e},{$inc:{views:1}}),a.NextResponse.json({success:!0,data:{post:s}})}catch(e){return console.error("Error fetching post:",e),a.NextResponse.json({success:!1,error:"Failed to fetch post"},{status:500})}}async function f(e,{params:t}){try{await (0,u.A)();let{id:s}=await t,r=await (0,d._)(e);if(!r.success)return a.NextResponse.json({success:!1,error:r.error},{status:401});let o=await e.json(),i=h.parse(o);if(!await l.A.findById(s))return a.NextResponse.json({success:!1,error:"Post not found"},{status:404});let n=await l.A.findByIdAndUpdate(s,i,{new:!0,runValidators:!0}).populate("author","username firstName lastName avatar").populate("category","name slug color");return a.NextResponse.json({success:!0,message:"Post updated successfully",data:{post:n}})}catch(e){if(e instanceof c.z.ZodError)return a.NextResponse.json({success:!1,error:"Validation failed",details:e.errors},{status:400});return console.error("Error updating post:",e),a.NextResponse.json({success:!1,error:"Failed to update post"},{status:500})}}async function x(e,{params:t}){try{await (0,u.A)();let{id:s}=await t,r=await (0,d._)(e);if(!r.success)return a.NextResponse.json({success:!1,error:r.error},{status:401});if(!await l.A.findById(s))return a.NextResponse.json({success:!1,error:"Post not found"},{status:404});return await l.A.findByIdAndDelete(s),a.NextResponse.json({success:!0,message:"Post deleted successfully"})}catch(e){return console.error("Error deleting post:",e),a.NextResponse.json({success:!1,error:"Failed to delete post"},{status:500})}}let y=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/posts/[id]/route",pathname:"/api/posts/[id]",filename:"route",bundlePath:"app/api/posts/[id]/route"},resolvedPagePath:"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\api\\posts\\[id]\\route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:v,workUnitAsyncStorage:w,serverHooks:A}=y;function k(){return(0,n.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:w})}},75745:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(56037),o=s.n(r);let i=process.env.MONGODB_URI;if(!i)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let n=global?.mongoose;n||(n=global.mongoose={conn:null,promise:null});let a=async function(){if(n.conn)return n.conn;n.promise||(n.promise=o().connect(i,{bufferCommands:!1}).then(e=>e));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,3205,5697],()=>s(66663));module.exports=r})();