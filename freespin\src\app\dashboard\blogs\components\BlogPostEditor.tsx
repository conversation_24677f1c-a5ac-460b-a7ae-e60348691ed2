// "use client";

// import {
//   cleanObject,
//   convertToBase64,
// } from "@/app/shared/cleanObject";
// import { getCategoriesAction, createPostAction } from "../actions";
// import { <PERSON>ton } from "@/components/ui/button";
// import { Eye, ImagePlus, Info, Loader2, Plus } from "lucide-react";
// import type React from "react";
// import { useState, useEffect } from "react";
// import { Controller, useForm } from "react-hook-form";
// import { useRouter } from "next/navigation";
// import { toast } from "react-toastify";
// import { z } from "zod";
// import WysiwygEditor from "./WysiwygEditor";

// // Form schema matching the API
// const formSchema = z.object({
//   title: z.string().min(1, "Title is required"),
//   slug: z.string().optional(),
//   // canonicalUrl: z
//   //   .string()
//   //   .url("Must be a valid URL")
//   //   .optional()
//   //   .or(z.literal("")),
//   // existingUrl: z.boolean().default(false),
//   content: z.string().min(1, "Content is required"),
//   isBlog: z.boolean().default(true),
//   categories: z.array(z.string()).default([]),
//   tags: z.array(z.string()).default([]),
//   metaTitle: z.string().min(1, "Meta title is required"),

//   metaDescription: z.string().min(1, "Meta description is required"),

//   metaKeywords: z.string().min(1, "Meta keywords are required"),

//   banner: z.object({
//     title: z.string().min(1, "Banner title is required"),

//     description: z.string().min(1, "Banner description is required"),

//     image: z.string().min(1, "Banner image is required"),
//     altText: z.string().min(1, "Alt text is required"),
//   }),
// });

// type FormData = z.infer<typeof formSchema>;

// // Field configuration for dynamic rendering
// const fieldConfigs = {
//   blogPost: [
//     {
//       name: "title" as const,
//       label: "Title",
//       type: "text",
//       placeholder: "Enter the title of the blog post",
//       tooltip: true,
//       required: true,
//     },
//     {
//       name: "slug" as const,
//       label: "Slug (Optional)",
//       type: "text",
//       placeholder: "Enter a unique slug (auto-generated if empty)",
//       tooltip: true,
//       required: false,
//     },
//   ],
//   seo: [
//     {
//       name: "metaTitle" as const,
//       label: "Meta Title",
//       type: "text",
//       placeholder: "Enter meta title ",
//       tooltip: true,
//       required: true,
//     },
//     {
//       name: "metaDescription" as const,
//       label: "Meta Description",
//       type: "textarea",
//       placeholder: "Enter meta description",
//       rows: 3,
//       tooltip: true,
//       required: true,
//     },
//     {
//       name: "metaKeywords" as const,
//       label: "Meta Keywords (comma-separated)",
//       type: "text",
//       placeholder: "Enter meta keywords ",
//       tooltip: true,
//       required: true,
//     },
//   ],
//   banner: [
//     {
//       name: "title" as const,
//       label: "Banner Title",
//       type: "text",
//       placeholder: "Enter banner title",
//       tooltip: true,
//       required: true,
//     },
//     {
//       name: "description" as const,
//       label: "Banner Description",
//       type: "textarea",
//       placeholder: "Enter banner description",
//       rows: 4,
//       tooltip: true,
//       required: true,
//     },
//     {
//       name: "altText" as const,
//       label: "Banner Image Alt Text",
//       type: "text",
//       placeholder: "Enter banner image alt text",
//       tooltip: true,
//       required: true,
//     },
//   ],
// };

// const InfoIcon = ({ className }: { className?: string }) => (
//   <Info className={`w-4 h-4 text-gray-400 ${className}`} />
// );

// const FormField = ({
//   label,
//   error,
//   children,
//   required = false,
//   tooltip = false,
// }: {
//   label: string;
//   error?: string;
//   children: React.ReactNode;
//   required?: boolean;
//   tooltip?: boolean;
// }) => (
//   <div className="mb-4">
//     <label className="block text-sm font-medium text-gray-700 mb-2">
//       {label}
//       {required && <span className="text-red-500 ml-1">*</span>}
//       {tooltip && <InfoIcon className="inline ml-1" />}
//     </label>
//     {children}
//     {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
//   </div>
// );

// const DynamicField = ({
//   config,
//   register,
//   error,
// }: {
//   config: any;
//   register: any;
//   error?: string;
// }) => {
//   return (
//     <FormField
//       label={config.label}
//       error={error}
//       tooltip={config.tooltip}
//       required={config.required}
//     >
//       {config.type === "textarea" ? (
//         <textarea
//           {...register(config.name)}
//           placeholder={config.placeholder}
//           // placeholder="Enter meta description"
//           rows={config.rows || 3}
//           className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
//         />
//       ) : (
//         <input
//           {...register(config.name)}
//           type={config.type}
//           placeholder={config.placeholder}
//           className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
//         />
//       )}
//     </FormField>
//   );
// };

// // Multi-select component for categories and tags

// export interface MultiSelectOption {
//   label: string;
//   value: string;
// }

// export interface MultiSelectProps {
//   label: string;
//   placeholder: string;
//   options: MultiSelectOption[]; // Accepts array of objects
//   selectedValues: string[]; // Still stores only the 'value'
//   onChange: (values: string[]) => void;
//   onAddNew?: () => void;
//   allowCustom?: boolean;
// }

// export const MultiSelect = ({
//   label,
//   placeholder,
//   options,
//   selectedValues,
//   onChange,
//   onAddNew,
//   allowCustom = true,
// }: MultiSelectProps) => {
//   const [isOpen, setIsOpen] = useState(false);
//   const [searchTerm, setSearchTerm] = useState("");

//   // Filter options by label
//   const filteredOptions = options.filter(
//     (option) =>
//       option.label.toLowerCase().includes(searchTerm.toLowerCase()) &&
//       !selectedValues.includes(option.value)
//   );

//   const handleSelect = (option: MultiSelectOption) => {
//     if (!selectedValues.includes(option.value)) {
//       onChange([...selectedValues, option.value]);
//     }
//     setSearchTerm("");
//     setIsOpen(false);
//   };

//   const handleRemove = (value: string) => {
//     onChange(selectedValues.filter((item) => item !== value));
//   };

//   const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
//     if (e.key === "Enter" && searchTerm.trim() && allowCustom) {
//       e.preventDefault();
//       const newOption = searchTerm.trim();
//       if (!selectedValues.includes(newOption)) {
//         onChange([...selectedValues, newOption]);
//         setSearchTerm("");
//         setIsOpen(false);
//       }
//     }
//   };

//   // Helper to get label by value
//   const getLabelByValue = (value: string) => {
//     return options.find((option) => option.value === value)?.label || value;
//   };

//   return (
//     <div className="relative">
//       <div className="flex items-center justify-between mb-3">
//         <h3 className="font-semibold">{label}</h3>
//         {onAddNew && (
//           <button
//             type="button"
//             onClick={onAddNew}
//             className="bg-black text-white px-3 py-1 rounded text-sm flex items-center gap-1"
//           >
//             <Plus className="w-3 h-3" />
//             Add New
//           </button>
//         )}
//       </div>

//       {/* Selected items */}
//       {selectedValues.length > 0 && (
//         <div className="flex flex-wrap gap-2 mb-3">
//           {selectedValues.map((value, index) => (
//             <span
//               key={index}
//               className="bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm flex items-center gap-1"
//             >
//               {getLabelByValue(value)}
//               <button
//                 type="button"
//                 onClick={() => handleRemove(value)}
//                 className="text-blue-600 hover:text-blue-800 ml-1"
//               >
//                 ×
//               </button>
//             </span>
//           ))}
//         </div>
//       )}

//       {/* Search input */}
//       <div className="relative">
//         <input
//           type="text"
//           placeholder={placeholder}
//           value={searchTerm}
//           onChange={(e) => {
//             setSearchTerm(e.target.value);
//             setIsOpen(true);
//           }}
//           onFocus={() => setIsOpen(true)}
//           onKeyDown={handleKeyDown}
//           className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
//         />

//         {/* Dropdown */}
//         {isOpen &&
//           (filteredOptions.length > 0 ||
//             (allowCustom && searchTerm.trim())) && (
//             <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto">
//               {filteredOptions.map((option, index) => (
//                 <div
//                   key={index}
//                   onClick={() => handleSelect(option)}
//                   className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm"
//                 >
//                   {option.label}
//                 </div>
//               ))}
//               {allowCustom && searchTerm.trim() && (
//                 <div
//                   onClick={() =>
//                     handleSelect({
//                       label: searchTerm.trim(),
//                       value: searchTerm.trim(),
//                     })
//                   }
//                   className="px-3 py-2 hover:bg-green-50 cursor-pointer text-sm border-t border-gray-200 text-green-700"
//                 >
//                   Add &quot;{searchTerm.trim()}&quot;
//                 </div>
//               )}
//             </div>
//           )}
//       </div>

//       {/* Click outside to close */}
//       {isOpen && (
//         <div className="fixed inset-0 z-5" onClick={() => setIsOpen(false)} />
//       )}
//     </div>
//   );
// };

// // Original DynamicContentManager (preserved for backward compatibility)
// export default function DynamicContentManager() {
//   const router = useRouter();
//   const [categories, setCategories] = useState<string[]>([]);
//   const [tags, setTags] = useState<string[]>([]);
//   const [availableCategories, setAvailableCategories] = useState<
//     MultiSelectOption[]
//   >([]);
//   const [isSubmitting, setIsSubmitting] = useState(false);
//   const [bannerImageFile, setBannerImageFile] = useState<File | null>(null);

//   const {
//     control,
//     register,
//     handleSubmit,
//     watch,
//     setValue,
//     formState: { errors },
//   } = useForm<FormData>({
//     defaultValues: {
//       isBlog: true,
//       // existingUrl: false,
//       categories: [],
//       tags: [],
//       content: "",
//       banner: {
//         title: "",
//         description: "",
//         altText: "",
//         image: "",
//       },
//     },
//   });

//   // const existingUrl = watch("existingUrl");

//   // Fetch available categories on component mount
//   useEffect(() => {
//     const fetchCategories = async () => {
//       try {
//         const response = await getCategoriesAction();
//         if (response.success && response.data) {
//           const categoriesData = (response.data as any).categories || [];
//           const categoryNames = categoriesData.map((cat: any) => ({
//             label: cat.name,
//             value: cat._id,
//           }));
//           setAvailableCategories(categoryNames);
//         }
//       } catch (error) {
//         console.error("Failed to fetch categories:", error);
//       }
//     };

//     fetchCategories();
//   }, []);

//   const onSubmit = async (data: FormData) => {
//     setIsSubmitting(true);

//     try {
//       console.log("Form data before cleaning:", data);

//       // Clean and format the data
//       const formattedData = cleanObject(data);
//       const bannerImageBase64 = bannerImageFile
//         ? ((await convertToBase64(bannerImageFile)) as string)
//         : "";

//       const res = await createPostAction(formattedData, bannerImageBase64);

//       if (!res.success) {
//         console.error("API Error:", res);
//         toast.error(res?.message || "Failed to create post");
//       } else {
//         console.log("Post created successfully:", res);
//         toast.success(res?.message || "Post created successfully");

//         // Redirect to blogs management page after successful creation
//         setTimeout(() => {
//           router.push("/dashboard/blogs");
//         }, 1500); // Wait 1.5 seconds to show the success toast
//       }
//     } catch (error) {
//       console.error("Submission error:", error);
//       toast.error("An unexpected error occurred");
//     } finally {
//       setIsSubmitting(false);
//     }
//   };

//   const handleBannerImageChange = (
//     event: React.ChangeEvent<HTMLInputElement>
//   ) => {
//     const file = event.target.files?.[0];
//     if (file) {
//       console.log("Banner image selected:", {
//         name: file.name,
//         size: file.size,
//         type: file.type,
//       });

//       // Validate file
//       const allowedTypes = [
//         "image/jpeg",
//         "image/jpg",
//         "image/png",
//         "image/webp",
//         "image/gif",
//         "image/svg+xml",
//       ];
//       const maxSize = 10 * 1024 * 1024; // 10MB

//       if (!allowedTypes.includes(file.type)) {
//         toast.error(
//           `Invalid file type: ${file.type}. Only images are allowed.`
//         );
//         return;
//       }

//       if (file.size > maxSize) {
//         toast.error(
//           `File too large: ${(file.size / 1024 / 1024).toFixed(
//             2
//           )}MB. Maximum 10MB allowed.`
//         );
//         return;
//       }

//       if (file.size === 0) {
//         toast.error("File is empty");
//         return;
//       }

//       setBannerImageFile(file);
//       // Optionally create a preview URL
//       const previewUrl = URL.createObjectURL(file);
//       setValue("banner.image", previewUrl); // For preview purposes
//     } else {
//       setBannerImageFile(null);
//       setValue("banner.image", "");
//     }
//   };

//   return (
//     <div className="min-h-screen bg-gray-50 p-6">
//       <div className="container px-3 mx-auto">
//         <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
//           {/* Blog Post Section */}
//           <div className="bg-white p-6 rounded-lg shadow-sm border">
//             <div className="flex items-center justify-between mb-6">
//               <div className="flex items-center gap-4">
//                 <span className="text-teal-600">Create</span>
//                 <span className="text-gray-400">•</span>
//                 <span>Blog Post</span>
//               </div>
//               <div className="flex items-center gap-2">
//                 <button
//                   onClick={() => router.push("/dashboard/blogs")}
//                   type="button"
//                   className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
//                 >
//                   Discard
//                 </button>
//                 <button
//                   type="submit"
//                   disabled={isSubmitting}
//                   className="bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 disabled:opacity-50 flex items-center gap-2"
//                 >
//                   {isSubmitting && <Loader2 className="w-4 h-4 animate-spin" />}
//                   {isSubmitting ? "Creating..." : "Create Post"}
//                 </button>
//               </div>
//             </div>

//             <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
//               <div className="lg:col-span-2">
//                 <div className="space-y-6">
//                   <div>
//                     <h3 className="text-lg font-semibold mb-4">Post Details</h3>

//                     {fieldConfigs.blogPost.map((config) => (
//                       <DynamicField
//                         key={config.name}
//                         config={config}
//                         register={register}
//                         error={errors[config.name]?.message}
//                       />
//                     ))}

//                     {/* <FormField
//                       label="Canonical URL"
//                       error={errors.canonicalUrl?.message}
//                       tooltip
//                     >
//                       <div className="space-y-2">
//                         <div className="flex items-center gap-2">
//                           <input
//                             {...register("existingUrl")}
//                             type="checkbox"
//                             className="rounded"
//                           />
//                           <label className="text-sm">Existing Url</label>
//                         </div>
//                         <div className="flex items-center gap-2">
//                           <input
//                             {...register("canonicalUrl")}
//                             type="url"
//                             placeholder="https://example.com/blog/post/"
//                             disabled={!existingUrl}
//                             className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
//                           />
//                           <button
//                             type="button"
//                             disabled={!existingUrl}
//                             className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 flex items-center gap-1"
//                           >
//                             <Eye className="w-4 h-4" />
//                             Visit
//                           </button>
//                         </div>
//                       </div>
//                     </FormField> */}

//                     {/* Categories and Tags Section */}
//                     <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//                       <div>
//                         <MultiSelect
//                           label="Categories"
//                           placeholder="Search for categories..."
//                           options={availableCategories}
//                           selectedValues={categories}
//                           onChange={(values) => {
//                             setCategories(values);
//                             setValue("categories", values);
//                           }}
//                           allowCustom={true}
//                         />
//                       </div>

//                       <div>
//                         <MultiSelect
//                           label="Tags"
//                           placeholder="Search for tags..."
//                           options={[]} // Tags are usually custom, so no predefined options
//                           selectedValues={tags}
//                           onChange={(values) => {
//                             setTags(values);
//                             setValue("tags", values);
//                           }}
//                           allowCustom={true}
//                         />
//                       </div>
//                     </div>
//                   </div>

//                   <div>
//                     <h3 className="text-lg font-semibold mb-4">Blog Content</h3>
//                     <FormField
//                       label="Content"
//                       error={errors.content?.message}
//                       required
//                     >
//                       <Controller
//                         name="content"
//                         control={control}
//                         render={({ field }) => (
//                           <WysiwygEditor
//                             content={field.value || ""}
//                             onChange={field.onChange}
//                             placeholder="Start writing your blog post content..."
//                           />
//                         )}
//                       />
//                     </FormField>
//                   </div>
//                 </div>
//               </div>

//               <div className="space-y-6">
//                 {/* Sidebar content removed as requested */}
//               </div>
//             </div>
//           </div>

//           {/* SEO Section */}
//           <div className="bg-white p-6 rounded-lg shadow-sm border">
//             <h2 className="text-lg font-semibold mb-6">SEO</h2>
//             <div className="space-y-4">
//               {fieldConfigs.seo.map((config) => (
//                 <DynamicField
//                   key={config.name}
//                   config={config}
//                   register={register}
//                   error={errors[config.name]?.message}
//                 />
//               ))}
//             </div>
//           </div>

//           {/* Banner Section */}
//           <div className="bg-white p-6 rounded-lg shadow-sm border">
//             <h2 className="text-lg font-semibold mb-6">Banner</h2>
//             <div className="space-y-4">
//               {fieldConfigs.banner.map((config) => (
//                 <DynamicField
//                   key={config.name}
//                   config={config}
//                   register={register}
//                   error={errors.banner?.[config.name]?.message}
//                 />
//               ))}

//               <div className="space-y-4">
//                 <label
//                   htmlFor="bannerImage"
//                   className="block text-sm font-medium text-gray-700 jakarta"
//                 >
//                   Banner Image
//                 </label>
//                 <div className="flex items-center gap-4">
//                   <Button
//                     type="button"
//                     variant="default"
//                     className="flex items-center gap-2 text-white transition-all duration-300 rounded-md shadow-md"
//                     onClick={() =>
//                       document.getElementById("bannerImage")?.click()
//                     }
//                   >
//                     <ImagePlus size={20} />
//                     <span>Choose Image</span>
//                   </Button>
//                   <input
//                     type="file"
//                     id="bannerImage"
//                     accept="image/*"
//                     onChange={handleBannerImageChange}
//                     className="hidden"
//                   />
//                 </div>
//                 {bannerImageFile && (
//                   <div className="mt-4">
//                     <p className="text-sm text-gray-600 jakarta">
//                       Selected: {bannerImageFile.name} (
//                       {(bannerImageFile.size / 1024).toFixed(2)} KB)
//                     </p>
//                     <img
//                       src={URL.createObjectURL(bannerImageFile)}
//                       alt="Preview"
//                       className="mt-2 max-w-[200px] max-h-[200px] rounded-md shadow-lg object-cover"
//                     />
//                   </div>
//                 )}
//               </div>
//             </div>
//           </div>
//         </form>
//       </div>
//     </div>
//   );
// }

// Second

"use client";

import {
  cleanObject,
  convertToBase64,
} from "@/app/shared/cleanObject";
import { getCategoriesAction, createPostAction } from "../actions";
import { Button } from "@/components/ui/button";
import { ImagePlus, Info, Loader2, Plus } from "lucide-react";
import type React from "react";
import { useState, useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";
import { z } from "zod";
import WysiwygEditor from "./WysiwygEditor";

// Form schema matching the API
const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  slug: z.string().optional(),
  // canonicalUrl: z
  //   .string()
  //   .url("Must be a valid URL")
  //   .optional()
  //   .or(z.literal("")),
  // existingUrl: z.boolean().default(false),
  content: z.string().min(1, "Content is required"),
  isBlog: z.boolean().default(true),
  categories: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
  metaTitle: z.string().min(1, "Meta title is required"),
  metaDescription: z.string().min(1, "Meta description is required"),
  metaKeywords: z.string().min(1, "Meta keywords are required"),
  banner: z.object({
    image: z.string().min(1, "Banner image is required"),
  }),
});

type FormData = z.infer<typeof formSchema>;

// Field configuration for dynamic rendering
const fieldConfigs = {
  blogPost: [
    {
      name: "title" as const,
      label: "Title",
      type: "text",
      placeholder: "Enter the title of the blog post",
      tooltip: true,
      required: true,
    },
    {
      name: "slug" as const,
      label: "Slug (Optional)",
      type: "text",
      placeholder: "Enter a unique slug (auto-generated if empty)",
      tooltip: true,
      required: false,
    },
  ],
  seo: [
    {
      name: "metaTitle" as const,
      label: "Meta Title",
      type: "text",
      placeholder: "Enter meta title ",
      tooltip: true,
      required: true,
    },
    {
      name: "metaDescription" as const,
      label: "Meta Description",
      type: "textarea",
      placeholder: "Enter meta description",
      rows: 3,
      tooltip: true,
      required: true,
    },
    {
      name: "metaKeywords" as const,
      label: "Meta Keywords (comma-separated)",
      type: "text",
      placeholder: "Enter meta keywords ",
      tooltip: true,
      required: true,
    },
  ],
  banner: [],
};

const InfoIcon = ({ className }: { className?: string }) => (
  <Info className={`w-4 h-4 text-gray-400 ${className}`} />
);

const FormField = ({
  label,
  error,
  children,
  required = false,
  tooltip = false,
}: {
  label: string;
  error?: string;
  children: React.ReactNode;
  required?: boolean;
  tooltip?: boolean;
}) => (
  <div className="mb-4">
    <label className="block text-sm font-medium text-gray-700 mb-2">
      {label}
      {required && <span className="text-red-500 ml-1">*</span>}
      {tooltip && <InfoIcon className="inline ml-1" />}
    </label>
    {children}
    {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
  </div>
);

const DynamicField = ({
  config,
  register,
  error,
}: {
  config: any;
  register: any;
  error?: string;
}) => {
  return (
    <FormField
      label={config.label}
      error={error}
      tooltip={config.tooltip}
      required={config.required}
    >
      {config.type === "textarea" ? (
        <textarea
          {...register(config.name)}
          placeholder={config.placeholder}
          // placeholder="Enter meta description"
          rows={config.rows || 3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
        />
      ) : (
        <input
          {...register(config.name)}
          type={config.type}
          placeholder={config.placeholder}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      )}
    </FormField>
  );
};

// Multi-select component for categories and tags
export interface MultiSelectOption {
  label: string;
  value: string;
}

export interface MultiSelectProps {
  label: string;
  placeholder: string;
  options: MultiSelectOption[]; // Accepts array of objects
  selectedValues: string[]; // Still stores only the 'value'
  onChange: (values: string[]) => void;
  onAddNew?: () => void;
  allowCustom?: boolean;
}

export const MultiSelect = ({
  label,
  placeholder,
  options,
  selectedValues,
  onChange,
  onAddNew,
  allowCustom = true,
}: MultiSelectProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  // Filter options by label
  const filteredOptions = options.filter(
    (option) =>
      option.label.toLowerCase().includes(searchTerm.toLowerCase()) &&
      !selectedValues.includes(option.value)
  );

  const handleSelect = (option: MultiSelectOption) => {
    if (!selectedValues.includes(option.value)) {
      onChange([...selectedValues, option.value]);
    }
    setSearchTerm("");
    setIsOpen(false);
  };

  const handleRemove = (value: string) => {
    onChange(selectedValues.filter((item) => item !== value));
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && searchTerm.trim() && allowCustom) {
      e.preventDefault();
      const newOption = searchTerm.trim();
      if (!selectedValues.includes(newOption)) {
        onChange([...selectedValues, newOption]);
        setSearchTerm("");
        setIsOpen(false);
      }
    }
  };

  // Helper to get label by value
  const getLabelByValue = (value: string) => {
    return options.find((option) => option.value === value)?.label || value;
  };

  return (
    <div className="relative">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-semibold">{label}</h3>
        {onAddNew && (
          <button
            type="button"
            onClick={onAddNew}
            className="bg-black text-white px-3 py-1 rounded text-sm flex items-center gap-1"
          >
            <Plus className="w-3 h-3" />
            Add New
          </button>
        )}
      </div>

      {/* Selected items */}
      {selectedValues.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-3">
          {selectedValues.map((value, index) => (
            <span
              key={index}
              className="bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm flex items-center gap-1"
            >
              {getLabelByValue(value)}
              <button
                type="button"
                onClick={() => handleRemove(value)}
                className="text-blue-600 hover:text-blue-800 ml-1"
              >
                ×
              </button>
            </span>
          ))}
        </div>
      )}

      {/* Search input */}
      <div className="relative">
        <input
          type="text"
          placeholder={placeholder}
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setIsOpen(true);
          }}
          onFocus={() => setIsOpen(true)}
          onKeyDown={handleKeyDown}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />

        {/* Dropdown */}
        {isOpen &&
          (filteredOptions.length > 0 ||
            (allowCustom && searchTerm.trim())) && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto">
              {filteredOptions.map((option, index) => (
                <div
                  key={index}
                  onClick={() => handleSelect(option)}
                  className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm"
                >
                  {option.label}
                </div>
              ))}
              {allowCustom && searchTerm.trim() && (
                <div
                  onClick={() =>
                    handleSelect({
                      label: searchTerm.trim(),
                      value: searchTerm.trim(),
                    })
                  }
                  className="px-3 py-2 hover:bg-green-50 cursor-pointer text-sm border-t border-gray-200 text-green-700"
                >
                  Add &quot;{searchTerm.trim()}&quot;
                </div>
              )}
            </div>
          )}
      </div>

      {/* Click outside to close */}
      {isOpen && (
        <div className="fixed inset-0 z-5" onClick={() => setIsOpen(false)} />
      )}
    </div>
  );
};

// Original DynamicContentManager (preserved for backward compatibility)
export default function DynamicContentManager() {
  const router = useRouter();
  const [categories, setCategories] = useState<string[]>([]);
  const [tags, setTags] = useState<string[]>([]);
  const [availableCategories, setAvailableCategories] = useState<
    MultiSelectOption[]
  >([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bannerImageFile, setBannerImageFile] = useState<File | null>(null);

  const {
    control,
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      isBlog: true,
      // existingUrl: false,
      categories: [],
      tags: [],
      content: "",
      banner: {
        image: "",
      },
    },
  });

  // const existingUrl = watch("existingUrl");

  // Fetch available categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await getCategoriesAction();
        if (response.success && response.data) {
          const categoriesData = (response.data as any).categories || [];
          const categoryNames = categoriesData.map((cat: any) => ({
            label: cat.name,
            value: cat._id,
          }));
          setAvailableCategories(categoryNames);
        }
      } catch (error) {
        console.error("Failed to fetch categories:", error);
      }
    };

    fetchCategories();
  }, []);

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    try {
      console.log("Form data before cleaning:", data);

      // Clean and format the data
      const formattedData = cleanObject(data);

      const bannerImageBase64 = bannerImageFile
        ? ((await convertToBase64(bannerImageFile)) as string)
        : "";

      const res = await createPostAction(formattedData, bannerImageBase64);

      if (!res.success) {
        console.error("API Error:", res);
        toast.error(res?.message || "Failed to create post");
      } else {
        console.log("Post created successfully:", res);
        toast.success(res?.message || "Post created successfully");
        // Redirect to blogs management page after successful creation
        setTimeout(() => {
          router.push("/dashboard/blogs");
        }, 1500); // Wait 1.5 seconds to show the success toast
      }
    } catch (error) {
      console.error("Submission error:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBannerImageChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      console.log("Banner image selected:", {
        name: file.name,
        size: file.size,
        type: file.type,
      });

      // Validate file
      const allowedTypes = [
        "image/jpeg",
        "image/jpg",
        "image/png",
        "image/webp",
        "image/gif",
        "image/svg+xml",
      ];
      const maxSize = 10 * 1024 * 1024; // 10MB

      if (!allowedTypes.includes(file.type)) {
        toast.error(
          `Invalid file type: ${file.type}. Only images are allowed.`
        );
        return;
      }

      if (file.size > maxSize) {
        toast.error(
          `File too large: ${(file.size / 1024 / 1024).toFixed(
            2
          )}MB. Maximum 10MB allowed.`
        );
        return;
      }

      if (file.size === 0) {
        toast.error("File is empty");
        return;
      }

      setBannerImageFile(file);

      // Optionally create a preview URL
      const previewUrl = URL.createObjectURL(file);
      setValue("banner.image", previewUrl); // For preview purposes
    } else {
      setBannerImageFile(null);
      setValue("banner.image", "");
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="container px-3 mx-auto">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Blog Post Section */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <span className="text-teal-600">Create</span>
                <span className="text-gray-400">•</span>
                <span>Blog Post</span>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => router.push("/dashboard/blogs")}
                  type="button"
                  className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Discard
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 disabled:opacity-50 flex items-center gap-2"
                >
                  {isSubmitting && <Loader2 className="w-4 h-4 animate-spin" />}
                  {isSubmitting ? "Creating..." : "Create Post"}
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Post Details</h3>
                    {fieldConfigs.blogPost.map((config) => (
                      <DynamicField
                        key={config.name}
                        config={config}
                        register={register}
                        error={errors[config.name]?.message}
                      />
                    ))}
                    {/* <FormField
                      label="Canonical URL"
                      error={errors.canonicalUrl?.message}
                      tooltip
                    >
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <input
                            {...register("existingUrl")}
                            type="checkbox"
                            className="rounded"
                          />
                          <label className="text-sm">Existing Url</label>
                        </div>
                        <div className="flex items-center gap-2">
                          <input
                            {...register("canonicalUrl")}
                            type="url"
                            placeholder="https://example.com/blog/post/"
                            disabled={!existingUrl}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                          />
                          <button
                            type="button"
                            disabled={!existingUrl}
                            className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 flex items-center gap-1"
                          >
                            <Eye className="w-4 h-4" />
                            Visit
                          </button>
                        </div>
                      </div>
                    </FormField> */}

                    {/* Categories and Tags Section */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <MultiSelect
                          label="Categories"
                          placeholder="Search for categories..."
                          options={availableCategories}
                          selectedValues={categories}
                          onChange={(values) => {
                            setCategories(values);
                            setValue("categories", values);
                          }}
                          allowCustom={true}
                        />
                      </div>
                      <div>
                        <MultiSelect
                          label="Tags"
                          placeholder="Search for tags..."
                          options={[]} // Tags are usually custom, so no predefined options
                          selectedValues={tags}
                          onChange={(values) => {
                            setTags(values);
                            setValue("tags", values);
                          }}
                          allowCustom={true}
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-4">Blog Content</h3>
                    <FormField
                      label="Content"
                      error={errors.content?.message}
                      required
                    >
                      <Controller
                        name="content"
                        control={control}
                        render={({ field }) => (
                          <WysiwygEditor
                            content={field.value || ""}
                            onChange={field.onChange}
                            placeholder="Start writing your blog post content..."
                          />
                        )}
                      />
                    </FormField>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                {/* Sidebar content removed as requested */}
              </div>
            </div>
          </div>

          {/* SEO Section */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-semibold mb-6">SEO</h2>
            <div className="space-y-4">
              {fieldConfigs.seo.map((config) => (
                <DynamicField
                  key={config.name}
                  config={config}
                  register={register}
                  error={errors[config.name]?.message}
                />
              ))}
            </div>
          </div>

          {/* Banner Section */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-semibold mb-6">Banner</h2>
            <div className="space-y-4">
              <div className="space-y-4">
                <label
                  htmlFor="bannerImage"
                  className="block text-sm font-medium text-gray-700 jakarta"
                >
                  Banner Image
                </label>
                <div className="flex items-center gap-4">
                  <Button
                    type="button"
                    variant="default"
                    className="flex items-center gap-2 text-white transition-all duration-300 rounded-md shadow-md"
                    onClick={() =>
                      document.getElementById("bannerImage")?.click()
                    }
                  >
                    <ImagePlus size={20} />
                    <span>Choose Image</span>
                  </Button>
                  <input
                    type="file"
                    id="bannerImage"
                    accept="image/*"
                    onChange={handleBannerImageChange}
                    className="hidden"
                  />
                </div>

                {bannerImageFile && (
                  <div className="mt-4">
                    <p className="text-sm text-gray-600 jakarta">
                      Selected: {bannerImageFile.name} (
                      {(bannerImageFile.size / 1024).toFixed(2)} KB)
                    </p>
                    <img
                      src={
                        URL.createObjectURL(bannerImageFile) ||
                        "/placeholder.svg"
                      }
                      alt="Preview"
                      className="mt-2 max-w-[200px] max-h-[200px] rounded-md shadow-lg object-cover"
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
