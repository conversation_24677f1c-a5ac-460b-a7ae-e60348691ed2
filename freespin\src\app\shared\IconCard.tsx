import { IconOne } from "../(pages)/home/<USER>/IconList";
import React from "react";

// const IconCard = () => {
//   return (
//     <div className="rounded-xl px-3 py-3 h-full w-full ">
//       <IconOne />
//       <h3 className="jakarta text-base md:text-lg lg:text-[22px] font-bold text-[#FCFCFC]">
//         Deeply Engaging Play
//       </h3>
//       <p className="text-[#A9A7B0] text-base jakarta">
//         Dive into visually stunning, smooth, and immersive gameplay that keeps
//         users coming back for more.
//       </p>
//     </div>
//   );
// };

interface IconCardProps {
  title: string;
  text: string;
  IconName: any;
}

const IconCard = ({ title, text, IconName }: IconCardProps) => {
  return (
    <div className="group flex flex-col items-center justify-center rounded-xl px-3 py-3 h-full w-full bg-[#1C1C24] transition-colors duration-300 hover:bg-[#A945F1]">
      <IconName className="fill-[#3159C6] group-hover:text-white  group-hover:fill-white transition-colors duration-300" />
      <h3 className="jakarta text-base md:text-lg lg:text-[22px] font-bold text-[#FCFCFC]">
        {title}
      </h3>
      <p className="text-[#A9A7B0] text-base text-center jakarta">{text}</p>
    </div>
  );
};
export default IconCard;
