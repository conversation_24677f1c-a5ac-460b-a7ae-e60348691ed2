{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from \"mongoose\";\r\n\r\nconst MONGODB_URI = process.env.MONGODB_URI!;\r\n\r\nif (!MONGODB_URI) {\r\n  throw new Error(\r\n    \"Please define the MONGODB_URI environment variable inside .env.local\"\r\n  );\r\n}\r\n\r\n/**\r\n * Global is used here to maintain a cached connection across hot reloads\r\n * in development. This prevents connections growing exponentially\r\n * during API Route usage.\r\n */\r\n// @ts-expect-error //global can have type any\r\nlet cached = global?.mongoose;\r\n\r\nif (!cached) {\r\n  // @ts-expect-error //global mongoose can have type any\r\n  cached = global.mongoose = { conn: null, promise: null };\r\n}\r\n\r\nasync function connectDB() {\r\n  if (cached.conn) {\r\n    return cached.conn;\r\n  }\r\n\r\n  if (!cached.promise) {\r\n    const opts = {\r\n      bufferCommands: false,\r\n    };\r\n\r\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\r\n      return mongoose;\r\n    });\r\n  }\r\n\r\n  try {\r\n    cached.conn = await cached.promise;\r\n  } catch (e) {\r\n    cached.promise = null;\r\n    throw e;\r\n  }\r\n\r\n  return cached.conn;\r\n}\r\n\r\nexport default connectDB;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MACR;AAEJ;AAEA;;;;CAIC,GACD,8CAA8C;AAC9C,IAAI,SAAS,QAAQ;AAErB,IAAI,CAAC,QAAQ;IACX,uDAAuD;IACvD,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/models/Post.ts"], "sourcesContent": ["import mongoose, { type Document, Mongoose, Schema, Types } from \"mongoose\";\r\n\r\nexport interface IPost extends Document {\r\n  // Blog Post fields\r\n  title: string;\r\n  slug?: string;\r\n  canonicalUrl?: string;\r\n  existingUrl: boolean;\r\n  content: string; // Main content field\r\n  excerpt?: string; // Short description/excerpt\r\n  description: string; // For backward compatibility\r\n  isBlog: boolean;\r\n  categories: Types.ObjectId[];\r\n  tags: string[];\r\n  author?: string; // Author reference\r\n\r\n  // SEO fields\r\n  metaTitle: string;\r\n  metaDescription: string;\r\n  metaKeywords: string;\r\n\r\n  // FAQ fields\r\n  // faqs: {\r\n  //   question: string;\r\n  //   answer: string;\r\n  //   index: number;\r\n  // }[];\r\n\r\n  // Banner fields\r\n  banner: string;\r\n\r\n  // Additional blog functionality fields\r\n  status: \"draft\" | \"published\" | \"archived\";\r\n  isPublished: boolean;\r\n  publishedAt?: Date;\r\n  views: number;\r\n  readTime: number;\r\n  isTopNews: boolean;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\nconst PostSchema = new Schema<IPost>(\r\n  {\r\n    // Blog Post fields\r\n    title: {\r\n      type: String,\r\n      required: [true, \"Post title is required\"],\r\n      trim: true,\r\n      maxlength: [1000, \"Title cannot exceed 200 characters\"],\r\n    },\r\n\r\n    slug: {\r\n      type: String,\r\n      trim: true,\r\n      lowercase: true,\r\n    },\r\n\r\n    canonicalUrl: {\r\n      type: String,\r\n      trim: true,\r\n      validate: {\r\n        validator: (v: string) => {\r\n          if (!v) return true; // Allow empty string\r\n          return /^https?:\\/\\/.+/.test(v);\r\n        },\r\n        message: \"Canonical URL must be a valid URL\",\r\n      },\r\n    },\r\n    existingUrl: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n\r\n    content: {\r\n      type: String,\r\n      required: [true, \"Post content is required\"],\r\n    },\r\n\r\n    excerpt: {\r\n      type: String,\r\n      trim: true,\r\n      maxlength: [1000, \"Excerpt cannot exceed 300 characters\"],\r\n    },\r\n\r\n    description: {\r\n      type: String,\r\n      // Not required anymore since we have content field\r\n    },\r\n\r\n    author: {\r\n      type: String,\r\n      trim: true,\r\n    },\r\n    isBlog: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    categories: [\r\n      {\r\n        type: mongoose.Schema.Types.ObjectId,\r\n        ref: \"Category\",\r\n      },\r\n    ],\r\n    tags: [\r\n      {\r\n        type: String,\r\n        trim: true,\r\n        lowercase: true,\r\n      },\r\n    ],\r\n\r\n    // SEO fields\r\n    metaTitle: {\r\n      type: String,\r\n      required: [true, \"Meta title is required\"],\r\n      maxlength: [1000, \"Meta title cannot exceed 60 characters\"],\r\n    },\r\n    metaDescription: {\r\n      type: String,\r\n      required: [true, \"Meta description is required\"],\r\n      maxlength: [1000, \"Meta description cannot exceed 160 characters\"],\r\n    },\r\n    metaKeywords: {\r\n      type: String,\r\n      required: [true, \"Meta keywords are required\"],\r\n      maxlength: [1000, \"Meta keywords cannot exceed 200 characters\"],\r\n    },\r\n\r\n    // Banner fields\r\n    banner: {\r\n      type: String,\r\n      required: [true, \"Banner image is required\"],\r\n      trim: true,\r\n    },\r\n\r\n    // Additional blog functionality fields\r\n    status: {\r\n      type: String,\r\n      enum: [\"draft\", \"published\", \"archived\"],\r\n      default: \"draft\",\r\n    },\r\n    isPublished: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    publishedAt: {\r\n      type: Date,\r\n      default: null,\r\n    },\r\n    views: {\r\n      type: Number,\r\n      default: 0,\r\n    },\r\n    readTime: {\r\n      type: Number,\r\n      default: 1,\r\n    },\r\n    isTopNews: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  {\r\n    timestamps: true,\r\n  }\r\n);\r\n\r\n// Pre-save middleware\r\nPostSchema.pre(\"save\", function (next) {\r\n  // Generate slug from title if not provided\r\n  if (this.isModified(\"title\") && !this.slug) {\r\n    this.slug = this.title\r\n      .toLowerCase()\r\n      .replace(/[^a-z0-9]+/g, \"-\")\r\n      .replace(/^-+|-+$/g, \"\");\r\n  }\r\n\r\n  // Calculate read time (average 200 words per minute)\r\n  if (this.isModified(\"content\")) {\r\n    const wordCount = (this.content || \"\").split(/\\s+/).length;\r\n    this.readTime = Math.ceil(wordCount / 200);\r\n  }\r\n\r\n  // Set published date when status changes to published\r\n  if (\r\n    this.isModified(\"status\") &&\r\n    this.status === \"published\" &&\r\n    !this.publishedAt\r\n  ) {\r\n    this.publishedAt = new Date();\r\n    this.isPublished = true;\r\n  }\r\n\r\n  next();\r\n});\r\n\r\n// Create indexes for better query performance\r\nPostSchema.index({ categories: 1 });\r\nPostSchema.index({ tags: 1 });\r\nPostSchema.index({ status: 1 });\r\nPostSchema.index({ isPublished: 1 });\r\nPostSchema.index({ publishedAt: -1 });\r\nPostSchema.index({ \"banner.title\": 1 });\r\nPostSchema.index({\r\n  title: \"text\",\r\n  content: \"text\",\r\n  metaTitle: \"text\",\r\n  metaDescription: \"text\",\r\n}); // Text search index\r\n\r\nexport default mongoose.models.Post ||\r\n  mongoose.model<IPost>(\"Post\", PostSchema);\r\n"], "names": [], "mappings": ";;;AAAA;;AA0CA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAC3B;IACE,mBAAmB;IACnB,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,MAAM;QACN,WAAW;YAAC;YAAM;SAAqC;IACzD;IAEA,MAAM;QACJ,MAAM;QACN,MAAM;QACN,WAAW;IACb;IAEA,cAAc;QACZ,MAAM;QACN,MAAM;QACN,UAAU;YACR,WAAW,CAAC;gBACV,IAAI,CAAC,GAAG,OAAO,MAAM,qBAAqB;gBAC1C,OAAO,iBAAiB,IAAI,CAAC;YAC/B;YACA,SAAS;QACX;IACF;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IAEA,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;IAC9C;IAEA,SAAS;QACP,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAAuC;IAC3D;IAEA,aAAa;QACX,MAAM;IAER;IAEA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,YAAY;QACV;YACE,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;YACpC,KAAK;QACP;KACD;IACD,MAAM;QACJ;YACE,MAAM;YACN,MAAM;YACN,WAAW;QACb;KACD;IAED,aAAa;IACb,WAAW;QACT,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,WAAW;YAAC;YAAM;SAAyC;IAC7D;IACA,iBAAiB;QACf,MAAM;QACN,UAAU;YAAC;YAAM;SAA+B;QAChD,WAAW;YAAC;YAAM;SAAgD;IACpE;IACA,cAAc;QACZ,MAAM;QACN,UAAU;YAAC;YAAM;SAA6B;QAC9C,WAAW;YAAC;YAAM;SAA6C;IACjE;IAEA,gBAAgB;IAChB,QAAQ;QACN,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,MAAM;IACR;IAEA,uCAAuC;IACvC,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAS;YAAa;SAAW;QACxC,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IACA,OAAO;QACL,MAAM;QACN,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,SAAS;IACX;AACF,GACA;IACE,YAAY;AACd;AAGF,sBAAsB;AACtB,WAAW,GAAG,CAAC,QAAQ,SAAU,IAAI;IACnC,2CAA2C;IAC3C,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE;QAC1C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CACnB,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;IACzB;IAEA,qDAAqD;IACrD,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY;QAC9B,MAAM,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,EAAE,KAAK,CAAC,OAAO,MAAM;QAC1D,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,YAAY;IACxC;IAEA,sDAAsD;IACtD,IACE,IAAI,CAAC,UAAU,CAAC,aAChB,IAAI,CAAC,MAAM,KAAK,eAChB,CAAC,IAAI,CAAC,WAAW,EACjB;QACA,IAAI,CAAC,WAAW,GAAG,IAAI;QACvB,IAAI,CAAC,WAAW,GAAG;IACrB;IAEA;AACF;AAEA,8CAA8C;AAC9C,WAAW,KAAK,CAAC;IAAE,YAAY;AAAE;AACjC,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC7B,WAAW,KAAK,CAAC;IAAE,aAAa;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,aAAa,CAAC;AAAE;AACnC,WAAW,KAAK,CAAC;IAAE,gBAAgB;AAAE;AACrC,WAAW,KAAK,CAAC;IACf,OAAO;IACP,SAAS;IACT,WAAW;IACX,iBAAiB;AACnB,IAAI,oBAAoB;uCAET,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IACjC,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/lib/minio.ts"], "sourcesContent": ["import { Client } from \"minio\";\r\nimport { Readable } from \"stream\";\r\n\r\n// MinIO configuration\r\nconst minioConfig = {\r\n  endPoint:\r\n    process.env.MINIO_ENDPOINT?.replace(/^https?:\\/\\//, \"\") || \"localhost\",\r\n  port: process.env.MINIO_PORT ? Number.parseInt(process.env.MINIO_PORT) : 9000,\r\n  useSSL: process.env.MINIO_USE_SSL === \"true\",\r\n  accessKey: process.env.MINIO_ACCESS_KEY || \"3uiq5emitjasdfghyjui\",\r\n  secretKey:\r\n    process.env.MINIO_SECRET_KEY || \"TYo1ruP1PqbOx3fONapGwawKQGqCDQsdfadsdfgh\",\r\n};\r\n\r\nconst BUCKET_NAME = process.env.MINIO_BUCKET_NAME || \"spinfree\";\r\n\r\n// Create MinIO client\r\nconst minioClient = new Client(minioConfig);\r\n\r\n// Initialize bucket if it doesn't exist\r\nconst initializeBucket = async (): Promise<void> => {\r\n  try {\r\n    console.log(\"Checking MinIO connection...\");\r\n    console.log(\"MinIO config:\", {\r\n      endPoint: minioConfig.endPoint,\r\n      port: minioConfig.port,\r\n      useSSL: minioConfig.useSSL,\r\n      bucket: BUCKET_NAME,\r\n    });\r\n\r\n    const bucketExists = await minioClient.bucketExists(BUCKET_NAME);\r\n\r\n    if (!bucketExists) {\r\n      await minioClient.makeBucket(BUCKET_NAME, \"us-east-1\");\r\n      console.log(`Bucket '${BUCKET_NAME}' created successfully`);\r\n\r\n      // Set bucket policy to allow public read access\r\n      const policy = {\r\n        Version: \"2012-10-17\",\r\n        Statement: [\r\n          {\r\n            Effect: \"Allow\",\r\n            Principal: { AWS: [\"*\"] },\r\n            Action: [\"s3:GetObject\"],\r\n            Resource: [`arn:aws:s3:::${BUCKET_NAME}/*`],\r\n          },\r\n        ],\r\n      };\r\n\r\n      await minioClient.setBucketPolicy(BUCKET_NAME, JSON.stringify(policy));\r\n      console.log(`Bucket policy set for '${BUCKET_NAME}'`);\r\n    } else {\r\n      console.log(`Bucket '${BUCKET_NAME}' already exists`);\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error initializing MinIO bucket:\", error);\r\n    // Don't throw error, allow application to continue\r\n  }\r\n};\r\n\r\n// File type detection\r\nconst detectImageType = (buffer: Buffer): string | null => {\r\n  // Check for PNG\r\n  if (\r\n    buffer[0] === 0x89 &&\r\n    buffer[1] === 0x50 &&\r\n    buffer[2] === 0x4e &&\r\n    buffer[3] === 0x47\r\n  ) {\r\n    return \"image/png\";\r\n  }\r\n  // Check for JPEG\r\n  if (buffer[0] === 0xff && buffer[1] === 0xd8 && buffer[2] === 0xff) {\r\n    return \"image/jpeg\";\r\n  }\r\n  // Check for SVG\r\n  const possibleSvg = buffer.toString(\"ascii\", 0, 100).toLowerCase();\r\n  if (possibleSvg.includes(\"<svg\") || possibleSvg.includes(\"<?xml\")) {\r\n    return \"image/svg+xml\";\r\n  }\r\n  return null;\r\n};\r\n\r\nexport class MinioService {\r\n  static async uploadFile(\r\n    fileBuffer: Buffer,\r\n    fileName: string,\r\n    contentType: string,\r\n    folder = \"posts\"\r\n  ): Promise<string> {\r\n    try {\r\n      console.log(\"Starting file upload to MinIO...\");\r\n\r\n      // Check MinIO connection first\r\n      try {\r\n        await minioClient.bucketExists(BUCKET_NAME);\r\n        console.log(\"MinIO connection verified\");\r\n      } catch (connectionError) {\r\n        console.error(\"MinIO connection failed:\", connectionError);\r\n        throw new Error(\r\n          \"MinIO service is unavailable. Please try again later.\"\r\n        );\r\n      }\r\n\r\n      // Initialize bucket if needed\r\n      await initializeBucket();\r\n\r\n      // Detect actual file type if it's octet-stream\r\n      let mimeType = contentType;\r\n      if (contentType === \"application/octet-stream\") {\r\n        const detectedType = detectImageType(fileBuffer);\r\n        if (!detectedType) {\r\n          throw new Error(\"Invalid or unsupported image format\");\r\n        }\r\n        mimeType = detectedType;\r\n      }\r\n\r\n      // Generate unique filename with date structure\r\n      const date = new Date();\r\n      const fileExtension =\r\n        mimeType === \"image/svg+xml\"\r\n          ? \"svg\"\r\n          : mimeType === \"image/png\"\r\n          ? \"png\"\r\n          : \"jpg\";\r\n      const uniqueFileName = `${Date.now()}-${fileName.replace(\r\n        /\\.[^/.]+$/,\r\n        \"\"\r\n      )}.${fileExtension}`;\r\n\r\n      const relativePath = [\r\n        folder,\r\n        date.getFullYear().toString(),\r\n        (date.getMonth() + 1).toString().padStart(2, \"0\"),\r\n        uniqueFileName,\r\n      ].join(\"/\");\r\n\r\n      console.log(\"Uploading to path:\", relativePath);\r\n\r\n      // Create readable stream from buffer\r\n      const fileStream = new Readable();\r\n      fileStream.push(fileBuffer);\r\n      fileStream.push(null);\r\n\r\n      // Upload to MinIO\r\n      await minioClient.putObject(\r\n        BUCKET_NAME,\r\n        relativePath,\r\n        fileStream,\r\n        fileBuffer.length,\r\n        {\r\n          \"Content-Type\": mimeType,\r\n        }\r\n      );\r\n\r\n      // Generate URL through our API proxy instead of direct MinIO URL\r\n      const baseUrl =\r\n        process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:3001\";\r\n      const fileUrl = `${baseUrl}/api/uploads/${relativePath}`;\r\n\r\n      console.log(`File uploaded to MinIO successfully!`);\r\n      console.log(`- MinIO path: ${relativePath}`);\r\n      console.log(`- Proxy URL: ${fileUrl}`);\r\n      console.log(`- Base URL: ${baseUrl}`);\r\n      return fileUrl;\r\n    } catch (error) {\r\n      console.error(\"Error uploading file to MinIO:\", error);\r\n      console.error(\"MinIO config:\", {\r\n        endPoint: minioConfig.endPoint,\r\n        port: minioConfig.port,\r\n        useSSL: minioConfig.useSSL,\r\n        bucket: BUCKET_NAME,\r\n      });\r\n      console.error(\"Upload details:\", {\r\n        fileName,\r\n        contentType,\r\n        folder,\r\n        bufferSize: fileBuffer.length,\r\n      });\r\n\r\n      // Fallback: return a placeholder URL instead of throwing\r\n      const fallbackUrl = `/uploads/${folder}/${Date.now()}-${fileName}`;\r\n      console.log(\"Upload failed, using fallback URL:\", fallbackUrl);\r\n      return fallbackUrl;\r\n    }\r\n  }\r\n\r\n  static async deleteFile(fileUrl: string): Promise<void> {\r\n    try {\r\n      // Extract object name from URL\r\n      const url = new URL(fileUrl);\r\n      const objectName = url.pathname.substring(\r\n        url.pathname.indexOf(\"/\", 1) + 1\r\n      );\r\n\r\n      await minioClient.removeObject(BUCKET_NAME, objectName);\r\n      console.log(`File deleted from MinIO: ${objectName}`);\r\n    } catch (error) {\r\n      console.error(\"Error deleting file from MinIO:\", error);\r\n      throw new Error(\"Failed to delete file\");\r\n    }\r\n  }\r\n\r\n  static async listFiles(folder = \"posts\", maxKeys = 100): Promise<any[]> {\r\n    try {\r\n      const files: any[] = [];\r\n      const stream = minioClient.listObjects(BUCKET_NAME, `${folder}/`, true);\r\n\r\n      return new Promise((resolve, reject) => {\r\n        stream.on(\"data\", (obj) => {\r\n          if (files.length < maxKeys) {\r\n            const protocol = minioConfig.useSSL ? \"https\" : \"http\";\r\n            const port =\r\n              minioConfig.port !== (minioConfig.useSSL ? 443 : 80)\r\n                ? `:${minioConfig.port}`\r\n                : \"\";\r\n            const fileUrl = `${protocol}://${minioConfig.endPoint}${port}/${BUCKET_NAME}/${obj.name}`;\r\n\r\n            files.push({\r\n              name: obj.name,\r\n              size: obj.size,\r\n              lastModified: obj.lastModified,\r\n              url: fileUrl,\r\n              fileName: obj.name?.split(\"/\").pop() || \"\",\r\n              folder: obj.name?.split(\"/\").slice(0, -1).join(\"/\") || \"\",\r\n            });\r\n          }\r\n        });\r\n\r\n        stream.on(\"end\", () => resolve(files));\r\n        stream.on(\"error\", (err) => reject(err));\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Error listing files:\", error);\r\n      throw new Error(\"Failed to list files\");\r\n    }\r\n  }\r\n\r\n  static async getPresignedUrl(\r\n    objectName: string,\r\n    expiry = 3600\r\n  ): Promise<string> {\r\n    try {\r\n      return await minioClient.presignedGetObject(\r\n        BUCKET_NAME,\r\n        objectName,\r\n        expiry\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Error generating presigned URL:\", error);\r\n      throw new Error(\"Failed to generate presigned URL\");\r\n    }\r\n  }\r\n\r\n  static async getFileStream(objectName: string): Promise<Buffer> {\r\n    try {\r\n      const stream = await minioClient.getObject(BUCKET_NAME, objectName);\r\n      const chunks: Buffer[] = [];\r\n\r\n      return new Promise((resolve, reject) => {\r\n        stream.on(\"data\", (chunk) => chunks.push(chunk));\r\n        stream.on(\"end\", () => resolve(Buffer.concat(chunks)));\r\n        stream.on(\"error\", reject);\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Error getting file stream from MinIO:\", error);\r\n      throw new Error(\"Failed to get file from MinIO\");\r\n    }\r\n  }\r\n}\r\n\r\n// Initialize bucket on module load (but don't block)\r\ninitializeBucket().catch((err) => {\r\n  console.error(\"Critical error during MinIO initialization:\", err);\r\n});\r\n\r\nexport { minioClient, minioConfig, BUCKET_NAME };\r\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;;;AAEA,sBAAsB;AACtB,MAAM,cAAc;IAClB,UACE,QAAQ,GAAG,CAAC,cAAc,EAAE,QAAQ,gBAAgB,OAAO;IAC7D,MAAM,QAAQ,GAAG,CAAC,UAAU,GAAG,OAAO,QAAQ,CAAC,QAAQ,GAAG,CAAC,UAAU,IAAI;IACzE,QAAQ,QAAQ,GAAG,CAAC,aAAa,KAAK;IACtC,WAAW,QAAQ,GAAG,CAAC,gBAAgB,IAAI;IAC3C,WACE,QAAQ,GAAG,CAAC,gBAAgB,IAAI;AACpC;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,iBAAiB,IAAI;AAErD,sBAAsB;AACtB,MAAM,cAAc,IAAI,gKAAA,CAAA,SAAM,CAAC;AAE/B,wCAAwC;AACxC,MAAM,mBAAmB;IACvB,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,iBAAiB;YAC3B,UAAU,YAAY,QAAQ;YAC9B,MAAM,YAAY,IAAI;YACtB,QAAQ,YAAY,MAAM;YAC1B,QAAQ;QACV;QAEA,MAAM,eAAe,MAAM,YAAY,YAAY,CAAC;QAEpD,IAAI,CAAC,cAAc;YACjB,MAAM,YAAY,UAAU,CAAC,aAAa;YAC1C,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,sBAAsB,CAAC;YAE1D,gDAAgD;YAChD,MAAM,SAAS;gBACb,SAAS;gBACT,WAAW;oBACT;wBACE,QAAQ;wBACR,WAAW;4BAAE,KAAK;gCAAC;6BAAI;wBAAC;wBACxB,QAAQ;4BAAC;yBAAe;wBACxB,UAAU;4BAAC,CAAC,aAAa,EAAE,YAAY,EAAE,CAAC;yBAAC;oBAC7C;iBACD;YACH;YAEA,MAAM,YAAY,eAAe,CAAC,aAAa,KAAK,SAAS,CAAC;YAC9D,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC;QACtD,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,gBAAgB,CAAC;QACtD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;IAClD,mDAAmD;IACrD;AACF;AAEA,sBAAsB;AACtB,MAAM,kBAAkB,CAAC;IACvB,gBAAgB;IAChB,IACE,MAAM,CAAC,EAAE,KAAK,QACd,MAAM,CAAC,EAAE,KAAK,QACd,MAAM,CAAC,EAAE,KAAK,QACd,MAAM,CAAC,EAAE,KAAK,MACd;QACA,OAAO;IACT;IACA,iBAAiB;IACjB,IAAI,MAAM,CAAC,EAAE,KAAK,QAAQ,MAAM,CAAC,EAAE,KAAK,QAAQ,MAAM,CAAC,EAAE,KAAK,MAAM;QAClE,OAAO;IACT;IACA,gBAAgB;IAChB,MAAM,cAAc,OAAO,QAAQ,CAAC,SAAS,GAAG,KAAK,WAAW;IAChE,IAAI,YAAY,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC,UAAU;QACjE,OAAO;IACT;IACA,OAAO;AACT;AAEO,MAAM;IACX,aAAa,WACX,UAAkB,EAClB,QAAgB,EAChB,WAAmB,EACnB,SAAS,OAAO,EACC;QACjB,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,+BAA+B;YAC/B,IAAI;gBACF,MAAM,YAAY,YAAY,CAAC;gBAC/B,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,iBAAiB;gBACxB,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,MAAM,IAAI,MACR;YAEJ;YAEA,8BAA8B;YAC9B,MAAM;YAEN,+CAA+C;YAC/C,IAAI,WAAW;YACf,IAAI,gBAAgB,4BAA4B;gBAC9C,MAAM,eAAe,gBAAgB;gBACrC,IAAI,CAAC,cAAc;oBACjB,MAAM,IAAI,MAAM;gBAClB;gBACA,WAAW;YACb;YAEA,+CAA+C;YAC/C,MAAM,OAAO,IAAI;YACjB,MAAM,gBACJ,aAAa,kBACT,QACA,aAAa,cACb,QACA;YACN,MAAM,iBAAiB,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,SAAS,OAAO,CACtD,aACA,IACA,CAAC,EAAE,eAAe;YAEpB,MAAM,eAAe;gBACnB;gBACA,KAAK,WAAW,GAAG,QAAQ;gBAC3B,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;gBAC7C;aACD,CAAC,IAAI,CAAC;YAEP,QAAQ,GAAG,CAAC,sBAAsB;YAElC,qCAAqC;YACrC,MAAM,aAAa,IAAI,qGAAA,CAAA,WAAQ;YAC/B,WAAW,IAAI,CAAC;YAChB,WAAW,IAAI,CAAC;YAEhB,kBAAkB;YAClB,MAAM,YAAY,SAAS,CACzB,aACA,cACA,YACA,WAAW,MAAM,EACjB;gBACE,gBAAgB;YAClB;YAGF,iEAAiE;YACjE,MAAM,UACJ,gEAAoC;YACtC,MAAM,UAAU,GAAG,QAAQ,aAAa,EAAE,cAAc;YAExD,QAAQ,GAAG,CAAC,CAAC,oCAAoC,CAAC;YAClD,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc;YAC3C,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,SAAS;YACrC,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,SAAS;YACpC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,QAAQ,KAAK,CAAC,iBAAiB;gBAC7B,UAAU,YAAY,QAAQ;gBAC9B,MAAM,YAAY,IAAI;gBACtB,QAAQ,YAAY,MAAM;gBAC1B,QAAQ;YACV;YACA,QAAQ,KAAK,CAAC,mBAAmB;gBAC/B;gBACA;gBACA;gBACA,YAAY,WAAW,MAAM;YAC/B;YAEA,yDAAyD;YACzD,MAAM,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,UAAU;YAClE,QAAQ,GAAG,CAAC,sCAAsC;YAClD,OAAO;QACT;IACF;IAEA,aAAa,WAAW,OAAe,EAAiB;QACtD,IAAI;YACF,+BAA+B;YAC/B,MAAM,MAAM,IAAI,IAAI;YACpB,MAAM,aAAa,IAAI,QAAQ,CAAC,SAAS,CACvC,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,KAAK;YAGjC,MAAM,YAAY,YAAY,CAAC,aAAa;YAC5C,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,YAAY;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,UAAU,SAAS,OAAO,EAAE,UAAU,GAAG,EAAkB;QACtE,IAAI;YACF,MAAM,QAAe,EAAE;YACvB,MAAM,SAAS,YAAY,WAAW,CAAC,aAAa,GAAG,OAAO,CAAC,CAAC,EAAE;YAElE,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC3B,OAAO,EAAE,CAAC,QAAQ,CAAC;oBACjB,IAAI,MAAM,MAAM,GAAG,SAAS;wBAC1B,MAAM,WAAW,YAAY,MAAM,GAAG,UAAU;wBAChD,MAAM,OACJ,YAAY,IAAI,KAAK,CAAC,YAAY,MAAM,GAAG,MAAM,EAAE,IAC/C,CAAC,CAAC,EAAE,YAAY,IAAI,EAAE,GACtB;wBACN,MAAM,UAAU,GAAG,SAAS,GAAG,EAAE,YAAY,QAAQ,GAAG,KAAK,CAAC,EAAE,YAAY,CAAC,EAAE,IAAI,IAAI,EAAE;wBAEzF,MAAM,IAAI,CAAC;4BACT,MAAM,IAAI,IAAI;4BACd,MAAM,IAAI,IAAI;4BACd,cAAc,IAAI,YAAY;4BAC9B,KAAK;4BACL,UAAU,IAAI,IAAI,EAAE,MAAM,KAAK,SAAS;4BACxC,QAAQ,IAAI,IAAI,EAAE,MAAM,KAAK,MAAM,GAAG,CAAC,GAAG,KAAK,QAAQ;wBACzD;oBACF;gBACF;gBAEA,OAAO,EAAE,CAAC,OAAO,IAAM,QAAQ;gBAC/B,OAAO,EAAE,CAAC,SAAS,CAAC,MAAQ,OAAO;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,gBACX,UAAkB,EAClB,SAAS,IAAI,EACI;QACjB,IAAI;YACF,OAAO,MAAM,YAAY,kBAAkB,CACzC,aACA,YACA;QAEJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,aAAa,cAAc,UAAkB,EAAmB;QAC9D,IAAI;YACF,MAAM,SAAS,MAAM,YAAY,SAAS,CAAC,aAAa;YACxD,MAAM,SAAmB,EAAE;YAE3B,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC3B,OAAO,EAAE,CAAC,QAAQ,CAAC,QAAU,OAAO,IAAI,CAAC;gBACzC,OAAO,EAAE,CAAC,OAAO,IAAM,QAAQ,OAAO,MAAM,CAAC;gBAC7C,OAAO,EAAE,CAAC,SAAS;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAEA,qDAAqD;AACrD,mBAAmB,KAAK,CAAC,CAAC;IACxB,QAAQ,KAAK,CAAC,+CAA+C;AAC/D", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/api/posts/route.ts"], "sourcesContent": ["import { type NextRequest, NextResponse } from \"next/server\";\r\nimport { z } from \"zod\";\r\nimport connectDB from \"@/lib/mongodb\";\r\nimport Post from \"@/models/Post\";\r\nimport Category from \"@/models/Category\";\r\nimport { verifyAuth } from \"@/lib/auth\";\r\nimport { MinioService } from \"@/lib/minio\";\r\nimport { CardDescription } from \"@/components/ui/card\";\r\n\r\n// File validation constants\r\nconst MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB\r\nconst ALLOWED_MIME_TYPES = new Set([\r\n  \"image/jpeg\",\r\n  \"image/jpg\",\r\n  \"image/png\",\r\n  \"image/webp\",\r\n  \"image/gif\",\r\n  \"image/svg+xml\",\r\n]);\r\n\r\n// Validation schema\r\nconst postSchema = z.object({\r\n  title: z.string().min(1),\r\n  description: z.string().min(1),\r\n});\r\n\r\nfunction validateFileBuffer(buffer: Buffer, maxSizeMB = 5) {\r\n  if (buffer.length > maxSizeMB * 1024 * 1024) {\r\n    throw new Error(`File size exceeds ${maxSizeMB}MB`);\r\n  }\r\n}\r\n// Helper function to validate file\r\nconst validateFile = (file: File): void => {\r\n  if (!ALLOWED_MIME_TYPES.has(file.type)) {\r\n    throw new Error(\r\n      `Invalid file type: ${file.type}. Only images are allowed.`\r\n    );\r\n  }\r\n\r\n  if (file.size > MAX_FILE_SIZE) {\r\n    throw new Error(\r\n      `File size too large: ${(file.size / 1024 / 1024).toFixed(\r\n        2\r\n      )}MB. Maximum 10MB allowed.`\r\n    );\r\n  }\r\n};\r\n\r\n// Helper function to process base64 images in content\r\nasync function processContentImages(content: string): Promise<string> {\r\n  const base64ImageRegex =\r\n    /<img[^>]+src=\"data:image\\/([^;]+);base64,([^\"]+)\"[^>]*>/g;\r\n  let processedContent = content;\r\n  const matches = [...content.matchAll(base64ImageRegex)];\r\n\r\n  for (const match of matches) {\r\n    try {\r\n      const [fullMatch, imageType, base64Data] = match;\r\n      const buffer = Buffer.from(base64Data, \"base64\");\r\n      const fileName = `content-image-${Date.now()}.${imageType}`;\r\n      const contentType = `image/${imageType}`;\r\n\r\n      // Upload to MinIO\r\n      const imageUrl = await MinioService.uploadFile(\r\n        buffer,\r\n        fileName,\r\n        contentType,\r\n        \"content\"\r\n      );\r\n\r\n      // Replace base64 image with uploaded URL\r\n      processedContent = processedContent.replace(\r\n        fullMatch,\r\n        fullMatch.replace(/src=\"[^\"]*\"/, `src=\"${imageUrl}\"`)\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Error processing content image:\", error);\r\n      // Continue with other images if one fails\r\n    }\r\n  }\r\n\r\n  return processedContent;\r\n}\r\n\r\n// GET /api/posts - Get all posts with filtering and pagination\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    await connectDB();\r\n\r\n    const data = await Post.find()\r\n      .populate(\"categories\", \"name description isActive\")\r\n      .lean();\r\n\r\n    // const { searchParams } = new URL(request.url);\r\n    // const page = Number.parseInt(searchParams.get(\"page\") || \"1\");\r\n    // const limit = Number.parseInt(searchParams.get(\"limit\") || \"10\");\r\n    // const category = searchParams.get(\"category\");\r\n    // const tag = searchParams.get(\"tag\");\r\n    // const search = searchParams.get(\"search\");\r\n    // const status = searchParams.get(\"status\") || \"published\";\r\n    // const author = searchParams.get(\"author\");\r\n    // const isBlog = searchParams.get(\"isBlog\");\r\n\r\n    // // Build query\r\n    // const query: any = {};\r\n\r\n    // if (status) query.status = status;\r\n    // if (category) query.categories = { $in: [category] };\r\n    // if (tag) query.tags = { $in: [tag] };\r\n    // if (author) query.author = author;\r\n    // if (isBlog !== null) query.isBlog = isBlog === \"true\";\r\n\r\n    // if (search) {\r\n    //   query.$or = [\r\n    //     { title: { $regex: search, $options: \"i\" } },\r\n    //     { content: { $regex: search, $options: \"i\" } },\r\n    //     { metaTitle: { $regex: search, $options: \"i\" } },\r\n    //     { metaDescription: { $regex: search, $options: \"i\" } },\r\n    //   ];\r\n    // }\r\n\r\n    // const skip = (page - 1) * limit;\r\n\r\n    // const posts = await Post.find(query)\r\n    //   .sort({ publishedAt: -1, createdAt: -1 })\r\n    //   .skip(skip)\r\n    //   .limit(limit)\r\n    //   .lean();\r\n\r\n    // const total = await Post.countDocuments(query);\r\n\r\n    // return NextResponse.json({\r\n    //   success: true,\r\n    //   data: {\r\n    //     posts,\r\n    //     pagination: {\r\n    //       page,\r\n    //       limit,\r\n    //       total,\r\n    //       pages: Math.ceil(total / limit),\r\n    //       hasNext: page < Math.ceil(total / limit),\r\n    //       hasPrev: page > 1,\r\n    //     },\r\n    //   },\r\n    console.log(\"data\", data);\r\n    // });\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      data: {\r\n        posts: data,\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching posts:\", error);\r\n    return NextResponse.json(\r\n      { success: false, error: \"Failed to fetch posts\" },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// POST /api/posts - Create a new post with image handling\r\n// POST /api/posts\r\nexport async function POST(req: NextRequest) {\r\n  try {\r\n    const contentType = req.headers.get(\"content-type\") || \"\";\r\n    console.log(\"createingn, creating\");\r\n    if (!contentType.includes(\"application/json\")) {\r\n      return NextResponse.json(\r\n        { success: false, error: \"Unsupported Content-Type\" },\r\n        { status: 415 }\r\n      );\r\n    }\r\n\r\n    const body = await req.json();\r\n\r\n    const { data, bannerImageBase64 } = body;\r\n\r\n    if (!data || typeof data !== \"object\") {\r\n      return NextResponse.json(\r\n        { success: false, error: \"Missing or invalid 'data' field\" },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Validate post data\r\n    const validatedData = data;\r\n\r\n    // Convert and validate base64 image\r\n    let bannerImageBuffer: Buffer | null = null;\r\n    let mimeType = \"\";\r\n\r\n    if (bannerImageBase64 && typeof bannerImageBase64 === \"string\") {\r\n      const matches = bannerImageBase64.match(/^data:(.+);base64,(.+)$/);\r\n      if (!matches || matches.length !== 3) {\r\n        return NextResponse.json(\r\n          { success: false, error: \"Invalid base64 image format\" },\r\n          { status: 400 }\r\n        );\r\n      }\r\n\r\n      mimeType = matches[1];\r\n      const base64Data = matches[2];\r\n      bannerImageBuffer = Buffer.from(base64Data, \"base64\");\r\n\r\n      // Validate file size\r\n      validateFileBuffer(bannerImageBuffer);\r\n\r\n      // Determine file extension\r\n      const fileExtension = mimeType.split(\"/\")[1];\r\n      const fileName = `banner-${Date.now()}.${fileExtension}`;\r\n\r\n      // Upload to MinIO\r\n      const imageUrl = await MinioService.uploadFile(\r\n        bannerImageBuffer,\r\n        fileName,\r\n        mimeType,\r\n        \"banners\"\r\n      );\r\n      console.log(\"imageUrl\", imageUrl);\r\n\r\n      validatedData.banner = imageUrl;\r\n    }\r\n    console.log(\"validatedData\", validatedData);\r\n    await connectDB();\r\n    const newPost = await Post.create(validatedData);\r\n\r\n    return NextResponse.json(\r\n      {\r\n        success: true,\r\n        message: \"Post created successfully\",\r\n        post: newPost,\r\n      },\r\n      { status: 201 }\r\n    );\r\n  } catch (error: any) {\r\n    console.error(\"Error in POST /api/posts:\", error);\r\n    return NextResponse.json(\r\n      {\r\n        success: false,\r\n        error: error?.message || \"Internal server error\",\r\n      },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;AAGA;;;;;;AAGA,4BAA4B;AAC5B,MAAM,gBAAgB,KAAK,OAAO,MAAM,OAAO;AAC/C,MAAM,qBAAqB,IAAI,IAAI;IACjC;IACA;IACA;IACA;IACA;IACA;CACD;AAED,oBAAoB;AACpB,MAAM,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1B,OAAO,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACtB,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;AAC9B;AAEA,SAAS,mBAAmB,MAAc,EAAE,YAAY,CAAC;IACvD,IAAI,OAAO,MAAM,GAAG,YAAY,OAAO,MAAM;QAC3C,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,UAAU,EAAE,CAAC;IACpD;AACF;AACA,mCAAmC;AACnC,MAAM,eAAe,CAAC;IACpB,IAAI,CAAC,mBAAmB,GAAG,CAAC,KAAK,IAAI,GAAG;QACtC,MAAM,IAAI,MACR,CAAC,mBAAmB,EAAE,KAAK,IAAI,CAAC,0BAA0B,CAAC;IAE/D;IAEA,IAAI,KAAK,IAAI,GAAG,eAAe;QAC7B,MAAM,IAAI,MACR,CAAC,qBAAqB,EAAE,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CACvD,GACA,yBAAyB,CAAC;IAEhC;AACF;AAEA,sDAAsD;AACtD,eAAe,qBAAqB,OAAe;IACjD,MAAM,mBACJ;IACF,IAAI,mBAAmB;IACvB,MAAM,UAAU;WAAI,QAAQ,QAAQ,CAAC;KAAkB;IAEvD,KAAK,MAAM,SAAS,QAAS;QAC3B,IAAI;YACF,MAAM,CAAC,WAAW,WAAW,WAAW,GAAG;YAC3C,MAAM,SAAS,OAAO,IAAI,CAAC,YAAY;YACvC,MAAM,WAAW,CAAC,cAAc,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,WAAW;YAC3D,MAAM,cAAc,CAAC,MAAM,EAAE,WAAW;YAExC,kBAAkB;YAClB,MAAM,WAAW,MAAM,qHAAA,CAAA,eAAY,CAAC,UAAU,CAC5C,QACA,UACA,aACA;YAGF,yCAAyC;YACzC,mBAAmB,iBAAiB,OAAO,CACzC,WACA,UAAU,OAAO,CAAC,eAAe,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAExD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,0CAA0C;QAC5C;IACF;IAEA,OAAO;AACT;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,IAAI,GACzB,QAAQ,CAAC,cAAc,6BACvB,IAAI;QAEP,iDAAiD;QACjD,iEAAiE;QACjE,oEAAoE;QACpE,iDAAiD;QACjD,uCAAuC;QACvC,6CAA6C;QAC7C,4DAA4D;QAC5D,6CAA6C;QAC7C,6CAA6C;QAE7C,iBAAiB;QACjB,yBAAyB;QAEzB,qCAAqC;QACrC,wDAAwD;QACxD,wCAAwC;QACxC,qCAAqC;QACrC,yDAAyD;QAEzD,gBAAgB;QAChB,kBAAkB;QAClB,oDAAoD;QACpD,sDAAsD;QACtD,wDAAwD;QACxD,8DAA8D;QAC9D,OAAO;QACP,IAAI;QAEJ,mCAAmC;QAEnC,uCAAuC;QACvC,8CAA8C;QAC9C,gBAAgB;QAChB,kBAAkB;QAClB,aAAa;QAEb,kDAAkD;QAElD,6BAA6B;QAC7B,mBAAmB;QACnB,YAAY;QACZ,aAAa;QACb,oBAAoB;QACpB,cAAc;QACd,eAAe;QACf,eAAe;QACf,yCAAyC;QACzC,kDAAkD;QAClD,2BAA2B;QAC3B,SAAS;QACT,OAAO;QACP,QAAQ,GAAG,CAAC,QAAQ;QACpB,MAAM;QAEN,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,OAAO;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAwB,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF;AAIO,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,MAAM,cAAc,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB;QACvD,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,YAAY,QAAQ,CAAC,qBAAqB;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA2B,GACpD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,IAAI,IAAI;QAE3B,MAAM,EAAE,IAAI,EAAE,iBAAiB,EAAE,GAAG;QAEpC,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;YACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAkC,GAC3D;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,MAAM,gBAAgB;QAEtB,oCAAoC;QACpC,IAAI,oBAAmC;QACvC,IAAI,WAAW;QAEf,IAAI,qBAAqB,OAAO,sBAAsB,UAAU;YAC9D,MAAM,UAAU,kBAAkB,KAAK,CAAC;YACxC,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;gBACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAA8B,GACvD;oBAAE,QAAQ;gBAAI;YAElB;YAEA,WAAW,OAAO,CAAC,EAAE;YACrB,MAAM,aAAa,OAAO,CAAC,EAAE;YAC7B,oBAAoB,OAAO,IAAI,CAAC,YAAY;YAE5C,qBAAqB;YACrB,mBAAmB;YAEnB,2BAA2B;YAC3B,MAAM,gBAAgB,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE;YAC5C,MAAM,WAAW,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,eAAe;YAExD,kBAAkB;YAClB,MAAM,WAAW,MAAM,qHAAA,CAAA,eAAY,CAAC,UAAU,CAC5C,mBACA,UACA,UACA;YAEF,QAAQ,GAAG,CAAC,YAAY;YAExB,cAAc,MAAM,GAAG;QACzB;QACA,QAAQ,GAAG,CAAC,iBAAiB;QAC7B,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,MAAM,UAAU,MAAM,uHAAA,CAAA,UAAI,CAAC,MAAM,CAAC;QAElC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,SAAS;YACT,MAAM;QACR,GACA;YAAE,QAAQ;QAAI;IAElB,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,OAAO,OAAO,WAAW;QAC3B,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}