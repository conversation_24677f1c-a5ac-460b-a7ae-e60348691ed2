"use client";

import { Alert<PERSON>riangle, Home, ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";

export default function UnauthorizedPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="max-w-lg w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="mb-6">
          <AlertTriangle className="h-16 w-16 text-amber-500 mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Access Denied
          </h1>
          <p className="text-lg text-gray-600">
            You don&apos;t have permission to access this resource.
          </p>
        </div>

        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-gray-900 mb-2">
            Access Information
          </h3>
          {isAuthenticated ? (
            <div className="text-sm text-gray-600 space-y-1">
              <p>
                <span className="font-medium">User:</span>{" "}
                {user?.username || user?.email}
              </p>
              <p>
                <span className="font-medium">Role:</span> {user?.role}
              </p>
              <p>
                <span className="font-medium">Required:</span> Admin access
              </p>
            </div>
          ) : (
            <p className="text-sm text-gray-600">
              You need to be logged in as an administrator to access this page.
            </p>
          )}
        </div>

        <div className="space-y-3">
          {!isAuthenticated ? (
            <button
              onClick={() => router.push("/auth")}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Sign In
            </button>
          ) : (
            <div className="text-sm text-gray-600 mb-4">
              <p>
                If you believe you should have access to this page, please
                contact your administrator.
              </p>
            </div>
          )}

          <div className="flex space-x-3">
            <button
              onClick={() => router.back()}
              className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors font-medium flex items-center justify-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </button>
            <button
              onClick={() => router.push("/")}
              className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium flex items-center justify-center"
            >
              <Home className="h-4 w-4 mr-2" />
              Home
            </button>
          </div>
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200">
          <p className="text-xs text-gray-500">
            If you&apos;re experiencing issues, please contact support or try
            refreshing the page.
          </p>
        </div>
      </div>
    </div>
  );
}
