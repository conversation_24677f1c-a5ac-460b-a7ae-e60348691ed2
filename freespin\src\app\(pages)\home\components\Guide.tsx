import React from "react";
import PulseCircle from "./PulseCircle";
const Guide = () => {
  return (
    <section className=" bg-black px-7">
      <div className="my-auto container mx-auto px-3 md:px-[4rem] grid grid-cols-1 md:grid-cols-2 gap-y-6 gap-x-4">
        <div>
          <PulseCircle />
        </div>
        <div className="text-white py-18 space-y-4">
          <h3 className="font-bold  md:text-[30px] sm:text-3xl  text-2xl">
            {
              "คู่มือของ FreeSpin168 ในการค้นหาคาสิโนออนไลน์ที่เชื่อถือได้มากที่สุดในประเทศไทย"
            }
          </h3>
          <div className="space-y-3">
            <p className="text-justify text-sm">
              {
                "ด้วยคาสิโนออนไลน์จำนวนมากที่มีอยู่จึงจำเป็นที่จะต้องรู้วิธีการเลือกที่น่าเชื่อถืออย่างแท้จริง "
              }
            </p>
            <p className="text-justify text-sm">
              {
                "แพลตฟอร์มคาสิโนที่เชื่อถือได้ควรใช้เกตเวย์การชำระเงินที่ปลอดภัยและมีชื่อเสียงเพื่อให้แน่ใจว่าเงินฝากและการถอนอย่างปลอดภัย"
              }
            </p>
            <p className="text-justify text-sm">
              {
                "นอกจากนี้เว็บไซต์ควรใช้พลังงานจากผู้ให้บริการซอฟต์แวร์ที่เชื่อถือได้ "
              }
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Guide;
