import type { NextConfig } from "next";
// import createNextIntlPlugin from "next-intl/plugin";

const nextConfig: NextConfig = {
  // output: "standalone",
  images: {
    remotePatterns: [
      {
        protocol: "http",
        hostname: "localhost",
        port: "3000",
        pathname: "/api/uploads/**",
      },
      {
        protocol: "http",
        hostname: "localhost",
        port: "3001",
        pathname: "/api/uploads/**",
      },
      {
        protocol: "https",
        hostname: "s3.webstudiomatrix.com",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "freespin168.asia",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "www.freespin168.asia",
        pathname: "/**",
      },
    ],
  },
};

// const withNextIntl = createNextIntlPlugin();
// export default withNextIntl(nextConfig);

export default nextConfig;
