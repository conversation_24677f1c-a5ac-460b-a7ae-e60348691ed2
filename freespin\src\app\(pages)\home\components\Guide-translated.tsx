import React from "react";
import PulseCircle from "./PulseCircle";
const Guide = () => {
  return (
    <section className=" bg-black px-7">
      <div className="my-auto container grid grid-cols-1 md:grid-cols-2 gap-y-6 gap-x-4">
        <div className="my-auto container grid grid-cols-1 md:grid-cols-2 gap-y-6 gap-x-4">
          <div>
            <PulseCircle />
          </div>
          <div className="text-white py-18 space-y-4">
            <h3 className="font-bold  md:text-[30px] sm:text-3xl  text-2xl">
              คู่มือของ Freespin168
              สำหรับการค้นหาคาสิโนออนไลน์ที่น่าเชื่อถือที่สุดในประเทศไทย
            </h3>
            <div className="space-y-3">
              <p className="text-justify text-sm">
                ในปัจจุบันมีคาสิโนออนไลน์ให้เลือกมากมาย
                การรู้วิธีเลือกแพลตฟอร์มที่เชื่อถือได้จึงเป็นสิ่งสำคัญ ที่
                Freespin168 เราเชื่อว่า
                คาสิโนออนไลน์ที่น่าเชื่อถือควรมีมาตรฐานที่ชัดเจน เช่น
                ต้องมีใบอนุญาตการพนันที่ถูกต้องจากหน่วยงานที่ได้รับการยอมรับ
                เช่น Isle of Man หรือ MGA
              </p>
              <p className="text-justify text-sm">
                แพลตฟอร์มคาสิโนที่เชื่อถือได้ควรใช้ระบบการชำระเงินที่ปลอดภัยและมีชื่อเสียง
                เพื่อให้มั่นใจว่าการฝากและถอนเงินเป็นไปอย่างปลอดภัย
              </p>
              <p className="text-justify text-sm">
                นอกจากนี้
                เว็บไซต์ควรให้บริการโดยผู้ให้บริการซอฟต์แวร์ที่เชื่อถือได้
                เพื่อรับรองความยุติธรรมในการเล่นเกม
                และมอบเกมคุณภาพหลากหลายที่พัฒนาด้วยประสบการณ์ในอุตสาหกรรมมาอย่างยาวนาน
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Guide;
