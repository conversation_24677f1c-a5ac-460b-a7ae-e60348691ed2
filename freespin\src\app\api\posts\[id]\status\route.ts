import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import Post from "@/models/Post";
import { verifyAuth } from "@/lib/auth";

// PATCH /api/posts/[id]/status - Update post status
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify authentication and admin role
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, error: authResult.error },
        { status: 401 }
      );
    }

    if (authResult.user?.role !== "admin") {
      return NextResponse.json(
        { success: false, error: "Admin access required" },
        { status: 403 }
      );
    }

    await connectDB();

    // Await the params Promise
    const { id } = await params;
    const body = await request.json();
    const { status } = body;

    // Validate status
    if (!["draft", "published", "archived"].includes(status)) {
      return NextResponse.json(
        { success: false, error: "Invalid status value" },
        { status: 400 }
      );
    }

    // Find and update the post
    const post = await Post.findById(id);

    if (!post) {
      return NextResponse.json(
        { success: false, error: "Post not found" },
        { status: 404 }
      );
    }

    // Update status and related fields
    post.status = status;

    if (status === "published") {
      post.isPublished = true;
      if (!post.publishedAt) {
        post.publishedAt = new Date();
      }
    } else {
      post.isPublished = false;
      if (status === "draft") {
        post.publishedAt = null;
      }
    }

    await post.save();

    return NextResponse.json({
      success: true,
      message: `Post ${status} successfully`,
      data: { post },
    });
  } catch (error) {
    console.error("Error updating post status:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update post status" },
      { status: 500 }
    );
  }
}
