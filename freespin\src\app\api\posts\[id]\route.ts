import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import connectDB from "@/lib/mongodb";
import Post from "@/models/Post";
import Category from "@/models/Category";
import { verifyAuth } from "@/lib/auth";

const updatePostSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  content: z.string().min(1).optional(),
  excerpt: z.string().max(300).optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  status: z.enum(["draft", "published", "archived"]).optional(),
  featuredImage: z.string().url().optional(),
  seoTitle: z.string().max(60).optional(),
  seoDescription: z.string().max(160).optional(),
});

// GET /api/posts/[id] - Get a specific post
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    // Await the params Promise
    const { id } = await params;
    console.log("id", id);

    let post = await Post.findOne({ slug: id })
      .populate("categories", "name isActive color description")
      .lean();

    if (!post) {
      return NextResponse.json(
        { success: false, error: "Post not found" },
        { status: 404 }
      );
    }
    // Increment view count
    await Post.findOneAndUpdate({ slug: id }, { $inc: { views: 1 } });

    return NextResponse.json({
      success: true,
      data: { post },
    });
  } catch (error) {
    console.error("Error fetching post:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch post" },
      { status: 500 }
    );
  }
}

// PUT /api/posts/[id] - Update a specific post
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    // Await the params Promise
    const { id } = await params;

    // Verify authentication
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, error: authResult.error },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = updatePostSchema.parse(body);

    // Find the post
    const post = await Post.findById(id);

    if (!post) {
      return NextResponse.json(
        { success: false, error: "Post not found" },
        { status: 404 }
      );
    }

    // Check if user owns the post or is admin
    // Add your authorization logic here

    // Update the post
    const updatedPost = await Post.findByIdAndUpdate(id, validatedData, {
      new: true,
      runValidators: true,
    })
      .populate("author", "username firstName lastName avatar")
      .populate("category", "name slug color");

    return NextResponse.json({
      success: true,
      message: "Post updated successfully",
      data: { post: updatedPost },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error updating post:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update post" },
      { status: 500 }
    );
  }
}

// DELETE /api/posts/[id] - Delete a specific post
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();

    // Await the params Promise
    const { id } = await params;

    // Verify authentication
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, error: authResult.error },
        { status: 401 }
      );
    }

    // Find the post
    const post = await Post.findById(id);

    if (!post) {
      return NextResponse.json(
        { success: false, error: "Post not found" },
        { status: 404 }
      );
    }

    // Delete the post
    await Post.findByIdAndDelete(id);

    return NextResponse.json({
      success: true,
      message: "Post deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting post:", error);
    return NextResponse.json(
      { success: false, error: "Failed to delete post" },
      { status: 500 }
    );
  }
}

// Second

// import { type NextRequest, NextResponse } from "next/server";
// import { z } from "zod";
// import connectDB from "@/lib/mongodb";
// import Post from "@/models/Post";
// import { verifyAuth } from "@/lib/auth";

// const updatePostSchema = z.object({
//   title: z.string().min(1).max(200).optional(),
//   content: z.string().min(1).optional(),
//   slug: z.string().optional(),
//   canonicalUrl: z.string().url().optional().or(z.literal("")),
//   isBlog: z.boolean().optional(),
//   categories: z.array(z.string()).optional(),
//   tags: z.array(z.string()).optional(),
//   metaTitle: z.string().max(60).optional(),
//   metaDescription: z.string().max(160).optional(),
//   metaKeywords: z.string().max(200).optional(),
//   banner: z
//     .object({
//       title: z.string().max(100).optional(),
//       description: z.string().max(500).optional(),
//       image: z.string().optional(),
//       altText: z.string().max(100).optional(),
//     })
//     .optional(),
//   status: z.enum(["draft", "published", "archived"]).optional(),
// });

// // GET /api/posts/[id] - Get a specific post
// export async function GET(
//   request: NextRequest,
//   { params }: { params: Promise<{ id: string }> }
// ) {
//   try {
//     await connectDB();

//     // Await the params Promise
//     const { id } = await params;

//     let post = await Post.findOne({ slug: id })
//       .populate("author", "username firstName lastName avatar bio")
//       .populate("category", "name slug color description")
//       .lean();

//     if (!post) {
//       return NextResponse.json(
//         { success: false, error: "Post not found" },
//         { status: 404 }
//       );
//     }
//     // Increment view count
//     await Post.findOneAndUpdate({ slug: id }, { $inc: { views: 1 } });

//     return NextResponse.json({
//       success: true,
//       data: { post },
//     });
//   } catch (error) {
//     console.error("Error fetching post:", error);
//     return NextResponse.json(
//       { success: false, error: "Failed to fetch post" },
//       { status: 500 }
//     );
//   }
// }

// // PUT /api/posts/[id] - Update a specific post
// export async function PUT(
//   request: NextRequest,
//   { params }: { params: Promise<{ id: string }> }
// ) {
//   try {
//     await connectDB();

//     // Await the params Promise
//     const { id } = await params;

//     // Verify authentication
//     const authResult = await verifyAuth(request);
//     if (!authResult.success) {
//       return NextResponse.json(
//         { success: false, error: authResult.error },
//         { status: 401 }
//       );
//     }

//     const body = await request.json();
//     const validatedData = updatePostSchema.parse(body);

//     // Find the post
//     const post = await Post.findById(id);

//     if (!post) {
//       return NextResponse.json(
//         { success: false, error: "Post not found" },
//         { status: 404 }
//       );
//     }

//     // Check if user owns the post or is admin
//     // Add your authorization logic here

//     // Update the post
//     const updatedPost = await Post.findByIdAndUpdate(id, validatedData, {
//       new: true,
//       runValidators: true,
//     })
//       .populate("author", "username firstName lastName avatar")
//       .populate("category", "name slug color");

//     return NextResponse.json({
//       success: true,
//       message: "Post updated successfully",
//       data: { post: updatedPost },
//     });
//   } catch (error) {
//     if (error instanceof z.ZodError) {
//       return NextResponse.json(
//         { success: false, error: "Validation failed", details: error.errors },
//         { status: 400 }
//       );
//     }

//     console.error("Error updating post:", error);
//     return NextResponse.json(
//       { success: false, error: "Failed to update post" },
//       { status: 500 }
//     );
//   }
// }

// // DELETE /api/posts/[id] - Delete a specific post
// export async function DELETE(
//   request: NextRequest,
//   { params }: { params: Promise<{ id: string }> }
// ) {
//   try {
//     await connectDB();

//     // Await the params Promise
//     const { id } = await params;

//     // Verify authentication
//     const authResult = await verifyAuth(request);
//     if (!authResult.success) {
//       return NextResponse.json(
//         { success: false, error: authResult.error },
//         { status: 401 }
//       );
//     }

//     // Find the post
//     const post = await Post.findById(id);

//     if (!post) {
//       return NextResponse.json(
//         { success: false, error: "Post not found" },
//         { status: 404 }
//       );
//     }

//     // Delete the post
//     await Post.findByIdAndDelete(id);

//     return NextResponse.json({
//       success: true,
//       message: "Post deleted successfully",
//     });
//   } catch (error) {
//     console.error("Error deleting post:", error);
//     return NextResponse.json(
//       { success: false, error: "Failed to delete post" },
//       { status: 500 }
//     );
//   }
// }
