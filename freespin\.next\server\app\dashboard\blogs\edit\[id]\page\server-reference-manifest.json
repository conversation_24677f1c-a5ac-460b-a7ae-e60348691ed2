{"node": {"0083a550d7711b950e0380eaf183245f5ff600cd2d": {"workers": {"app/dashboard/blogs/edit/[id]/page": {"moduleId": "[project]/.next-internal/server/app/dashboard/blogs/edit/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/dashboard/blogs/edit/[id]/page": "action-browser"}}, "00aaf3229ad66ce8c0e05f422008f0d7c0448605e4": {"workers": {"app/dashboard/blogs/edit/[id]/page": {"moduleId": "[project]/.next-internal/server/app/dashboard/blogs/edit/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/dashboard/blogs/edit/[id]/page": "action-browser"}}, "70ca012623c33fa3ea1bb858d64dc26cce9ffda769": {"workers": {"app/dashboard/blogs/edit/[id]/page": {"moduleId": "[project]/.next-internal/server/app/dashboard/blogs/edit/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/dashboard/blogs/edit/[id]/page": "action-browser"}}, "60e82a0f1ab060d924d48393e65f78bcf87b8015d6": {"workers": {"app/dashboard/blogs/edit/[id]/page": {"moduleId": "[project]/.next-internal/server/app/dashboard/blogs/edit/[id]/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/dashboard/blogs/edit/[id]/page": "action-browser"}}}, "edge": {}}