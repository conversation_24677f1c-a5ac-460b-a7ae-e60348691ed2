(()=>{var e={};e.id=8159,e.ids=[8159],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8285:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>p});var s=t(65239),o=t(48088),i=t(88170),n=t.n(i),a=t(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let p={children:["",{children:["dashboard",{children:["blogs",{children:["add",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,39417)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\dashboard\\blogs\\add\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\dashboard\\blogs\\add\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/dashboard/blogs/add/page",pathname:"/dashboard/blogs/add",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},8745:(e,r,t)=>{Promise.resolve().then(t.bind(t,15316))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15316:(e,r,t)=>{"use strict";t.d(r,{default:()=>o});var s=t(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call MultiSelect() from the server but MultiSelect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\dashboard\\blogs\\components\\BlogPostEditor.tsx","MultiSelect");let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Web Studio Nepal\\\\FreeSpin168\\\\freespin\\\\src\\\\app\\\\dashboard\\\\blogs\\\\components\\\\BlogPostEditor.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\dashboard\\blogs\\components\\BlogPostEditor.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39417:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(37413);t(61120);var o=t(15316);let i=()=>(0,s.jsx)(o.default,{})},41204:e=>{"use strict";e.exports=require("string_decoder")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66136:e=>{"use strict";e.exports=require("timers")},73024:e=>{"use strict";e.exports=require("node:fs")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},99017:(e,r,t)=>{Promise.resolve().then(t.bind(t,28932))}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,6944,1658,9733,40,4247,5021,7042,6356,7116,5820,8932],()=>t(8285));module.exports=s})();