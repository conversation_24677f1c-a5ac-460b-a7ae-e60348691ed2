import { NextRequest, NextResponse } from "next/server";
import { MinioService } from "@/lib/minio";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { path: filePath } = await params;
    const fullPath = filePath.join("/");

    console.log("=== IMAGE PROXY REQUEST ===");
    console.log("Requested path:", fullPath);
    console.log("Full request URL:", request.url);

    // Get presigned URL from MinIO and redirect
    try {
      const presignedUrl = await MinioService.getPresignedUrl(fullPath, 3600);
      console.log("Generated presigned URL:", presignedUrl);
      return NextResponse.redirect(presignedUrl);
    } catch (minioError) {
      console.error("MinIO failed to generate presigned URL:", minioError);

      // Try to get the file directly from MinIO and stream it
      try {
        const fileStream = await MinioService.getFileStream(fullPath);
        const ext = fullPath.split(".").pop()?.toLowerCase();

        let contentType = "application/octet-stream";
        switch (ext) {
          case "jpg":
          case "jpeg":
            contentType = "image/jpeg";
            break;
          case "png":
            contentType = "image/png";
            break;
          case "gif":
            contentType = "image/gif";
            break;
          case "webp":
            contentType = "image/webp";
            break;
          case "svg":
            contentType = "image/svg+xml";
            break;
        }

        return new NextResponse(fileStream, {
          headers: {
            "Content-Type": contentType,
            "Cache-Control": "public, max-age=31536000, immutable",
          },
        });
      } catch (streamError) {
        console.error("Failed to stream file from MinIO:", streamError);
        return new NextResponse("Image not found", { status: 404 });
      }
    }
  } catch (error) {
    console.error("Error serving image:", error);
    return new NextResponse("Internal server error", { status: 500 });
  }
}
