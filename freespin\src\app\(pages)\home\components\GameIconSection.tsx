"use client";

import IconCard from "@/app/shared/IconCard";
import React from "react";
import { IconFour, IconOne, IconThree, IconTwo } from "./IconList";

const GameIconSection = () => {
  const iconDetails = [
    {
      title: "การเล่นที่ดึงดูดใจอย่างลึกซึ้ง",
      text: "ดำดิ่งสู่การเล่นเกมที่สวยงาม ลื่นไหล และดื่มด่ำ ที่ทำให้ผู้เล่นอยากกลับมาเล่นอีกครั้ง",
      itemIcon: IconOne,
    },
    {
      title: "กลไกเกมอัจฉริยะ",
      text: "ออกแบบด้วยตรรกะอันล้ำสมัยและรูปแบบการเล่นที่ให้รางวัล เพื่อสร้างประสบการณ์ที่สดใหม่ ยุติธรรม และสนุกสนาน",
      itemIcon: IconTwo,
    },
    {
      title: "ความหลากหลายของเกมไม่รู้จบ",
      text: "ตั้งแต่สล็อตคลาสสิกไปจนถึงโต๊ะคาสิโนแบบสด - สำรวจเกมคาสิโนหลากหลายได้ในที่เดียว",
      itemIcon: IconThree,
    },
    {
      title: "แก่นของเกมยุคใหม่",
      text: "ขับเคลื่อนด้วยเทคโนโลยีล้ำสมัย เพื่อการทำงานแบบเรียลไทม์ กราฟิกที่เคลื่อนไหวได้อย่างลื่นไหล และการเล่นที่ปลอดภัย",
      itemIcon: IconFour,
    },
  ];
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 container mx-auto px-3 md:px-[4rem] py-18">
      {iconDetails.map((item, index) => (
        <IconCard
          key={index}
          IconName={item.itemIcon}
          title={item.title}
          text={item.text}
        />
      ))}
    </div>
  );
};

export default GameIconSection;
