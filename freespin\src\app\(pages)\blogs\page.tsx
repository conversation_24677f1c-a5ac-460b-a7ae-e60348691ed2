import React from "react";
import { BlogsPageClient } from "./components/BlogsPageClient";
import { PublicBlogPost } from "@/client_apis/api/blog";
import connectDB from "@/lib/mongodb";
import Post from "@/models/Post";
import Category from "@/models/Category";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Casino News & Gaming Blog | Latest Updates & Reviews – FreeSpin168",
  alternates: {
    canonical: "https://www.freespin168.asia/blogs",
  },
  description:
    "Stay updated with the latest casino news, game reviews, and industry insights. Read expert analysis and tips from FreeSpin168's gaming blog.",
  keywords: [
    "casino news",
    "gaming blog",
    "casino reviews",
    "online casino updates",
  ],
};

interface BlogsPageProps {
  searchParams: Promise<{
    page?: string;
  }>;
}

const page = async ({ searchParams }: BlogsPageProps) => {
  try {
    // Connect to database directly
    await connectDB();

    // Debug: Check all posts first
    const allPosts = await Post.find({ isBlog: true }).lean();
    console.log("=== BLOGS PAGE DEBUG ===");
    console.log("Total blog posts in database:", allPosts.length);
    console.log(
      "Posts by status:",
      allPosts.reduce((acc, post) => {
        acc[post.status] = (acc[post.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    );
    console.log(
      "Published posts:",
      allPosts.filter((p) => p.status === "published" && p.isPublished).length
    );

    // Fetch latest posts directly from database (initial 6 posts)
    const latestPostsData = await Post.find({
      status: "published",
      isPublished: true,
      isBlog: true,
    })
      .populate("categories", "name description")
      .sort({ publishedAt: -1, createdAt: -1 })
      .limit(6)
      .lean();

    console.log("Latest posts found:", latestPostsData.length);

    // Fetch all explore posts (client-side pagination will handle the rest)
    const explorePostsData = await Post.find({
      status: "published",
      isPublished: true,
      isBlog: true,
    })
      .sort({ publishedAt: -1, createdAt: -1 })
      .lean();

    // Fetch top news posts
    const topNewsPostsData = await Post.find({
      status: "published",
      isPublished: true,
      isBlog: true,
      isTopNews: true,
    })
      .sort({ publishedAt: -1, createdAt: -1 })
      .limit(5) // Limit to 5 top news posts
      .lean();

    // Serialize posts for client components
    const serializePosts = (posts: any[]): PublicBlogPost[] => {
      return posts.map((post) => ({
        _id: post._id.toString(),
        title: post.title,
        description:
          post.excerpt ||
          post.description ||
          post.content?.substring(0, 200) ||
          "",
        banner: post.banner || "",
        slug: post?.slug || post?._id,
        // categories: post.categories || [], // categories is already an array of strings
        categories: post.categories?.map((cat: any) => cat.name) || [],
        tags: post.tags || [],
        publishedAt:
          post.publishedAt?.toISOString() ||
          post.createdAt?.toISOString() ||
          "",
        createdAt: post.createdAt?.toISOString() || "",
        readTime: post.readTime || 5,
        views: post.views || 0,

        metaTitle: post.metaTitle || post.title || "",
        metaDescription:
          post.metaDescription || post.excerpt || post.description || "",
      }));
    };

    const latestPosts = serializePosts(latestPostsData);
    const explorePosts = serializePosts(explorePostsData);
    const topNewsPosts = serializePosts(topNewsPostsData);

    return (
      <BlogsPageClient
        initialLatestPosts={latestPosts}
        explorePosts={explorePosts}
        topNewsPosts={topNewsPosts}
      />
    );
  } catch (error) {
    console.error("Error fetching posts:", error);

    // Return empty state on error
    return (
      <BlogsPageClient
        initialLatestPosts={[]}
        explorePosts={[]}
        topNewsPosts={[]}
      />
    );
  }
};

export default page;
