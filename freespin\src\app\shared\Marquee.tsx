// "use client";
// import { motion, useAnimation } from "framer-motion";
// import { useEffect, useRef } from "react";

// interface MarqueeProps {
//   images: string[];
//   speed?: number;
//   direction?: "left" | "right";
// }

// const Marquee: React.FC<MarqueeProps> = ({
  //   images,
//   speed = 100,
//   direction = "left",
// }) => {
//   const controls = useAnimation();
//   const containerRef = useRef<HTMLDivElement>(null);

//   useEffect(() => {
//     const containerWidth = containerRef.current?.scrollWidth || 0;
//     const animationDuration = containerWidth / speed;

//     const startAnimation = async () => {
//       await controls.start({
//         x: direction === "left" ? -containerWidth / 2 : containerWidth / 2,
//         transition: {
//           x: {
//             repeat: Infinity,
//             repeatType: "loop",
//             duration: animationDuration,
//             ease: "linear",
//           },
//         },
//       });
//     };

//     startAnimation();
//   }, [controls, direction, speed]);

//   return (
//     <div className="w-full overflow-hidden bg-gray-100">
//       <motion.div
//         ref={containerRef}
//         className="flex"
//         animate={controls}
//         style={{ width: "max-content" }}
//       >
//         {[...images, ...images].map((src, index) => (
//           <div
//             key={index}
//             className="flex-shrink-0 mx-4"
//             style={{ width: 200, height: 200 }}
//           >
//             <img
//               src={src}
//               alt={`Marquee image ${index + 1}`}
//               className="w-full h-full object-cover rounded-lg"
//             />
//           </div>
//         ))}
//       </motion.div>
//     </div>
//   );
// };

// export default Marquee;

// Second

"use client";
import { motion, useAnimation } from "framer-motion";
import { useEffect, useRef } from "react";

;
interface MarqueeProps {
  images: string[];
  speed?: number;
  direction?: "left" | "right";
  className?: string; // Add className prop for custom styling
}

const Marquee: React.FC<MarqueeProps> = ({
  images,
  speed = 100,
  direction = "left",
  className = "",
}) => {
  const controls = useAnimation();
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const containerWidth = containerRef.current?.scrollWidth || 1000; // Fallback width
    const animationDuration = containerWidth / speed;

    const startAnimation = async () => {
      await controls.start({
        x: direction === "left" ? -containerWidth / 2 : containerWidth / 2,
        transition: {
          x: {
            repeat: Infinity,
            repeatType: "loop",
            duration: animationDuration,
            ease: "linear",
          },
        },
      });
    };

    startAnimation();
  }, [controls, direction, speed]);

  return (
    <div className={`w-full overflow-hidden bg-transparent ${className}`}>
      <motion.div
        ref={containerRef}
        className="flex"
        animate={controls}
        style={{ width: "max-content" }}
      >
        {[...images, ...images].map((src, index) => (
          <div
            key={index}
            className="flex-shrink-0 mx-4"
            style={{ width: 350, height: 350, position: "relative" }}
          >
            <img
              src={src}
              alt={`Marquee image ${index + 1}`}
              className="w-full h-full object-cover rounded-lg transform -rotate-90 opacity-0.9"
            />
          </div>
        ))}
      </motion.div>
    </div>
  );
};

export default Marquee;
