/* Lexical Editor Styles */
.lexical-editor {
  font-family: inherit;
  font-size: 16px;
  line-height: 1.6;
  color: inherit;
  direction: ltr !important;
  text-align: left !important;
  writing-mode: horizontal-tb !important;
  unicode-bidi: normal !important;
}

/* Force horizontal text layout - but only for content area, not toolbar */
.lexical-content-area [contenteditable],
.lexical-content-area [data-lexical-editor] {
  direction: ltr !important;
  text-align: left !important;
  writing-mode: horizontal-tb !important;
  unicode-bidi: normal !important;
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
}

/* Ensure toolbar stays horizontal */
.lexical-editor .flex {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
}

/* Toolbar specific styling */
.lexical-editor .flex button,
.lexical-editor .flex select {
  display: inline-flex !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Toolbar container */
.lexical-editor > div:first-child {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
  align-items: center !important;
}

/* Specific styling for content area only */
.lexical-content-area [contenteditable] {
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  text-align: left !important;
  direction: ltr !important;
  writing-mode: horizontal-tb !important;
  unicode-bidi: normal !important;
  font-family: inherit !important;
  font-size: inherit !important;
  line-height: inherit !important;
  color: inherit !important;
  background: transparent !important;
  border: none !important;
  outline: none !important;
  resize: none !important;
  display: block !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

/* Content area paragraphs and text elements */
.lexical-content-area [contenteditable] p,
.lexical-content-area [contenteditable] div,
.lexical-content-area [contenteditable] span {
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  text-align: left !important;
  direction: ltr !important;
  writing-mode: horizontal-tb !important;
  unicode-bidi: normal !important;
}
