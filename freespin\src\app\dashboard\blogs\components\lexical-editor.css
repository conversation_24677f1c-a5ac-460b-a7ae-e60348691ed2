/* Lexical Editor Styles */
.lexical-editor {
  font-family: inherit;
  font-size: 16px;
  line-height: 1.6;
  color: inherit;
  direction: ltr !important;
  text-align: left !important;
  writing-mode: horizontal-tb !important;
  unicode-bidi: normal !important;
}

/* Force horizontal text layout */
.lexical-editor,
.lexical-editor *,
.lexical-editor [contenteditable],
.lexical-editor [data-lexical-editor] {
  direction: ltr !important;
  text-align: left !important;
  writing-mode: horizontal-tb !important;
  unicode-bidi: normal !important;
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  display: block !important;
}

.lexical-editor [contenteditable] {
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  text-align: left !important;
  direction: ltr !important;
  writing-mode: horizontal-tb !important;
  unicode-bidi: normal !important;
  font-family: inherit !important;
  font-size: inherit !important;
  line-height: inherit !important;
  color: inherit !important;
  background: transparent !important;
  border: none !important;
  outline: none !important;
  resize: none !important;
  display: block !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.lexical-editor p {
  margin: 0 0 1rem 0;
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  text-align: left !important;
  direction: ltr !important;
  writing-mode: horizontal-tb !important;
  unicode-bidi: normal !important;
}

.lexical-editor div {
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  text-align: left !important;
  direction: ltr !important;
  writing-mode: horizontal-tb !important;
  unicode-bidi: normal !important;
}

/* Ensure text flows horizontally */
.lexical-editor * {
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  text-align: left !important;
  direction: ltr !important;
  writing-mode: horizontal-tb !important;
  unicode-bidi: normal !important;
}

/* Override any potential conflicting styles */
.lexical-editor [data-lexical-editor] {
  white-space: normal !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  text-align: left !important;
  direction: ltr !important;
  writing-mode: horizontal-tb !important;
  unicode-bidi: normal !important;
}
