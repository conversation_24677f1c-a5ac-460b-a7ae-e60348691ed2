import Banner from "@/app/shared/Banner";
import Image from "next/image";
import React from "react";
import BasicHeader from "../home/<USER>/BasicHeader";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Play Jili Games Online Real Money | Top Slots & Fishing Games",
  alternates: {
    canonical: "https://www.freespin168.asia/jili-games",
  },
  description:
    "Enjoy the best Jili games online for real money! Play exciting slots, fishing games, and more on trusted platforms with fast payouts and great bonuses.",
  keywords: ["Jili games online real money"],
};

const images = [
  "/jili/img1.png",
  "/jili/img2.png",
  "/jili/img3.png",
  "/jili/img4.png",
  "/jili/img5.png",
  "/jili/img6.png",
  "/jili/img7.png",
  "/jili/img8.png",
  "/jili/img9.png",
  "/jili/img10.png",
];

const page = () => {
  return (
    <main className="bg-black  ">
      <Banner
        url="/jili/background.png"
        secondText="JILI Games "
        secondTitleText="Games"
      />
      <div className="container h-full w-full ">
        <div
          className="bg-cover bg-center py-10"
          style={{
            backgroundImage: "url('/jili/background2.png')",
          }}
        >
          <div className="place-items-center grid grid-cols-1 md:grid-cols-3 pb-[8rem] gap-4 md:gap-4 lg:gap-8 lg:grid-cols-5 px-3 md:px-[3rem] lg:px-[6rem]">
            {images.map((item, index) => (
              <Image
                key={index}
                src={item}
                alt={`Jili game ${index + 1}`}
                height={200}
                width={300}
              />
            ))}
          </div>
        </div>
      </div>
      <div className=" container pb-[4rem] px-3 md:px-[2rem] gap-[4rem] lg:px-[6rem] grid grid-cols-1 md:grid-cols-2">
        <div>
          <div className="pb-4">
            <BasicHeader
              text="หมุนเข้าสู่ความตื่นเต้นด้วยเกม Jili ที่ FreeSpin168"
              className="text-start pb-4 "
            />
            <p className="jakarta text-sm md:text-base text-[#A4A3A3]">
              {`เตรียมพร้อมสำหรับการนั่งที่น่าหลงใหลในโลกแห่งโอกาส
ความตื่นเต้นและผลตอบแทนพราวด้วยพรีเมี่ยมของ FreeSpin168
คอลเลกชันของเกม Jiliด้วยกราฟิกที่น่าทึ่งดื่มด่ำ
ซาวด์แทร็กและการกระทำที่ไม่หยุดยั้งการหมุนทุกครั้งจะไม่เพียง แต่จะไม่เพียง แต่ก
ยิงด้วยชัยชนะครั้งใหญ่ แต่ยังมีความบันเทิงที่ลืมเลือน`}
            </p>
          </div>
          <div className="pb-4">
            <BasicHeader
              text="ทำไมต้องเลือกเกม Jili ที่ FreeSpin168?"
              className="text-start pb-4 "
            />
            <p className="jakarta text-sm md:text-base text-[#A4A3A3]">
              {`เมื่อคุณเล่นที่ FreeSpin168 คุณกำลังก้าวเข้าสู่โลกแห่ง
เล่นเกมสนุกและมีคุณภาพที่น่าเชื่อถือนี่คือเหตุผลที่ผู้เล่นของเราเก็บไว้
กลับมาอีกครั้ง:`}
            </p>
            <ul className="jakarta text-sm md:text-base text-[#A4A3A3] list-disc pl-4">
              <li>
                การเลือกที่กว้างขวาง: สำรวจชื่อ jili ที่หลากหลาย
                ตอบสนองทุกสไตล์และกลยุทธ์
              </li>
              <li>
                การเล่นเกมที่ปลอดภัยและปลอดภัย:
                เล่นด้วยความอุ่นใจในการป้องกันและ สภาพแวดล้อมที่เป็นธรรม
              </li>
              <li>
                True Fairness: ด้วยระบบการเล่นที่ยุติธรรมขั้นสูงทุกสปินคือ
                คาดเดาไม่ได้และเป็นกลาง
              </li>
              <li>
                {`ความบันเทิงที่บริสุทธิ์: ไม่ว่าคุณจะไล่ล่าแจ็คพอตหรือการปั่น
เพื่อความสนุกสนานรับประกันความตื่นเต้น`}
              </li>
            </ul>
          </div>
          <div className="pb-4">
            <BasicHeader
              text="รับโบนัส Jili พิเศษของคุณ - เล่นตอนนี้!"
              className="text-start pb-4 "
            />
            <p className="jakarta text-sm md:text-base text-[#A4A3A3]">
              {`อย่าพลาดโอกาสที่จะเริ่มการผจญภัยเกม Jili ของคุณ
ด้วยปัง!ลงทะเบียนที่ FreeSpin168 และปลดล็อกสล็อตที่น่าทึ่ง
โบนัส - จากสปินฟรีไปจนถึงเครดิตโบนัสและอื่น ๆวงล้อคือ
รอ - คุณพร้อมที่จะหมุนและชนะหรือยัง?`}
            </p>
          </div>
        </div>
        <Image
          src="/jili/lvl15.png"
          alt="ภาพเกมระดับ 15"
          height={500}
          width={700}
        />
      </div>
    </main>
  );
};

export default page;
