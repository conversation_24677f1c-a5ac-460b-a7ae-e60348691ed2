(()=>{var e={};e.id=9722,e.ids=[9722],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17063:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(56037),a=t.n(s),n=t(85663);let o=new s.Schema({username:{type:String,required:[!0,"Username is required"],unique:!0,trim:!0,minlength:[3,"Username must be at least 3 characters"],maxlength:[30,"Username cannot exceed 30 characters"]},email:{type:String,required:[!0,"Email is required"],unique:!0,lowercase:!0,trim:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Please enter a valid email"]},password:{type:String,required:[!0,"Password is required"],minlength:[6,"Password must be at least 6 characters"],select:!1},firstName:{type:String,trim:!0,maxlength:[50,"First name cannot exceed 50 characters"]},lastName:{type:String,trim:!0,maxlength:[50,"Last name cannot exceed 50 characters"]},bio:{type:String,maxlength:[500,"Bio cannot exceed 500 characters"]},avatar:{type:String,default:null},role:{type:String,enum:["user","admin","moderator"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1}},{timestamps:!0});o.pre("save",async function(e){if(!this.isModified("password"))return e();try{let r=await n.Ay.genSalt(12);this.password=await n.Ay.hash(this.password,r),e()}catch(r){e(r)}}),o.methods.comparePassword=async function(e){return n.Ay.compare(e,this.password)};let i=a().models.User||a().model("User",o)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45972:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>h,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>y});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>g});var a=t(96559),n=t(48088),o=t(37719),i=t(32190),c=t(45697),u=t(75745),l=t(73944),d=t(57595);let m=c.z.object({name:c.z.string().min(1,"Name is required").max(50,"Name too long").trim(),description:c.z.string().max(200,"Description too long").optional(),color:c.z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,"Invalid color format").optional()});async function p(){try{await (0,u.A)();let e=await l.A.find({isActive:!0}).sort({name:1}).lean();return i.NextResponse.json({success:!0,data:{categories:e}})}catch(e){return console.error("Error fetching categories:",e),i.NextResponse.json({success:!1,error:"Failed to fetch categories"},{status:500})}}let g=(0,d.eh)((0,d.ru)(async(e,r)=>{try{await (0,u.A)();let r=await e.json(),t=m.parse(r);if(await l.A.findOne({name:{$regex:RegExp(`^${t.name}$`,"i")}}))return i.NextResponse.json({success:!1,error:"Category already exists"},{status:400});let s=new l.A(t);return await s.save(),i.NextResponse.json({success:!0,message:"Category created successfully",data:{category:s},createdBy:e.user.username},{status:201})}catch(e){if(e instanceof c.z.ZodError)return i.NextResponse.json({success:!1,error:"Validation failed",details:e.errors},{status:400});if(11e3===e.code&&e.keyPattern?.name)return i.NextResponse.json({success:!1,error:"Category name already exists"},{status:400});return console.error("Error creating category:",e),i.NextResponse.json({success:!1,error:"Failed to create category"},{status:500})}})),h=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/categories/route",pathname:"/api/categories",filename:"route",bundlePath:"app/api/categories/route"},resolvedPagePath:"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\api\\categories\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:y,serverHooks:f}=h;function w(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:y})}},55511:e=>{"use strict";e.exports=require("crypto")},56037:e=>{"use strict";e.exports=require("mongoose")},57595:(e,r,t)=>{"use strict";t.d(r,{eh:()=>m,m6:()=>d,ru:()=>u});var s=t(32190),a=t(43205),n=t.n(a),o=t(75745),i=t(17063);let c=process.env.JWT_SECRET||"your-secret-key";function u(e){return async(r,t)=>{try{let a=r.headers.get("authorization"),u=a?.replace("Bearer ","")||r.cookies.get("session-token")?.value;if(!u)return s.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let l=n().verify(u,c);await (0,o.A)();let d=await i.A.findById(l.userId).select("_id email username role isActive");if(!d||!d.isActive)return s.NextResponse.json({success:!1,error:"User not found or inactive"},{status:401});return r.user={userId:d._id.toString(),email:d.email,username:d.username,role:d.role},e(r,t)}catch(e){if(e instanceof n().JsonWebTokenError)return s.NextResponse.json({success:!1,error:"Invalid authentication token"},{status:401});return console.error("Authentication middleware error:",e),s.NextResponse.json({success:!1,error:"Authentication failed"},{status:500})}}}function l(e){return r=>u(async(t,a)=>{let n=t.user;return e.includes(n.role)?r(t,a):s.NextResponse.json({success:!1,error:`Access denied. Required roles: ${e.join(", ")}. Your role: ${n.role}`},{status:403})})}let d=l(["admin"]);function m(e){return async(r,t)=>{let s=Date.now(),a=r.method,n=r.url;console.log(`[${new Date().toISOString()}] ${a} ${n} - Started`);try{let o=await e(r,t),i=Date.now()-s;return console.log(`[${new Date().toISOString()}] ${a} ${n} - ${o.status} (${i}ms)`),o}catch(r){let e=Date.now()-s;throw console.error(`[${new Date().toISOString()}] ${a} ${n} - Error (${e}ms):`,r),r}}}l(["admin","editor"]),l(["admin","editor","author"])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73944:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(56037),a=t.n(s);let n=new s.Schema({name:{type:String,required:[!0,"Category name is required"],unique:!0,trim:!0,maxlength:[50,"Category name cannot exceed 50 characters"]},description:{type:String,maxlength:[200,"Description cannot exceed 200 characters"]},color:{type:String,default:"#6366f1",match:[/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,"Please enter a valid hex color"]},isActive:{type:Boolean,default:!0}},{timestamps:!0});n.index({isActive:1});let o=a().models.Category||a().model("Category",n)},75745:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(56037),a=t.n(s);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global?.mongoose;o||(o=global.mongoose={conn:null,promise:null});let i=async function(){if(o.conn)return o.conn;o.promise||(o.promise=a().connect(n,{bufferCommands:!1}).then(e=>e));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,3205,5697,5663],()=>t(45972));module.exports=s})();