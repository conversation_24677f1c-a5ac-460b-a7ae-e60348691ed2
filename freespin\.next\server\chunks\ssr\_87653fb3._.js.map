{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/components/magicui/marquee.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\r\nimport { ComponentPropsWithoutRef } from \"react\";\r\n\r\ninterface MarqueeProps extends ComponentPropsWithoutRef<\"div\"> {\r\n  /**\r\n   * Optional CSS class name to apply custom styles\r\n   */\r\n  className?: string;\r\n  /**\r\n   * Whether to reverse the animation direction\r\n   * @default false\r\n   */\r\n  reverse?: boolean;\r\n  /**\r\n   * Whether to pause the animation on hover\r\n   * @default false\r\n   */\r\n  pauseOnHover?: boolean;\r\n  /**\r\n   * Content to be displayed in the marquee\r\n   */\r\n  children: React.ReactNode;\r\n  /**\r\n   * Whether to animate vertically instead of horizontally\r\n   * @default false\r\n   */\r\n  vertical?: boolean;\r\n  /**\r\n   * Number of times to repeat the content\r\n   * @default 4\r\n   */\r\n  repeat?: number;\r\n}\r\n\r\nexport function MagicMarquee({\r\n  className,\r\n  reverse = false,\r\n  pauseOnHover = false,\r\n  children,\r\n  vertical = false,\r\n  repeat = 4,\r\n  ...props\r\n}: MarqueeProps) {\r\n  return (\r\n    <div\r\n      {...props}\r\n      className={cn(\r\n        \"group flex overflow-hidden p-2 [--duration:40s] [--gap:1rem] [gap:var(--gap)]\",\r\n        {\r\n          \"flex-row\": !vertical,\r\n          \"flex-col\": vertical,\r\n        },\r\n        className\r\n      )}\r\n    >\r\n      {Array(repeat)\r\n        .fill(0)\r\n        .map((_, i) => (\r\n          <div\r\n            key={i}\r\n            className={cn(\"flex shrink-0 justify-around [gap:var(--gap)]\", {\r\n              \"animate-marquee flex-row\": !vertical,\r\n              \"animate-marquee-vertical flex-col\": vertical,\r\n              \"group-hover:[animation-play-state:paused]\": pauseOnHover,\r\n              \"[animation-direction:reverse]\": reverse,\r\n            })}\r\n          >\r\n            {children}\r\n          </div>\r\n        ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAkCO,SAAS,aAAa,EAC3B,SAAS,EACT,UAAU,KAAK,EACf,eAAe,KAAK,EACpB,QAAQ,EACR,WAAW,KAAK,EAChB,SAAS,CAAC,EACV,GAAG,OACU;IACb,qBACE,8OAAC;QACE,GAAG,KAAK;QACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iFACA;YACE,YAAY,CAAC;YACb,YAAY;QACd,GACA;kBAGD,MAAM,QACJ,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,8OAAC;gBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;oBAC7D,4BAA4B,CAAC;oBAC7B,qCAAqC;oBACrC,6CAA6C;oBAC7C,iCAAiC;gBACnC;0BAEC;eARI;;;;;;;;;;AAajB", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/home/<USER>/HeroSection.tsx"], "sourcesContent": ["// import { MagicMarquee } from \"@/components/magicui/marquee\";\r\n// import Image from \"next/image\";\r\n// import React from \"react\";\r\n\r\n// const HeroSection: React.FC = () => {\r\n//   const images = [\r\n//     \"/homepage/marquee1.png\",\r\n//     \"/homepage/marquee2.png\",\r\n//     \"/homepage/marquee3.png\",\r\n//     \"/homepage/marquee4.png\",\r\n//     \"/homepage/marquee5.png\",\r\n//     \"/homepage/marquee6.png\",\r\n//     \"/homepage/marquee7.png\",\r\n//   ];\r\n\r\n//   const texts = [\r\n//     \"Slot\",\r\n//     \"Casino\",\r\n//     \"Sport\",\r\n//     \"Lotto\",\r\n//     \"Fishing\",\r\n//     \"Table\",\r\n//     \"Keno\",\r\n//     t(\"hitsbrand\"),\r\n//   ];\r\n\r\n//   return (\r\n//     <div className=\"relative w-full h-screen overflow-hidden\">\r\n//       {/* Video Background */}\r\n//       <video\r\n//         className=\"absolute top-0 left-0 w-full h-full object-cover\"\r\n//         autoPlay\r\n//         loop\r\n//         muted\r\n//         playsInline\r\n//       >\r\n//         <source src=\"/homepage/video.mp4\" type=\"video/mp4\" />\r\n//         Your browser does not support the video tag.\r\n//       </video>\r\n\r\n//       {/* Overlay for better text readability */}\r\n//       <div className=\"absolute inset-0 bg-black/50\"></div>\r\n\r\n//       {/* Text Content */}\r\n//       <div className=\"relative z-20 flex flex-col items-start mt-[8rem] md:mt-0 md:justify-center h-full text-center text-white px-3 md:px-[4rem] lg:px-[6rem]\">\r\n//         <h1 className=\"text-2xl md:text-4xl lg:text-5xl sm:text-3xl font-bold mb-4\">\r\n//           Next-Level Thrill-\r\n//         </h1>\r\n//         <p className=\"text-2xl md:text-4xl lg:text-5xl sm:text-3xl  font-bold mb-4\">\r\n//           Mobile Sports\r\n//         </p>\r\n//         <div className=\"flex flex-col md:flex-row items-center gap-4\">\r\n//           <p className=\"text-2xl md:text-4xl lg:text-5xl sm:text-3xl font-bold mb-4\">\r\n//             Games Built for\r\n//           </p>\r\n//           <Image\r\n//             src=\"/homepage/Fun.png\"\r\n//             alt={t(\"funlogo\")}\r\n//             width={70}\r\n//             height={40}\r\n//           />\r\n//         </div>\r\n//       </div>\r\n\r\n//       <div className=\"hidden md:flex absolute top-[80%] left-15 w-full z-10 transform -rotate-300 origin-top-right\">\r\n//         <MagicMarquee pauseOnHover className=\"[--duration:40s]\">\r\n//           {images.map((src, index) => (\r\n//             <div\r\n//               key={index}\r\n//               className=\"flex-shrink-0 mx-4\"\r\n//               style={{ width: 350, height: 350, position: \"relative\" }}\r\n//             >\r\n//               <img\r\n//                 src={src}\r\n//                 alt={`Marquee image ${index + 1}`}\r\n//                 className=\"w-full h-full object-cover rounded-lg transform -rotate-90 opacity-0.9\"\r\n//               />\r\n//             </div>\r\n//           ))}\r\n//         </MagicMarquee>\r\n//       </div>\r\n\r\n//       <div className=\"flex absolute bottom-0 left-0 w-full z-10 \">\r\n//         <MagicMarquee pauseOnHover className=\"[--duration:10s]\">\r\n//           {texts.map((review, index) => (\r\n//             <div\r\n//               key={index}\r\n//               className=\"text-sm py-2 px-4  bg-[#171A24] text-[#EFEFEF] rounded-xl\"\r\n//             >\r\n//               {review}\r\n//             </div>\r\n//           ))}\r\n//         </MagicMarquee>\r\n//       </div>\r\n//     </div>\r\n//   );\r\n// };\r\n\r\n// export default HeroSection;\r\n\r\n// Second\r\n\r\nimport { MagicMarquee } from \"@/components/magicui/marquee\";\r\nimport Image from \"next/image\";\r\nimport React from \"react\";\r\n\r\nconst HeroSection: React.FC = () => {\r\n  const images = [\r\n    \"/homepage/marquee1.png\",\r\n    \"/homepage/marquee2.png\",\r\n    \"/homepage/marquee3.png\",\r\n    \"/homepage/marquee4.png\",\r\n    \"/homepage/marquee5.png\",\r\n    \"/homepage/marquee6.png\",\r\n    \"/homepage/marquee7.png\",\r\n  ];\r\n\r\n  const texts = [\r\n    \"สล็อต\",\r\n    \"คาสิโน\",\r\n    \"กีฬา\",\r\n    \"ล็อตโต้\",\r\n    \"ตกปลา\",\r\n    \"โต๊ะ\",\r\n    \"คีโน่\",\r\n    \"HitsBrand\",\r\n  ];\r\n\r\n  return (\r\n    <div className=\"relative w-full h-screen overflow-hidden\">\r\n      {/* Video Background */}\r\n      <video\r\n        className=\"absolute top-0 left-0 w-full h-full object-cover\"\r\n        autoPlay\r\n        loop\r\n        muted\r\n        playsInline\r\n      >\r\n        <source src=\"/homepage/video.mp4\" type=\"video/mp4\" />\r\n        Your browser does not support the video tag.\r\n      </video>\r\n\r\n      {/* Overlay for better text readability */}\r\n      <div className=\"absolute inset-0 bg-black/50\"></div>\r\n\r\n      {/* Text Content */}\r\n      {/* <div className=\"relative z-20 flex flex-col lg:items-start mt-[23rem] md:mt-0 md:justify-center h-full text-center text-white px-3 pr-[8rem] md:pr-0 md:px-[4rem] lg:px-[6rem]\">\r\n        {/* <p className=\"text-4xl pr-14 md:pr-0 text-nowrap md:text-4xl lg:text-5xl sm:text-3xl  font-bold mb-4\">\r\n          {t(\"title2\")}\r\n          </p> */}\r\n      {/* <div className=\"flex   items-center gap-4\"> */}\r\n      {/* <p className=\"text-4xl text-nowrap md:text-4xl lg:text-5xl sm:text-3xl font-bold mb-4\">\r\n            {t(\"title3\")}\r\n            </p> */}\r\n      {/* <h1 className=\"text-4xl md:text-4xl lg:text-5xl text-wrap sm:text-3xl font-bold mb-4\"> */}\r\n      {/* Your Premier Online Casino in Thailand for Online Gambling, Betting */}\r\n      {/* </h1> */}\r\n      {/* <Image */}\r\n      {/* src=\"/homepage/Fun.png\" */}\r\n      {/* alt=\"Fun Logo\" */}\r\n      {/* width={70} */}\r\n      {/* height={40} */}\r\n      {/* /> */}\r\n      {/* </div> */}\r\n      {/* </div>  */}\r\n\r\n      <div className=\"relative z-20 flex flex-col lg:items-start mt-[23rem] md:mt-0 md:justify-center h-full text-left text-white px-3 pr-[8rem] md:pr-0 md:px-[4rem] lg:px-[6rem]\">\r\n        <div className=\"flex items-start gap-4\">\r\n          <h2 className=\"text-4xl md:text-4xl lg:text-5xl sm:text-3xl font-bold mb-4 break-words max-w-4xl\">\r\n            คาสิโนออนไลน์ชั้นนำของคุณในประเทศไทยสำหรับการพนันออนไลน์และการเดิมพัน\r\n          </h2>\r\n          {/* <Image\r\n            src=\"/homepage/Fun.png\"\r\n            alt=\"Fun Logo\"\r\n            width={70}\r\n            height={40}\r\n          /> */}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\" flex absolute top-[50%] md:top-[80%] left-15 w-full z-10 transform -rotate-297 md:-rotate-300 origin-top-right\">\r\n        <MagicMarquee pauseOnHover className=\"[--duration:40s]\">\r\n          {images.map((src, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"relative w-full h-full flex-shrink-0 mx-4\"\r\n              style={{ width: 350, height: 350, position: \"relative\" }}\r\n            >\r\n              <Image\r\n                src={src}\r\n                alt={`Marquee image ${index + 1}`}\r\n                fill={true}\r\n                className=\"-rotate-90\"\r\n              />\r\n            </div>\r\n          ))}\r\n        </MagicMarquee>\r\n      </div>\r\n\r\n      <div className=\"flex absolute -bottom-2 left-0 w-full z-10 \">\r\n        <MagicMarquee pauseOnHover className=\"[--duration:10s]\">\r\n          {texts.map((review, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"text-sm py-2 px-4  bg-[#171A24] text-[#EFEFEF] rounded-xl\"\r\n            >\r\n              {review}\r\n            </div>\r\n          ))}\r\n        </MagicMarquee>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HeroSection;\r\n"], "names": [], "mappings": "AAAA,+DAA+D;AAC/D,kCAAkC;AAClC,6BAA6B;AAE7B,wCAAwC;AACxC,qBAAqB;AACrB,gCAAgC;AAChC,gCAAgC;AAChC,gCAAgC;AAChC,gCAAgC;AAChC,gCAAgC;AAChC,gCAAgC;AAChC,gCAAgC;AAChC,OAAO;AAEP,oBAAoB;AACpB,cAAc;AACd,gBAAgB;AAChB,eAAe;AACf,eAAe;AACf,iBAAiB;AACjB,eAAe;AACf,cAAc;AACd,sBAAsB;AACtB,OAAO;AAEP,aAAa;AACb,iEAAiE;AACjE,iCAAiC;AACjC,eAAe;AACf,uEAAuE;AACvE,mBAAmB;AACnB,eAAe;AACf,gBAAgB;AAChB,sBAAsB;AACtB,UAAU;AACV,gEAAgE;AAChE,uDAAuD;AACvD,iBAAiB;AAEjB,oDAAoD;AACpD,6DAA6D;AAE7D,6BAA6B;AAC7B,mKAAmK;AACnK,uFAAuF;AACvF,+BAA+B;AAC/B,gBAAgB;AAChB,uFAAuF;AACvF,0BAA0B;AAC1B,eAAe;AACf,yEAAyE;AACzE,wFAAwF;AACxF,8BAA8B;AAC9B,iBAAiB;AACjB,mBAAmB;AACnB,sCAAsC;AACtC,iCAAiC;AACjC,yBAAyB;AACzB,0BAA0B;AAC1B,eAAe;AACf,iBAAiB;AACjB,eAAe;AAEf,uHAAuH;AACvH,mEAAmE;AACnE,0CAA0C;AAC1C,mBAAmB;AACnB,4BAA4B;AAC5B,+CAA+C;AAC/C,0EAA0E;AAC1E,gBAAgB;AAChB,qBAAqB;AACrB,4BAA4B;AAC5B,qDAAqD;AACrD,qGAAqG;AACrG,mBAAmB;AACnB,qBAAqB;AACrB,gBAAgB;AAChB,0BAA0B;AAC1B,eAAe;AAEf,qEAAqE;AACrE,mEAAmE;AACnE,4CAA4C;AAC5C,mBAAmB;AACnB,4BAA4B;AAC5B,sFAAsF;AACtF,gBAAgB;AAChB,yBAAyB;AACzB,qBAAqB;AACrB,gBAAgB;AAChB,0BAA0B;AAC1B,eAAe;AACf,aAAa;AACb,OAAO;AACP,KAAK;AAEL,8BAA8B;AAE9B,SAAS;;;;;AAET;AACA;;;;AAGA,MAAM,cAAwB;IAC5B,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,QAAQ;gBACR,IAAI;gBACJ,KAAK;gBACL,WAAW;;kCAEX,8OAAC;wBAAO,KAAI;wBAAsB,MAAK;;;;;;oBAAc;;;;;;;0BAKvD,8OAAC;gBAAI,WAAU;;;;;;0BAuBf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAAoF;;;;;;;;;;;;;;;;0BAYtG,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,wIAAA,CAAA,eAAY;oBAAC,YAAY;oBAAC,WAAU;8BAClC,OAAO,GAAG,CAAC,CAAC,KAAK,sBAChB,8OAAC;4BAEC,WAAU;4BACV,OAAO;gCAAE,OAAO;gCAAK,QAAQ;gCAAK,UAAU;4BAAW;sCAEvD,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK;gCACL,KAAK,CAAC,cAAc,EAAE,QAAQ,GAAG;gCACjC,MAAM;gCACN,WAAU;;;;;;2BARP;;;;;;;;;;;;;;;0BAeb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,wIAAA,CAAA,eAAY;oBAAC,YAAY;oBAAC,WAAU;8BAClC,MAAM,GAAG,CAAC,CAAC,QAAQ,sBAClB,8OAAC;4BAEC,WAAU;sCAET;2BAHI;;;;;;;;;;;;;;;;;;;;;AAUnB;uCAEe", "debugId": null}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/home/<USER>/BasicHeader.tsx"], "sourcesContent": ["import clsx from \"clsx\";\r\nimport React from \"react\";\r\n\r\nconst BasicHeader: React.FC<{ className?: string; text: string }> = ({\r\n  className,\r\n  text,\r\n}) => {\r\n  return (\r\n    <h2\r\n      className={clsx(\"text-2xl font-bold text-center text-white\", className)}\r\n    >\r\n      {text}\r\n    </h2>\r\n  );\r\n};\r\n\r\nexport default BasicHeader;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,cAA8D,CAAC,EACnE,SAAS,EACT,IAAI,EACL;IACC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,6CAA6C;kBAE5D;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/home/<USER>/AboutUs.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Image from \"next/image\";\r\nimport BasicHeader from \"./BasicHeader\";\r\n;\r\n\r\nconst AboutUs = () => {\r\n  \r\n\r\n  return (\r\n    <section className=\"bg-black text-white\">\r\n      <div className=\"container mx-auto px-3 md:px-[4rem]  py-6 grid grid-cols-1 md:grid-cols-2\">\r\n        <div className=\"flex justify-center flex-col\">\r\n          <BasicHeader text=\"เกี่ยวกับเรา\" className=\"text-start\" />\r\n          {/* <h3 className=\"font-bold  md:text-4xl sm:text-3xl text-2xl\">\r\n            เกี่ยวกับเรา\r\n          </h3> */}\r\n          <div className=\"my-6 text-[#A9A7B0] space-y-3 text-justify\">\r\n            <p>\r\n              จุดหมายปลายทางการพนันและการเดิมพันออนไลน์ชั้นนำของประเทศไทย\r\n              Freespin168\r\n              เป็นหนึ่งในแพลตฟอร์มการพนันออนไลน์ที่ได้รับการจัดอันดับสูงสุดและน่าเชื่อถือที่สุดของประเทศไทย\r\n              ตั้งแต่ปี 2013 เราเป็นผู้นำด้านการเดิมพันคาสิโนและกีฬา\r\n              ด้วยประสบการณ์ความเป็นเลิศกว่า 8 ปี\r\n              ผู้เล่นมากมายทั่วประเทศไทยต่างยกย่อง Freespin168\r\n              ให้เป็นคาสิโนออนไลน์ชั้นนำในประเทศไทยและเป็นจุดหมายปลายทางสำหรับการเดิมพันออนไลน์ที่ปลอดภัย\r\n              น่าตื่นเต้น และเชื่อถือได้\r\n            </p>\r\n            <p>\r\n              แพลตฟอร์มของเรามีเกมคาสิโนให้เลือกมากมาย ทั้งเกมสล็อต แบล็คแจ็ค\r\n              บาคาร่า เกมตกปลา และอื่นๆ อีกมากมาย\r\n              มอบประสบการณ์การเล่นเกมที่เหนือชั้นสำหรับผู้เล่นทุกประเภท\r\n            </p>\r\n            <p>\r\n              เรามีความภูมิใจที่ได้ร่วมมือกับแบรนด์ที่ได้รับการยอมรับมากที่สุดในอุตสาหกรรม\r\n              เช่น SBOBET, 918KISS, MEGA888, BETWOS, WWBET, GCLUB, PUSSY888 และ\r\n              JOKER เพื่อมอบประสบการณ์การเล่นเกมออนไลน์ที่ดีที่สุดให้กับคุณ\r\n            </p>\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-center justify-center\">\r\n          <Image\r\n            src=\"/homepage/about.png\"\r\n            alt=\"about\"\r\n            width={500}\r\n            height={500}\r\n            className=\"border-transparent \"\r\n          />\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default AboutUs;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAGA,MAAM,UAAU;IAGd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6JAAA,CAAA,UAAW;4BAAC,MAAK;4BAAe,WAAU;;;;;;sCAI3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CAUH,8OAAC;8CAAE;;;;;;8CAKH,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;8BAOP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB;uCAEe", "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/home/<USER>/OnlineCasino.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(pages)/home/<USER>/OnlineCasino.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(pages)/home/<USER>/OnlineCasino.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoT,GACjV,kFACA", "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/home/<USER>/OnlineCasino.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(pages)/home/<USER>/OnlineCasino.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(pages)/home/<USER>/OnlineCasino.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgS,GAC7T,8DACA", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/home/<USER>/Destination.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(pages)/home/<USER>/Destination.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(pages)/home/<USER>/Destination.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmT,GAChV,iFACA", "debugId": null}}, {"offset": {"line": 509, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/home/<USER>/Destination.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(pages)/home/<USER>/Destination.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(pages)/home/<USER>/Destination.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/home/<USER>/PulseCircle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(pages)/home/<USER>/PulseCircle.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(pages)/home/<USER>/PulseCircle.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmT,GAChV,iFACA", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/home/<USER>/PulseCircle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(pages)/home/<USER>/PulseCircle.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(pages)/home/<USER>/PulseCircle.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 561, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 571, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/home/<USER>/Guide.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport PulseCircle from \"./PulseCircle\";\r\nconst Guide = () => {\r\n  return (\r\n    <section className=\" bg-black px-7\">\r\n      <div className=\"my-auto container mx-auto px-3 md:px-[4rem] grid grid-cols-1 md:grid-cols-2 gap-y-6 gap-x-4\">\r\n        <div>\r\n          <PulseCircle />\r\n        </div>\r\n        <div className=\"text-white py-18 space-y-4\">\r\n          <h3 className=\"font-bold  md:text-[30px] sm:text-3xl  text-2xl\">\r\n            {\r\n              \"คู่มือของ FreeSpin168 ในการค้นหาคาสิโนออนไลน์ที่เชื่อถือได้มากที่สุดในประเทศไทย\"\r\n            }\r\n          </h3>\r\n          <div className=\"space-y-3\">\r\n            <p className=\"text-justify text-sm\">\r\n              {\r\n                \"ด้วยคาสิโนออนไลน์จำนวนมากที่มีอยู่จึงจำเป็นที่จะต้องรู้วิธีการเลือกที่น่าเชื่อถืออย่างแท้จริง \"\r\n              }\r\n            </p>\r\n            <p className=\"text-justify text-sm\">\r\n              {\r\n                \"แพลตฟอร์มคาสิโนที่เชื่อถือได้ควรใช้เกตเวย์การชำระเงินที่ปลอดภัยและมีชื่อเสียงเพื่อให้แน่ใจว่าเงินฝากและการถอนอย่างปลอดภัย\"\r\n              }\r\n            </p>\r\n            <p className=\"text-justify text-sm\">\r\n              {\r\n                \"นอกจากนี้เว็บไซต์ควรใช้พลังงานจากผู้ให้บริการซอฟต์แวร์ที่เชื่อถือได้ \"\r\n              }\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Guide;\r\n"], "names": [], "mappings": ";;;;AACA;;;AACA,MAAM,QAAQ;IACZ,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;8BACC,cAAA,8OAAC,6JAAA,CAAA,UAAW;;;;;;;;;;8BAEd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAEV;;;;;;sCAGJ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAET;;;;;;8CAGJ,8OAAC;oCAAE,WAAU;8CAET;;;;;;8CAGJ,8OAAC;oCAAE,WAAU;8CAET;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;uCAEe", "debugId": null}}, {"offset": {"line": 664, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/home/<USER>/Trending.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(pages)/home/<USER>/Trending.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(pages)/home/<USER>/Trending.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/home/<USER>/Trending.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(pages)/home/<USER>/Trending.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(pages)/home/<USER>/Trending.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 692, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 702, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/shared/TextHoverEffect.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TextHoverEffect = registerClientReference(\n    function() { throw new Error(\"Attempted to call TextHoverEffect() from the server but TextHoverEffect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/shared/TextHoverEffect.tsx <module evaluation>\",\n    \"TextHoverEffect\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,oEACA", "debugId": null}}, {"offset": {"line": 716, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/shared/TextHoverEffect.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TextHoverEffect = registerClientReference(\n    function() { throw new Error(\"Attempted to call TextHoverEffect() from the server but TextHoverEffect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/shared/TextHoverEffect.tsx\",\n    \"TextHoverEffect\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,gDACA", "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 740, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/shared/FreeSpinText.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport clsx from \"clsx\";\r\nimport { MagicMarquee } from \"@/components/magicui/marquee\";\r\n\r\nconst FreeSpinText = () => {\r\n  const texts = [\r\n    { text: \"FreeSpin168\", className: `from-[#3159C6] to-[#9A6D6D]` },\r\n    {\r\n      text: \"▪\",\r\n      className: `to-[#3159C6] from-[#9A6D6D] text-lg flex items-end`,\r\n    },\r\n    { text: \"FreeSpin168\", className: `to-[#3159C6] from-[#9A6D6D]` },\r\n    {\r\n      text: \"▪\",\r\n      className: `to-[#3159C6] from-[#9A6D6D] text-lg flex items-end`,\r\n    },\r\n    { text: \"FreeSpin168\", className: `from-[#3159C6] to-[#9A6D6D]` },\r\n    {\r\n      text: \"▪\",\r\n      className: `to-[#3159C6] from-[#9A6D6D] text-lg flex items-end`,\r\n    },\r\n    { text: \"<PERSON>Spin168\", className: `to-[#3159C6] from-[#9A6D6D]` },\r\n    {\r\n      text: \"▪\",\r\n      className: `to-[#3159C6] from-[#9A6D6D] text-lg flex items-end`,\r\n    },\r\n  ];\r\n  return (\r\n    <MagicMarquee className=\"[--duration:30s] \">\r\n      {texts.map((item, index) => (\r\n        <span\r\n          key={index}\r\n          className={clsx(\r\n            \"bg-clip-text text-transparent text-6xl font-bold bg-gradient-to-r\",\r\n            item.className\r\n          )}\r\n        >\r\n          {item.text}\r\n        </span>\r\n      ))}\r\n    </MagicMarquee>\r\n  );\r\n};\r\n\r\nexport default FreeSpinText;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,MAAM,eAAe;IACnB,MAAM,QAAQ;QACZ;YAAE,MAAM;YAAe,WAAW,CAAC,2BAA2B,CAAC;QAAC;QAChE;YACE,MAAM;YACN,WAAW,CAAC,kDAAkD,CAAC;QACjE;QACA;YAAE,MAAM;YAAe,WAAW,CAAC,2BAA2B,CAAC;QAAC;QAChE;YACE,MAAM;YACN,WAAW,CAAC,kDAAkD,CAAC;QACjE;QACA;YAAE,MAAM;YAAe,WAAW,CAAC,2BAA2B,CAAC;QAAC;QAChE;YACE,MAAM;YACN,WAAW,CAAC,kDAAkD,CAAC;QACjE;QACA;YAAE,MAAM;YAAe,WAAW,CAAC,2BAA2B,CAAC;QAAC;QAChE;YACE,MAAM;YACN,WAAW,CAAC,kDAAkD,CAAC;QACjE;KACD;IACD,qBACE,8OAAC,wIAAA,CAAA,eAAY;QAAC,WAAU;kBACrB,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gBAEC,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EACZ,qEACA,KAAK,SAAS;0BAGf,KAAK,IAAI;eANL;;;;;;;;;;AAWf;uCAEe", "debugId": null}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/home/<USER>/GameIconSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(pages)/home/<USER>/GameIconSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(pages)/home/<USER>/GameIconSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuT,GACpV,qFACA", "debugId": null}}, {"offset": {"line": 821, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/home/<USER>/GameIconSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(pages)/home/<USER>/GameIconSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(pages)/home/<USER>/GameIconSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA", "debugId": null}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 845, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/utils/metadata.ts"], "sourcesContent": ["import { Metadata } from \"next\";\r\n\r\ninterface MetadataConfig {\r\n  title: string;\r\n  description: string;\r\n  keywords?: string[];\r\n  path?: string;\r\n  locale?: string;\r\n  image?: string;\r\n  type?: \"website\" | \"article\";\r\n}\r\n\r\nexport function generateMetadata({\r\n  title,\r\n  description,\r\n  keywords = [],\r\n  path = \"\",\r\n  locale = \"th\",\r\n  image = \"https://www.freespin168.asia/logo.png\",\r\n  type = \"website\",\r\n}: MetadataConfig): Metadata {\r\n  const baseUrl = \"https://www.freespin168.asia\";\r\n\r\n  // Generate canonical URL (Thai only)\r\n  const canonicalUrl = `${baseUrl}${path}`;\r\n\r\n  return {\r\n    title,\r\n    description,\r\n    keywords,\r\n    alternates: {\r\n      canonical: canonicalUrl,\r\n    },\r\n    openGraph: {\r\n      title,\r\n      description,\r\n      url: canonicalUrl,\r\n      siteName: \"FreeSpin168\",\r\n      images: [\r\n        {\r\n          url: image,\r\n          width: 1200,\r\n          height: 630,\r\n          alt: title,\r\n        },\r\n      ],\r\n      locale: \"th_TH\",\r\n      type,\r\n    },\r\n    twitter: {\r\n      card: \"summary_large_image\",\r\n      title,\r\n      description,\r\n      images: [image],\r\n    },\r\n    robots: {\r\n      index: true,\r\n      follow: true,\r\n      googleBot: {\r\n        index: true,\r\n        follow: true,\r\n        \"max-video-preview\": -1,\r\n        \"max-image-preview\": \"large\",\r\n        \"max-snippet\": -1,\r\n      },\r\n    },\r\n    other: {\r\n      language: \"Thai\",\r\n    },\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAYO,SAAS,iBAAiB,EAC/B,KAAK,EACL,WAAW,EACX,WAAW,EAAE,EACb,OAAO,EAAE,EACT,SAAS,IAAI,EACb,QAAQ,uCAAuC,EAC/C,OAAO,SAAS,EACD;IACf,MAAM,UAAU;IAEhB,qCAAqC;IACrC,MAAM,eAAe,GAAG,UAAU,MAAM;IAExC,OAAO;QACL;QACA;QACA;QACA,YAAY;YACV,WAAW;QACb;QACA,WAAW;YACT;YACA;YACA,KAAK;YACL,UAAU;YACV,QAAQ;gBACN;oBACE,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,KAAK;gBACP;aACD;YACD,QAAQ;YACR;QACF;QACA,SAAS;YACP,MAAM;YACN;YACA;YACA,QAAQ;gBAAC;aAAM;QACjB;QACA,QAAQ;YACN,OAAO;YACP,QAAQ;YACR,WAAW;gBACT,OAAO;gBACP,QAAQ;gBACR,qBAAqB,CAAC;gBACtB,qBAAqB;gBACrB,eAAe,CAAC;YAClB;QACF;QACA,OAAO;YACL,UAAU;QACZ;IACF;AACF", "debugId": null}}, {"offset": {"line": 905, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/home/<USER>"], "sourcesContent": ["import React from \"react\";\r\nimport HeroSection from \"./components/HeroSection\";\r\nimport WeDo from \"./components/WeDo\";\r\nimport AboutUs from \"./components/AboutUs\";\r\nimport OnlineCasino from \"./components/OnlineCasino\";\r\nimport Destination from \"./components/Destination\";\r\nimport Guide from \"./components/Guide\";\r\nimport Trending from \"./components/Trending\";\r\nimport { TextHoverEffect } from \"@/app/shared/TextHoverEffect\";\r\nimport FreeSpinText from \"@/app/shared/FreeSpinText\";\r\nimport GameIconSection from \"./components/GameIconSection\";\r\n\r\nimport { Metadata } from \"next\";\r\nimport { generateMetadata as createMetadata } from \"@/utils/metadata\";\r\n\r\nexport async function generateMetadata({\r\n  params,\r\n  searchParams,\r\n}: {\r\n  params: Promise<{ locale: string }>;\r\n  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;\r\n}): Promise<Metadata> {\r\n  // Always use Thai locale\r\n  const urlLocale = \"th\";\r\n\r\n  return createMetadata({\r\n    title:\r\n      \"คาสิโนออนไลน์ในประเทศไทย | ฟรีสปินที่เชื่อถือได้และรีวิวเกม – FreeSpin168\",\r\n    description:\r\n      \"สำรวจคาสิโนออนไลน์ที่ดีที่สุดในประเทศไทยกับ FreeSpin168.asia เพลิดเพลินกับการเล่นเกมที่ปลอดภัย แพลตฟอร์มที่ได้รับการตรวจสอบ และอัปเดตฟรีสปินรายวัน ทั้งหมดในที่เดียว\",\r\n    keywords: [\r\n      \"คาสิโนออนไลน์ในประเทศไทย\",\r\n      \"ฟรีสปิน\",\r\n      \"เกมคาสิโน\",\r\n      \"FreeSpin168\",\r\n    ],\r\n    path: \"/\",\r\n    locale: urlLocale,\r\n    type: \"website\",\r\n  });\r\n}\r\n\r\nconst HomePage = () => {\r\n  return (\r\n    <main className=\"bg-black\">\r\n      <HeroSection />\r\n      {/* <WeDo /> */}\r\n      <TextHoverEffect\r\n        text=\"สำรวจมิติใหม่ของเกมคาสิโน ที่ถูกสร้างสรรค์อย่างยอดเยี่ยม ด้วยความคิดสร้างสรรค์ และการออกแบบที่ดื่มด่ำ\"\r\n        spotlightRadiusPx={200}\r\n      />\r\n\r\n      <GameIconSection />\r\n      <FreeSpinText />\r\n      <AboutUs />\r\n      <OnlineCasino />\r\n      <Destination />\r\n      <Guide />\r\n      <Trending />\r\n    </main>\r\n  );\r\n};\r\n\r\nexport default HomePage;\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;;;;;;;;;;;;AAEO,eAAe,iBAAiB,EACrC,MAAM,EACN,YAAY,EAIb;IACC,yBAAyB;IACzB,MAAM,YAAY;IAElB,OAAO,CAAA,GAAA,wHAAA,CAAA,mBAAc,AAAD,EAAE;QACpB,OACE;QACF,aACE;QACF,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,MAAM;QACN,QAAQ;QACR,MAAM;IACR;AACF;AAEA,MAAM,WAAW;IACf,qBACE,8OAAC;QAAK,WAAU;;0BACd,8OAAC,6JAAA,CAAA,UAAW;;;;;0BAEZ,8OAAC,wIAAA,CAAA,kBAAe;gBACd,MAAK;gBACL,mBAAmB;;;;;;0BAGrB,8OAAC,iKAAA,CAAA,UAAe;;;;;0BAChB,8OAAC,qIAAA,CAAA,UAAY;;;;;0BACb,8OAAC,yJAAA,CAAA,UAAO;;;;;0BACR,8OAAC,8JAAA,CAAA,UAAY;;;;;0BACb,8OAAC,6JAAA,CAAA,UAAW;;;;;0BACZ,8OAAC,uJAAA,CAAA,UAAK;;;;;0BACN,8OAAC,0JAAA,CAAA,UAAQ;;;;;;;;;;;AAGf;uCAEe", "debugId": null}}, {"offset": {"line": 1014, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/page.tsx"], "sourcesContent": ["import HomePage from \"./home/<USER>\";\r\n\r\nexport default function Home() {\r\n  return (\r\n    <div>\r\n      <HomePage />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,8OAAC;kBACC,cAAA,8OAAC,wIAAA,CAAA,UAAQ;;;;;;;;;;AAGf", "debugId": null}}]}