import Banner from "@/app/shared/Banner";
import React from "react";
import Hero from "../sexy/components/Hero";
import IrresistibleComponent from "@/app/shared/IrresistibleComponent";
import GamingScreenShot from "@/app/shared/GamingScreenShot";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "เข้าร่วม SV388 Portal Online Casino | ไก่ชนสดและโบส – FreeSpin168",
  alternates: {
    canonical: "https://www.freespin168.asia/sv388",
  },
  description:
    "เล่นบนพอร์ลออนไลน์ SV388 อย่างเป็นทางการ พร้อมไก่ชนสด การจ่าย และโบส เข้าอย่างและกฎหมายผ่าน FreeSpin168",
  keywords: ["Sv388 portal online casino", "ไก่ชนออนไลน์", "SV388"],
};

const Page = () => {
  const obj = {
    t1: "เล่นเกมออนไลน์",
    text1:
      "ประสบการณ์การไก่ชนออนไลน์ SV388 บน FreeSpin168 เพลิดเพลิน ถ่ายทอดสด ต่อรอง ธรรม และการจ่าย รวดเร็ว",
    t2: "ทำไม SV388 Portal ถูกใจ",
    text2:
      "SV388 บน FreeSpin168 นำเสนอประสบการณ์การไก่ชนออนไลน์ที่ไม่เหมือนใคร ด้วยการถ่ายทอดสด อินเทอร์เฟซใช้งานง่าย และการค้าตลอด 24 ชั่วโมง คุณจะได้ประสบการณ์การพนันที่ราบรืennesาน",
    imageUrl: "/sv388/sv388.png",
  };

  const irresistibleTextData = [
    {
      titleText: "ลเลอร์สดในเวลา",
      text: "ความตื่นเต้นของการไก่ชนสด ถ่ายทอดสด และลเลอร์",
    },
    {
      titleText: "เกมที่สามารถเล่นได้",
      text: "เข้า SV388 ได้จากทุกที่ ด้วยแอปพลิเคชันที่ใช้งานง่ายและตอบสนองได้",
    },
    {
      titleText: "เกมคลาสกหลากหลาย",
      text: "จากการแข่งขันไก่ชนหลากหลาย พร้อมต่อรอง ธรรมและโอกาสในการชนะ",
    },
    {
      titleText: "หลายภาษา",
      text: "ใช้งานได้ในภาษาไทยและอื่นๆ ทำให้เล่นจากทั่วโลกสามารถเพลิดเพลินได้",
    },
  ];

  const gamingScreenshotObj = {
    header: "ภาพหน้าจอ SV388 Portal",
    imgList: ["/sv388/sc1.png", "/sv388/sc2.png", "/sv388/sc3.png"],
    contents: [
      "สำรวจอินเทอร์เฟซที่ใช้งานง่ายของ SV388 ออกแบบมาเพื่อประสบการณ์การพนันที่ราบรืennesาน",
      "ชมการถ่ายทอดสดของการแข่งขันไก่ชนที่น่าตื่นเต้นจากสนามแข่งขันได้",
      "เพลิดเพลินกับการแข่งขันที่หลากหลายและต่อรอง ธรรม",
    ],
  };

  return (
    <div className="bg-black">
      <Banner text="SV388 Portal ออนไลน์" />
      <div className="container mx-auto px-3 md:px-[4rem]">
        <Hero {...obj} />
        <GamingScreenShot {...gamingScreenshotObj} />
        <IrresistibleComponent
          topHeading="เหตุผลที่คุณไม่ควรพลาด SV388"
          src="/casino.png"
          altImage="sv388 portal"
          textData={irresistibleTextData}
        />
      </div>
    </div>
  );
};

export default Page;
