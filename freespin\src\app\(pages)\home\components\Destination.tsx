"use client";
// import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON> } from "lucide-react";
// import Slider from "react-slick";
// import DestinationCard from "./DestinationCard";
// import { destinationObj, destinationSettings } from "./home.obj";
// import { motion } from "framer-motion";
// import { useRef } from "react";
// import BasicHeader from "./BasicHeader";
// const Destination = () => {
//   const sliderRef = useRef<Slider | null>(null);

//   return (
//     <section className="bg-black">
//       <div className="container mx-auto">
//         <div className=" text-white w-full grid py-12  grid-cols-1  md:grid-cols-2 gap-4">
//           <div className="space-y-4">
//             <BasicHeader
//               text={t("yourultimateonlinegaming")}
//               className="text-start"
//             />
//             <h3 className="font-bold  md:text-4xl sm:text-3xl text-2xl">
//               Why Choose Freespin168 –<br /> Top Online Casino in
//               <br /> Thailand
//             </h3>
//           </div>
//           <div className="flex items-center text-justify text-[#A9A7B0] justify-center">
//             <div className="h-auto w-auto text-sm">
//               If you enjoy the thrill of casino games anytime, anywhere, then
//               Freespin168 is the perfect choice for you. Say goodbye to the
//               hassle of visiting physical casinos — online platforms like
//               Freespin168 offer a smoother, more convenient experience right
//               from the comfort of your home.
//             </div>
//             <div className="flex items-center gap-4 mx-6">
//               <motion.button
//                 whileTap={{ scale: 0.9 }}
//                 onClick={() => sliderRef.current?.slickPrev()}
//                 className="border hover:bg-[#A945F1] bg-black transition-colors duration-500  text-white cursor-pointer  p-2 rounded-full"
//               >
//                 <ArrowLeft size={14} />
//               </motion.button>
//               <motion.button
//                 onClick={() => sliderRef.current?.slickNext()}
//                 whileTap={{ scale: 0.9 }}
//                 className="border bg-black hover:bg-[#A945F1] transition-colors duration-500 text-white cursor-pointer  p-2 rounded-full"
//               >
//                 <ArrowRight size={14} />
//               </motion.button>
//             </div>
//           </div>
//         </div>
//         <Slider ref={sliderRef} {...destinationSettings} className="space-x-4">
//           {destinationObj.map((item, index) => (
//             <DestinationCard key={index} {...item} />
//           ))}
//         </Slider>
//       </div>
//     </section>
//   );
import { ArrowLeft, ArrowRight } from "lucide-react";
import Slider from "react-slick";
import DestinationCard from "./DestinationCard";
import { destinationObj, destinationSettings } from "./home.obj";
import { motion } from "framer-motion";
import { useRef } from "react";
import BasicHeader from "./BasicHeader";

const Destination = () => {
  const sliderRef = useRef<Slider | null>(null);

  return (
    <section className="bg-black ">
      <div className="container mx-auto px-3 md:px-[4rem]">
        <div className="text-white w-full grid py-12 grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-4">
            <BasicHeader
              text="จุดหมายปลายทางแห่งการเล่นเกมออนไลน์ที่ดีที่สุดของคุณ"
              className="text-start"
            />
            <h3 className="font-bold md:text-4xl sm:text-3xl text-2xl">
              ทำไมต้องเลือก Freespin168 -
              <br /> คาสิโนออนไลน์ชั้นนำในประเทศไทย
            </h3>
          </div>

          <div className="flex items-center text-justify text-[#A9A7B0] justify-center">
            <div className="h-auto w-auto text-sm">
              หากคุณเพลิดเพลินกับความตื่นเต้นของเกมคาสิโนได้ทุกที่ทุกเวลา
              Freespin168 คือตัวเลือกที่สมบูรณ์แบบสำหรับคุณ
              บอกลาความยุ่งยากในการไปคาสิโนจริง ๆ ได้เลย — แพลตฟอร์มออนไลน์อย่าง
              Freespin168
              มอบประสบการณ์ที่ราบรื่นและสะดวกสบายยิ่งขึ้นจากที่บ้านของคุณ
            </div>

            {/* Navigation buttons */}
            <div className="flex items-center gap-4 mx-6">
              {/* Left arrow button with gradient border */}
              <motion.button
                whileTap={{ scale: 0.9 }}
                onClick={() => sliderRef.current?.slickPrev()}
                className="gradient-border text-white cursor-pointer p-2"
              >
                <ArrowLeft size={14} />
              </motion.button>

              {/* Right arrow button with gradient border */}
              <motion.button
                whileTap={{ scale: 0.9 }}
                onClick={() => sliderRef.current?.slickNext()}
                className="gradient-border text-white cursor-pointer p-2"
              >
                <ArrowRight size={14} />
              </motion.button>
            </div>
          </div>
        </div>

        {/* Slider */}
        <Slider ref={sliderRef} {...destinationSettings} className="space-x-4">
          {destinationObj.map((item, index) => (
            <DestinationCard key={index} {...item} />
          ))}
        </Slider>
      </div>
    </section>
  );
};

export default Destination;
