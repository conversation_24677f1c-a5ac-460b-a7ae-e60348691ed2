// "use client";

// import { useState } from "react";
// import { motion } from "framer-motion";
// import { Menu, X, ChevronLeft, ChevronRight } from "lucide-react";
// import {
//   Sheet,
//   SheetContent,
//   SheetTrigger,
//   SheetTitle,
// } from "@/components/ui/sheet";
// import { Button } from "@/components/ui/button";
// import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
// import Link from "next/link";

// const navItems = [
//   { name: "Blogs", href: "/dashboard/blogs" },
//   { name: "Categories", href: "/dashboard/blogs/categories" },
// ];

// const SidebarDrawer: React.FC = () => {
//   const [isOpen, setIsOpen] = useState<boolean>(false);
//   const [isDesktopCollapsed, setIsDesktopCollapsed] = useState<boolean>(false);

//   const sidebarVariants = {
//     hidden: { opacity: 0, x: -20 },
//     visible: { opacity: 1, x: 0, transition: { duration: 0.3 } },
//   };

//   const desktopSidebarVariants = {
//     expanded: { width: 256, transition: { duration: 0.3 } },
//     collapsed: { width: 64, transition: { duration: 0.3 } },
//   };

//   return (
//     <>
//       <Sheet open={isOpen} onOpenChange={setIsOpen}>
//         <SheetTrigger asChild>
//           <Button
//             variant="outline"
//             size="icon"
//             className="fixed top-4 left-4 z-10 md:hidden"
//           >
//             <Menu size={24} />
//             <span className="sr-only">Toggle menu</span>
//           </Button>
//         </SheetTrigger>
//         <SheetContent side="left" className="w-64 p-4 bg-gray-800 text-white">
//           <VisuallyHidden asChild>
//             <SheetTitle>Navigation Menu</SheetTitle>
//           </VisuallyHidden>
//           <motion.div
//             variants={sidebarVariants}
//             initial="hidden"
//             animate="visible"
//           >
//             <div className="flex justify-between items-center mb-8">
//               <h2 className="text-2xl font-bold jakarta">Dashboard</h2>
//             </div>
//             <nav>
//               <ul className="space-y-4">
//                 {navItems.map((item) => (
//                   <li key={item.name} className="jakarta">
//                     <Link
//                       href={item.href}
//                       className="block p-2 rounded-md hover:bg-gray-700 transition-colors"
//                       onClick={() => setIsOpen(false)}
//                     >
//                       {item.name}
//                     </Link>
//                   </li>
//                 ))}
//               </ul>
//             </nav>
//           </motion.div>
//         </SheetContent>
//       </Sheet>

//       {/* Desktop Sidebar */}
//       <motion.aside
//         className="hidden md:block min-h-screen bg-gray-800 text-white shadow-lg"
//         variants={desktopSidebarVariants}
//         animate={isDesktopCollapsed ? "collapsed" : "expanded"}
//       >
//         <div className="p-4 relative">
//           <Button
//             variant="ghost"
//             size="icon"
//             className="absolute top-4 right-2 text-white hover:bg-gray-700"
//             onClick={() => setIsDesktopCollapsed(!isDesktopCollapsed)}
//           >
//             {isDesktopCollapsed ? (
//               <ChevronRight size={24} />
//             ) : (
//               <ChevronLeft size={24} />
//             )}
//             <span className="sr-only">Toggle sidebar</span>
//           </Button>
//           {!isDesktopCollapsed && (
//             <h2 className="text-2xl font-bold mb-8 jakarta">Dashboard</h2>
//           )}
//           <nav>
//             <ul className="space-y-4">
//               {navItems.map((item) => (
//                 <li key={item.name} className="jakarta">
//                   <Link
//                     href={item.href}
//                     className="block p-2 rounded-md hover:bg-gray-700 transition-colors"
//                     title={isDesktopCollapsed ? item.name : ""}
//                   >
//                     {isDesktopCollapsed ? item.name[0] : item.name}
//                   </Link>
//                 </li>
//               ))}
//             </ul>
//           </nav>
//         </div>
//       </motion.aside>
//     </>
//   );
// };

// export default SidebarDrawer;

// "use client";

// import { useState } from "react";
// import { motion } from "framer-motion";
// import {
//   Menu,
//   X,
//   ChevronLeft,
//   ChevronRight,
//   FileText,
//   Folder,
// } from "lucide-react";
// import {
//   Sheet,
//   SheetContent,
//   SheetTrigger,
//   SheetTitle,
// } from "@/components/ui/sheet";
// import { Button } from "@/components/ui/button";
// import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
// import Link from "next/link";

// const navItems = [
//   { name: "Blogs", href: "/dashboard/blogs", icon: FileText },
//   { name: "Categories", href: "/dashboard/blogs/categories", icon: Folder },
// ];

// const SidebarDrawer: React.FC = () => {
//   const [isOpen, setIsOpen] = useState<boolean>(false);
//   const [isDesktopCollapsed, setIsDesktopCollapsed] = useState<boolean>(false);

//   const sidebarVariants = {
//     hidden: { opacity: 0, x: -20 },
//     visible: { opacity: 1, x: 0, transition: { duration: 0.3 } },
//   };

//   const desktopSidebarVariants = {
//     expanded: { width: 256, transition: { duration: 0.3 } },
//     collapsed: { width: 64, transition: { duration: 0.3 } },
//   };

//   return (
//     <>
//       <Sheet open={isOpen} onOpenChange={setIsOpen}>
//         <SheetTrigger asChild>
//           <Button
//             variant="outline"
//             size="icon"
//             className="fixed top-4 left-4 z-10 md:hidden"
//           >
//             <Menu size={24} />
//             <span className="sr-only">Toggle menu</span>
//           </Button>
//         </SheetTrigger>
//         <SheetContent side="left" className="w-64 p-4 bg-gray-800 text-white">
//           <VisuallyHidden asChild>
//             <SheetTitle>Navigation Menu</SheetTitle>
//           </VisuallyHidden>
//           <motion.div
//             variants={sidebarVariants}
//             initial="hidden"
//             animate="visible"
//           >
//             <div className="flex justify-between items-center mb-8">
//               <h2 className="text-2xl font-bold jakarta">Dashboard</h2>
//             </div>
//             <nav>
//               <ul className="space-y-4">
//                 {navItems.map((item) => (
//                   <li key={item.name} className="jakarta">
//                     <Link
//                       href={item.href}
//                       className="flex items-center gap-2 p-2 rounded-md hover:bg-gray-700 transition-colors"
//                       onClick={() => setIsOpen(false)}
//                     >
//                       <item.icon size={20} />
//                       {item.name}
//                     </Link>
//                   </li>
//                 ))}
//               </ul>
//             </nav>
//           </motion.div>
//         </SheetContent>
//       </Sheet>

//       {/* Desktop Sidebar */}
//       <motion.aside
//         className="hidden md:block min-h-screen bg-gray-800 text-white shadow-lg"
//         variants={desktopSidebarVariants}
//         animate={isDesktopCollapsed ? "collapsed" : "expanded"}
//       >
//         <div className="p-4 relative">
//           <Button
//             variant="ghost"
//             size="icon"
//             className="absolute top-4 right-2 text-white hover:bg-gray-700"
//             onClick={() => setIsDesktopCollapsed(!isDesktopCollapsed)}
//           >
//             {isDesktopCollapsed ? (
//               <ChevronRight size={24} />
//             ) : (
//               <ChevronLeft size={24} />
//             )}
//             <span className="sr-only">Toggle sidebar</span>
//           </Button>
//           {!isDesktopCollapsed && (
//             <h2 className="text-2xl font-bold mb-8 jakarta">Dashboard</h2>
//           )}
//           <nav>
//             <ul className="space-y-4">
//               {navItems.map((item) => (
//                 <li key={item.name} className="jakarta">
//                   <Link
//                     href={item.href}
//                     className="flex items-center gap-2 p-2 rounded-md hover:bg-gray-700 transition-colors"
//                     title={isDesktopCollapsed ? item.name : ""}
//                   >
//                     <item.icon size={20} />
//                     {!isDesktopCollapsed && item.name}
//                   </Link>
//                 </li>
//               ))}
//             </ul>
//           </nav>
//         </div>
//       </motion.aside>
//     </>
//   );
// };

// export default SidebarDrawer;

"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import {
  Menu,
  X,
  ChevronLeft,
  ChevronRight,
  FileText,
  Folder,
  LogOut,
  User,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
  SheetTitle,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import Link from "next/link";
import { useAuth } from "@/hooks/useAuth";
import { logoutUser } from "@/client_apis/auth";
import { useRouter, usePathname } from "next/navigation";
import { toast } from "react-toastify";

const navItems = [
  { name: "Blogs", href: "/dashboard/blogs", icon: FileText },
  { name: "Categories", href: "/dashboard/blogs/categories", icon: Folder },
];

const SidebarDrawer: React.FC = () => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isDesktopCollapsed, setIsDesktopCollapsed] = useState<boolean>(false);
  const { user } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  // Function to check if a link is active
  const isActiveLink = (href: string) => {
    // Remove locale from pathname for comparison (e.g., /en/dashboard/blogs -> /dashboard/blogs)
    const pathWithoutLocale = pathname.replace(/^\/[a-z]{2}/, "");

    // Exact match
    if (pathWithoutLocale === href) {
      return true;
    }

    // For sub-routes, only match if the current path starts with href + "/"
    // AND there's no other nav item that's a more specific match
    const isSubRoute = pathWithoutLocale.startsWith(href + "/");
    if (isSubRoute) {
      // Check if any other nav item is a more specific match
      const moreSpecificMatch = navItems.some(
        (item) =>
          item.href !== href &&
          item.href.startsWith(href) &&
          pathWithoutLocale.startsWith(item.href)
      );
      return !moreSpecificMatch;
    }

    return false;
  };

  const handleLogout = async () => {
    try {
      const result = await logoutUser();
      if (result.success) {
        toast.success("Logged out successfully");
        router.push("/auth");
      } else {
        toast.error("Logout failed");
      }
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("Logout failed");
    }
  };

  const sidebarVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.3 } },
  };

  const desktopSidebarVariants = {
    expanded: { width: 256, transition: { duration: 0.3 } },
    collapsed: { width: 64, transition: { duration: 0.3 } },
  };

  return (
    <>
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button
            variant="outline"
            size="icon"
            className="fixed top-4 left-4 z-10 md:hidden"
          >
            <Menu size={24} />
            <span className="sr-only">Toggle menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-64 p-4 bg-gray-800 text-white">
          <VisuallyHidden asChild>
            <SheetTitle>Navigation Menu</SheetTitle>
          </VisuallyHidden>
          <motion.div
            variants={sidebarVariants}
            initial="hidden"
            animate="visible"
          >
            <div className="flex justify-between items-center mb-8">
              <h2 className="text-2xl font-bold jakarta">Dashboard</h2>
            </div>

            {/* User Info */}
            {user && (
              <div className="mb-6 p-3 bg-gray-700 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <User size={16} />
                  <span className="text-sm font-medium">{user.username}</span>
                </div>
                <div className="text-xs text-gray-300">Role: {user.role}</div>
              </div>
            )}

            <nav>
              <ul className="space-y-4">
                {navItems.map((item) => {
                  const isActive = isActiveLink(item.href);
                  return (
                    <li key={item.name} className="jakarta">
                      <Link
                        href={item.href}
                        className={`flex items-center gap-2 p-2 rounded-md transition-colors ${
                          isActive
                            ? "bg-blue-600 text-white"
                            : "hover:bg-gray-700"
                        }`}
                        onClick={() => setIsOpen(false)}
                      >
                        <item.icon size={20} />
                        {item.name}
                      </Link>
                    </li>
                  );
                })}
              </ul>

              {/* Logout Button - directly below nav links */}
              <div className="mt-6 pt-4 border-t border-gray-600">
                <button
                  onClick={handleLogout}
                  className="flex items-center gap-2 w-full p-2 rounded-md hover:bg-gray-700 transition-colors text-red-300 hover:text-red-200"
                >
                  <LogOut size={20} />
                  Logout
                </button>
              </div>
            </nav>
          </motion.div>
        </SheetContent>
      </Sheet>

      {/* Desktop Sidebar */}
      <motion.aside
        className="hidden md:flex md:flex-col min-h-screen bg-gray-800 text-white shadow-lg"
        variants={desktopSidebarVariants}
        animate={isDesktopCollapsed ? "collapsed" : "expanded"}
      >
        <div className="p-4 relative flex-1 flex flex-col">
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-4 right-2 text-white hover:bg-gray-700 z-10"
            onClick={() => setIsDesktopCollapsed(!isDesktopCollapsed)}
          >
            {isDesktopCollapsed ? (
              <ChevronRight size={24} />
            ) : (
              <ChevronLeft size={24} />
            )}
            <span className="sr-only">Toggle sidebar</span>
          </Button>

          {!isDesktopCollapsed && (
            <h2 className="text-2xl font-bold mb-8 jakarta">Dashboard</h2>
          )}

          {/* User Info for Desktop */}
          {user && !isDesktopCollapsed && (
            <div className="mb-6 p-3 bg-gray-700 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <User size={16} />
                <span className="text-sm font-medium">{user.username}</span>
              </div>
              <div className="text-xs text-gray-300">Role: {user.role}</div>
            </div>
          )}

          {/* User Icon for Collapsed */}
          {user && isDesktopCollapsed && (
            <div className="mb-6 flex justify-center mt-12">
              <div
                className="p-2 bg-gray-700 rounded-lg"
                title={`${user.username} (${user.role})`}
              >
                <User size={20} />
              </div>
            </div>
          )}

          <nav className="flex-1">
            <ul
              className={`space-y-4 ${
                isDesktopCollapsed && !user ? "mt-12" : ""
              }`}
            >
              {navItems.map((item) => {
                const isActive = isActiveLink(item.href);
                return (
                  <li key={item.name} className="jakarta">
                    <Link
                      href={item.href}
                      className={`flex items-center gap-2 p-2 rounded-md transition-colors justify-center md:justify-start ${
                        isActive
                          ? "bg-gray-700 text-white"
                          : "hover:bg-gray-700"
                      }`}
                      title={isDesktopCollapsed ? item.name : ""}
                    >
                      <item.icon size={20} />
                      {!isDesktopCollapsed && item.name}
                    </Link>
                  </li>
                );
              })}
            </ul>

            {/* Logout Button for Desktop - directly below nav links */}
            <div className="mt-6 pt-4 border-t border-gray-600">
              <button
                onClick={handleLogout}
                className="flex items-center gap-2 w-full p-2 rounded-md hover:bg-gray-700 transition-colors text-red-300 hover:text-red-200 justify-center md:justify-start"
                title={isDesktopCollapsed ? "Logout" : ""}
              >
                <LogOut size={20} />
                {!isDesktopCollapsed && "Logout"}
              </button>
            </div>
          </nav>
        </div>
      </motion.aside>
    </>
  );
};

export default SidebarDrawer;
