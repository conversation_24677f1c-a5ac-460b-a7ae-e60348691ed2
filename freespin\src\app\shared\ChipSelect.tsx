"use client";
import React, { useState, useRef, useEffect } from "react";

const allOptions = [
  "Apple",
  "Banana",
  "Grapes",
  "Orange",
  "Mango",
  "Pineapple",
];

const ChipSelect = () => {
  const [tags, setTags] = useState<string[]>([]);
  const [input, setInput] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const wrapperRef = useRef<HTMLDivElement>(null);

  const filteredOptions = allOptions.filter(
    (opt) =>
      opt.toLowerCase().includes(input.toLowerCase()) && !tags.includes(opt)
  );

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && input.trim()) {
      e.preventDefault();
      const match = allOptions.find(
        (opt) => opt.toLowerCase() === input.trim().toLowerCase()
      );
      const tagToAdd = match || input.trim();
      if (!tags.includes(tagToAdd)) {
        setTags([...tags, tagToAdd]);
        setInput("");
      }
    } else if (e.key === "Backspace" && !input && tags.length) {
      setTags(tags.slice(0, -1));
    }
  };

  const removeTag = (i: number) => {
    setTags(tags.filter((_, index) => index !== i));
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      wrapperRef.current &&
      !wrapperRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="relative w-full max-w-md" ref={wrapperRef}>
      <div
        className="border border-gray-300 rounded p-2 flex flex-wrap gap-2 items-center min-h-[44px] cursor-text"
        onClick={() => setIsOpen(true)}
      >
        {tags.map((tag, i) => (
          <span
            key={i}
            className="bg-blue-200 px-2 py-1 rounded flex items-center gap-1 text-sm"
          >
            {tag}
            <button
              type="button"
              onClick={() => removeTag(i)}
              className="text-red-500 hover:text-red-700"
            >
              &times;
            </button>
          </span>
        ))}
        <input
          className="flex-grow outline-none min-w-[100px]"
          type="text"
          value={input}
          onFocus={() => setIsOpen(true)}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Select or type..."
        />
      </div>

      {isOpen && filteredOptions.length > 0 && (
        <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded shadow-lg max-h-40 overflow-y-auto">
          {filteredOptions.map((option, index) => (
            <div
              key={index}
              onClick={() => {
                setTags([...tags, option]);
                setInput("");
                setIsOpen(false);
              }}
              className="px-4 py-2 hover:bg-blue-100 cursor-pointer"
            >
              {option}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ChipSelect;
