import type { NextRequest } from "next/server";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

export interface AuthUser {
  userId: string;
  email: string;
  username: string;
  role: string;
}

export interface AuthResult {
  success: boolean;
  user?: AuthUser;
  error?: string;
}

export async function verifyAuth(request: NextRequest): Promise<AuthResult> {
  try {
    // Debug: Log all cookies and headers
    console.log("=== AUTH DEBUG ===");
    console.log("All cookies:", request.cookies.getAll());
    console.log("Authorization header:", request.headers.get("authorization"));
    console.log("Cookie header:", request.headers.get("cookie"));

    // Get token from Authorization header or cookies
    const authHeader = request.headers.get("authorization");
    const authTokenCookie = request.cookies.get("auth-token")?.value;
    const sessionTokenCookie = request.cookies.get("session-token")?.value;

    console.log("Auth header token:", authHeader?.replace("Bearer ", ""));
    console.log("Auth token cookie:", authTokenCookie);
    console.log("Session token cookie:", sessionTokenCookie);

    const token =
      authHeader?.replace("Bearer ", "") ||
      authTokenCookie ||
      sessionTokenCookie;

    console.log("Final token:", token ? "Found" : "Not found");
    console.log("==================");

    if (!token) {
      return { success: false, error: "No authentication token provided" };
    }

    // Verify token
    const decoded = jwt.verify(token, JWT_SECRET) as AuthUser;

    return { success: true, user: decoded };
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return { success: false, error: "Invalid authentication token" };
    }

    return { success: false, error: "Authentication failed" };
  }
}

export function requireAuth(roles?: string[]) {
  return async (request: NextRequest) => {
    const authResult = await verifyAuth(request);

    if (!authResult.success) {
      return authResult;
    }

    if (roles && !roles.includes(authResult.user!.role)) {
      return { success: false, error: "Insufficient permissions" };
    }

    return authResult;
  };
}
