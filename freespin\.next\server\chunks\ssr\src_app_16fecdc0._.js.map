{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/shared/cleanObject.ts"], "sourcesContent": ["export function cleanObject(obj: any): any {\r\n  if (typeof obj !== \"object\" || obj === null) return obj;\r\n\r\n  const cleaned: any = {};\r\n\r\n  for (const [key, value] of Object.entries(obj)) {\r\n    if (\r\n      value === undefined ||\r\n      value === null ||\r\n      value === \"\" ||\r\n      (typeof value === \"object\" &&\r\n        !Array.isArray(value) &&\r\n        Object.keys(cleanObject(value)).length === 0)\r\n    ) {\r\n      continue;\r\n    }\r\n\r\n    if (Array.isArray(value)) {\r\n      const cleanedArray = value\r\n        .map((item) => (typeof item === \"object\" ? cleanObject(item) : item))\r\n        .filter((item) =>\r\n          typeof item === \"object\"\r\n            ? Object.keys(item).length > 0\r\n            : item !== null && item !== undefined && item !== \"\"\r\n        );\r\n\r\n      if (cleanedArray.length > 0) {\r\n        cleaned[key] = cleanedArray;\r\n      }\r\n    } else if (typeof value === \"object\") {\r\n      const cleanedObj = cleanObject(value);\r\n      if (Object.keys(cleanedObj).length > 0) {\r\n        cleaned[key] = cleanedObj;\r\n      }\r\n    } else {\r\n      cleaned[key] = value;\r\n    }\r\n  }\r\n\r\n  return cleaned;\r\n}\r\n\r\nexport const convertToBase64 = (file: File) => {\r\n  return new Promise((resolve, reject) => {\r\n    const reader = new FileReader();\r\n\r\n    reader.readAsDataURL(file); // This encodes file to base64\r\n    reader.onload = () => resolve(reader.result);\r\n    reader.onerror = (error) => reject(error);\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;AAAO,SAAS,YAAY,GAAQ;IAClC,IAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM,OAAO;IAEpD,MAAM,UAAe,CAAC;IAEtB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAM;QAC9C,IACE,UAAU,aACV,UAAU,QACV,UAAU,MACT,OAAO,UAAU,YAChB,CAAC,MAAM,OAAO,CAAC,UACf,OAAO,IAAI,CAAC,YAAY,QAAQ,MAAM,KAAK,GAC7C;YACA;QACF;QAEA,IAAI,MAAM,OAAO,CAAC,QAAQ;YACxB,MAAM,eAAe,MAClB,GAAG,CAAC,CAAC,OAAU,OAAO,SAAS,WAAW,YAAY,QAAQ,MAC9D,MAAM,CAAC,CAAC,OACP,OAAO,SAAS,WACZ,OAAO,IAAI,CAAC,MAAM,MAAM,GAAG,IAC3B,SAAS,QAAQ,SAAS,aAAa,SAAS;YAGxD,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,OAAO,CAAC,IAAI,GAAG;YACjB;QACF,OAAO,IAAI,OAAO,UAAU,UAAU;YACpC,MAAM,aAAa,YAAY;YAC/B,IAAI,OAAO,IAAI,CAAC,YAAY,MAAM,GAAG,GAAG;gBACtC,OAAO,CAAC,IAAI,GAAG;YACjB;QACF,OAAO;YACL,OAAO,CAAC,IAAI,GAAG;QACjB;IACF;IAEA,OAAO;AACT;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,SAAS,IAAI;QAEnB,OAAO,aAAa,CAAC,OAAO,8BAA8B;QAC1D,OAAO,MAAM,GAAG,IAAM,QAAQ,OAAO,MAAM;QAC3C,OAAO,OAAO,GAAG,CAAC,QAAU,OAAO;IACrC;AACF", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/dashboard/blogs/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { cookies } from \"next/headers\";\r\nimport { revalidateTag, revalidatePath } from \"next/cache\";\r\nimport connectDB from \"@/lib/mongodb\";\r\nimport Post from \"@/models/Post\";\r\nimport Category from \"@/models/Category\";\r\nimport { MinioService } from \"@/lib/minio\";\r\nimport jwt from \"jsonwebtoken\";\r\n\r\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key\";\r\n\r\nexport async function updatePostStatusAction(postId: string, status: string) {\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    let userInfo = null;\r\n\r\n    // Try both auth methods\r\n    if (authToken) {\r\n      try {\r\n        userInfo = jwt.verify(authToken, JWT_SECRET) as any;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Fallback to user-info cookie\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"Authentication required\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Find the post first\r\n    const post = await Post.findById(postId);\r\n\r\n    if (!post) {\r\n      return { success: false, error: \"Post not found\" };\r\n    }\r\n\r\n    // Update the status and save to trigger middleware\r\n    post.status = status as \"draft\" | \"published\" | \"archived\";\r\n\r\n    // Manually handle the published state logic\r\n    if (status === \"published\") {\r\n      post.isPublished = true;\r\n      if (!post.publishedAt) {\r\n        post.publishedAt = new Date();\r\n      }\r\n    } else {\r\n      post.isPublished = false;\r\n    }\r\n\r\n    const updatedPost = await post.save();\r\n\r\n    // Revalidate the public posts cache to show updated posts immediately\r\n    revalidateTag(\"public-posts\");\r\n\r\n    // Also revalidate the blogs page to show updated posts\r\n    revalidatePath(\"/en/blogs\");\r\n    revalidatePath(\"/th/blogs\");\r\n\r\n    // Serialize the post data for client components\r\n    const serializedPost = {\r\n      _id: updatedPost._id.toString(),\r\n      title: updatedPost.title,\r\n      status: updatedPost.status,\r\n      isPublished: updatedPost.isPublished,\r\n      publishedAt: updatedPost.publishedAt?.toISOString(),\r\n      createdAt: updatedPost.createdAt?.toISOString(),\r\n      updatedAt: updatedPost.updatedAt?.toISOString(),\r\n    };\r\n\r\n    return {\r\n      success: true,\r\n      data: { post: serializedPost },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error updating post status:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getCategoriesAction() {\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n\r\n    console.log(\"=== CATEGORIES ACTION DEBUG ===\");\r\n    console.log(\"All cookies:\", cookieStore.getAll());\r\n\r\n    // Try both auth methods: httpOnly token and user-info cookie\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    console.log(\"Auth token:\", authToken ? \"Found\" : \"Not found\");\r\n    console.log(\"User info cookie:\", userInfoCookie ? \"Found\" : \"Not found\");\r\n\r\n    let userInfo = null;\r\n\r\n    // Method 1: Try httpOnly token\r\n    if (authToken) {\r\n      try {\r\n        const decoded = jwt.verify(authToken, JWT_SECRET) as any;\r\n        console.log(\"Token verified successfully for user:\", decoded.userId);\r\n        userInfo = decoded;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Method 2: Try user-info cookie (fallback)\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        console.log(\"User info from cookie:\", parsedUserInfo);\r\n\r\n        // Verify user is admin\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n          console.log(\"Admin access granted via user-info cookie\");\r\n        } else {\r\n          return { success: false, error: \"Admin access required\" };\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"No valid authentication found\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Get all categories\r\n    const categories = await Category.find({ isActive: true })\r\n      .sort({ name: 1 })\r\n      .lean();\r\n\r\n    // Convert MongoDB documents to plain objects for client components\r\n    const serializedCategories = categories.map((category: any) => ({\r\n      _id: category._id.toString(), // Convert ObjectId to string\r\n      name: category.name,\r\n      description: category.description || \"\",\r\n      color: category.color || \"#6366f1\",\r\n      isActive: category.isActive,\r\n      createdAt: category.createdAt?.toISOString() || new Date().toISOString(),\r\n      updatedAt: category.updatedAt?.toISOString() || new Date().toISOString(),\r\n    }));\r\n\r\n    return {\r\n      success: true,\r\n      data: { categories: serializedCategories },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching categories:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createCategoryAction(data: {\r\n  name: string;\r\n  description: string;\r\n}) {\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    let userInfo = null;\r\n\r\n    // Try both auth methods\r\n    if (authToken) {\r\n      try {\r\n        userInfo = jwt.verify(authToken, JWT_SECRET) as any;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Fallback to user-info cookie\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"Authentication required\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Check if category already exists (case-insensitive)\r\n    const existingCategory = await Category.findOne({\r\n      name: { $regex: new RegExp(`^${data.name}$`, \"i\") },\r\n    });\r\n\r\n    if (existingCategory) {\r\n      return { success: false, error: \"Category already exists\" };\r\n    }\r\n\r\n    // Create new category\r\n    const category = new Category(data);\r\n    await category.save();\r\n\r\n    // Serialize the category for client components\r\n    const serializedCategory = {\r\n      _id: category._id.toString(),\r\n      name: category.name,\r\n      description: category.description || \"\",\r\n      color: category.color || \"#6366f1\",\r\n      isActive: category.isActive,\r\n      createdAt: category.createdAt?.toISOString() || new Date().toISOString(),\r\n      updatedAt: category.updatedAt?.toISOString() || new Date().toISOString(),\r\n    };\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Category created successfully\",\r\n      data: { category: serializedCategory },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error creating category:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createPostAction(\r\n  postData: any,\r\n  bannerImageBase64?: string\r\n) {\r\n  console.log(\"hello\");\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    let userInfo = null;\r\n\r\n    // Try both auth methods\r\n    if (authToken) {\r\n      try {\r\n        userInfo = jwt.verify(authToken, JWT_SECRET) as any;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Fallback to user-info cookie\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"Authentication required\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Handle banner image upload if provided\r\n    let bannerUrl = \"\";\r\n    if (bannerImageBase64) {\r\n      try {\r\n        // Extract content type and base64 data\r\n        const matches = bannerImageBase64.match(/^data:(.+);base64,(.+)$/);\r\n        if (!matches || matches.length !== 3) {\r\n          throw new Error(\"Invalid base64 image format\");\r\n        }\r\n\r\n        const contentType = matches[1]; // e.g., \"image/jpeg\", \"image/png\"\r\n        const base64Data = matches[2];\r\n        const imageBuffer = Buffer.from(base64Data, \"base64\");\r\n\r\n        // Generate filename with correct extension\r\n        const timestamp = Date.now();\r\n        const fileExtension = contentType.split(\"/\")[1] || \"jpg\";\r\n        const filename = `banner-${timestamp}.${fileExtension}`;\r\n\r\n        console.log(\"Uploading banner image:\", {\r\n          contentType,\r\n          filename,\r\n          bufferSize: imageBuffer.length,\r\n        });\r\n\r\n        // Upload to MinIO\r\n        bannerUrl = await MinioService.uploadFile(\r\n          imageBuffer,\r\n          filename,\r\n          contentType, // Use detected content type\r\n          \"banners\" // folder\r\n        );\r\n        console.log(\"Banner uploaded successfully:\", bannerUrl);\r\n      } catch (error) {\r\n        console.error(\"Error uploading banner:\", error);\r\n        // Continue without banner if upload fails\r\n      }\r\n    }\r\n\r\n    // Create the post\r\n    const postToCreate = {\r\n      ...postData,\r\n      banner: bannerUrl || postData.banner || \"\",\r\n      author: userInfo.userId || userInfo._id,\r\n      status: \"draft\", // Default to draft\r\n      isBlog: true,\r\n      isPublished: false,\r\n    };\r\n\r\n    const post = new Post(postToCreate);\r\n    await post.save();\r\n\r\n    // Serialize the post for client components\r\n    const serializedPost = {\r\n      _id: post._id.toString(),\r\n      title: post.title,\r\n      slug: post.slug,\r\n      excerpt: post.excerpt,\r\n      content: post.content,\r\n      banner: post.banner,\r\n      status: post.status,\r\n      isPublished: post.isPublished,\r\n      publishedAt: post.publishedAt?.toISOString(),\r\n      createdAt: post.createdAt?.toISOString(),\r\n      updatedAt: post.updatedAt?.toISOString(),\r\n    };\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Post created successfully\",\r\n      data: { post: serializedPost },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error creating post:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePostAction(\r\n  postId: string,\r\n  postData: any,\r\n  bannerImageBase64?: string\r\n) {\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    let userInfo = null;\r\n\r\n    // Try both auth methods\r\n    if (authToken) {\r\n      try {\r\n        userInfo = jwt.verify(authToken, JWT_SECRET) as any;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Fallback to user-info cookie\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"Authentication required\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Find the post\r\n    const post = await Post.findOne({ slug: postId });\r\n    if (!post) {\r\n      return { success: false, error: \"Post not found\" };\r\n    }\r\n\r\n    // Handle banner image upload if provided\r\n    let bannerUrl =\r\n      postData.banner?.image || post.banner?.image || post.banner || \"\";\r\n    if (bannerImageBase64) {\r\n      try {\r\n        // Extract content type and base64 data\r\n        const matches = bannerImageBase64.match(/^data:(.+);base64,(.+)$/);\r\n        if (!matches || matches.length !== 3) {\r\n          throw new Error(\"Invalid base64 image format\");\r\n        }\r\n\r\n        const contentType = matches[1];\r\n        const base64Data = matches[2];\r\n        const imageBuffer = Buffer.from(base64Data, \"base64\");\r\n\r\n        // Generate filename with correct extension\r\n        const timestamp = Date.now();\r\n        const fileExtension = contentType.split(\"/\")[1] || \"jpg\";\r\n        const filename = `banner-${timestamp}.${fileExtension}`;\r\n\r\n        console.log(\"Uploading banner image:\", {\r\n          contentType,\r\n          filename,\r\n          bufferSize: imageBuffer.length,\r\n        });\r\n\r\n        // Upload to MinIO\r\n        bannerUrl = await MinioService.uploadFile(\r\n          imageBuffer,\r\n          filename,\r\n          contentType,\r\n          \"banners\"\r\n        );\r\n        console.log(\"Banner uploaded successfully:\", bannerUrl);\r\n      } catch (error) {\r\n        console.error(\"Error uploading banner:\", error);\r\n        // Continue without banner if upload fails\r\n      }\r\n    }\r\n\r\n    // Clean the postData to avoid circular references\r\n    const cleanPostData = {\r\n      title: postData.title,\r\n      slug: postData.slug, // Include slug field for updates\r\n      content: postData.content,\r\n      categories: postData.categories,\r\n      tags: postData.tags,\r\n      status: postData.status,\r\n      isPublished: postData.isPublished,\r\n      publishedAt: postData.publishedAt,\r\n      metaTitle: postData.metaTitle,\r\n      metaDescription: postData.metaDescription,\r\n      metaKeywords: postData.metaKeywords,\r\n    };\r\n\r\n    const updateData = {\r\n      ...cleanPostData,\r\n      banner: bannerUrl, // Store as string, not object\r\n      seoTitle: postData.metaTitle,\r\n      seoDescription: postData.metaDescription,\r\n      metaKeywords: postData.metaKeywords,\r\n      author: userInfo.userId || userInfo._id,\r\n    };\r\n\r\n    // Update the post\r\n    const updatedPost = (await Post.findOneAndUpdate(\r\n      { slug: postId },\r\n      updateData,\r\n      {\r\n        new: true,\r\n        runValidators: true,\r\n      }\r\n    )\r\n      .populate(\"categories\", \"name description color\")\r\n      .lean()) as any; // Add .lean() to get plain objects\r\n\r\n    if (!updatedPost) {\r\n      return { success: false, error: \"Failed to update post\" };\r\n    }\r\n\r\n    // Serialize the post for client components (ensure no circular references)\r\n    const serializedPost = {\r\n      _id: updatedPost._id.toString(),\r\n      title: updatedPost.title,\r\n      slug: updatedPost.slug,\r\n      content: updatedPost.content,\r\n      banner: updatedPost.banner,\r\n      categories: Array.isArray(updatedPost.categories)\r\n        ? updatedPost.categories.map((cat: any) => ({\r\n            _id: cat._id?.toString(),\r\n            name: cat.name,\r\n            description: cat.description,\r\n            color: cat.color,\r\n          }))\r\n        : [],\r\n      tags: updatedPost.tags || [],\r\n      seoTitle: updatedPost.seoTitle,\r\n      seoDescription: updatedPost.seoDescription,\r\n      metaKeywords: updatedPost.metaKeywords,\r\n      status: updatedPost.status,\r\n      isPublished: updatedPost.isPublished,\r\n      publishedAt:\r\n        updatedPost.publishedAt?.toISOString?.() || updatedPost.publishedAt,\r\n      createdAt:\r\n        updatedPost.createdAt?.toISOString?.() || updatedPost.createdAt,\r\n      updatedAt:\r\n        updatedPost.updatedAt?.toISOString?.() || updatedPost.updatedAt,\r\n    };\r\n\r\n    // Revalidate caches\r\n    revalidateTag(\"public-posts\");\r\n    revalidatePath(\"/en/blogs\");\r\n    revalidatePath(\"/th/blogs\");\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Post updated successfully\",\r\n      data: { post: serializedPost },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error updating post:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAsGsB,sBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/dashboard/blogs/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { cookies } from \"next/headers\";\r\nimport { revalidateTag, revalidatePath } from \"next/cache\";\r\nimport connectDB from \"@/lib/mongodb\";\r\nimport Post from \"@/models/Post\";\r\nimport Category from \"@/models/Category\";\r\nimport { MinioService } from \"@/lib/minio\";\r\nimport jwt from \"jsonwebtoken\";\r\n\r\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key\";\r\n\r\nexport async function updatePostStatusAction(postId: string, status: string) {\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    let userInfo = null;\r\n\r\n    // Try both auth methods\r\n    if (authToken) {\r\n      try {\r\n        userInfo = jwt.verify(authToken, JWT_SECRET) as any;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Fallback to user-info cookie\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"Authentication required\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Find the post first\r\n    const post = await Post.findById(postId);\r\n\r\n    if (!post) {\r\n      return { success: false, error: \"Post not found\" };\r\n    }\r\n\r\n    // Update the status and save to trigger middleware\r\n    post.status = status as \"draft\" | \"published\" | \"archived\";\r\n\r\n    // Manually handle the published state logic\r\n    if (status === \"published\") {\r\n      post.isPublished = true;\r\n      if (!post.publishedAt) {\r\n        post.publishedAt = new Date();\r\n      }\r\n    } else {\r\n      post.isPublished = false;\r\n    }\r\n\r\n    const updatedPost = await post.save();\r\n\r\n    // Revalidate the public posts cache to show updated posts immediately\r\n    revalidateTag(\"public-posts\");\r\n\r\n    // Also revalidate the blogs page to show updated posts\r\n    revalidatePath(\"/en/blogs\");\r\n    revalidatePath(\"/th/blogs\");\r\n\r\n    // Serialize the post data for client components\r\n    const serializedPost = {\r\n      _id: updatedPost._id.toString(),\r\n      title: updatedPost.title,\r\n      status: updatedPost.status,\r\n      isPublished: updatedPost.isPublished,\r\n      publishedAt: updatedPost.publishedAt?.toISOString(),\r\n      createdAt: updatedPost.createdAt?.toISOString(),\r\n      updatedAt: updatedPost.updatedAt?.toISOString(),\r\n    };\r\n\r\n    return {\r\n      success: true,\r\n      data: { post: serializedPost },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error updating post status:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function getCategoriesAction() {\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n\r\n    console.log(\"=== CATEGORIES ACTION DEBUG ===\");\r\n    console.log(\"All cookies:\", cookieStore.getAll());\r\n\r\n    // Try both auth methods: httpOnly token and user-info cookie\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    console.log(\"Auth token:\", authToken ? \"Found\" : \"Not found\");\r\n    console.log(\"User info cookie:\", userInfoCookie ? \"Found\" : \"Not found\");\r\n\r\n    let userInfo = null;\r\n\r\n    // Method 1: Try httpOnly token\r\n    if (authToken) {\r\n      try {\r\n        const decoded = jwt.verify(authToken, JWT_SECRET) as any;\r\n        console.log(\"Token verified successfully for user:\", decoded.userId);\r\n        userInfo = decoded;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Method 2: Try user-info cookie (fallback)\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        console.log(\"User info from cookie:\", parsedUserInfo);\r\n\r\n        // Verify user is admin\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n          console.log(\"Admin access granted via user-info cookie\");\r\n        } else {\r\n          return { success: false, error: \"Admin access required\" };\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"No valid authentication found\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Get all categories\r\n    const categories = await Category.find({ isActive: true })\r\n      .sort({ name: 1 })\r\n      .lean();\r\n\r\n    // Convert MongoDB documents to plain objects for client components\r\n    const serializedCategories = categories.map((category: any) => ({\r\n      _id: category._id.toString(), // Convert ObjectId to string\r\n      name: category.name,\r\n      description: category.description || \"\",\r\n      color: category.color || \"#6366f1\",\r\n      isActive: category.isActive,\r\n      createdAt: category.createdAt?.toISOString() || new Date().toISOString(),\r\n      updatedAt: category.updatedAt?.toISOString() || new Date().toISOString(),\r\n    }));\r\n\r\n    return {\r\n      success: true,\r\n      data: { categories: serializedCategories },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching categories:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createCategoryAction(data: {\r\n  name: string;\r\n  description: string;\r\n}) {\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    let userInfo = null;\r\n\r\n    // Try both auth methods\r\n    if (authToken) {\r\n      try {\r\n        userInfo = jwt.verify(authToken, JWT_SECRET) as any;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Fallback to user-info cookie\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"Authentication required\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Check if category already exists (case-insensitive)\r\n    const existingCategory = await Category.findOne({\r\n      name: { $regex: new RegExp(`^${data.name}$`, \"i\") },\r\n    });\r\n\r\n    if (existingCategory) {\r\n      return { success: false, error: \"Category already exists\" };\r\n    }\r\n\r\n    // Create new category\r\n    const category = new Category(data);\r\n    await category.save();\r\n\r\n    // Serialize the category for client components\r\n    const serializedCategory = {\r\n      _id: category._id.toString(),\r\n      name: category.name,\r\n      description: category.description || \"\",\r\n      color: category.color || \"#6366f1\",\r\n      isActive: category.isActive,\r\n      createdAt: category.createdAt?.toISOString() || new Date().toISOString(),\r\n      updatedAt: category.updatedAt?.toISOString() || new Date().toISOString(),\r\n    };\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Category created successfully\",\r\n      data: { category: serializedCategory },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error creating category:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function createPostAction(\r\n  postData: any,\r\n  bannerImageBase64?: string\r\n) {\r\n  console.log(\"hello\");\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    let userInfo = null;\r\n\r\n    // Try both auth methods\r\n    if (authToken) {\r\n      try {\r\n        userInfo = jwt.verify(authToken, JWT_SECRET) as any;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Fallback to user-info cookie\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"Authentication required\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Handle banner image upload if provided\r\n    let bannerUrl = \"\";\r\n    if (bannerImageBase64) {\r\n      try {\r\n        // Extract content type and base64 data\r\n        const matches = bannerImageBase64.match(/^data:(.+);base64,(.+)$/);\r\n        if (!matches || matches.length !== 3) {\r\n          throw new Error(\"Invalid base64 image format\");\r\n        }\r\n\r\n        const contentType = matches[1]; // e.g., \"image/jpeg\", \"image/png\"\r\n        const base64Data = matches[2];\r\n        const imageBuffer = Buffer.from(base64Data, \"base64\");\r\n\r\n        // Generate filename with correct extension\r\n        const timestamp = Date.now();\r\n        const fileExtension = contentType.split(\"/\")[1] || \"jpg\";\r\n        const filename = `banner-${timestamp}.${fileExtension}`;\r\n\r\n        console.log(\"Uploading banner image:\", {\r\n          contentType,\r\n          filename,\r\n          bufferSize: imageBuffer.length,\r\n        });\r\n\r\n        // Upload to MinIO\r\n        bannerUrl = await MinioService.uploadFile(\r\n          imageBuffer,\r\n          filename,\r\n          contentType, // Use detected content type\r\n          \"banners\" // folder\r\n        );\r\n        console.log(\"Banner uploaded successfully:\", bannerUrl);\r\n      } catch (error) {\r\n        console.error(\"Error uploading banner:\", error);\r\n        // Continue without banner if upload fails\r\n      }\r\n    }\r\n\r\n    // Create the post\r\n    const postToCreate = {\r\n      ...postData,\r\n      banner: bannerUrl || postData.banner || \"\",\r\n      author: userInfo.userId || userInfo._id,\r\n      status: \"draft\", // Default to draft\r\n      isBlog: true,\r\n      isPublished: false,\r\n    };\r\n\r\n    const post = new Post(postToCreate);\r\n    await post.save();\r\n\r\n    // Serialize the post for client components\r\n    const serializedPost = {\r\n      _id: post._id.toString(),\r\n      title: post.title,\r\n      slug: post.slug,\r\n      excerpt: post.excerpt,\r\n      content: post.content,\r\n      banner: post.banner,\r\n      status: post.status,\r\n      isPublished: post.isPublished,\r\n      publishedAt: post.publishedAt?.toISOString(),\r\n      createdAt: post.createdAt?.toISOString(),\r\n      updatedAt: post.updatedAt?.toISOString(),\r\n    };\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Post created successfully\",\r\n      data: { post: serializedPost },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error creating post:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function updatePostAction(\r\n  postId: string,\r\n  postData: any,\r\n  bannerImageBase64?: string\r\n) {\r\n  try {\r\n    // Get cookies directly\r\n    const cookieStore = await cookies();\r\n    const authToken = cookieStore.get(\"auth-token\")?.value;\r\n    const userInfoCookie = cookieStore.get(\"user-info\")?.value;\r\n\r\n    let userInfo = null;\r\n\r\n    // Try both auth methods\r\n    if (authToken) {\r\n      try {\r\n        userInfo = jwt.verify(authToken, JWT_SECRET) as any;\r\n      } catch (error) {\r\n        console.log(\"Token verification failed:\", error);\r\n      }\r\n    }\r\n\r\n    // Fallback to user-info cookie\r\n    if (!userInfo && userInfoCookie) {\r\n      try {\r\n        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));\r\n        if (parsedUserInfo.role === \"admin\") {\r\n          userInfo = parsedUserInfo;\r\n        }\r\n      } catch (error) {\r\n        console.log(\"Failed to parse user-info cookie:\", error);\r\n      }\r\n    }\r\n\r\n    if (!userInfo) {\r\n      return { success: false, error: \"Authentication required\" };\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Find the post\r\n    const post = await Post.findOne({ slug: postId });\r\n    if (!post) {\r\n      return { success: false, error: \"Post not found\" };\r\n    }\r\n\r\n    // Handle banner image upload if provided\r\n    let bannerUrl =\r\n      postData.banner?.image || post.banner?.image || post.banner || \"\";\r\n    if (bannerImageBase64) {\r\n      try {\r\n        // Extract content type and base64 data\r\n        const matches = bannerImageBase64.match(/^data:(.+);base64,(.+)$/);\r\n        if (!matches || matches.length !== 3) {\r\n          throw new Error(\"Invalid base64 image format\");\r\n        }\r\n\r\n        const contentType = matches[1];\r\n        const base64Data = matches[2];\r\n        const imageBuffer = Buffer.from(base64Data, \"base64\");\r\n\r\n        // Generate filename with correct extension\r\n        const timestamp = Date.now();\r\n        const fileExtension = contentType.split(\"/\")[1] || \"jpg\";\r\n        const filename = `banner-${timestamp}.${fileExtension}`;\r\n\r\n        console.log(\"Uploading banner image:\", {\r\n          contentType,\r\n          filename,\r\n          bufferSize: imageBuffer.length,\r\n        });\r\n\r\n        // Upload to MinIO\r\n        bannerUrl = await MinioService.uploadFile(\r\n          imageBuffer,\r\n          filename,\r\n          contentType,\r\n          \"banners\"\r\n        );\r\n        console.log(\"Banner uploaded successfully:\", bannerUrl);\r\n      } catch (error) {\r\n        console.error(\"Error uploading banner:\", error);\r\n        // Continue without banner if upload fails\r\n      }\r\n    }\r\n\r\n    // Clean the postData to avoid circular references\r\n    const cleanPostData = {\r\n      title: postData.title,\r\n      slug: postData.slug, // Include slug field for updates\r\n      content: postData.content,\r\n      categories: postData.categories,\r\n      tags: postData.tags,\r\n      status: postData.status,\r\n      isPublished: postData.isPublished,\r\n      publishedAt: postData.publishedAt,\r\n      metaTitle: postData.metaTitle,\r\n      metaDescription: postData.metaDescription,\r\n      metaKeywords: postData.metaKeywords,\r\n    };\r\n\r\n    const updateData = {\r\n      ...cleanPostData,\r\n      banner: bannerUrl, // Store as string, not object\r\n      seoTitle: postData.metaTitle,\r\n      seoDescription: postData.metaDescription,\r\n      metaKeywords: postData.metaKeywords,\r\n      author: userInfo.userId || userInfo._id,\r\n    };\r\n\r\n    // Update the post\r\n    const updatedPost = (await Post.findOneAndUpdate(\r\n      { slug: postId },\r\n      updateData,\r\n      {\r\n        new: true,\r\n        runValidators: true,\r\n      }\r\n    )\r\n      .populate(\"categories\", \"name description color\")\r\n      .lean()) as any; // Add .lean() to get plain objects\r\n\r\n    if (!updatedPost) {\r\n      return { success: false, error: \"Failed to update post\" };\r\n    }\r\n\r\n    // Serialize the post for client components (ensure no circular references)\r\n    const serializedPost = {\r\n      _id: updatedPost._id.toString(),\r\n      title: updatedPost.title,\r\n      slug: updatedPost.slug,\r\n      content: updatedPost.content,\r\n      banner: updatedPost.banner,\r\n      categories: Array.isArray(updatedPost.categories)\r\n        ? updatedPost.categories.map((cat: any) => ({\r\n            _id: cat._id?.toString(),\r\n            name: cat.name,\r\n            description: cat.description,\r\n            color: cat.color,\r\n          }))\r\n        : [],\r\n      tags: updatedPost.tags || [],\r\n      seoTitle: updatedPost.seoTitle,\r\n      seoDescription: updatedPost.seoDescription,\r\n      metaKeywords: updatedPost.metaKeywords,\r\n      status: updatedPost.status,\r\n      isPublished: updatedPost.isPublished,\r\n      publishedAt:\r\n        updatedPost.publishedAt?.toISOString?.() || updatedPost.publishedAt,\r\n      createdAt:\r\n        updatedPost.createdAt?.toISOString?.() || updatedPost.createdAt,\r\n      updatedAt:\r\n        updatedPost.updatedAt?.toISOString?.() || updatedPost.updatedAt,\r\n    };\r\n\r\n    // Revalidate caches\r\n    revalidateTag(\"public-posts\");\r\n    revalidatePath(\"/en/blogs\");\r\n    revalidatePath(\"/th/blogs\");\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Post updated successfully\",\r\n      data: { post: serializedPost },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error updating post:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Internal server error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAsQsB,mBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/dashboard/blogs/components/WysiwygEditor.tsx"], "sourcesContent": ["// \"use client\";\r\n\r\n// import { LexicalComposer } from \"@lexical/react/LexicalComposer\";\r\n// import { RichTextPlugin } from \"@lexical/react/LexicalRichTextPlugin\";\r\n// import { ContentEditable } from \"@lexical/react/LexicalContentEditable\";\r\n// import { HistoryPlugin } from \"@lexical/react/LexicalHistoryPlugin\";\r\n// import { AutoFocusPlugin } from \"@lexical/react/LexicalAutoFocusPlugin\";\r\n// import { LinkPlugin } from \"@lexical/react/LexicalLinkPlugin\";\r\n// import { ListPlugin } from \"@lexical/react/LexicalListPlugin\";\r\n// import { LexicalErrorBoundary } from \"@lexical/react/LexicalErrorBoundary\";\r\n// import { HeadingNode, QuoteNode } from \"@lexical/rich-text\";\r\n// import { ListItemNode, ListNode } from \"@lexical/list\";\r\n// import { AutoLinkNode, LinkNode } from \"@lexical/link\";\r\n// import { useLexicalComposerContext } from \"@lexical/react/LexicalComposerContext\";\r\n// import { OnChangePlugin } from \"@lexical/react/LexicalOnChangePlugin\";\r\n// import {\r\n//   Bold,\r\n//   Italic,\r\n//   Underline,\r\n//   List,\r\n//   ListOrdered,\r\n//   Undo,\r\n//   Redo,\r\n//   Link as LinkIcon,\r\n// } from \"lucide-react\";\r\n// import { useCallback, useState, useEffect } from \"react\";\r\n// import {\r\n//   $getSelection,\r\n//   $isRangeSelection,\r\n//   FORMAT_TEXT_COMMAND,\r\n//   UNDO_COMMAND,\r\n//   REDO_COMMAND,\r\n//   EditorState,\r\n//   TextFormatType,\r\n//   $getRoot,\r\n// } from \"lexical\";\r\n// import { $generateHtmlFromNodes } from \"@lexical/html\";\r\n// import {\r\n//   $createHeadingNode,\r\n//   $createQuoteNode,\r\n//   $isHeadingNode,\r\n// } from \"@lexical/rich-text\";\r\n// import {\r\n//   INSERT_UNORDERED_LIST_COMMAND,\r\n//   INSERT_ORDERED_LIST_COMMAND,\r\n//   $isListNode,\r\n// } from \"@lexical/list\";\r\n// import { TOGGLE_LINK_COMMAND } from \"@lexical/link\";\r\n\r\n// interface WysiwygEditorProps {\r\n//   content: string;\r\n//   onChange: (content: string) => void;\r\n//   placeholder?: string;\r\n// }\r\n\r\n// // Lexical theme configuration\r\n// const theme = {\r\n//   ltr: \"ltr\",\r\n//   rtl: \"rtl\",\r\n//   placeholder: \"text-gray-400 text-sm\",\r\n//   paragraph: \"mb-2\",\r\n//   quote: \"border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4\",\r\n//   heading: {\r\n//     h1: \"text-3xl font-bold mb-4\",\r\n//     h2: \"text-2xl font-bold mb-3\",\r\n//     h3: \"text-xl font-bold mb-2\",\r\n//   },\r\n//   list: {\r\n//     nested: {\r\n//       listitem: \"list-none\",\r\n//     },\r\n//     ol: \"list-decimal list-inside mb-2\",\r\n//     ul: \"list-disc list-inside mb-2\",\r\n//     listitem: \"mb-1\",\r\n//   },\r\n//   link: \"text-blue-600 underline hover:text-blue-800\",\r\n//   text: {\r\n//     bold: \"font-bold\",\r\n//     italic: \"italic\",\r\n//     underline: \"underline\",\r\n//     strikethrough: \"line-through\",\r\n//   },\r\n// };\r\n\r\n// // Error boundary fallback\r\n// function onError(error: Error) {\r\n//   console.error(error);\r\n// }\r\n\r\n// // Initial editor configuration\r\n// const initialConfig = {\r\n//   namespace: \"WysiwygEditor\",\r\n//   theme,\r\n//   onError,\r\n//   nodes: [\r\n//     HeadingNode,\r\n//     ListNode,\r\n//     ListItemNode,\r\n//     QuoteNode,\r\n//     AutoLinkNode,\r\n//     LinkNode,\r\n//   ],\r\n// };\r\n\r\n// // Toolbar component\r\n// function ToolbarPlugin() {\r\n//   const [editor] = useLexicalComposerContext();\r\n//   const [isBold, setIsBold] = useState(false);\r\n//   const [isItalic, setIsItalic] = useState(false);\r\n//   const [isUnderline, setIsUnderline] = useState(false);\r\n//   const [blockType, setBlockType] = useState(\"paragraph\");\r\n\r\n//   const updateToolbar = useCallback(() => {\r\n//     const selection = $getSelection();\r\n//     if ($isRangeSelection(selection)) {\r\n//       setIsBold(selection.hasFormat(\"bold\"));\r\n//       setIsItalic(selection.hasFormat(\"italic\"));\r\n//       setIsUnderline(selection.hasFormat(\"underline\"));\r\n\r\n//       const anchorNode = selection.anchor.getNode();\r\n//       const element =\r\n//         anchorNode.getKey() === \"root\"\r\n//           ? anchorNode\r\n//           : anchorNode.getTopLevelElementOrThrow();\r\n\r\n//       if ($isListNode(element)) {\r\n//         const type = element.getListType();\r\n//         setBlockType(type === \"bullet\" ? \"ul\" : \"ol\");\r\n//       } else {\r\n//         const type = $isHeadingNode(element)\r\n//           ? element.getTag()\r\n//           : element.getType();\r\n//         setBlockType(type);\r\n//       }\r\n//     }\r\n//   }, []);\r\n\r\n//   useEffect(() => {\r\n//     return editor.registerUpdateListener(({ editorState }) => {\r\n//       editorState.read(() => {\r\n//         updateToolbar();\r\n//       });\r\n//     });\r\n//   }, [updateToolbar, editor]);\r\n\r\n//   const formatText = (format: TextFormatType) => {\r\n//     editor.dispatchCommand(FORMAT_TEXT_COMMAND, format);\r\n//   };\r\n\r\n//   const formatHeading = (headingSize: string) => {\r\n//     editor.update(() => {\r\n//       const selection = $getSelection();\r\n//       if ($isRangeSelection(selection)) {\r\n//         if (blockType !== headingSize) {\r\n//           const heading = $createHeadingNode(headingSize as any);\r\n//           selection.insertNodes([heading]);\r\n//         }\r\n//       }\r\n//     });\r\n//   };\r\n\r\n//   const formatQuote = () => {\r\n//     editor.update(() => {\r\n//       const selection = $getSelection();\r\n//       if ($isRangeSelection(selection)) {\r\n//         const quote = $createQuoteNode();\r\n//         selection.insertNodes([quote]);\r\n//       }\r\n//     });\r\n//   };\r\n\r\n//   const formatBulletList = () => {\r\n//     editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);\r\n//   };\r\n\r\n//   const formatNumberedList = () => {\r\n//     editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);\r\n//   };\r\n\r\n//   const insertLink = () => {\r\n//     const url = prompt(\"Enter URL:\");\r\n//     if (url) {\r\n//       editor.dispatchCommand(TOGGLE_LINK_COMMAND, url);\r\n//     }\r\n//   };\r\n\r\n//   return (\r\n//     <div className=\"flex flex-wrap items-center gap-1 p-2 border-b border-gray-300 bg-gray-50\">\r\n//       {/* Text Formatting */}\r\n//       <button\r\n//         type=\"button\"\r\n//         onClick={() => formatText(\"bold\")}\r\n//         className={`p-2 rounded hover:bg-gray-200 ${\r\n//           isBold ? \"bg-gray-300\" : \"\"\r\n//         }`}\r\n//         title=\"Bold\"\r\n//       >\r\n//         <Bold className=\"w-4 h-4\" />\r\n//       </button>\r\n\r\n//       <button\r\n//         type=\"button\"\r\n//         onClick={() => formatText(\"italic\")}\r\n//         className={`p-2 rounded hover:bg-gray-200 ${\r\n//           isItalic ? \"bg-gray-300\" : \"\"\r\n//         }`}\r\n//         title=\"Italic\"\r\n//       >\r\n//         <Italic className=\"w-4 h-4\" />\r\n//       </button>\r\n\r\n//       <button\r\n//         type=\"button\"\r\n//         onClick={() => formatText(\"underline\")}\r\n//         className={`p-2 rounded hover:bg-gray-200 ${\r\n//           isUnderline ? \"bg-gray-300\" : \"\"\r\n//         }`}\r\n//         title=\"Underline\"\r\n//       >\r\n//         <Underline className=\"w-4 h-4\" />\r\n//       </button>\r\n\r\n//       <div className=\"w-px h-6 bg-gray-300 mx-1\" />\r\n\r\n//       {/* Block Type */}\r\n//       <select\r\n//         onChange={(e) => {\r\n//           const value = e.target.value;\r\n//           if (value === \"quote\") {\r\n//             formatQuote();\r\n//           } else if (value.startsWith(\"h\")) {\r\n//             formatHeading(value);\r\n//           }\r\n//         }}\r\n//         value={blockType}\r\n//         className=\"px-2 py-1 border border-gray-300 rounded text-sm\"\r\n//       >\r\n//         <option value=\"paragraph\">Paragraph</option>\r\n//         <option value=\"h1\">Heading 1</option>\r\n//         <option value=\"h2\">Heading 2</option>\r\n//         <option value=\"h3\">Heading 3</option>\r\n//         <option value=\"quote\">Quote</option>\r\n//       </select>\r\n\r\n//       <div className=\"w-px h-6 bg-gray-300 mx-1\" />\r\n\r\n//       {/* Lists */}\r\n//       <button\r\n//         type=\"button\"\r\n//         onClick={formatBulletList}\r\n//         className=\"p-2 rounded hover:bg-gray-200\"\r\n//         title=\"Bullet List\"\r\n//       >\r\n//         <List className=\"w-4 h-4\" />\r\n//       </button>\r\n\r\n//       <button\r\n//         type=\"button\"\r\n//         onClick={formatNumberedList}\r\n//         className=\"p-2 rounded hover:bg-gray-200\"\r\n//         title=\"Numbered List\"\r\n//       >\r\n//         <ListOrdered className=\"w-4 h-4\" />\r\n//       </button>\r\n\r\n//       <div className=\"w-px h-6 bg-gray-300 mx-1\" />\r\n\r\n//       {/* Link */}\r\n//       <button\r\n//         type=\"button\"\r\n//         onClick={insertLink}\r\n//         className=\"p-2 rounded hover:bg-gray-200\"\r\n//         title=\"Add Link\"\r\n//       >\r\n//         <LinkIcon className=\"w-4 h-4\" />\r\n//       </button>\r\n\r\n//       <div className=\"w-px h-6 bg-gray-300 mx-1\" />\r\n\r\n//       {/* Undo/Redo */}\r\n//       <button\r\n//         type=\"button\"\r\n//         onClick={() => editor.dispatchCommand(UNDO_COMMAND, undefined)}\r\n//         className=\"p-2 rounded hover:bg-gray-200\"\r\n//         title=\"Undo\"\r\n//       >\r\n//         <Undo className=\"w-4 h-4\" />\r\n//       </button>\r\n\r\n//       <button\r\n//         type=\"button\"\r\n//         onClick={() => editor.dispatchCommand(REDO_COMMAND, undefined)}\r\n//         className=\"p-2 rounded hover:bg-gray-200\"\r\n//         title=\"Redo\"\r\n//       >\r\n//         <Redo className=\"w-4 h-4\" />\r\n//       </button>\r\n//     </div>\r\n//   );\r\n// }\r\n\r\n// // Main WYSIWYG Editor Component\r\n// const WysiwygEditor = ({\r\n//   onChange,\r\n//   placeholder = \"Start writing...\",\r\n// }: Omit<WysiwygEditorProps, \"content\">) => {\r\n//   return (\r\n//     <div className=\"border border-gray-300 rounded-lg overflow-hidden\">\r\n//       <LexicalComposer initialConfig={initialConfig}>\r\n//         <ToolbarPlugin />\r\n//         <div className=\"relative\">\r\n//           <RichTextPlugin\r\n//             contentEditable={\r\n//               <ContentEditable\r\n//                 className=\"min-h-[300px] p-4 focus:outline-none prose max-w-none\"\r\n//                 style={{ resize: \"none\" }}\r\n//               />\r\n//             }\r\n//             placeholder={\r\n//               <div className=\"absolute top-4 left-4 text-gray-400 pointer-events-none\">\r\n//                 {placeholder}\r\n//               </div>\r\n//             }\r\n//             ErrorBoundary={LexicalErrorBoundary}\r\n//           />\r\n//           <OnChangePlugin\r\n//             onChange={(editorState: EditorState, editor) => {\r\n//               editorState.read(() => {\r\n//                 // Generate HTML from the editor state to preserve formatting\r\n//                 const htmlContent = $generateHtmlFromNodes(editor, null);\r\n//                 onChange(htmlContent);\r\n//               });\r\n//             }}\r\n//           />\r\n//           <HistoryPlugin />\r\n//           <AutoFocusPlugin />\r\n//           <LinkPlugin />\r\n//           <ListPlugin />\r\n//         </div>\r\n//       </LexicalComposer>\r\n//     </div>\r\n//   );\r\n// };\r\n\r\n// export default WysiwygEditor;\r\n\r\n//  Second\r\n\r\n// Complete WYSIWYG Editor with Fixed Content Prop Handling\r\n\r\n\"use client\";\r\n\r\nimport \"./lexical-editor.css\";\r\nimport { LexicalComposer } from \"@lexical/react/LexicalComposer\";\r\nimport { RichTextPlugin } from \"@lexical/react/LexicalRichTextPlugin\";\r\nimport { ContentEditable } from \"@lexical/react/LexicalContentEditable\";\r\nimport { HistoryPlugin } from \"@lexical/react/LexicalHistoryPlugin\";\r\nimport { LinkPlugin } from \"@lexical/react/LexicalLinkPlugin\";\r\nimport { ListPlugin } from \"@lexical/react/LexicalListPlugin\";\r\nimport { LexicalErrorBoundary } from \"@lexical/react/LexicalErrorBoundary\";\r\nimport { HeadingNode, QuoteNode } from \"@lexical/rich-text\";\r\nimport { ListItemNode, ListNode } from \"@lexical/list\";\r\nimport { AutoLinkNode, LinkNode } from \"@lexical/link\";\r\nimport { useLexicalComposerContext } from \"@lexical/react/LexicalComposerContext\";\r\nimport { OnChangePlugin } from \"@lexical/react/LexicalOnChangePlugin\";\r\nimport {\r\n  Bold,\r\n  Italic,\r\n  Underline,\r\n  List,\r\n  ListOrdered,\r\n  Undo,\r\n  Redo,\r\n  Link as LinkIcon,\r\n} from \"lucide-react\";\r\nimport { useCallback, useState, useEffect } from \"react\";\r\nimport {\r\n  $getSelection,\r\n  $isRangeSelection,\r\n  FORMAT_TEXT_COMMAND,\r\n  UNDO_COMMAND,\r\n  REDO_COMMAND,\r\n  EditorState,\r\n  TextFormatType,\r\n  $getRoot,\r\n  $createParagraphNode,\r\n  $createTextNode,\r\n} from \"lexical\";\r\nimport { $generateHtmlFromNodes, $generateNodesFromDOM } from \"@lexical/html\";\r\nimport {\r\n  $createHeadingNode,\r\n  $createQuoteNode,\r\n  $isHeadingNode,\r\n} from \"@lexical/rich-text\";\r\nimport {\r\n  INSERT_UNORDERED_LIST_COMMAND,\r\n  INSERT_ORDERED_LIST_COMMAND,\r\n  $isListNode,\r\n} from \"@lexical/list\";\r\nimport { TOGGLE_LINK_COMMAND } from \"@lexical/link\";\r\n\r\ninterface WysiwygEditorProps {\r\n  content: string;\r\n  onChange: (content: string) => void;\r\n  placeholder?: string;\r\n}\r\n\r\n// Lexical theme configuration\r\nconst theme = {\r\n  ltr: \"ltr\",\r\n  rtl: \"rtl\",\r\n  placeholder: \"text-gray-400 text-sm\",\r\n  paragraph: \"mb-2\",\r\n  quote: \"border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4\",\r\n  heading: {\r\n    h1: \"text-3xl font-bold mb-4\",\r\n    h2: \"text-2xl font-bold mb-3\",\r\n    h3: \"text-xl font-bold mb-2\",\r\n  },\r\n  list: {\r\n    nested: {\r\n      listitem: \"list-none\",\r\n    },\r\n    ol: \"list-decimal list-inside mb-2\",\r\n    ul: \"list-disc list-inside mb-2\",\r\n    listitem: \"mb-1\",\r\n  },\r\n  link: \"text-blue-600 underline hover:text-blue-800\",\r\n  text: {\r\n    bold: \"font-bold\",\r\n    italic: \"italic\",\r\n    underline: \"underline\",\r\n    strikethrough: \"line-through\",\r\n  },\r\n};\r\n\r\nfunction onError(error: Error) {\r\n  console.error(error);\r\n}\r\n\r\nfunction ToolbarPlugin() {\r\n  const [editor] = useLexicalComposerContext();\r\n  const [isBold, setIsBold] = useState(false);\r\n  const [isItalic, setIsItalic] = useState(false);\r\n  const [isUnderline, setIsUnderline] = useState(false);\r\n  const [blockType, setBlockType] = useState(\"paragraph\");\r\n\r\n  const updateToolbar = useCallback(() => {\r\n    const selection = $getSelection();\r\n    if ($isRangeSelection(selection)) {\r\n      setIsBold(selection.hasFormat(\"bold\"));\r\n      setIsItalic(selection.hasFormat(\"italic\"));\r\n      setIsUnderline(selection.hasFormat(\"underline\"));\r\n\r\n      const anchorNode = selection.anchor.getNode();\r\n      const element =\r\n        anchorNode.getKey() === \"root\"\r\n          ? anchorNode\r\n          : anchorNode.getTopLevelElementOrThrow();\r\n\r\n      if ($isListNode(element)) {\r\n        const type = element.getListType();\r\n        setBlockType(type === \"bullet\" ? \"ul\" : \"ol\");\r\n      } else {\r\n        const type = $isHeadingNode(element)\r\n          ? element.getTag()\r\n          : element.getType();\r\n        setBlockType(type);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    return editor.registerUpdateListener(({ editorState }) => {\r\n      editorState.read(() => {\r\n        updateToolbar();\r\n      });\r\n    });\r\n  }, [updateToolbar, editor]);\r\n\r\n  const formatText = (format: TextFormatType) => {\r\n    editor.dispatchCommand(FORMAT_TEXT_COMMAND, format);\r\n  };\r\n\r\n  const formatHeading = (headingSize: string) => {\r\n    editor.update(() => {\r\n      const selection = $getSelection();\r\n      if ($isRangeSelection(selection)) {\r\n        if (blockType !== headingSize) {\r\n          const heading = $createHeadingNode(headingSize as any);\r\n          selection.insertNodes([heading]);\r\n        }\r\n      }\r\n    });\r\n  };\r\n\r\n  const formatQuote = () => {\r\n    editor.update(() => {\r\n      const selection = $getSelection();\r\n      if ($isRangeSelection(selection)) {\r\n        const quote = $createQuoteNode();\r\n        selection.insertNodes([quote]);\r\n      }\r\n    });\r\n  };\r\n\r\n  const formatBulletList = () => {\r\n    editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);\r\n  };\r\n\r\n  const formatNumberedList = () => {\r\n    editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);\r\n  };\r\n\r\n  const insertLink = () => {\r\n    const url = prompt(\"Enter URL:\");\r\n    if (url) {\r\n      editor.dispatchCommand(TOGGLE_LINK_COMMAND, url);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className=\"flex flex-wrap items-center gap-1 p-2 border-b border-gray-300 bg-gray-50\"\r\n      style={{\r\n        display: \"flex\",\r\n        flexDirection: \"row\",\r\n        flexWrap: \"wrap\",\r\n        alignItems: \"center\",\r\n        gap: \"4px\",\r\n        padding: \"8px\",\r\n      }}\r\n    >\r\n      <button\r\n        type=\"button\"\r\n        onClick={() => formatText(\"bold\")}\r\n        className={`p-2 rounded hover:bg-gray-200 ${\r\n          isBold ? \"bg-gray-300\" : \"\"\r\n        }`}\r\n        title=\"Bold\"\r\n      >\r\n        <Bold className=\"w-4 h-4\" />\r\n      </button>\r\n\r\n      <button\r\n        type=\"button\"\r\n        onClick={() => formatText(\"italic\")}\r\n        className={`p-2 rounded hover:bg-gray-200 ${\r\n          isItalic ? \"bg-gray-300\" : \"\"\r\n        }`}\r\n        title=\"Italic\"\r\n      >\r\n        <Italic className=\"w-4 h-4\" />\r\n      </button>\r\n\r\n      <button\r\n        type=\"button\"\r\n        onClick={() => formatText(\"underline\")}\r\n        className={`p-2 rounded hover:bg-gray-200 ${\r\n          isUnderline ? \"bg-gray-300\" : \"\"\r\n        }`}\r\n        title=\"Underline\"\r\n      >\r\n        <Underline className=\"w-4 h-4\" />\r\n      </button>\r\n\r\n      <div className=\"w-px h-6 bg-gray-300 mx-1\" />\r\n\r\n      <select\r\n        onChange={(e) => {\r\n          const value = e.target.value;\r\n          if (value === \"quote\") {\r\n            formatQuote();\r\n          } else if (value.startsWith(\"h\")) {\r\n            formatHeading(value);\r\n          }\r\n        }}\r\n        value={blockType}\r\n        className=\"px-2 py-1 border border-gray-300 rounded text-sm\"\r\n      >\r\n        <option value=\"paragraph\">Paragraph</option>\r\n        <option value=\"h1\">Heading 1</option>\r\n        <option value=\"h2\">Heading 2</option>\r\n        <option value=\"h3\">Heading 3</option>\r\n        <option value=\"quote\">Quote</option>\r\n      </select>\r\n\r\n      <div className=\"w-px h-6 bg-gray-300 mx-1\" />\r\n\r\n      <button\r\n        type=\"button\"\r\n        onClick={formatBulletList}\r\n        className=\"p-2 rounded hover:bg-gray-200\"\r\n        title=\"Bullet List\"\r\n      >\r\n        <List className=\"w-4 h-4\" />\r\n      </button>\r\n\r\n      <button\r\n        type=\"button\"\r\n        onClick={formatNumberedList}\r\n        className=\"p-2 rounded hover:bg-gray-200\"\r\n        title=\"Numbered List\"\r\n      >\r\n        <ListOrdered className=\"w-4 h-4\" />\r\n      </button>\r\n\r\n      <div className=\"w-px h-6 bg-gray-300 mx-1\" />\r\n\r\n      <button\r\n        type=\"button\"\r\n        onClick={insertLink}\r\n        className=\"p-2 rounded hover:bg-gray-200\"\r\n        title=\"Add Link\"\r\n      >\r\n        <LinkIcon className=\"w-4 h-4\" />\r\n      </button>\r\n\r\n      <div className=\"w-px h-6 bg-gray-300 mx-1\" />\r\n\r\n      <button\r\n        type=\"button\"\r\n        onClick={() => editor.dispatchCommand(UNDO_COMMAND, undefined)}\r\n        className=\"p-2 rounded hover:bg-gray-200\"\r\n        title=\"Undo\"\r\n      >\r\n        <Undo className=\"w-4 h-4\" />\r\n      </button>\r\n\r\n      <button\r\n        type=\"button\"\r\n        onClick={() => editor.dispatchCommand(REDO_COMMAND, undefined)}\r\n        className=\"p-2 rounded hover:bg-gray-200\"\r\n        title=\"Redo\"\r\n      >\r\n        <Redo className=\"w-4 h-4\" />\r\n      </button>\r\n    </div>\r\n  );\r\n}\r\n\r\n// Plugin to update editor content when content prop changes\r\nfunction ContentUpdatePlugin({ content }: { content: string }) {\r\n  const [editor] = useLexicalComposerContext();\r\n  const [isFirstLoad, setIsFirstLoad] = useState(true);\r\n\r\n  useEffect(() => {\r\n    // Only update on first load or when content actually changes\r\n    if (isFirstLoad && content && content.trim() !== \"\") {\r\n      editor.update(() => {\r\n        try {\r\n          // Fix vertical stacking issue by detecting and merging single-character paragraphs\r\n          let cleanedContent = content;\r\n\r\n          // Pattern to detect single character paragraphs that cause vertical stacking\r\n          const singleCharPattern = /<p[^>]*><span[^>]*>(.)<\\/span><\\/p>/g;\r\n          const matches = [...content.matchAll(singleCharPattern)];\r\n\r\n          if (matches.length > 1) {\r\n            // If we have multiple single-character paragraphs, merge them\r\n            const characters = matches.map((match) => match[1]).join(\"\");\r\n            // Replace all single-character paragraphs with one paragraph containing all characters\r\n            cleanedContent = content.replace(singleCharPattern, \"\");\r\n            cleanedContent =\r\n              `<p class=\"mb-2\" dir=\"ltr\"><span style=\"white-space: pre-wrap;\">${characters}</span></p>` +\r\n              cleanedContent;\r\n          }\r\n\r\n          // Additional cleaning\r\n          cleanedContent = cleanedContent\r\n            .replace(/\\s+/g, \" \") // Replace multiple whitespace with single space\r\n            .replace(/>\\s+</g, \"><\") // Remove whitespace between tags\r\n            .trim();\r\n\r\n          const parser = new DOMParser();\r\n          const dom = parser.parseFromString(cleanedContent, \"text/html\");\r\n\r\n          // Ensure all text nodes maintain proper spacing\r\n          const walker = document.createTreeWalker(\r\n            dom.body,\r\n            NodeFilter.SHOW_TEXT\r\n          );\r\n\r\n          let textNode;\r\n          while ((textNode = walker.nextNode())) {\r\n            if (textNode.textContent) {\r\n              // Ensure text content flows normally\r\n              textNode.textContent = textNode.textContent.replace(/\\s+/g, \" \");\r\n            }\r\n          }\r\n\r\n          const nodes = $generateNodesFromDOM(editor, dom);\r\n          const root = $getRoot();\r\n          root.clear();\r\n          root.append(...nodes);\r\n\r\n          // Force a re-render to ensure proper text flow\r\n          setTimeout(() => {\r\n            editor.update(() => {\r\n              const root = $getRoot();\r\n              root.markDirty();\r\n            });\r\n          }, 100);\r\n        } catch (error) {\r\n          console.error(\"Error updating editor content:\", error);\r\n          // Fallback: create a simple paragraph with the content\r\n          const root = $getRoot();\r\n          root.clear();\r\n\r\n          // Strip HTML tags for fallback\r\n          const textContent = content\r\n            .replace(/<[^>]*>/g, \" \")\r\n            .replace(/\\s+/g, \" \")\r\n            .trim();\r\n          if (textContent) {\r\n            const paragraph = $createParagraphNode();\r\n            paragraph.append($createTextNode(textContent));\r\n            root.append(paragraph);\r\n          }\r\n        }\r\n      });\r\n      setIsFirstLoad(false);\r\n    }\r\n  }, [content, editor, isFirstLoad]);\r\n\r\n  return null;\r\n}\r\n\r\nconst WysiwygEditor = ({\r\n  content,\r\n  onChange,\r\n  placeholder = \"Start writing...\",\r\n}: WysiwygEditorProps) => {\r\n  const initialConfig = {\r\n    namespace: \"WysiwygEditor\",\r\n    theme,\r\n    onError,\r\n    nodes: [\r\n      HeadingNode,\r\n      ListNode,\r\n      ListItemNode,\r\n      QuoteNode,\r\n      AutoLinkNode,\r\n      LinkNode,\r\n    ],\r\n  };\r\n\r\n  return (\r\n    <div className=\"border border-gray-300 rounded-lg overflow-hidden lexical-editor\">\r\n      <LexicalComposer key=\"wysiwyg-editor\" initialConfig={initialConfig}>\r\n        <ToolbarPlugin />\r\n        <ContentUpdatePlugin content={content} />\r\n        <div\r\n          className=\"relative lexical-content-area\"\r\n          style={{\r\n            direction: \"ltr\",\r\n            textAlign: \"left\",\r\n            writingMode: \"horizontal-tb\",\r\n          }}\r\n        >\r\n          <RichTextPlugin\r\n            contentEditable={\r\n              <ContentEditable\r\n                className=\"min-h-[300px] p-4 focus:outline-none\"\r\n                style={{\r\n                  minHeight: \"300px\",\r\n                  padding: \"16px\",\r\n                  whiteSpace: \"normal\",\r\n                  wordWrap: \"break-word\",\r\n                  textAlign: \"left\",\r\n                  direction: \"ltr\",\r\n                  writingMode: \"horizontal-tb\",\r\n                  unicodeBidi: \"normal\",\r\n                  display: \"block\",\r\n                  width: \"100%\",\r\n                  boxSizing: \"border-box\",\r\n                  fontFamily: \"inherit\",\r\n                  fontSize: \"16px\",\r\n                  lineHeight: \"1.6\",\r\n                  color: \"inherit\",\r\n                  background: \"transparent\",\r\n                  border: \"none\",\r\n                  outline: \"none\",\r\n                }}\r\n                spellCheck={true}\r\n                dir=\"ltr\"\r\n              />\r\n            }\r\n            placeholder={\r\n              <div className=\"absolute top-4 left-4 text-gray-400 pointer-events-none\">\r\n                {placeholder}\r\n              </div>\r\n            }\r\n            ErrorBoundary={LexicalErrorBoundary}\r\n          />\r\n          <OnChangePlugin\r\n            onChange={(editorState: EditorState, editor) => {\r\n              editorState.read(() => {\r\n                const htmlContent = $generateHtmlFromNodes(editor, null);\r\n                onChange(htmlContent);\r\n              });\r\n            }}\r\n          />\r\n          <HistoryPlugin />\r\n          <LinkPlugin />\r\n          <ListPlugin />\r\n        </div>\r\n      </LexicalComposer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WysiwygEditor;\r\n"], "names": [], "mappings": "AAAA,gBAAgB;AAEhB,oEAAoE;AACpE,yEAAyE;AACzE,2EAA2E;AAC3E,uEAAuE;AACvE,2EAA2E;AAC3E,iEAAiE;AACjE,iEAAiE;AACjE,8EAA8E;AAC9E,+DAA+D;AAC/D,0DAA0D;AAC1D,0DAA0D;AAC1D,qFAAqF;AACrF,yEAAyE;AACzE,WAAW;AACX,UAAU;AACV,YAAY;AACZ,eAAe;AACf,UAAU;AACV,iBAAiB;AACjB,UAAU;AACV,UAAU;AACV,sBAAsB;AACtB,yBAAyB;AACzB,4DAA4D;AAC5D,WAAW;AACX,mBAAmB;AACnB,uBAAuB;AACvB,yBAAyB;AACzB,kBAAkB;AAClB,kBAAkB;AAClB,iBAAiB;AACjB,oBAAoB;AACpB,cAAc;AACd,oBAAoB;AACpB,0DAA0D;AAC1D,WAAW;AACX,wBAAwB;AACxB,sBAAsB;AACtB,oBAAoB;AACpB,+BAA+B;AAC/B,WAAW;AACX,mCAAmC;AACnC,iCAAiC;AACjC,iBAAiB;AACjB,0BAA0B;AAC1B,uDAAuD;AAEvD,iCAAiC;AACjC,qBAAqB;AACrB,yCAAyC;AACzC,0BAA0B;AAC1B,IAAI;AAEJ,iCAAiC;AACjC,kBAAkB;AAClB,gBAAgB;AAChB,gBAAgB;AAChB,0CAA0C;AAC1C,uBAAuB;AACvB,wEAAwE;AACxE,eAAe;AACf,qCAAqC;AACrC,qCAAqC;AACrC,oCAAoC;AACpC,OAAO;AACP,YAAY;AACZ,gBAAgB;AAChB,+BAA+B;AAC/B,SAAS;AACT,2CAA2C;AAC3C,wCAAwC;AACxC,wBAAwB;AACxB,OAAO;AACP,yDAAyD;AACzD,YAAY;AACZ,yBAAyB;AACzB,wBAAwB;AACxB,8BAA8B;AAC9B,qCAAqC;AACrC,OAAO;AACP,KAAK;AAEL,6BAA6B;AAC7B,mCAAmC;AACnC,0BAA0B;AAC1B,IAAI;AAEJ,kCAAkC;AAClC,0BAA0B;AAC1B,gCAAgC;AAChC,WAAW;AACX,aAAa;AACb,aAAa;AACb,mBAAmB;AACnB,gBAAgB;AAChB,oBAAoB;AACpB,iBAAiB;AACjB,oBAAoB;AACpB,gBAAgB;AAChB,OAAO;AACP,KAAK;AAEL,uBAAuB;AACvB,6BAA6B;AAC7B,kDAAkD;AAClD,iDAAiD;AACjD,qDAAqD;AACrD,2DAA2D;AAC3D,6DAA6D;AAE7D,8CAA8C;AAC9C,yCAAyC;AACzC,0CAA0C;AAC1C,gDAAgD;AAChD,oDAAoD;AACpD,0DAA0D;AAE1D,uDAAuD;AACvD,wBAAwB;AACxB,yCAAyC;AACzC,yBAAyB;AACzB,sDAAsD;AAEtD,oCAAoC;AACpC,8CAA8C;AAC9C,yDAAyD;AACzD,iBAAiB;AACjB,+CAA+C;AAC/C,+BAA+B;AAC/B,iCAAiC;AACjC,8BAA8B;AAC9B,UAAU;AACV,QAAQ;AACR,YAAY;AAEZ,sBAAsB;AACtB,kEAAkE;AAClE,iCAAiC;AACjC,2BAA2B;AAC3B,YAAY;AACZ,UAAU;AACV,iCAAiC;AAEjC,qDAAqD;AACrD,2DAA2D;AAC3D,OAAO;AAEP,qDAAqD;AACrD,4BAA4B;AAC5B,2CAA2C;AAC3C,4CAA4C;AAC5C,2CAA2C;AAC3C,oEAAoE;AACpE,8CAA8C;AAC9C,YAAY;AACZ,UAAU;AACV,UAAU;AACV,OAAO;AAEP,gCAAgC;AAChC,4BAA4B;AAC5B,2CAA2C;AAC3C,4CAA4C;AAC5C,4CAA4C;AAC5C,0CAA0C;AAC1C,UAAU;AACV,UAAU;AACV,OAAO;AAEP,qCAAqC;AACrC,wEAAwE;AACxE,OAAO;AAEP,uCAAuC;AACvC,sEAAsE;AACtE,OAAO;AAEP,+BAA+B;AAC/B,wCAAwC;AACxC,iBAAiB;AACjB,0DAA0D;AAC1D,QAAQ;AACR,OAAO;AAEP,aAAa;AACb,kGAAkG;AAClG,gCAAgC;AAChC,gBAAgB;AAChB,wBAAwB;AACxB,6CAA6C;AAC7C,uDAAuD;AACvD,wCAAwC;AACxC,cAAc;AACd,uBAAuB;AACvB,UAAU;AACV,uCAAuC;AACvC,kBAAkB;AAElB,gBAAgB;AAChB,wBAAwB;AACxB,+CAA+C;AAC/C,uDAAuD;AACvD,0CAA0C;AAC1C,cAAc;AACd,yBAAyB;AACzB,UAAU;AACV,yCAAyC;AACzC,kBAAkB;AAElB,gBAAgB;AAChB,wBAAwB;AACxB,kDAAkD;AAClD,uDAAuD;AACvD,6CAA6C;AAC7C,cAAc;AACd,4BAA4B;AAC5B,UAAU;AACV,4CAA4C;AAC5C,kBAAkB;AAElB,sDAAsD;AAEtD,2BAA2B;AAC3B,gBAAgB;AAChB,6BAA6B;AAC7B,0CAA0C;AAC1C,qCAAqC;AACrC,6BAA6B;AAC7B,gDAAgD;AAChD,oCAAoC;AACpC,cAAc;AACd,aAAa;AACb,4BAA4B;AAC5B,uEAAuE;AACvE,UAAU;AACV,uDAAuD;AACvD,gDAAgD;AAChD,gDAAgD;AAChD,gDAAgD;AAChD,+CAA+C;AAC/C,kBAAkB;AAElB,sDAAsD;AAEtD,sBAAsB;AACtB,gBAAgB;AAChB,wBAAwB;AACxB,qCAAqC;AACrC,oDAAoD;AACpD,8BAA8B;AAC9B,UAAU;AACV,uCAAuC;AACvC,kBAAkB;AAElB,gBAAgB;AAChB,wBAAwB;AACxB,uCAAuC;AACvC,oDAAoD;AACpD,gCAAgC;AAChC,UAAU;AACV,8CAA8C;AAC9C,kBAAkB;AAElB,sDAAsD;AAEtD,qBAAqB;AACrB,gBAAgB;AAChB,wBAAwB;AACxB,+BAA+B;AAC/B,oDAAoD;AACpD,2BAA2B;AAC3B,UAAU;AACV,2CAA2C;AAC3C,kBAAkB;AAElB,sDAAsD;AAEtD,0BAA0B;AAC1B,gBAAgB;AAChB,wBAAwB;AACxB,0EAA0E;AAC1E,oDAAoD;AACpD,uBAAuB;AACvB,UAAU;AACV,uCAAuC;AACvC,kBAAkB;AAElB,gBAAgB;AAChB,wBAAwB;AACxB,0EAA0E;AAC1E,oDAAoD;AACpD,uBAAuB;AACvB,UAAU;AACV,uCAAuC;AACvC,kBAAkB;AAClB,aAAa;AACb,OAAO;AACP,IAAI;AAEJ,mCAAmC;AACnC,2BAA2B;AAC3B,cAAc;AACd,sCAAsC;AACtC,+CAA+C;AAC/C,aAAa;AACb,0EAA0E;AAC1E,wDAAwD;AACxD,4BAA4B;AAC5B,qCAAqC;AACrC,4BAA4B;AAC5B,gCAAgC;AAChC,iCAAiC;AACjC,oFAAoF;AACpF,6CAA6C;AAC7C,mBAAmB;AACnB,gBAAgB;AAChB,4BAA4B;AAC5B,0FAA0F;AAC1F,gCAAgC;AAChC,uBAAuB;AACvB,gBAAgB;AAChB,mDAAmD;AACnD,eAAe;AACf,4BAA4B;AAC5B,gEAAgE;AAChE,yCAAyC;AACzC,gFAAgF;AAChF,4EAA4E;AAC5E,yCAAyC;AACzC,oBAAoB;AACpB,iBAAiB;AACjB,eAAe;AACf,8BAA8B;AAC9B,gCAAgC;AAChC,2BAA2B;AAC3B,2BAA2B;AAC3B,iBAAiB;AACjB,2BAA2B;AAC3B,aAAa;AACb,OAAO;AACP,KAAK;AAEL,gCAAgC;AAEhC,UAAU;AAEV,2DAA2D;;;;;AAK3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAYA;AAtCA;;;;;;;;;;;;;;;;;;;;;;AAyDA,8BAA8B;AAC9B,MAAM,QAAQ;IACZ,KAAK;IACL,KAAK;IACL,aAAa;IACb,WAAW;IACX,OAAO;IACP,SAAS;QACP,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IACA,MAAM;QACJ,QAAQ;YACN,UAAU;QACZ;QACA,IAAI;QACJ,IAAI;QACJ,UAAU;IACZ;IACA,MAAM;IACN,MAAM;QACJ,MAAM;QACN,QAAQ;QACR,WAAW;QACX,eAAe;IACjB;AACF;AAEA,SAAS,QAAQ,KAAY;IAC3B,QAAQ,KAAK,CAAC;AAChB;AAEA,SAAS;IACP,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,oKAAA,CAAA,4BAAyB,AAAD;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,MAAM,YAAY,CAAA,GAAA,0IAAA,CAAA,gBAAa,AAAD;QAC9B,IAAI,CAAA,GAAA,0IAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;YAChC,UAAU,UAAU,SAAS,CAAC;YAC9B,YAAY,UAAU,SAAS,CAAC;YAChC,eAAe,UAAU,SAAS,CAAC;YAEnC,MAAM,aAAa,UAAU,MAAM,CAAC,OAAO;YAC3C,MAAM,UACJ,WAAW,MAAM,OAAO,SACpB,aACA,WAAW,yBAAyB;YAE1C,IAAI,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,UAAU;gBACxB,MAAM,OAAO,QAAQ,WAAW;gBAChC,aAAa,SAAS,WAAW,OAAO;YAC1C,OAAO;gBACL,MAAM,OAAO,CAAA,GAAA,oKAAA,CAAA,iBAAc,AAAD,EAAE,WACxB,QAAQ,MAAM,KACd,QAAQ,OAAO;gBACnB,aAAa;YACf;QACF;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO,OAAO,sBAAsB,CAAC,CAAC,EAAE,WAAW,EAAE;YACnD,YAAY,IAAI,CAAC;gBACf;YACF;QACF;IACF,GAAG;QAAC;QAAe;KAAO;IAE1B,MAAM,aAAa,CAAC;QAClB,OAAO,eAAe,CAAC,0IAAA,CAAA,sBAAmB,EAAE;IAC9C;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,MAAM,CAAC;YACZ,MAAM,YAAY,CAAA,GAAA,0IAAA,CAAA,gBAAa,AAAD;YAC9B,IAAI,CAAA,GAAA,0IAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;gBAChC,IAAI,cAAc,aAAa;oBAC7B,MAAM,UAAU,CAAA,GAAA,oKAAA,CAAA,qBAAkB,AAAD,EAAE;oBACnC,UAAU,WAAW,CAAC;wBAAC;qBAAQ;gBACjC;YACF;QACF;IACF;IAEA,MAAM,cAAc;QAClB,OAAO,MAAM,CAAC;YACZ,MAAM,YAAY,CAAA,GAAA,0IAAA,CAAA,gBAAa,AAAD;YAC9B,IAAI,CAAA,GAAA,0IAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;gBAChC,MAAM,QAAQ,CAAA,GAAA,oKAAA,CAAA,mBAAgB,AAAD;gBAC7B,UAAU,WAAW,CAAC;oBAAC;iBAAM;YAC/B;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,OAAO,eAAe,CAAC,wJAAA,CAAA,gCAA6B,EAAE;IACxD;IAEA,MAAM,qBAAqB;QACzB,OAAO,eAAe,CAAC,wJAAA,CAAA,8BAA2B,EAAE;IACtD;IAEA,MAAM,aAAa;QACjB,MAAM,MAAM,OAAO;QACnB,IAAI,KAAK;YACP,OAAO,eAAe,CAAC,wJAAA,CAAA,sBAAmB,EAAE;QAC9C;IACF;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YACL,SAAS;YACT,eAAe;YACf,UAAU;YACV,YAAY;YACZ,KAAK;YACL,SAAS;QACX;;0BAEA,8OAAC;gBACC,MAAK;gBACL,SAAS,IAAM,WAAW;gBAC1B,WAAW,CAAC,8BAA8B,EACxC,SAAS,gBAAgB,IACzB;gBACF,OAAM;0BAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;0BAGlB,8OAAC;gBACC,MAAK;gBACL,SAAS,IAAM,WAAW;gBAC1B,WAAW,CAAC,8BAA8B,EACxC,WAAW,gBAAgB,IAC3B;gBACF,OAAM;0BAEN,cAAA,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;0BAGpB,8OAAC;gBACC,MAAK;gBACL,SAAS,IAAM,WAAW;gBAC1B,WAAW,CAAC,8BAA8B,EACxC,cAAc,gBAAgB,IAC9B;gBACF,OAAM;0BAEN,cAAA,8OAAC,4MAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;;;;;;0BAGvB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBACC,UAAU,CAAC;oBACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oBAC5B,IAAI,UAAU,SAAS;wBACrB;oBACF,OAAO,IAAI,MAAM,UAAU,CAAC,MAAM;wBAChC,cAAc;oBAChB;gBACF;gBACA,OAAO;gBACP,WAAU;;kCAEV,8OAAC;wBAAO,OAAM;kCAAY;;;;;;kCAC1B,8OAAC;wBAAO,OAAM;kCAAK;;;;;;kCACnB,8OAAC;wBAAO,OAAM;kCAAK;;;;;;kCACnB,8OAAC;wBAAO,OAAM;kCAAK;;;;;;kCACnB,8OAAC;wBAAO,OAAM;kCAAQ;;;;;;;;;;;;0BAGxB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBACC,MAAK;gBACL,SAAS;gBACT,WAAU;gBACV,OAAM;0BAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;0BAGlB,8OAAC;gBACC,MAAK;gBACL,SAAS;gBACT,WAAU;gBACV,OAAM;0BAEN,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;0BAGzB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBACC,MAAK;gBACL,SAAS;gBACT,WAAU;gBACV,OAAM;0BAEN,cAAA,8OAAC,kMAAA,CAAA,OAAQ;oBAAC,WAAU;;;;;;;;;;;0BAGtB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBACC,MAAK;gBACL,SAAS,IAAM,OAAO,eAAe,CAAC,0IAAA,CAAA,eAAY,EAAE;gBACpD,WAAU;gBACV,OAAM;0BAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;0BAGlB,8OAAC;gBACC,MAAK;gBACL,SAAS,IAAM,OAAO,eAAe,CAAC,0IAAA,CAAA,eAAY,EAAE;gBACpD,WAAU;gBACV,OAAM;0BAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIxB;AAEA,4DAA4D;AAC5D,SAAS,oBAAoB,EAAE,OAAO,EAAuB;IAC3D,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,oKAAA,CAAA,4BAAyB,AAAD;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6DAA6D;QAC7D,IAAI,eAAe,WAAW,QAAQ,IAAI,OAAO,IAAI;YACnD,OAAO,MAAM,CAAC;gBACZ,IAAI;oBACF,mFAAmF;oBACnF,IAAI,iBAAiB;oBAErB,6EAA6E;oBAC7E,MAAM,oBAAoB;oBAC1B,MAAM,UAAU;2BAAI,QAAQ,QAAQ,CAAC;qBAAmB;oBAExD,IAAI,QAAQ,MAAM,GAAG,GAAG;wBACtB,8DAA8D;wBAC9D,MAAM,aAAa,QAAQ,GAAG,CAAC,CAAC,QAAU,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC;wBACzD,uFAAuF;wBACvF,iBAAiB,QAAQ,OAAO,CAAC,mBAAmB;wBACpD,iBACE,CAAC,+DAA+D,EAAE,WAAW,WAAW,CAAC,GACzF;oBACJ;oBAEA,sBAAsB;oBACtB,iBAAiB,eACd,OAAO,CAAC,QAAQ,KAAK,gDAAgD;qBACrE,OAAO,CAAC,UAAU,MAAM,iCAAiC;qBACzD,IAAI;oBAEP,MAAM,SAAS,IAAI;oBACnB,MAAM,MAAM,OAAO,eAAe,CAAC,gBAAgB;oBAEnD,gDAAgD;oBAChD,MAAM,SAAS,SAAS,gBAAgB,CACtC,IAAI,IAAI,EACR,WAAW,SAAS;oBAGtB,IAAI;oBACJ,MAAQ,WAAW,OAAO,QAAQ,GAAK;wBACrC,IAAI,SAAS,WAAW,EAAE;4BACxB,qCAAqC;4BACrC,SAAS,WAAW,GAAG,SAAS,WAAW,CAAC,OAAO,CAAC,QAAQ;wBAC9D;oBACF;oBAEA,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ;oBAC5C,MAAM,OAAO,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD;oBACpB,KAAK,KAAK;oBACV,KAAK,MAAM,IAAI;oBAEf,+CAA+C;oBAC/C,WAAW;wBACT,OAAO,MAAM,CAAC;4BACZ,MAAM,OAAO,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD;4BACpB,KAAK,SAAS;wBAChB;oBACF,GAAG;gBACL,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kCAAkC;oBAChD,uDAAuD;oBACvD,MAAM,OAAO,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD;oBACpB,KAAK,KAAK;oBAEV,+BAA+B;oBAC/B,MAAM,cAAc,QACjB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,QAAQ,KAChB,IAAI;oBACP,IAAI,aAAa;wBACf,MAAM,YAAY,CAAA,GAAA,0IAAA,CAAA,uBAAoB,AAAD;wBACrC,UAAU,MAAM,CAAC,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD,EAAE;wBACjC,KAAK,MAAM,CAAC;oBACd;gBACF;YACF;YACA,eAAe;QACjB;IACF,GAAG;QAAC;QAAS;QAAQ;KAAY;IAEjC,OAAO;AACT;AAEA,MAAM,gBAAgB,CAAC,EACrB,OAAO,EACP,QAAQ,EACR,cAAc,kBAAkB,EACb;IACnB,MAAM,gBAAgB;QACpB,WAAW;QACX;QACA;QACA,OAAO;YACL,oKAAA,CAAA,cAAW;YACX,wJAAA,CAAA,WAAQ;YACR,wJAAA,CAAA,eAAY;YACZ,oKAAA,CAAA,YAAS;YACT,wJAAA,CAAA,eAAY;YACZ,wJAAA,CAAA,WAAQ;SACT;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,6JAAA,CAAA,kBAAe;YAAsB,eAAe;;8BACnD,8OAAC;;;;;8BACD,8OAAC;oBAAoB,SAAS;;;;;;8BAC9B,8OAAC;oBACC,WAAU;oBACV,OAAO;wBACL,WAAW;wBACX,WAAW;wBACX,aAAa;oBACf;;sCAEA,8OAAC,mKAAA,CAAA,iBAAc;4BACb,+BACE,8OAAC,oKAAA,CAAA,kBAAe;gCACd,WAAU;gCACV,OAAO;oCACL,WAAW;oCACX,SAAS;oCACT,YAAY;oCACZ,UAAU;oCACV,WAAW;oCACX,WAAW;oCACX,aAAa;oCACb,aAAa;oCACb,SAAS;oCACT,OAAO;oCACP,WAAW;oCACX,YAAY;oCACZ,UAAU;oCACV,YAAY;oCACZ,OAAO;oCACP,YAAY;oCACZ,QAAQ;oCACR,SAAS;gCACX;gCACA,YAAY;gCACZ,KAAI;;;;;;4BAGR,2BACE,8OAAC;gCAAI,WAAU;0CACZ;;;;;;4BAGL,eAAe,kKAAA,CAAA,uBAAoB;;;;;;sCAErC,8OAAC,mKAAA,CAAA,iBAAc;4BACb,UAAU,CAAC,aAA0B;gCACnC,YAAY,IAAI,CAAC;oCACf,MAAM,cAAc,CAAA,GAAA,wJAAA,CAAA,yBAAsB,AAAD,EAAE,QAAQ;oCACnD,SAAS;gCACX;4BACF;;;;;;sCAEF,8OAAC,kLAAA,CAAA,gBAAa;;;;;sCACd,8OAAC,+JAAA,CAAA,aAAU;;;;;sCACX,8OAAC,+JAAA,CAAA,aAAU;;;;;;;;;;;;WAxDM;;;;;;;;;;AA6D3B;uCAEe", "debugId": null}}, {"offset": {"line": 989, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/dashboard/blogs/components/BlogPostEditor.tsx"], "sourcesContent": ["// \"use client\";\r\n\r\n// import {\r\n//   cleanObject,\r\n//   convertToBase64,\r\n// } from \"@/app/shared/cleanObject\";\r\n// import { getCategoriesAction, createPostAction } from \"../actions\";\r\n// import { <PERSON>ton } from \"@/components/ui/button\";\r\n// import { Eye, ImagePlus, Info, Loader2, Plus } from \"lucide-react\";\r\n// import type React from \"react\";\r\n// import { useState, useEffect } from \"react\";\r\n// import { Controller, useForm } from \"react-hook-form\";\r\n// import { useRouter } from \"next/navigation\";\r\n// import { toast } from \"react-toastify\";\r\n// import { z } from \"zod\";\r\n// import WysiwygEditor from \"./WysiwygEditor\";\r\n\r\n// // Form schema matching the API\r\n// const formSchema = z.object({\r\n//   title: z.string().min(1, \"Title is required\"),\r\n//   slug: z.string().optional(),\r\n//   // canonicalUrl: z\r\n//   //   .string()\r\n//   //   .url(\"Must be a valid URL\")\r\n//   //   .optional()\r\n//   //   .or(z.literal(\"\")),\r\n//   // existingUrl: z.boolean().default(false),\r\n//   content: z.string().min(1, \"Content is required\"),\r\n//   isBlog: z.boolean().default(true),\r\n//   categories: z.array(z.string()).default([]),\r\n//   tags: z.array(z.string()).default([]),\r\n//   metaTitle: z.string().min(1, \"Meta title is required\"),\r\n\r\n//   metaDescription: z.string().min(1, \"Meta description is required\"),\r\n\r\n//   metaKeywords: z.string().min(1, \"Meta keywords are required\"),\r\n\r\n//   banner: z.object({\r\n//     title: z.string().min(1, \"Banner title is required\"),\r\n\r\n//     description: z.string().min(1, \"Banner description is required\"),\r\n\r\n//     image: z.string().min(1, \"Banner image is required\"),\r\n//     altText: z.string().min(1, \"Alt text is required\"),\r\n//   }),\r\n// });\r\n\r\n// type FormData = z.infer<typeof formSchema>;\r\n\r\n// // Field configuration for dynamic rendering\r\n// const fieldConfigs = {\r\n//   blogPost: [\r\n//     {\r\n//       name: \"title\" as const,\r\n//       label: \"Title\",\r\n//       type: \"text\",\r\n//       placeholder: \"Enter the title of the blog post\",\r\n//       tooltip: true,\r\n//       required: true,\r\n//     },\r\n//     {\r\n//       name: \"slug\" as const,\r\n//       label: \"Slug (Optional)\",\r\n//       type: \"text\",\r\n//       placeholder: \"Enter a unique slug (auto-generated if empty)\",\r\n//       tooltip: true,\r\n//       required: false,\r\n//     },\r\n//   ],\r\n//   seo: [\r\n//     {\r\n//       name: \"metaTitle\" as const,\r\n//       label: \"Meta Title\",\r\n//       type: \"text\",\r\n//       placeholder: \"Enter meta title \",\r\n//       tooltip: true,\r\n//       required: true,\r\n//     },\r\n//     {\r\n//       name: \"metaDescription\" as const,\r\n//       label: \"Meta Description\",\r\n//       type: \"textarea\",\r\n//       placeholder: \"Enter meta description\",\r\n//       rows: 3,\r\n//       tooltip: true,\r\n//       required: true,\r\n//     },\r\n//     {\r\n//       name: \"metaKeywords\" as const,\r\n//       label: \"Meta Keywords (comma-separated)\",\r\n//       type: \"text\",\r\n//       placeholder: \"Enter meta keywords \",\r\n//       tooltip: true,\r\n//       required: true,\r\n//     },\r\n//   ],\r\n//   banner: [\r\n//     {\r\n//       name: \"title\" as const,\r\n//       label: \"Banner Title\",\r\n//       type: \"text\",\r\n//       placeholder: \"Enter banner title\",\r\n//       tooltip: true,\r\n//       required: true,\r\n//     },\r\n//     {\r\n//       name: \"description\" as const,\r\n//       label: \"Banner Description\",\r\n//       type: \"textarea\",\r\n//       placeholder: \"Enter banner description\",\r\n//       rows: 4,\r\n//       tooltip: true,\r\n//       required: true,\r\n//     },\r\n//     {\r\n//       name: \"altText\" as const,\r\n//       label: \"Banner Image Alt Text\",\r\n//       type: \"text\",\r\n//       placeholder: \"Enter banner image alt text\",\r\n//       tooltip: true,\r\n//       required: true,\r\n//     },\r\n//   ],\r\n// };\r\n\r\n// const InfoIcon = ({ className }: { className?: string }) => (\r\n//   <Info className={`w-4 h-4 text-gray-400 ${className}`} />\r\n// );\r\n\r\n// const FormField = ({\r\n//   label,\r\n//   error,\r\n//   children,\r\n//   required = false,\r\n//   tooltip = false,\r\n// }: {\r\n//   label: string;\r\n//   error?: string;\r\n//   children: React.ReactNode;\r\n//   required?: boolean;\r\n//   tooltip?: boolean;\r\n// }) => (\r\n//   <div className=\"mb-4\">\r\n//     <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n//       {label}\r\n//       {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n//       {tooltip && <InfoIcon className=\"inline ml-1\" />}\r\n//     </label>\r\n//     {children}\r\n//     {error && <p className=\"text-red-500 text-sm mt-1\">{error}</p>}\r\n//   </div>\r\n// );\r\n\r\n// const DynamicField = ({\r\n//   config,\r\n//   register,\r\n//   error,\r\n// }: {\r\n//   config: any;\r\n//   register: any;\r\n//   error?: string;\r\n// }) => {\r\n//   return (\r\n//     <FormField\r\n//       label={config.label}\r\n//       error={error}\r\n//       tooltip={config.tooltip}\r\n//       required={config.required}\r\n//     >\r\n//       {config.type === \"textarea\" ? (\r\n//         <textarea\r\n//           {...register(config.name)}\r\n//           placeholder={config.placeholder}\r\n//           // placeholder=\"Enter meta description\"\r\n//           rows={config.rows || 3}\r\n//           className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none\"\r\n//         />\r\n//       ) : (\r\n//         <input\r\n//           {...register(config.name)}\r\n//           type={config.type}\r\n//           placeholder={config.placeholder}\r\n//           className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n//         />\r\n//       )}\r\n//     </FormField>\r\n//   );\r\n// };\r\n\r\n// // Multi-select component for categories and tags\r\n\r\n// export interface MultiSelectOption {\r\n//   label: string;\r\n//   value: string;\r\n// }\r\n\r\n// export interface MultiSelectProps {\r\n//   label: string;\r\n//   placeholder: string;\r\n//   options: MultiSelectOption[]; // Accepts array of objects\r\n//   selectedValues: string[]; // Still stores only the 'value'\r\n//   onChange: (values: string[]) => void;\r\n//   onAddNew?: () => void;\r\n//   allowCustom?: boolean;\r\n// }\r\n\r\n// export const MultiSelect = ({\r\n//   label,\r\n//   placeholder,\r\n//   options,\r\n//   selectedValues,\r\n//   onChange,\r\n//   onAddNew,\r\n//   allowCustom = true,\r\n// }: MultiSelectProps) => {\r\n//   const [isOpen, setIsOpen] = useState(false);\r\n//   const [searchTerm, setSearchTerm] = useState(\"\");\r\n\r\n//   // Filter options by label\r\n//   const filteredOptions = options.filter(\r\n//     (option) =>\r\n//       option.label.toLowerCase().includes(searchTerm.toLowerCase()) &&\r\n//       !selectedValues.includes(option.value)\r\n//   );\r\n\r\n//   const handleSelect = (option: MultiSelectOption) => {\r\n//     if (!selectedValues.includes(option.value)) {\r\n//       onChange([...selectedValues, option.value]);\r\n//     }\r\n//     setSearchTerm(\"\");\r\n//     setIsOpen(false);\r\n//   };\r\n\r\n//   const handleRemove = (value: string) => {\r\n//     onChange(selectedValues.filter((item) => item !== value));\r\n//   };\r\n\r\n//   const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n//     if (e.key === \"Enter\" && searchTerm.trim() && allowCustom) {\r\n//       e.preventDefault();\r\n//       const newOption = searchTerm.trim();\r\n//       if (!selectedValues.includes(newOption)) {\r\n//         onChange([...selectedValues, newOption]);\r\n//         setSearchTerm(\"\");\r\n//         setIsOpen(false);\r\n//       }\r\n//     }\r\n//   };\r\n\r\n//   // Helper to get label by value\r\n//   const getLabelByValue = (value: string) => {\r\n//     return options.find((option) => option.value === value)?.label || value;\r\n//   };\r\n\r\n//   return (\r\n//     <div className=\"relative\">\r\n//       <div className=\"flex items-center justify-between mb-3\">\r\n//         <h3 className=\"font-semibold\">{label}</h3>\r\n//         {onAddNew && (\r\n//           <button\r\n//             type=\"button\"\r\n//             onClick={onAddNew}\r\n//             className=\"bg-black text-white px-3 py-1 rounded text-sm flex items-center gap-1\"\r\n//           >\r\n//             <Plus className=\"w-3 h-3\" />\r\n//             Add New\r\n//           </button>\r\n//         )}\r\n//       </div>\r\n\r\n//       {/* Selected items */}\r\n//       {selectedValues.length > 0 && (\r\n//         <div className=\"flex flex-wrap gap-2 mb-3\">\r\n//           {selectedValues.map((value, index) => (\r\n//             <span\r\n//               key={index}\r\n//               className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm flex items-center gap-1\"\r\n//             >\r\n//               {getLabelByValue(value)}\r\n//               <button\r\n//                 type=\"button\"\r\n//                 onClick={() => handleRemove(value)}\r\n//                 className=\"text-blue-600 hover:text-blue-800 ml-1\"\r\n//               >\r\n//                 ×\r\n//               </button>\r\n//             </span>\r\n//           ))}\r\n//         </div>\r\n//       )}\r\n\r\n//       {/* Search input */}\r\n//       <div className=\"relative\">\r\n//         <input\r\n//           type=\"text\"\r\n//           placeholder={placeholder}\r\n//           value={searchTerm}\r\n//           onChange={(e) => {\r\n//             setSearchTerm(e.target.value);\r\n//             setIsOpen(true);\r\n//           }}\r\n//           onFocus={() => setIsOpen(true)}\r\n//           onKeyDown={handleKeyDown}\r\n//           className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n//         />\r\n\r\n//         {/* Dropdown */}\r\n//         {isOpen &&\r\n//           (filteredOptions.length > 0 ||\r\n//             (allowCustom && searchTerm.trim())) && (\r\n//             <div className=\"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto\">\r\n//               {filteredOptions.map((option, index) => (\r\n//                 <div\r\n//                   key={index}\r\n//                   onClick={() => handleSelect(option)}\r\n//                   className=\"px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm\"\r\n//                 >\r\n//                   {option.label}\r\n//                 </div>\r\n//               ))}\r\n//               {allowCustom && searchTerm.trim() && (\r\n//                 <div\r\n//                   onClick={() =>\r\n//                     handleSelect({\r\n//                       label: searchTerm.trim(),\r\n//                       value: searchTerm.trim(),\r\n//                     })\r\n//                   }\r\n//                   className=\"px-3 py-2 hover:bg-green-50 cursor-pointer text-sm border-t border-gray-200 text-green-700\"\r\n//                 >\r\n//                   Add &quot;{searchTerm.trim()}&quot;\r\n//                 </div>\r\n//               )}\r\n//             </div>\r\n//           )}\r\n//       </div>\r\n\r\n//       {/* Click outside to close */}\r\n//       {isOpen && (\r\n//         <div className=\"fixed inset-0 z-5\" onClick={() => setIsOpen(false)} />\r\n//       )}\r\n//     </div>\r\n//   );\r\n// };\r\n\r\n// // Original DynamicContentManager (preserved for backward compatibility)\r\n// export default function DynamicContentManager() {\r\n//   const router = useRouter();\r\n//   const [categories, setCategories] = useState<string[]>([]);\r\n//   const [tags, setTags] = useState<string[]>([]);\r\n//   const [availableCategories, setAvailableCategories] = useState<\r\n//     MultiSelectOption[]\r\n//   >([]);\r\n//   const [isSubmitting, setIsSubmitting] = useState(false);\r\n//   const [bannerImageFile, setBannerImageFile] = useState<File | null>(null);\r\n\r\n//   const {\r\n//     control,\r\n//     register,\r\n//     handleSubmit,\r\n//     watch,\r\n//     setValue,\r\n//     formState: { errors },\r\n//   } = useForm<FormData>({\r\n//     defaultValues: {\r\n//       isBlog: true,\r\n//       // existingUrl: false,\r\n//       categories: [],\r\n//       tags: [],\r\n//       content: \"\",\r\n//       banner: {\r\n//         title: \"\",\r\n//         description: \"\",\r\n//         altText: \"\",\r\n//         image: \"\",\r\n//       },\r\n//     },\r\n//   });\r\n\r\n//   // const existingUrl = watch(\"existingUrl\");\r\n\r\n//   // Fetch available categories on component mount\r\n//   useEffect(() => {\r\n//     const fetchCategories = async () => {\r\n//       try {\r\n//         const response = await getCategoriesAction();\r\n//         if (response.success && response.data) {\r\n//           const categoriesData = (response.data as any).categories || [];\r\n//           const categoryNames = categoriesData.map((cat: any) => ({\r\n//             label: cat.name,\r\n//             value: cat._id,\r\n//           }));\r\n//           setAvailableCategories(categoryNames);\r\n//         }\r\n//       } catch (error) {\r\n//         console.error(\"Failed to fetch categories:\", error);\r\n//       }\r\n//     };\r\n\r\n//     fetchCategories();\r\n//   }, []);\r\n\r\n//   const onSubmit = async (data: FormData) => {\r\n//     setIsSubmitting(true);\r\n\r\n//     try {\r\n//       console.log(\"Form data before cleaning:\", data);\r\n\r\n//       // Clean and format the data\r\n//       const formattedData = cleanObject(data);\r\n//       const bannerImageBase64 = bannerImageFile\r\n//         ? ((await convertToBase64(bannerImageFile)) as string)\r\n//         : \"\";\r\n\r\n//       const res = await createPostAction(formattedData, bannerImageBase64);\r\n\r\n//       if (!res.success) {\r\n//         console.error(\"API Error:\", res);\r\n//         toast.error(res?.message || \"Failed to create post\");\r\n//       } else {\r\n//         console.log(\"Post created successfully:\", res);\r\n//         toast.success(res?.message || \"Post created successfully\");\r\n\r\n//         // Redirect to blogs management page after successful creation\r\n//         setTimeout(() => {\r\n//           router.push(\"/dashboard/blogs\");\r\n//         }, 1500); // Wait 1.5 seconds to show the success toast\r\n//       }\r\n//     } catch (error) {\r\n//       console.error(\"Submission error:\", error);\r\n//       toast.error(\"An unexpected error occurred\");\r\n//     } finally {\r\n//       setIsSubmitting(false);\r\n//     }\r\n//   };\r\n\r\n//   const handleBannerImageChange = (\r\n//     event: React.ChangeEvent<HTMLInputElement>\r\n//   ) => {\r\n//     const file = event.target.files?.[0];\r\n//     if (file) {\r\n//       console.log(\"Banner image selected:\", {\r\n//         name: file.name,\r\n//         size: file.size,\r\n//         type: file.type,\r\n//       });\r\n\r\n//       // Validate file\r\n//       const allowedTypes = [\r\n//         \"image/jpeg\",\r\n//         \"image/jpg\",\r\n//         \"image/png\",\r\n//         \"image/webp\",\r\n//         \"image/gif\",\r\n//         \"image/svg+xml\",\r\n//       ];\r\n//       const maxSize = 10 * 1024 * 1024; // 10MB\r\n\r\n//       if (!allowedTypes.includes(file.type)) {\r\n//         toast.error(\r\n//           `Invalid file type: ${file.type}. Only images are allowed.`\r\n//         );\r\n//         return;\r\n//       }\r\n\r\n//       if (file.size > maxSize) {\r\n//         toast.error(\r\n//           `File too large: ${(file.size / 1024 / 1024).toFixed(\r\n//             2\r\n//           )}MB. Maximum 10MB allowed.`\r\n//         );\r\n//         return;\r\n//       }\r\n\r\n//       if (file.size === 0) {\r\n//         toast.error(\"File is empty\");\r\n//         return;\r\n//       }\r\n\r\n//       setBannerImageFile(file);\r\n//       // Optionally create a preview URL\r\n//       const previewUrl = URL.createObjectURL(file);\r\n//       setValue(\"banner.image\", previewUrl); // For preview purposes\r\n//     } else {\r\n//       setBannerImageFile(null);\r\n//       setValue(\"banner.image\", \"\");\r\n//     }\r\n//   };\r\n\r\n//   return (\r\n//     <div className=\"min-h-screen bg-gray-50 p-6\">\r\n//       <div className=\"container px-3 mx-auto\">\r\n//         <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\r\n//           {/* Blog Post Section */}\r\n//           <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\r\n//             <div className=\"flex items-center justify-between mb-6\">\r\n//               <div className=\"flex items-center gap-4\">\r\n//                 <span className=\"text-teal-600\">Create</span>\r\n//                 <span className=\"text-gray-400\">•</span>\r\n//                 <span>Blog Post</span>\r\n//               </div>\r\n//               <div className=\"flex items-center gap-2\">\r\n//                 <button\r\n//                   onClick={() => router.push(\"/dashboard/blogs\")}\r\n//                   type=\"button\"\r\n//                   className=\"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50\"\r\n//                 >\r\n//                   Discard\r\n//                 </button>\r\n//                 <button\r\n//                   type=\"submit\"\r\n//                   disabled={isSubmitting}\r\n//                   className=\"bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 disabled:opacity-50 flex items-center gap-2\"\r\n//                 >\r\n//                   {isSubmitting && <Loader2 className=\"w-4 h-4 animate-spin\" />}\r\n//                   {isSubmitting ? \"Creating...\" : \"Create Post\"}\r\n//                 </button>\r\n//               </div>\r\n//             </div>\r\n\r\n//             <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n//               <div className=\"lg:col-span-2\">\r\n//                 <div className=\"space-y-6\">\r\n//                   <div>\r\n//                     <h3 className=\"text-lg font-semibold mb-4\">Post Details</h3>\r\n\r\n//                     {fieldConfigs.blogPost.map((config) => (\r\n//                       <DynamicField\r\n//                         key={config.name}\r\n//                         config={config}\r\n//                         register={register}\r\n//                         error={errors[config.name]?.message}\r\n//                       />\r\n//                     ))}\r\n\r\n//                     {/* <FormField\r\n//                       label=\"Canonical URL\"\r\n//                       error={errors.canonicalUrl?.message}\r\n//                       tooltip\r\n//                     >\r\n//                       <div className=\"space-y-2\">\r\n//                         <div className=\"flex items-center gap-2\">\r\n//                           <input\r\n//                             {...register(\"existingUrl\")}\r\n//                             type=\"checkbox\"\r\n//                             className=\"rounded\"\r\n//                           />\r\n//                           <label className=\"text-sm\">Existing Url</label>\r\n//                         </div>\r\n//                         <div className=\"flex items-center gap-2\">\r\n//                           <input\r\n//                             {...register(\"canonicalUrl\")}\r\n//                             type=\"url\"\r\n//                             placeholder=\"https://example.com/blog/post/\"\r\n//                             disabled={!existingUrl}\r\n//                             className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100\"\r\n//                           />\r\n//                           <button\r\n//                             type=\"button\"\r\n//                             disabled={!existingUrl}\r\n//                             className=\"px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 flex items-center gap-1\"\r\n//                           >\r\n//                             <Eye className=\"w-4 h-4\" />\r\n//                             Visit\r\n//                           </button>\r\n//                         </div>\r\n//                       </div>\r\n//                     </FormField> */}\r\n\r\n//                     {/* Categories and Tags Section */}\r\n//                     <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n//                       <div>\r\n//                         <MultiSelect\r\n//                           label=\"Categories\"\r\n//                           placeholder=\"Search for categories...\"\r\n//                           options={availableCategories}\r\n//                           selectedValues={categories}\r\n//                           onChange={(values) => {\r\n//                             setCategories(values);\r\n//                             setValue(\"categories\", values);\r\n//                           }}\r\n//                           allowCustom={true}\r\n//                         />\r\n//                       </div>\r\n\r\n//                       <div>\r\n//                         <MultiSelect\r\n//                           label=\"Tags\"\r\n//                           placeholder=\"Search for tags...\"\r\n//                           options={[]} // Tags are usually custom, so no predefined options\r\n//                           selectedValues={tags}\r\n//                           onChange={(values) => {\r\n//                             setTags(values);\r\n//                             setValue(\"tags\", values);\r\n//                           }}\r\n//                           allowCustom={true}\r\n//                         />\r\n//                       </div>\r\n//                     </div>\r\n//                   </div>\r\n\r\n//                   <div>\r\n//                     <h3 className=\"text-lg font-semibold mb-4\">Blog Content</h3>\r\n//                     <FormField\r\n//                       label=\"Content\"\r\n//                       error={errors.content?.message}\r\n//                       required\r\n//                     >\r\n//                       <Controller\r\n//                         name=\"content\"\r\n//                         control={control}\r\n//                         render={({ field }) => (\r\n//                           <WysiwygEditor\r\n//                             content={field.value || \"\"}\r\n//                             onChange={field.onChange}\r\n//                             placeholder=\"Start writing your blog post content...\"\r\n//                           />\r\n//                         )}\r\n//                       />\r\n//                     </FormField>\r\n//                   </div>\r\n//                 </div>\r\n//               </div>\r\n\r\n//               <div className=\"space-y-6\">\r\n//                 {/* Sidebar content removed as requested */}\r\n//               </div>\r\n//             </div>\r\n//           </div>\r\n\r\n//           {/* SEO Section */}\r\n//           <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\r\n//             <h2 className=\"text-lg font-semibold mb-6\">SEO</h2>\r\n//             <div className=\"space-y-4\">\r\n//               {fieldConfigs.seo.map((config) => (\r\n//                 <DynamicField\r\n//                   key={config.name}\r\n//                   config={config}\r\n//                   register={register}\r\n//                   error={errors[config.name]?.message}\r\n//                 />\r\n//               ))}\r\n//             </div>\r\n//           </div>\r\n\r\n//           {/* Banner Section */}\r\n//           <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\r\n//             <h2 className=\"text-lg font-semibold mb-6\">Banner</h2>\r\n//             <div className=\"space-y-4\">\r\n//               {fieldConfigs.banner.map((config) => (\r\n//                 <DynamicField\r\n//                   key={config.name}\r\n//                   config={config}\r\n//                   register={register}\r\n//                   error={errors.banner?.[config.name]?.message}\r\n//                 />\r\n//               ))}\r\n\r\n//               <div className=\"space-y-4\">\r\n//                 <label\r\n//                   htmlFor=\"bannerImage\"\r\n//                   className=\"block text-sm font-medium text-gray-700 jakarta\"\r\n//                 >\r\n//                   Banner Image\r\n//                 </label>\r\n//                 <div className=\"flex items-center gap-4\">\r\n//                   <Button\r\n//                     type=\"button\"\r\n//                     variant=\"default\"\r\n//                     className=\"flex items-center gap-2 text-white transition-all duration-300 rounded-md shadow-md\"\r\n//                     onClick={() =>\r\n//                       document.getElementById(\"bannerImage\")?.click()\r\n//                     }\r\n//                   >\r\n//                     <ImagePlus size={20} />\r\n//                     <span>Choose Image</span>\r\n//                   </Button>\r\n//                   <input\r\n//                     type=\"file\"\r\n//                     id=\"bannerImage\"\r\n//                     accept=\"image/*\"\r\n//                     onChange={handleBannerImageChange}\r\n//                     className=\"hidden\"\r\n//                   />\r\n//                 </div>\r\n//                 {bannerImageFile && (\r\n//                   <div className=\"mt-4\">\r\n//                     <p className=\"text-sm text-gray-600 jakarta\">\r\n//                       Selected: {bannerImageFile.name} (\r\n//                       {(bannerImageFile.size / 1024).toFixed(2)} KB)\r\n//                     </p>\r\n//                     <img\r\n//                       src={URL.createObjectURL(bannerImageFile)}\r\n//                       alt=\"Preview\"\r\n//                       className=\"mt-2 max-w-[200px] max-h-[200px] rounded-md shadow-lg object-cover\"\r\n//                     />\r\n//                   </div>\r\n//                 )}\r\n//               </div>\r\n//             </div>\r\n//           </div>\r\n//         </form>\r\n//       </div>\r\n//     </div>\r\n//   );\r\n// }\r\n\r\n// Second\r\n\r\n\"use client\";\r\n\r\nimport {\r\n  cleanObject,\r\n  convertToBase64,\r\n} from \"@/app/shared/cleanObject\";\r\nimport { getCategoriesAction, createPostAction } from \"../actions\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { ImagePlus, Info, Loader2, Plus } from \"lucide-react\";\r\nimport type React from \"react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { Controller, useForm } from \"react-hook-form\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { toast } from \"react-toastify\";\r\nimport { z } from \"zod\";\r\nimport WysiwygEditor from \"./WysiwygEditor\";\r\n\r\n// Form schema matching the API\r\nconst formSchema = z.object({\r\n  title: z.string().min(1, \"Title is required\"),\r\n  slug: z.string().optional(),\r\n  // canonicalUrl: z\r\n  //   .string()\r\n  //   .url(\"Must be a valid URL\")\r\n  //   .optional()\r\n  //   .or(z.literal(\"\")),\r\n  // existingUrl: z.boolean().default(false),\r\n  content: z.string().min(1, \"Content is required\"),\r\n  isBlog: z.boolean().default(true),\r\n  categories: z.array(z.string()).default([]),\r\n  tags: z.array(z.string()).default([]),\r\n  metaTitle: z.string().min(1, \"Meta title is required\"),\r\n  metaDescription: z.string().min(1, \"Meta description is required\"),\r\n  metaKeywords: z.string().min(1, \"Meta keywords are required\"),\r\n  banner: z.object({\r\n    image: z.string().min(1, \"Banner image is required\"),\r\n  }),\r\n});\r\n\r\ntype FormData = z.infer<typeof formSchema>;\r\n\r\n// Field configuration for dynamic rendering\r\nconst fieldConfigs = {\r\n  blogPost: [\r\n    {\r\n      name: \"title\" as const,\r\n      label: \"Title\",\r\n      type: \"text\",\r\n      placeholder: \"Enter the title of the blog post\",\r\n      tooltip: true,\r\n      required: true,\r\n    },\r\n    {\r\n      name: \"slug\" as const,\r\n      label: \"Slug (Optional)\",\r\n      type: \"text\",\r\n      placeholder: \"Enter a unique slug (auto-generated if empty)\",\r\n      tooltip: true,\r\n      required: false,\r\n    },\r\n  ],\r\n  seo: [\r\n    {\r\n      name: \"metaTitle\" as const,\r\n      label: \"Meta Title\",\r\n      type: \"text\",\r\n      placeholder: \"Enter meta title \",\r\n      tooltip: true,\r\n      required: true,\r\n    },\r\n    {\r\n      name: \"metaDescription\" as const,\r\n      label: \"Meta Description\",\r\n      type: \"textarea\",\r\n      placeholder: \"Enter meta description\",\r\n      rows: 3,\r\n      tooltip: true,\r\n      required: true,\r\n    },\r\n    {\r\n      name: \"metaKeywords\" as const,\r\n      label: \"Meta Keywords (comma-separated)\",\r\n      type: \"text\",\r\n      placeholder: \"Enter meta keywords \",\r\n      tooltip: true,\r\n      required: true,\r\n    },\r\n  ],\r\n  banner: [],\r\n};\r\n\r\nconst InfoIcon = ({ className }: { className?: string }) => (\r\n  <Info className={`w-4 h-4 text-gray-400 ${className}`} />\r\n);\r\n\r\nconst FormField = ({\r\n  label,\r\n  error,\r\n  children,\r\n  required = false,\r\n  tooltip = false,\r\n}: {\r\n  label: string;\r\n  error?: string;\r\n  children: React.ReactNode;\r\n  required?: boolean;\r\n  tooltip?: boolean;\r\n}) => (\r\n  <div className=\"mb-4\">\r\n    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n      {label}\r\n      {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n      {tooltip && <InfoIcon className=\"inline ml-1\" />}\r\n    </label>\r\n    {children}\r\n    {error && <p className=\"text-red-500 text-sm mt-1\">{error}</p>}\r\n  </div>\r\n);\r\n\r\nconst DynamicField = ({\r\n  config,\r\n  register,\r\n  error,\r\n}: {\r\n  config: any;\r\n  register: any;\r\n  error?: string;\r\n}) => {\r\n  return (\r\n    <FormField\r\n      label={config.label}\r\n      error={error}\r\n      tooltip={config.tooltip}\r\n      required={config.required}\r\n    >\r\n      {config.type === \"textarea\" ? (\r\n        <textarea\r\n          {...register(config.name)}\r\n          placeholder={config.placeholder}\r\n          // placeholder=\"Enter meta description\"\r\n          rows={config.rows || 3}\r\n          className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none\"\r\n        />\r\n      ) : (\r\n        <input\r\n          {...register(config.name)}\r\n          type={config.type}\r\n          placeholder={config.placeholder}\r\n          className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n        />\r\n      )}\r\n    </FormField>\r\n  );\r\n};\r\n\r\n// Multi-select component for categories and tags\r\nexport interface MultiSelectOption {\r\n  label: string;\r\n  value: string;\r\n}\r\n\r\nexport interface MultiSelectProps {\r\n  label: string;\r\n  placeholder: string;\r\n  options: MultiSelectOption[]; // Accepts array of objects\r\n  selectedValues: string[]; // Still stores only the 'value'\r\n  onChange: (values: string[]) => void;\r\n  onAddNew?: () => void;\r\n  allowCustom?: boolean;\r\n}\r\n\r\nexport const MultiSelect = ({\r\n  label,\r\n  placeholder,\r\n  options,\r\n  selectedValues,\r\n  onChange,\r\n  onAddNew,\r\n  allowCustom = true,\r\n}: MultiSelectProps) => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n\r\n  // Filter options by label\r\n  const filteredOptions = options.filter(\r\n    (option) =>\r\n      option.label.toLowerCase().includes(searchTerm.toLowerCase()) &&\r\n      !selectedValues.includes(option.value)\r\n  );\r\n\r\n  const handleSelect = (option: MultiSelectOption) => {\r\n    if (!selectedValues.includes(option.value)) {\r\n      onChange([...selectedValues, option.value]);\r\n    }\r\n    setSearchTerm(\"\");\r\n    setIsOpen(false);\r\n  };\r\n\r\n  const handleRemove = (value: string) => {\r\n    onChange(selectedValues.filter((item) => item !== value));\r\n  };\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n    if (e.key === \"Enter\" && searchTerm.trim() && allowCustom) {\r\n      e.preventDefault();\r\n      const newOption = searchTerm.trim();\r\n      if (!selectedValues.includes(newOption)) {\r\n        onChange([...selectedValues, newOption]);\r\n        setSearchTerm(\"\");\r\n        setIsOpen(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Helper to get label by value\r\n  const getLabelByValue = (value: string) => {\r\n    return options.find((option) => option.value === value)?.label || value;\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      <div className=\"flex items-center justify-between mb-3\">\r\n        <h3 className=\"font-semibold\">{label}</h3>\r\n        {onAddNew && (\r\n          <button\r\n            type=\"button\"\r\n            onClick={onAddNew}\r\n            className=\"bg-black text-white px-3 py-1 rounded text-sm flex items-center gap-1\"\r\n          >\r\n            <Plus className=\"w-3 h-3\" />\r\n            Add New\r\n          </button>\r\n        )}\r\n      </div>\r\n\r\n      {/* Selected items */}\r\n      {selectedValues.length > 0 && (\r\n        <div className=\"flex flex-wrap gap-2 mb-3\">\r\n          {selectedValues.map((value, index) => (\r\n            <span\r\n              key={index}\r\n              className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm flex items-center gap-1\"\r\n            >\r\n              {getLabelByValue(value)}\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => handleRemove(value)}\r\n                className=\"text-blue-600 hover:text-blue-800 ml-1\"\r\n              >\r\n                ×\r\n              </button>\r\n            </span>\r\n          ))}\r\n        </div>\r\n      )}\r\n\r\n      {/* Search input */}\r\n      <div className=\"relative\">\r\n        <input\r\n          type=\"text\"\r\n          placeholder={placeholder}\r\n          value={searchTerm}\r\n          onChange={(e) => {\r\n            setSearchTerm(e.target.value);\r\n            setIsOpen(true);\r\n          }}\r\n          onFocus={() => setIsOpen(true)}\r\n          onKeyDown={handleKeyDown}\r\n          className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n        />\r\n\r\n        {/* Dropdown */}\r\n        {isOpen &&\r\n          (filteredOptions.length > 0 ||\r\n            (allowCustom && searchTerm.trim())) && (\r\n            <div className=\"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto\">\r\n              {filteredOptions.map((option, index) => (\r\n                <div\r\n                  key={index}\r\n                  onClick={() => handleSelect(option)}\r\n                  className=\"px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm\"\r\n                >\r\n                  {option.label}\r\n                </div>\r\n              ))}\r\n              {allowCustom && searchTerm.trim() && (\r\n                <div\r\n                  onClick={() =>\r\n                    handleSelect({\r\n                      label: searchTerm.trim(),\r\n                      value: searchTerm.trim(),\r\n                    })\r\n                  }\r\n                  className=\"px-3 py-2 hover:bg-green-50 cursor-pointer text-sm border-t border-gray-200 text-green-700\"\r\n                >\r\n                  Add &quot;{searchTerm.trim()}&quot;\r\n                </div>\r\n              )}\r\n            </div>\r\n          )}\r\n      </div>\r\n\r\n      {/* Click outside to close */}\r\n      {isOpen && (\r\n        <div className=\"fixed inset-0 z-5\" onClick={() => setIsOpen(false)} />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\n// Original DynamicContentManager (preserved for backward compatibility)\r\nexport default function DynamicContentManager() {\r\n  const router = useRouter();\r\n  const [categories, setCategories] = useState<string[]>([]);\r\n  const [tags, setTags] = useState<string[]>([]);\r\n  const [availableCategories, setAvailableCategories] = useState<\r\n    MultiSelectOption[]\r\n  >([]);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [bannerImageFile, setBannerImageFile] = useState<File | null>(null);\r\n\r\n  const {\r\n    control,\r\n    register,\r\n    handleSubmit,\r\n    watch,\r\n    setValue,\r\n    formState: { errors },\r\n  } = useForm<FormData>({\r\n    defaultValues: {\r\n      isBlog: true,\r\n      // existingUrl: false,\r\n      categories: [],\r\n      tags: [],\r\n      content: \"\",\r\n      banner: {\r\n        image: \"\",\r\n      },\r\n    },\r\n  });\r\n\r\n  // const existingUrl = watch(\"existingUrl\");\r\n\r\n  // Fetch available categories on component mount\r\n  useEffect(() => {\r\n    const fetchCategories = async () => {\r\n      try {\r\n        const response = await getCategoriesAction();\r\n        if (response.success && response.data) {\r\n          const categoriesData = (response.data as any).categories || [];\r\n          const categoryNames = categoriesData.map((cat: any) => ({\r\n            label: cat.name,\r\n            value: cat._id,\r\n          }));\r\n          setAvailableCategories(categoryNames);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Failed to fetch categories:\", error);\r\n      }\r\n    };\r\n\r\n    fetchCategories();\r\n  }, []);\r\n\r\n  const onSubmit = async (data: FormData) => {\r\n    setIsSubmitting(true);\r\n    try {\r\n      console.log(\"Form data before cleaning:\", data);\r\n\r\n      // Clean and format the data\r\n      const formattedData = cleanObject(data);\r\n\r\n      const bannerImageBase64 = bannerImageFile\r\n        ? ((await convertToBase64(bannerImageFile)) as string)\r\n        : \"\";\r\n\r\n      const res = await createPostAction(formattedData, bannerImageBase64);\r\n\r\n      if (!res.success) {\r\n        console.error(\"API Error:\", res);\r\n        toast.error(res?.message || \"Failed to create post\");\r\n      } else {\r\n        console.log(\"Post created successfully:\", res);\r\n        toast.success(res?.message || \"Post created successfully\");\r\n        // Redirect to blogs management page after successful creation\r\n        setTimeout(() => {\r\n          router.push(\"/dashboard/blogs\");\r\n        }, 1500); // Wait 1.5 seconds to show the success toast\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Submission error:\", error);\r\n      toast.error(\"An unexpected error occurred\");\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleBannerImageChange = (\r\n    event: React.ChangeEvent<HTMLInputElement>\r\n  ) => {\r\n    const file = event.target.files?.[0];\r\n    if (file) {\r\n      console.log(\"Banner image selected:\", {\r\n        name: file.name,\r\n        size: file.size,\r\n        type: file.type,\r\n      });\r\n\r\n      // Validate file\r\n      const allowedTypes = [\r\n        \"image/jpeg\",\r\n        \"image/jpg\",\r\n        \"image/png\",\r\n        \"image/webp\",\r\n        \"image/gif\",\r\n        \"image/svg+xml\",\r\n      ];\r\n      const maxSize = 10 * 1024 * 1024; // 10MB\r\n\r\n      if (!allowedTypes.includes(file.type)) {\r\n        toast.error(\r\n          `Invalid file type: ${file.type}. Only images are allowed.`\r\n        );\r\n        return;\r\n      }\r\n\r\n      if (file.size > maxSize) {\r\n        toast.error(\r\n          `File too large: ${(file.size / 1024 / 1024).toFixed(\r\n            2\r\n          )}MB. Maximum 10MB allowed.`\r\n        );\r\n        return;\r\n      }\r\n\r\n      if (file.size === 0) {\r\n        toast.error(\"File is empty\");\r\n        return;\r\n      }\r\n\r\n      setBannerImageFile(file);\r\n\r\n      // Optionally create a preview URL\r\n      const previewUrl = URL.createObjectURL(file);\r\n      setValue(\"banner.image\", previewUrl); // For preview purposes\r\n    } else {\r\n      setBannerImageFile(null);\r\n      setValue(\"banner.image\", \"\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50 p-6\">\r\n      <div className=\"container px-3 mx-auto\">\r\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\r\n          {/* Blog Post Section */}\r\n          <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\r\n            <div className=\"flex items-center justify-between mb-6\">\r\n              <div className=\"flex items-center gap-4\">\r\n                <span className=\"text-teal-600\">Create</span>\r\n                <span className=\"text-gray-400\">•</span>\r\n                <span>Blog Post</span>\r\n              </div>\r\n              <div className=\"flex items-center gap-2\">\r\n                <button\r\n                  onClick={() => router.push(\"/dashboard/blogs\")}\r\n                  type=\"button\"\r\n                  className=\"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50\"\r\n                >\r\n                  Discard\r\n                </button>\r\n                <button\r\n                  type=\"submit\"\r\n                  disabled={isSubmitting}\r\n                  className=\"bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 disabled:opacity-50 flex items-center gap-2\"\r\n                >\r\n                  {isSubmitting && <Loader2 className=\"w-4 h-4 animate-spin\" />}\r\n                  {isSubmitting ? \"Creating...\" : \"Create Post\"}\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n              <div className=\"lg:col-span-2\">\r\n                <div className=\"space-y-6\">\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold mb-4\">Post Details</h3>\r\n                    {fieldConfigs.blogPost.map((config) => (\r\n                      <DynamicField\r\n                        key={config.name}\r\n                        config={config}\r\n                        register={register}\r\n                        error={errors[config.name]?.message}\r\n                      />\r\n                    ))}\r\n                    {/* <FormField\r\n                      label=\"Canonical URL\"\r\n                      error={errors.canonicalUrl?.message}\r\n                      tooltip\r\n                    >\r\n                      <div className=\"space-y-2\">\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <input\r\n                            {...register(\"existingUrl\")}\r\n                            type=\"checkbox\"\r\n                            className=\"rounded\"\r\n                          />\r\n                          <label className=\"text-sm\">Existing Url</label>\r\n                        </div>\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <input\r\n                            {...register(\"canonicalUrl\")}\r\n                            type=\"url\"\r\n                            placeholder=\"https://example.com/blog/post/\"\r\n                            disabled={!existingUrl}\r\n                            className=\"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100\"\r\n                          />\r\n                          <button\r\n                            type=\"button\"\r\n                            disabled={!existingUrl}\r\n                            className=\"px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 flex items-center gap-1\"\r\n                          >\r\n                            <Eye className=\"w-4 h-4\" />\r\n                            Visit\r\n                          </button>\r\n                        </div>\r\n                      </div>\r\n                    </FormField> */}\r\n\r\n                    {/* Categories and Tags Section */}\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                      <div>\r\n                        <MultiSelect\r\n                          label=\"Categories\"\r\n                          placeholder=\"Search for categories...\"\r\n                          options={availableCategories}\r\n                          selectedValues={categories}\r\n                          onChange={(values) => {\r\n                            setCategories(values);\r\n                            setValue(\"categories\", values);\r\n                          }}\r\n                          allowCustom={true}\r\n                        />\r\n                      </div>\r\n                      <div>\r\n                        <MultiSelect\r\n                          label=\"Tags\"\r\n                          placeholder=\"Search for tags...\"\r\n                          options={[]} // Tags are usually custom, so no predefined options\r\n                          selectedValues={tags}\r\n                          onChange={(values) => {\r\n                            setTags(values);\r\n                            setValue(\"tags\", values);\r\n                          }}\r\n                          allowCustom={true}\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold mb-4\">Blog Content</h3>\r\n                    <FormField\r\n                      label=\"Content\"\r\n                      error={errors.content?.message}\r\n                      required\r\n                    >\r\n                      <Controller\r\n                        name=\"content\"\r\n                        control={control}\r\n                        render={({ field }) => (\r\n                          <WysiwygEditor\r\n                            content={field.value || \"\"}\r\n                            onChange={field.onChange}\r\n                            placeholder=\"Start writing your blog post content...\"\r\n                          />\r\n                        )}\r\n                      />\r\n                    </FormField>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"space-y-6\">\r\n                {/* Sidebar content removed as requested */}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* SEO Section */}\r\n          <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\r\n            <h2 className=\"text-lg font-semibold mb-6\">SEO</h2>\r\n            <div className=\"space-y-4\">\r\n              {fieldConfigs.seo.map((config) => (\r\n                <DynamicField\r\n                  key={config.name}\r\n                  config={config}\r\n                  register={register}\r\n                  error={errors[config.name]?.message}\r\n                />\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Banner Section */}\r\n          <div className=\"bg-white p-6 rounded-lg shadow-sm border\">\r\n            <h2 className=\"text-lg font-semibold mb-6\">Banner</h2>\r\n            <div className=\"space-y-4\">\r\n              <div className=\"space-y-4\">\r\n                <label\r\n                  htmlFor=\"bannerImage\"\r\n                  className=\"block text-sm font-medium text-gray-700 jakarta\"\r\n                >\r\n                  Banner Image\r\n                </label>\r\n                <div className=\"flex items-center gap-4\">\r\n                  <Button\r\n                    type=\"button\"\r\n                    variant=\"default\"\r\n                    className=\"flex items-center gap-2 text-white transition-all duration-300 rounded-md shadow-md\"\r\n                    onClick={() =>\r\n                      document.getElementById(\"bannerImage\")?.click()\r\n                    }\r\n                  >\r\n                    <ImagePlus size={20} />\r\n                    <span>Choose Image</span>\r\n                  </Button>\r\n                  <input\r\n                    type=\"file\"\r\n                    id=\"bannerImage\"\r\n                    accept=\"image/*\"\r\n                    onChange={handleBannerImageChange}\r\n                    className=\"hidden\"\r\n                  />\r\n                </div>\r\n\r\n                {bannerImageFile && (\r\n                  <div className=\"mt-4\">\r\n                    <p className=\"text-sm text-gray-600 jakarta\">\r\n                      Selected: {bannerImageFile.name} (\r\n                      {(bannerImageFile.size / 1024).toFixed(2)} KB)\r\n                    </p>\r\n                    <img\r\n                      src={\r\n                        URL.createObjectURL(bannerImageFile) ||\r\n                        \"/placeholder.svg\"\r\n                      }\r\n                      alt=\"Preview\"\r\n                      className=\"mt-2 max-w-[200px] max-h-[200px] rounded-md shadow-lg object-cover\"\r\n                    />\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,gBAAgB;AAEhB,WAAW;AACX,iBAAiB;AACjB,qBAAqB;AACrB,qCAAqC;AACrC,sEAAsE;AACtE,mDAAmD;AACnD,sEAAsE;AACtE,kCAAkC;AAClC,+CAA+C;AAC/C,yDAAyD;AACzD,+CAA+C;AAC/C,0CAA0C;AAC1C,2BAA2B;AAC3B,+CAA+C;AAE/C,kCAAkC;AAClC,gCAAgC;AAChC,mDAAmD;AACnD,iCAAiC;AACjC,uBAAuB;AACvB,mBAAmB;AACnB,qCAAqC;AACrC,qBAAqB;AACrB,6BAA6B;AAC7B,gDAAgD;AAChD,uDAAuD;AACvD,uCAAuC;AACvC,iDAAiD;AACjD,2CAA2C;AAC3C,4DAA4D;AAE5D,wEAAwE;AAExE,mEAAmE;AAEnE,uBAAuB;AACvB,4DAA4D;AAE5D,wEAAwE;AAExE,4DAA4D;AAC5D,0DAA0D;AAC1D,QAAQ;AACR,MAAM;AAEN,8CAA8C;AAE9C,+CAA+C;AAC/C,yBAAyB;AACzB,gBAAgB;AAChB,QAAQ;AACR,gCAAgC;AAChC,wBAAwB;AACxB,sBAAsB;AACtB,yDAAyD;AACzD,uBAAuB;AACvB,wBAAwB;AACxB,SAAS;AACT,QAAQ;AACR,+BAA+B;AAC/B,kCAAkC;AAClC,sBAAsB;AACtB,sEAAsE;AACtE,uBAAuB;AACvB,yBAAyB;AACzB,SAAS;AACT,OAAO;AACP,WAAW;AACX,QAAQ;AACR,oCAAoC;AACpC,6BAA6B;AAC7B,sBAAsB;AACtB,0CAA0C;AAC1C,uBAAuB;AACvB,wBAAwB;AACxB,SAAS;AACT,QAAQ;AACR,0CAA0C;AAC1C,mCAAmC;AACnC,0BAA0B;AAC1B,+CAA+C;AAC/C,iBAAiB;AACjB,uBAAuB;AACvB,wBAAwB;AACxB,SAAS;AACT,QAAQ;AACR,uCAAuC;AACvC,kDAAkD;AAClD,sBAAsB;AACtB,6CAA6C;AAC7C,uBAAuB;AACvB,wBAAwB;AACxB,SAAS;AACT,OAAO;AACP,cAAc;AACd,QAAQ;AACR,gCAAgC;AAChC,+BAA+B;AAC/B,sBAAsB;AACtB,2CAA2C;AAC3C,uBAAuB;AACvB,wBAAwB;AACxB,SAAS;AACT,QAAQ;AACR,sCAAsC;AACtC,qCAAqC;AACrC,0BAA0B;AAC1B,iDAAiD;AACjD,iBAAiB;AACjB,uBAAuB;AACvB,wBAAwB;AACxB,SAAS;AACT,QAAQ;AACR,kCAAkC;AAClC,wCAAwC;AACxC,sBAAsB;AACtB,oDAAoD;AACpD,uBAAuB;AACvB,wBAAwB;AACxB,SAAS;AACT,OAAO;AACP,KAAK;AAEL,gEAAgE;AAChE,8DAA8D;AAC9D,KAAK;AAEL,uBAAuB;AACvB,WAAW;AACX,WAAW;AACX,cAAc;AACd,sBAAsB;AACtB,qBAAqB;AACrB,OAAO;AACP,mBAAmB;AACnB,oBAAoB;AACpB,+BAA+B;AAC/B,wBAAwB;AACxB,uBAAuB;AACvB,UAAU;AACV,2BAA2B;AAC3B,uEAAuE;AACvE,gBAAgB;AAChB,mEAAmE;AACnE,0DAA0D;AAC1D,eAAe;AACf,iBAAiB;AACjB,sEAAsE;AACtE,WAAW;AACX,KAAK;AAEL,0BAA0B;AAC1B,YAAY;AACZ,cAAc;AACd,WAAW;AACX,OAAO;AACP,iBAAiB;AACjB,mBAAmB;AACnB,oBAAoB;AACpB,UAAU;AACV,aAAa;AACb,iBAAiB;AACjB,6BAA6B;AAC7B,sBAAsB;AACtB,iCAAiC;AACjC,mCAAmC;AACnC,QAAQ;AACR,wCAAwC;AACxC,oBAAoB;AACpB,uCAAuC;AACvC,6CAA6C;AAC7C,oDAAoD;AACpD,oCAAoC;AACpC,2IAA2I;AAC3I,aAAa;AACb,cAAc;AACd,iBAAiB;AACjB,uCAAuC;AACvC,+BAA+B;AAC/B,6CAA6C;AAC7C,+HAA+H;AAC/H,aAAa;AACb,WAAW;AACX,mBAAmB;AACnB,OAAO;AACP,KAAK;AAEL,oDAAoD;AAEpD,uCAAuC;AACvC,mBAAmB;AACnB,mBAAmB;AACnB,IAAI;AAEJ,sCAAsC;AACtC,mBAAmB;AACnB,yBAAyB;AACzB,8DAA8D;AAC9D,+DAA+D;AAC/D,0CAA0C;AAC1C,2BAA2B;AAC3B,2BAA2B;AAC3B,IAAI;AAEJ,gCAAgC;AAChC,WAAW;AACX,iBAAiB;AACjB,aAAa;AACb,oBAAoB;AACpB,cAAc;AACd,cAAc;AACd,wBAAwB;AACxB,4BAA4B;AAC5B,iDAAiD;AACjD,sDAAsD;AAEtD,+BAA+B;AAC/B,4CAA4C;AAC5C,kBAAkB;AAClB,yEAAyE;AACzE,+CAA+C;AAC/C,OAAO;AAEP,0DAA0D;AAC1D,oDAAoD;AACpD,qDAAqD;AACrD,QAAQ;AACR,yBAAyB;AACzB,wBAAwB;AACxB,OAAO;AAEP,8CAA8C;AAC9C,iEAAiE;AACjE,OAAO;AAEP,0EAA0E;AAC1E,mEAAmE;AACnE,4BAA4B;AAC5B,6CAA6C;AAC7C,mDAAmD;AACnD,oDAAoD;AACpD,6BAA6B;AAC7B,4BAA4B;AAC5B,UAAU;AACV,QAAQ;AACR,OAAO;AAEP,oCAAoC;AACpC,iDAAiD;AACjD,+EAA+E;AAC/E,OAAO;AAEP,aAAa;AACb,iCAAiC;AACjC,iEAAiE;AACjE,qDAAqD;AACrD,yBAAyB;AACzB,oBAAoB;AACpB,4BAA4B;AAC5B,iCAAiC;AACjC,gGAAgG;AAChG,cAAc;AACd,2CAA2C;AAC3C,sBAAsB;AACtB,sBAAsB;AACtB,aAAa;AACb,eAAe;AAEf,+BAA+B;AAC/B,wCAAwC;AACxC,sDAAsD;AACtD,oDAAoD;AACpD,oBAAoB;AACpB,4BAA4B;AAC5B,2GAA2G;AAC3G,gBAAgB;AAChB,yCAAyC;AACzC,wBAAwB;AACxB,gCAAgC;AAChC,sDAAsD;AACtD,qEAAqE;AACrE,kBAAkB;AAClB,oBAAoB;AACpB,0BAA0B;AAC1B,sBAAsB;AACtB,gBAAgB;AAChB,iBAAiB;AACjB,WAAW;AAEX,6BAA6B;AAC7B,mCAAmC;AACnC,iBAAiB;AACjB,wBAAwB;AACxB,sCAAsC;AACtC,+BAA+B;AAC/B,+BAA+B;AAC/B,6CAA6C;AAC7C,+BAA+B;AAC/B,eAAe;AACf,4CAA4C;AAC5C,sCAAsC;AACtC,+HAA+H;AAC/H,aAAa;AAEb,2BAA2B;AAC3B,qBAAqB;AACrB,2CAA2C;AAC3C,uDAAuD;AACvD,wIAAwI;AACxI,0DAA0D;AAC1D,uBAAuB;AACvB,gCAAgC;AAChC,yDAAyD;AACzD,kFAAkF;AAClF,oBAAoB;AACpB,mCAAmC;AACnC,yBAAyB;AACzB,oBAAoB;AACpB,uDAAuD;AACvD,uBAAuB;AACvB,mCAAmC;AACnC,qCAAqC;AACrC,kDAAkD;AAClD,kDAAkD;AAClD,yBAAyB;AACzB,sBAAsB;AACtB,2HAA2H;AAC3H,oBAAoB;AACpB,wDAAwD;AACxD,yBAAyB;AACzB,mBAAmB;AACnB,qBAAqB;AACrB,eAAe;AACf,eAAe;AAEf,uCAAuC;AACvC,qBAAqB;AACrB,iFAAiF;AACjF,WAAW;AACX,aAAa;AACb,OAAO;AACP,KAAK;AAEL,2EAA2E;AAC3E,oDAAoD;AACpD,gCAAgC;AAChC,gEAAgE;AAChE,oDAAoD;AACpD,oEAAoE;AACpE,0BAA0B;AAC1B,WAAW;AACX,6DAA6D;AAC7D,+EAA+E;AAE/E,YAAY;AACZ,eAAe;AACf,gBAAgB;AAChB,oBAAoB;AACpB,aAAa;AACb,gBAAgB;AAChB,6BAA6B;AAC7B,4BAA4B;AAC5B,uBAAuB;AACvB,sBAAsB;AACtB,+BAA+B;AAC/B,wBAAwB;AACxB,kBAAkB;AAClB,qBAAqB;AACrB,kBAAkB;AAClB,qBAAqB;AACrB,2BAA2B;AAC3B,uBAAuB;AACvB,qBAAqB;AACrB,WAAW;AACX,SAAS;AACT,QAAQ;AAER,iDAAiD;AAEjD,qDAAqD;AACrD,sBAAsB;AACtB,4CAA4C;AAC5C,cAAc;AACd,wDAAwD;AACxD,mDAAmD;AACnD,4EAA4E;AAC5E,sEAAsE;AACtE,+BAA+B;AAC/B,8BAA8B;AAC9B,iBAAiB;AACjB,mDAAmD;AACnD,YAAY;AACZ,0BAA0B;AAC1B,+DAA+D;AAC/D,UAAU;AACV,SAAS;AAET,yBAAyB;AACzB,YAAY;AAEZ,iDAAiD;AACjD,6BAA6B;AAE7B,YAAY;AACZ,yDAAyD;AAEzD,qCAAqC;AACrC,iDAAiD;AACjD,kDAAkD;AAClD,iEAAiE;AACjE,gBAAgB;AAEhB,8EAA8E;AAE9E,4BAA4B;AAC5B,4CAA4C;AAC5C,gEAAgE;AAChE,iBAAiB;AACjB,0DAA0D;AAC1D,sEAAsE;AAEtE,yEAAyE;AACzE,6BAA6B;AAC7B,6CAA6C;AAC7C,kEAAkE;AAClE,UAAU;AACV,wBAAwB;AACxB,mDAAmD;AACnD,qDAAqD;AACrD,kBAAkB;AAClB,gCAAgC;AAChC,QAAQ;AACR,OAAO;AAEP,sCAAsC;AACtC,iDAAiD;AACjD,WAAW;AACX,4CAA4C;AAC5C,kBAAkB;AAClB,gDAAgD;AAChD,2BAA2B;AAC3B,2BAA2B;AAC3B,2BAA2B;AAC3B,YAAY;AAEZ,yBAAyB;AACzB,+BAA+B;AAC/B,wBAAwB;AACxB,uBAAuB;AACvB,uBAAuB;AACvB,wBAAwB;AACxB,uBAAuB;AACvB,2BAA2B;AAC3B,WAAW;AACX,kDAAkD;AAElD,iDAAiD;AACjD,uBAAuB;AACvB,wEAAwE;AACxE,aAAa;AACb,kBAAkB;AAClB,UAAU;AAEV,mCAAmC;AACnC,uBAAuB;AACvB,kEAAkE;AAClE,gBAAgB;AAChB,yCAAyC;AACzC,aAAa;AACb,kBAAkB;AAClB,UAAU;AAEV,+BAA+B;AAC/B,wCAAwC;AACxC,kBAAkB;AAClB,UAAU;AAEV,kCAAkC;AAClC,2CAA2C;AAC3C,sDAAsD;AACtD,sEAAsE;AACtE,eAAe;AACf,kCAAkC;AAClC,sCAAsC;AACtC,QAAQ;AACR,OAAO;AAEP,aAAa;AACb,oDAAoD;AACpD,iDAAiD;AACjD,yEAAyE;AACzE,sCAAsC;AACtC,uEAAuE;AACvE,uEAAuE;AACvE,0DAA0D;AAC1D,gEAAgE;AAChE,2DAA2D;AAC3D,yCAAyC;AACzC,uBAAuB;AACvB,0DAA0D;AAC1D,0BAA0B;AAC1B,oEAAoE;AACpE,kCAAkC;AAClC,6FAA6F;AAC7F,oBAAoB;AACpB,4BAA4B;AAC5B,4BAA4B;AAC5B,0BAA0B;AAC1B,kCAAkC;AAClC,4CAA4C;AAC5C,uIAAuI;AACvI,oBAAoB;AACpB,mFAAmF;AACnF,mEAAmE;AACnE,4BAA4B;AAC5B,uBAAuB;AACvB,qBAAqB;AAErB,sEAAsE;AACtE,gDAAgD;AAChD,8CAA8C;AAC9C,0BAA0B;AAC1B,mFAAmF;AAEnF,+DAA+D;AAC/D,sCAAsC;AACtC,4CAA4C;AAC5C,0CAA0C;AAC1C,8CAA8C;AAC9C,+DAA+D;AAC/D,2BAA2B;AAC3B,0BAA0B;AAE1B,qCAAqC;AACrC,8CAA8C;AAC9C,6DAA6D;AAC7D,gCAAgC;AAChC,wBAAwB;AACxB,oDAAoD;AACpD,oEAAoE;AACpE,mCAAmC;AACnC,2DAA2D;AAC3D,8CAA8C;AAC9C,kDAAkD;AAClD,+BAA+B;AAC/B,4EAA4E;AAC5E,iCAAiC;AACjC,oEAAoE;AACpE,mCAAmC;AACnC,4DAA4D;AAC5D,yCAAyC;AACzC,2EAA2E;AAC3E,sDAAsD;AACtD,sKAAsK;AACtK,+BAA+B;AAC/B,oCAAoC;AACpC,4CAA4C;AAC5C,sDAAsD;AACtD,mJAAmJ;AACnJ,8BAA8B;AAC9B,0DAA0D;AAC1D,oCAAoC;AACpC,sCAAsC;AACtC,iCAAiC;AACjC,+BAA+B;AAC/B,uCAAuC;AAEvC,0DAA0D;AAC1D,8EAA8E;AAC9E,8BAA8B;AAC9B,uCAAuC;AACvC,+CAA+C;AAC/C,mEAAmE;AACnE,0DAA0D;AAC1D,wDAAwD;AACxD,oDAAoD;AACpD,qDAAqD;AACrD,8DAA8D;AAC9D,+BAA+B;AAC/B,+CAA+C;AAC/C,6BAA6B;AAC7B,+BAA+B;AAE/B,8BAA8B;AAC9B,uCAAuC;AACvC,yCAAyC;AACzC,6DAA6D;AAC7D,8FAA8F;AAC9F,kDAAkD;AAClD,oDAAoD;AACpD,+CAA+C;AAC/C,wDAAwD;AACxD,+BAA+B;AAC/B,+CAA+C;AAC/C,6BAA6B;AAC7B,+BAA+B;AAC/B,6BAA6B;AAC7B,2BAA2B;AAE3B,0BAA0B;AAC1B,mFAAmF;AACnF,iCAAiC;AACjC,wCAAwC;AACxC,wDAAwD;AACxD,iCAAiC;AACjC,wBAAwB;AACxB,oCAAoC;AACpC,yCAAyC;AACzC,4CAA4C;AAC5C,mDAAmD;AACnD,2CAA2C;AAC3C,0DAA0D;AAC1D,wDAAwD;AACxD,oFAAoF;AACpF,+BAA+B;AAC/B,6BAA6B;AAC7B,2BAA2B;AAC3B,mCAAmC;AACnC,2BAA2B;AAC3B,yBAAyB;AACzB,uBAAuB;AAEvB,4CAA4C;AAC5C,+DAA+D;AAC/D,uBAAuB;AACvB,qBAAqB;AACrB,mBAAmB;AAEnB,gCAAgC;AAChC,uEAAuE;AACvE,kEAAkE;AAClE,0CAA0C;AAC1C,oDAAoD;AACpD,gCAAgC;AAChC,sCAAsC;AACtC,oCAAoC;AACpC,wCAAwC;AACxC,yDAAyD;AACzD,qBAAqB;AACrB,oBAAoB;AACpB,qBAAqB;AACrB,mBAAmB;AAEnB,mCAAmC;AACnC,uEAAuE;AACvE,qEAAqE;AACrE,0CAA0C;AAC1C,uDAAuD;AACvD,gCAAgC;AAChC,sCAAsC;AACtC,oCAAoC;AACpC,wCAAwC;AACxC,kEAAkE;AAClE,qBAAqB;AACrB,oBAAoB;AAEpB,4CAA4C;AAC5C,yBAAyB;AACzB,0CAA0C;AAC1C,gFAAgF;AAChF,oBAAoB;AACpB,iCAAiC;AACjC,2BAA2B;AAC3B,4DAA4D;AAC5D,4BAA4B;AAC5B,oCAAoC;AACpC,wCAAwC;AACxC,sHAAsH;AACtH,qCAAqC;AACrC,wEAAwE;AACxE,wBAAwB;AACxB,sBAAsB;AACtB,8CAA8C;AAC9C,gDAAgD;AAChD,8BAA8B;AAC9B,2BAA2B;AAC3B,kCAAkC;AAClC,uCAAuC;AACvC,uCAAuC;AACvC,yDAAyD;AACzD,yCAAyC;AACzC,uBAAuB;AACvB,yBAAyB;AACzB,wCAAwC;AACxC,2CAA2C;AAC3C,oEAAoE;AACpE,2DAA2D;AAC3D,uEAAuE;AACvE,2BAA2B;AAC3B,2BAA2B;AAC3B,mEAAmE;AACnE,sCAAsC;AACtC,uGAAuG;AACvG,yBAAyB;AACzB,2BAA2B;AAC3B,qBAAqB;AACrB,uBAAuB;AACvB,qBAAqB;AACrB,mBAAmB;AACnB,kBAAkB;AAClB,eAAe;AACf,aAAa;AACb,OAAO;AACP,IAAI;AAEJ,SAAS;;;;;;AAIT;AAIA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAfA;;;;;;;;;;;;AAiBA,+BAA+B;AAC/B,MAAM,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1B,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,MAAM,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACzB,kBAAkB;IAClB,cAAc;IACd,gCAAgC;IAChC,gBAAgB;IAChB,wBAAwB;IACxB,2CAA2C;IAC3C,SAAS,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,QAAQ,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC5B,YAAY,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IAC1C,MAAM,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,OAAO,CAAC,EAAE;IACpC,WAAW,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,iBAAiB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACnC,cAAc,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAChC,QAAQ,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACf,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B;AACF;AAIA,4CAA4C;AAC5C,MAAM,eAAe;IACnB,UAAU;QACR;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;YACb,SAAS;YACT,UAAU;QACZ;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;YACb,SAAS;YACT,UAAU;QACZ;KACD;IACD,KAAK;QACH;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;YACb,SAAS;YACT,UAAU;QACZ;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;YACb,MAAM;YACN,SAAS;YACT,UAAU;QACZ;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;YACb,SAAS;YACT,UAAU;QACZ;KACD;IACD,QAAQ,EAAE;AACZ;AAEA,MAAM,WAAW,CAAC,EAAE,SAAS,EAA0B,iBACrD,8OAAC,kMAAA,CAAA,OAAI;QAAC,WAAW,CAAC,sBAAsB,EAAE,WAAW;;;;;;AAGvD,MAAM,YAAY,CAAC,EACjB,KAAK,EACL,KAAK,EACL,QAAQ,EACR,WAAW,KAAK,EAChB,UAAU,KAAK,EAOhB,iBACC,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAM,WAAU;;oBACd;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;oBAChD,yBAAW,8OAAC;wBAAS,WAAU;;;;;;;;;;;;YAEjC;YACA,uBAAS,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIxD,MAAM,eAAe,CAAC,EACpB,MAAM,EACN,QAAQ,EACR,KAAK,EAKN;IACC,qBACE,8OAAC;QACC,OAAO,OAAO,KAAK;QACnB,OAAO;QACP,SAAS,OAAO,OAAO;QACvB,UAAU,OAAO,QAAQ;kBAExB,OAAO,IAAI,KAAK,2BACf,8OAAC;YACE,GAAG,SAAS,OAAO,IAAI,CAAC;YACzB,aAAa,OAAO,WAAW;YAC/B,uCAAuC;YACvC,MAAM,OAAO,IAAI,IAAI;YACrB,WAAU;;;;;iCAGZ,8OAAC;YACE,GAAG,SAAS,OAAO,IAAI,CAAC;YACzB,MAAM,OAAO,IAAI;YACjB,aAAa,OAAO,WAAW;YAC/B,WAAU;;;;;;;;;;;AAKpB;AAkBO,MAAM,cAAc,CAAC,EAC1B,KAAK,EACL,WAAW,EACX,OAAO,EACP,cAAc,EACd,QAAQ,EACR,QAAQ,EACR,cAAc,IAAI,EACD;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,0BAA0B;IAC1B,MAAM,kBAAkB,QAAQ,MAAM,CACpC,CAAC,SACC,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,CAAC,eAAe,QAAQ,CAAC,OAAO,KAAK;IAGzC,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,eAAe,QAAQ,CAAC,OAAO,KAAK,GAAG;YAC1C,SAAS;mBAAI;gBAAgB,OAAO,KAAK;aAAC;QAC5C;QACA,cAAc;QACd,UAAU;IACZ;IAEA,MAAM,eAAe,CAAC;QACpB,SAAS,eAAe,MAAM,CAAC,CAAC,OAAS,SAAS;IACpD;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,WAAW,IAAI,MAAM,aAAa;YACzD,EAAE,cAAc;YAChB,MAAM,YAAY,WAAW,IAAI;YACjC,IAAI,CAAC,eAAe,QAAQ,CAAC,YAAY;gBACvC,SAAS;uBAAI;oBAAgB;iBAAU;gBACvC,cAAc;gBACd,UAAU;YACZ;QACF;IACF;IAEA,+BAA+B;IAC/B,MAAM,kBAAkB,CAAC;QACvB,OAAO,QAAQ,IAAI,CAAC,CAAC,SAAW,OAAO,KAAK,KAAK,QAAQ,SAAS;IACpE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAiB;;;;;;oBAC9B,0BACC,8OAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;YAOjC,eAAe,MAAM,GAAG,mBACvB,8OAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC;wBAEC,WAAU;;4BAET,gBAAgB;0CACjB,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,aAAa;gCAC5B,WAAU;0CACX;;;;;;;uBARI;;;;;;;;;;0BAiBb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,MAAK;wBACL,aAAa;wBACb,OAAO;wBACP,UAAU,CAAC;4BACT,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC5B,UAAU;wBACZ;wBACA,SAAS,IAAM,UAAU;wBACzB,WAAW;wBACX,WAAU;;;;;;oBAIX,UACC,CAAC,gBAAgB,MAAM,GAAG,KACvB,eAAe,WAAW,IAAI,EAAG,mBAClC,8OAAC;wBAAI,WAAU;;4BACZ,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8OAAC;oCAEC,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CAET,OAAO,KAAK;mCAJR;;;;;4BAOR,eAAe,WAAW,IAAI,oBAC7B,8OAAC;gCACC,SAAS,IACP,aAAa;wCACX,OAAO,WAAW,IAAI;wCACtB,OAAO,WAAW,IAAI;oCACxB;gCAEF,WAAU;;oCACX;oCACY,WAAW,IAAI;oCAAG;;;;;;;;;;;;;;;;;;;YAQxC,wBACC,8OAAC;gBAAI,WAAU;gBAAoB,SAAS,IAAM,UAAU;;;;;;;;;;;;AAIpE;AAGe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE3D,EAAE;IACJ,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAEpE,MAAM,EACJ,OAAO,EACP,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAY;QACpB,eAAe;YACb,QAAQ;YACR,sBAAsB;YACtB,YAAY,EAAE;YACd,MAAM,EAAE;YACR,SAAS;YACT,QAAQ;gBACN,OAAO;YACT;QACF;IACF;IAEA,4CAA4C;IAE5C,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,wKAAA,CAAA,sBAAmB,AAAD;gBACzC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,MAAM,iBAAiB,AAAC,SAAS,IAAI,CAAS,UAAU,IAAI,EAAE;oBAC9D,MAAM,gBAAgB,eAAe,GAAG,CAAC,CAAC,MAAa,CAAC;4BACtD,OAAO,IAAI,IAAI;4BACf,OAAO,IAAI,GAAG;wBAChB,CAAC;oBACD,uBAAuB;gBACzB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;YAC/C;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAChB,IAAI;YACF,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,4BAA4B;YAC5B,MAAM,gBAAgB,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD,EAAE;YAElC,MAAM,oBAAoB,kBACpB,MAAM,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD,EAAE,mBACxB;YAEJ,MAAM,MAAM,MAAM,CAAA,GAAA,wKAAA,CAAA,mBAAgB,AAAD,EAAE,eAAe;YAElD,IAAI,CAAC,IAAI,OAAO,EAAE;gBAChB,QAAQ,KAAK,CAAC,cAAc;gBAC5B,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,WAAW;YAC9B,OAAO;gBACL,QAAQ,GAAG,CAAC,8BAA8B;gBAC1C,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,KAAK,WAAW;gBAC9B,8DAA8D;gBAC9D,WAAW;oBACT,OAAO,IAAI,CAAC;gBACd,GAAG,OAAO,6CAA6C;YACzD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,0BAA0B,CAC9B;QAEA,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,QAAQ,GAAG,CAAC,0BAA0B;gBACpC,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;YACjB;YAEA,gBAAgB;YAChB,MAAM,eAAe;gBACnB;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,MAAM,UAAU,KAAK,OAAO,MAAM,OAAO;YAEzC,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;gBACrC,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,CAAC,mBAAmB,EAAE,KAAK,IAAI,CAAC,0BAA0B,CAAC;gBAE7D;YACF;YAEA,IAAI,KAAK,IAAI,GAAG,SAAS;gBACvB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,CAAC,gBAAgB,EAAE,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAClD,GACA,yBAAyB,CAAC;gBAE9B;YACF;YAEA,IAAI,KAAK,IAAI,KAAK,GAAG;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,mBAAmB;YAEnB,kCAAkC;YAClC,MAAM,aAAa,IAAI,eAAe,CAAC;YACvC,SAAS,gBAAgB,aAAa,uBAAuB;QAC/D,OAAO;YACL,mBAAmB;YACnB,SAAS,gBAAgB;QAC3B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAK,UAAU,aAAa;gBAAW,WAAU;;kCAEhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,OAAO,IAAI,CAAC;gDAC3B,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;;oDAET,8BAAgB,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDACnC,eAAe,gBAAgB;;;;;;;;;;;;;;;;;;;0CAKtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA6B;;;;;;wDAC1C,aAAa,QAAQ,CAAC,GAAG,CAAC,CAAC,uBAC1B,8OAAC;gEAEC,QAAQ;gEACR,UAAU;gEACV,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,EAAE;+DAHvB,OAAO,IAAI;;;;;sEAyCpB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EACC,cAAA,8OAAC;wEACC,OAAM;wEACN,aAAY;wEACZ,SAAS;wEACT,gBAAgB;wEAChB,UAAU,CAAC;4EACT,cAAc;4EACd,SAAS,cAAc;wEACzB;wEACA,aAAa;;;;;;;;;;;8EAGjB,8OAAC;8EACC,cAAA,8OAAC;wEACC,OAAM;wEACN,aAAY;wEACZ,SAAS,EAAE;wEACX,gBAAgB;wEAChB,UAAU,CAAC;4EACT,QAAQ;4EACR,SAAS,QAAQ;wEACnB;wEACA,aAAa;;;;;;;;;;;;;;;;;;;;;;;8DAMrB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,8OAAC;4DACC,OAAM;4DACN,OAAO,OAAO,OAAO,EAAE;4DACvB,QAAQ;sEAER,cAAA,8OAAC,8JAAA,CAAA,aAAU;gEACT,MAAK;gEACL,SAAS;gEACT,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gKAAA,CAAA,UAAa;wEACZ,SAAS,MAAM,KAAK,IAAI;wEACxB,UAAU,MAAM,QAAQ;wEACxB,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAS1B,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;kCAOnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,uBACrB,8OAAC;wCAEC,QAAQ;wCACR,UAAU;wCACV,OAAO,MAAM,CAAC,OAAO,IAAI,CAAC,EAAE;uCAHvB,OAAO,IAAI;;;;;;;;;;;;;;;;kCAUxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAQ;4CACR,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS,IACP,SAAS,cAAc,CAAC,gBAAgB;;sEAG1C,8OAAC,gNAAA,CAAA,YAAS;4DAAC,MAAM;;;;;;sEACjB,8OAAC;sEAAK;;;;;;;;;;;;8DAER,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,QAAO;oDACP,UAAU;oDACV,WAAU;;;;;;;;;;;;wCAIb,iCACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;;wDAAgC;wDAChC,gBAAgB,IAAI;wDAAC;wDAC/B,CAAC,gBAAgB,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;wDAAG;;;;;;;8DAE5C,8OAAC;oDACC,KACE,IAAI,eAAe,CAAC,oBACpB;oDAEF,KAAI;oDACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhC", "debugId": null}}]}