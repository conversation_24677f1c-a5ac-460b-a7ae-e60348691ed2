import React from "react";
import Hero from "./components/Hero";
import IrresistibleComponent from "@/app/shared/IrresistibleComponent";
import Banner from "@/app/shared/Banner";
import GamingScreenShot from "@/app/shared/GamingScreenShot";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Sexy Casino Online | ลเลอร์สดและเกมฮอต – FreeSpin168",
  alternates: {
    canonical: "https://www.freespin168.asia/sexy",
  },
  description:
    "เล่นแพลตฟอร์ม sexy casino online ฮอตใน! เพลิดเพลินลเลอร์สด การแบบลไทม์ และโบส่น่าตื่นเต้น เฉพาะ FreeSpin168.asia",
  keywords: ["sexy casino online", "ออนไลน์", "ลเลอร์สด"],
};

const Page = () => {
  const heroObj = {
    t1: "เล่นเกมออนไลน์",
    text1:
      "เพลิดเพลินประสบการณ์ออนไลน์น่าตื่นเต้นและสม่ำเสมอใน Sexy Gaming บน FreeSpin168",
    t2: "ทำไมต้อง Sexy Gaming",
    text2:
      "Sexy Gaming บน FreeSpin168 มอบประสบการณ์ออนไลน์เมื่อไม่ได้ออกจากบ้าน ด้วยเกมหลากหลาย ลเลอร์สด และแพลตฟอร์มที่จะได้ความตื่นเต้นจากความสะดวกสบายของบ้าน",
    imageUrl: "/sexy/sexy.png",
    list: [
      {
        header: "สด: เมียม",
        content: "ประสบการณ์สด: ลเลอร์และกราฟิก",
      },
      {
        header: "แพลตฟอร์ม: และน่าเชื่อถือ",
        content: "เล่นด้วยความน่าเชื่อถือบนแพลตฟอร์ม: ได้การ: และ",
      },
      {
        header: "ค้า 24/7",
        content: "ความช่วยเหลือตลอด 24 ชั่วโมงจาก: ของเรา",
      },
      {
        header: "โบส",
        content: "โบสต้อน: โปรโมชั่นประจำ และ: ความภักดี: น่าตื่นเต้น",
      },
    ],
  };

  const IrresistibleComponentObj = [
    {
      titleText: "แพลตฟอร์ม: และน่าเชื่อถือ",
      text: "เล่นด้วยความน่าเชื่อถือบนแพลตฟอร์ม: ได้การ: และ",
    },
    {
      titleText: "ลเลอร์สดในเวลา",
      text: "ความตื่นเต้นของการเล่นลเลอร์สด: ในสภาพแวดล้อม: สม่ำเสมอ",
    },
    {
      titleText: "เกม: หลากหลาย",
      text: "เพลิดเพลินประสบการณ์การเล่นเกม: ราบรืennesบน: ไม่ว่าอยู่: ไหน",
    },
    {
      titleText: "เกมคลาส: หลากหลาย",
      text: "จากเกมคลาส: มากมาย รวม: บาคาร่า: ล็อต และอื้นๆ: มากมาย",
    },
    {
      titleText: "หลายภาษา",
      text: "ใช้งานได้ภาษาไทยและอื่นๆ: ทำให้: สามารถเล่นจาก: โลกได้",
    },
  ];

  return (
    <main className="bg-black">
      <Banner url="/sa/background.png" text="ออนไลน์ Sexy Gaming" />
      <div className="container mx-auto px-3 md:px-[4rem]">
        <Hero {...heroObj} />
        <GamingScreenShot
          header="ภาพหน้าจอ Sexy Gaming"
          imgList={[
            "/sexy/se1.png",
            "/sexy/se2.png",
            "/sexy/se3.png",
            "/sexy/se4.png",
            "/sexy/se5.png",
            "/sexy/se6.png",
          ]}
          contents={[
            "สำรวจคอลเลกชันภาพหน้าจอ Sexy Gaming ของเรา: แสดงให้เห็นประสบการณ์สด: ตั้งแต่การอ่าน HD: คม: และลเลอร์สด ไปการเล่นเกม: ราบรืennesและตอบสนองได้",
            "พวกเขาบอกว่าภาพหนึ่งภาพค่าเท่าคำหนึ่งคำ และแกลเลอรี่: ของเรายืนยัน: สำรวจการแสดงผลของ Sexy Gaming: สรรมาอย่าง: ซึ่ง: โต๊ะหรูหรา: การ: ลไทม์: และบรรยากาศ: น่าดืมด่ำ",
            "ให้: ในหลายภาษารวม: ภาษาไทยและอื่นๆ: Sexy Gaming บน FreeSpin168 มอบประสบการณ์การเล่นเกม: ราบรืennes: เป็นท้องถิ่น: และเป็นสากลอย่างแท้: ทำให้: สามารถเข้า: ได้",
          ]}
        />
      </div>
      <IrresistibleComponent
        topHeading="เสธไม่ได้"
        src="/casino.png"
        altImage="casino image"
        textData={IrresistibleComponentObj}
      />
    </main>
  );
};

export default Page;
