import React from "react";
import HeroSection from "./components/HeroSection";
import WeDo from "./components/WeDo";
import AboutUs from "./components/AboutUs";
import OnlineCasino from "./components/OnlineCasino";
import Destination from "./components/Destination";
import Guide from "./components/Guide";
import Trending from "./components/Trending";
import { TextHoverEffect } from "@/app/shared/TextHoverEffect";
import FreeSpinText from "@/app/shared/FreeSpinText";
import GameIconSection from "./components/GameIconSection";

import { Metadata } from "next";
import { generateMetadata as createMetadata } from "@/utils/metadata";

export async function generateMetadata({
  params,
  searchParams,
}: {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}): Promise<Metadata> {
  // Always use Thai locale
  const urlLocale = "th";

  return createMetadata({
    title:
      "คาสิโนออนไลน์ในประเทศไทย | ฟรีสปินที่เชื่อถือได้และรีวิวเกม – FreeSpin168",
    description:
      "สำรวจคาสิโนออนไลน์ที่ดีที่สุดในประเทศไทยกับ FreeSpin168.asia เพลิดเพลินกับการเล่นเกมที่ปลอดภัย แพลตฟอร์มที่ได้รับการตรวจสอบ และอัปเดตฟรีสปินรายวัน ทั้งหมดในที่เดียว",
    keywords: [
      "คาสิโนออนไลน์ในประเทศไทย",
      "ฟรีสปิน",
      "เกมคาสิโน",
      "FreeSpin168",
    ],
    path: "/",
    locale: urlLocale,
    type: "website",
  });
}

const HomePage = () => {
  return (
    <main className="bg-black">
      <HeroSection />
      {/* <WeDo /> */}
      <TextHoverEffect
        text="สำรวจมิติใหม่ของเกมคาสิโน ที่ถูกสร้างสรรค์อย่างยอดเยี่ยม ด้วยความคิดสร้างสรรค์ และการออกแบบที่ดื่มด่ำ"
        spotlightRadiusPx={200}
      />

      <GameIconSection />
      <FreeSpinText />
      <AboutUs />
      <OnlineCasino />
      <Destination />
      <Guide />
      <Trending />
    </main>
  );
};

export default HomePage;
