interface ArticleSection {
  type: "date" | "title" | "heading" | "paragraph" | "list";
  content: string | string[];
  className?: string;
}

const articleData: ArticleSection[] = [
  {
    type: "date",
    content: "06/02/2025",
  },
  {
    type: "title",
    content:
      "Why 918Kiss Thailand Stands Out as the Leading Choice for Online Slot Lovers",
  },
  {
    type: "heading",
    content:
      "Explore the Thrill of Online Slots with 918kiss Thailand: A Premier Gaming Destination",
  },
  {
    type: "paragraph",
    content:
      "In the ever-growing world of online gaming, 918kiss Thailand stands out as a top-tier platform, offering an immersive and secure environment for slot enthusiasts. Whether you're a seasoned player or new to the world of online casinos, 918kiss offers a dynamic experience that's easy to navigate, richly entertaining, and backed by robust security protocols.",
  },
  {
    type: "heading",
    content: "Why 918kiss Thailand Is a Fan Favorite",
  },
  {
    type: "paragraph",
    content:
      "What truly sets 918kiss Thailand apart is its diverse and expansive selection of slot games. Unlike many platforms that stick to a limited catalog, 918kiss constantly updates its game library with fresh themes, high-quality visuals, and engaging storylines. From classic fruit machine-style slots to modern video slots inspired by movies and pop culture, there's something for every taste.",
  },
  {
    type: "paragraph",
    content:
      "But the appeal doesn't stop at variety. The platform is designed with user experience in mind. Simple menus, intuitive navigation, and fast-loading interfaces make it easy for players to jump into the action without delays or confusion. It's a smooth and enjoyable process from login to payout.",
  },
  {
    type: "heading",
    content: "Game-Changing Features That Make It Stand Out",
  },
  {
    type: "paragraph",
    content:
      "Here are the key elements that make 918kiss Thailand a top choice for online slot players:",
  },
  {
    type: "list",
    content: [
      "Massive Game Library: With hundreds of slot games to choose from, players are never short of options. Whether you're chasing big jackpots or just looking for some casual fun, there's a game that fits your style.",
      "Secure Transactions: Safety is a priority. 918kiss Thailand uses advanced encryption technology to keep your data and payments secure, so you can focus on playing without worry.",
      "Fair Play Guarantee: Every game is powered by trusted Random Number Generator (RNG) systems, ensuring fair outcomes and building trust among players.",
      "24/7 Customer Support: Got a question or issue? The support team is available around the clock to assist with anything from game mechanics to account help, ensuring a smooth experience.",
    ],
  },
  {
    type: "heading",
    content: "Perfect for Beginners and Veterans Alike",
  },
  {
    type: "paragraph",
    content:
      "918kiss Thailand is built for everyone. New users will appreciate the user-friendly interface and helpful guides, while experienced gamers will enjoy the depth of features and game variations that keep gameplay fresh and exciting. Whether you're playing for fun or aiming for a big win, the platform delivers.",
  },
  {
    type: "heading",
    content: "Your Trusted Online Casino Hub",
  },
  {
    type: "paragraph",
    content:
      "In a competitive market flooded with online casino options, 918kiss Thailand distinguishes itself with reliability, rich content, and commitment to player satisfaction. With a constant stream of new releases, high-level security, and responsive service, it remains one of the most trusted names in the online gambling space.",
  },
];

export function ArticleContent() {
  const renderSection = (section: ArticleSection, index: number) => {
    const baseKey = `section-${index}`;

    switch (section.type) {
      case "date":
        return (
          <div key={baseKey} className="mb-6">
            <time className="text-gray-400 text-sm font-medium">
              {section.content as string}
            </time>
          </div>
        );

      case "title":
        return (
          <h3 className="text-3xl font-bold mb-4 text-white leading-tight">
            {section.content as string}
          </h3>
        );

      case "heading":
        return (
          <h2
            key={baseKey}
            className="text-2xl md:text-2xl font-bold mb-6 text-white mt-12 first:mt-0"
          >
            {section.content as string}
          </h2>
        );

      case "paragraph":
        return (
          <p
            key={baseKey}
            className="text-gray-300 leading-relaxed text-justify text-base mb-6"
          >
            {section.content as string}
          </p>
        );

      case "list":
        return (
          <ul key={baseKey} className="space-y-6 mb-8">
            {(section.content as string[]).map((item, itemIndex) => {
              const [title, ...descriptionParts] = item.split(": ");
              const description = descriptionParts.join(": ");

              return (
                <li
                  key={`${baseKey}-item-${itemIndex}`}
                  className="flex items-start gap-4"
                >
                  <div className="w-2 h-2 bg-orange-500 rounded-full mt-3 flex-shrink-0"></div>
                  <div>
                    <strong className="text-white font-semibold">
                      {title}:
                    </strong>
                    <span className="text-gray-300 ml-2">{description}</span>
                  </div>
                </li>
              );
            })}
          </ul>
        );

      default:
        return null;
    }
  };

  return (
    <article className="prose prose-invert prose-lg max-w-none">
      {articleData.map((section, index) => renderSection(section, index))}
    </article>
  );
}
