(()=>{var e={};e.id=7376,e.ids=[7376],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17530:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Web Studio Nepal\\\\FreeSpin168\\\\freespin\\\\src\\\\app\\\\dashboard\\\\blogs\\\\categories\\\\components\\\\CategoryList.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\dashboard\\blogs\\categories\\components\\CategoryList.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26083:(e,t,r)=>{"use strict";r.d(t,{default:()=>M});var a=r(60687),s=r(6475);let o=(0,s.createServerReference)("4028b030361181c413d3fc466747cf702ccff9b189",s.callServer,void 0,s.findSourceMapURL,"createBlogCategory"),i=(0,s.createServerReference)("4038792a88358da73fac1c13a273a76c89f53d3e2e",s.callServer,void 0,s.findSourceMapURL,"deleteBlogCategory");var n=r(29523),l=r(43210),c=r(26134),d=r(11860),u=r(4780);function p({...e}){return(0,a.jsx)(c.bL,{"data-slot":"dialog",...e})}function g({...e}){return(0,a.jsx)(c.l9,{"data-slot":"dialog-trigger",...e})}function f({...e}){return(0,a.jsx)(c.ZL,{"data-slot":"dialog-portal",...e})}function m({className:e,...t}){return(0,a.jsx)(c.hJ,{"data-slot":"dialog-overlay",className:(0,u.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function h({className:e,children:t,showCloseButton:r=!0,...s}){return(0,a.jsxs)(f,{"data-slot":"dialog-portal",children:[(0,a.jsx)(m,{}),(0,a.jsxs)(c.UC,{"data-slot":"dialog-content",className:(0,u.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...s,children:[t,r&&(0,a.jsxs)(c.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(d.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function b({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,u.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function y({className:e,...t}){return(0,a.jsx)(c.hE,{"data-slot":"dialog-title",className:(0,u.cn)("text-lg leading-none font-semibold",e),...t})}var x=r(89667),v=r(14163),w=l.forwardRef((e,t)=>(0,a.jsx)(v.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));function S({className:e,...t}){return(0,a.jsx)(w,{"data-slot":"label",className:(0,u.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}function A({className:e,...t}){return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,u.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}w.displayName="Label";var j=r(57335),N=r(96882),C=r(96474),O=r(99270),I=r(88233),k=r(27605),P=r(93853),$=r(9275);let T=$.z.object({name:$.z.string().min(1,"Category name is required"),image:$.z.string().url("Must be a valid URL").optional().or($.z.literal("")),description:$.z.string().min(1,"Category description is required")}),E=({label:e,error:t,children:r,required:s=!1,tooltip:o=!1,htmlFor:i})=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(S,{htmlFor:i,className:"text-sm font-medium text-gray-700",children:[e,s&&(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),o&&(0,a.jsx)(N.A,{className:"w-4 h-4 text-gray-400 cursor-help","aria-label":"Tooltip for additional information"})]}),r,t&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:t})]}),_=({trigger:e})=>{let[t,r]=(0,l.useState)(!1),[s,i]=(0,l.useState)(!1),{register:c,handleSubmit:d,formState:{errors:u,isSubmitting:f},reset:m}=(0,k.mN)({resolver:(0,j.u)(T),defaultValues:{name:"",description:""}}),v=async e=>{i(!0);try{let t=await o(e);t.success?(P.oR.success(t.message),r(!1),m()):P.oR.error(t.message)}catch(e){P.oR.error("Failed to create category")}finally{i(!1)}},w=e=>{r(e),e||m()};return(0,a.jsxs)(p,{open:t,onOpenChange:w,children:[(0,a.jsx)(g,{asChild:!0,children:e||(0,a.jsxs)(n.$,{className:"flex items-center gap-2 bg-black hover:bg-gray-800 transition-colors",onClick:()=>r(!0),children:[(0,a.jsx)(C.A,{className:"w-4 h-4"}),"Add New Category"]})}),(0,a.jsxs)(h,{className:"sm:max-w-md bg-white rounded-lg shadow-xl",children:[(0,a.jsx)(b,{children:(0,a.jsx)(y,{className:"text-xl font-semibold text-gray-900",children:"Add New Category"})}),(0,a.jsxs)("form",{onSubmit:d(v),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(E,{label:"Name",error:u.name?.message,tooltip:!0,htmlFor:"category-name",required:!0,children:(0,a.jsx)(x.p,{id:"category-name",placeholder:"Enter category name",className:"border-gray-200 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500",...c("name")})}),(0,a.jsx)(E,{label:"Description",error:u.description?.message,tooltip:!0,htmlFor:"category-description",required:!0,children:(0,a.jsx)(A,{id:"category-description",placeholder:"Enter category description",rows:4,className:"border-gray-200 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500",...c("description")})})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,a.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>w(!1),className:"border-gray-200 text-gray-700 hover:bg-gray-100",children:"Cancel"}),(0,a.jsx)(n.$,{type:"submit",disabled:f||s,className:"bg-black hover:bg-gray-800 transition-colors",children:f||s?(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsxs)("svg",{className:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8v8h8a8 8 0 01-8 8 8 8 0 01-8-8z"})]}),"Creating..."]}):"Create Category"})]})]})]})]})};function M({categories:e}){let[t,r]=(0,l.useState)(null),[s,o]=(0,l.useState)(""),c=async e=>{r(e);try{let t=await i(e);t.success?P.oR.success(t.message):P.oR.error(t.message)}catch(e){P.oR.error("Failed to delete category")}finally{r(null)}},d=e.filter(e=>""===s||e.name.toLowerCase().includes(s.toLowerCase())||e.slug&&e.slug.toLowerCase().includes(s.toLowerCase())||e.description.toLowerCase().includes(s.toLowerCase()));return(0,a.jsxs)("div",{className:"space-y-6 p-8 pt-12 max-w-4xl mx-auto border border-gray-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Category Management"}),(0,a.jsx)(_,{})]}),(0,a.jsxs)("div",{className:"relative w-80",children:[(0,a.jsx)(O.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)(x.p,{placeholder:"Search categories...",value:s,onChange:e=>o(e.target.value),className:"pl-10 bg-gray-50 border-gray-200 focus:bg-white"})]}),0===d.length?(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:s?"No categories found matching your search.":"No categories found."}):(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-4 text-gray-900",children:"Created Categories"}),(0,a.jsx)("div",{className:"grid gap-4",children:d.map(e=>(0,a.jsxs)("div",{className:"p-4 flex items-center justify-between border border-gray-200 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description})]}),(0,a.jsx)(n.$,{onClick:()=>c(e._id),variant:"outline",size:"sm",disabled:t===e._id,className:"h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50",children:t===e._id?(0,a.jsxs)("svg",{className:"animate-spin h-5 w-5 text-red-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8v8h8a8 8 0 01-8 8 8 8 0 01-8-8z"})]}):(0,a.jsx)(I.A,{className:"h-4 w-4"})})]},e._id))})]})]})}},27908:(e,t,r)=>{Promise.resolve().then(r.bind(r,17530))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36847:(e,t,r)=>{"use strict";r.d(t,{Kd:()=>d});var a=r(67288),s=r(27910);let o={endPoint:process.env.MINIO_ENDPOINT?.replace(/^https?:\/\//,"")||"localhost",port:process.env.MINIO_PORT?Number.parseInt(process.env.MINIO_PORT):9e3,useSSL:"true"===process.env.MINIO_USE_SSL,accessKey:process.env.MINIO_ACCESS_KEY||"3uiq5emitjasdfghyjui",secretKey:process.env.MINIO_SECRET_KEY||"TYo1ruP1PqbOx3fONapGwawKQGqCDQsdfadsdfgh"},i=process.env.MINIO_BUCKET_NAME||"spinfree",n=new a.Kj(o),l=async()=>{try{if(console.log("Checking MinIO connection..."),console.log("MinIO config:",{endPoint:o.endPoint,port:o.port,useSSL:o.useSSL,bucket:i}),await n.bucketExists(i))console.log(`Bucket '${i}' already exists`);else{await n.makeBucket(i,"us-east-1"),console.log(`Bucket '${i}' created successfully`);let e={Version:"2012-10-17",Statement:[{Effect:"Allow",Principal:{AWS:["*"]},Action:["s3:GetObject"],Resource:[`arn:aws:s3:::${i}/*`]}]};await n.setBucketPolicy(i,JSON.stringify(e)),console.log(`Bucket policy set for '${i}'`)}}catch(e){console.error("Error initializing MinIO bucket:",e)}},c=e=>{if(137===e[0]&&80===e[1]&&78===e[2]&&71===e[3])return"image/png";if(255===e[0]&&216===e[1]&&255===e[2])return"image/jpeg";let t=e.toString("ascii",0,100).toLowerCase();return t.includes("<svg")||t.includes("<?xml")?"image/svg+xml":null};class d{static async uploadFile(e,t,r,a="posts"){try{console.log("Starting file upload to MinIO...");try{await n.bucketExists(i),console.log("MinIO connection verified")}catch(e){throw console.error("MinIO connection failed:",e),Error("MinIO service is unavailable. Please try again later.")}await l();let o=r;if("application/octet-stream"===r){let t=c(e);if(!t)throw Error("Invalid or unsupported image format");o=t}let d=new Date,u="image/svg+xml"===o?"svg":"image/png"===o?"png":"jpg",p=`${Date.now()}-${t.replace(/\.[^/.]+$/,"")}.${u}`,g=[a,d.getFullYear().toString(),(d.getMonth()+1).toString().padStart(2,"0"),p].join("/");console.log("Uploading to path:",g);let f=new s.Readable;f.push(e),f.push(null),await n.putObject(i,g,f,e.length,{"Content-Type":o});let m="https://freespin168.asia",h=`${m}/api/uploads/${g}`;return console.log("File uploaded to MinIO successfully!"),console.log(`- MinIO path: ${g}`),console.log(`- Proxy URL: ${h}`),console.log(`- Base URL: ${m}`),h}catch(n){console.error("Error uploading file to MinIO:",n),console.error("MinIO config:",{endPoint:o.endPoint,port:o.port,useSSL:o.useSSL,bucket:i}),console.error("Upload details:",{fileName:t,contentType:r,folder:a,bufferSize:e.length});let s=`/uploads/${a}/${Date.now()}-${t}`;return console.log("Upload failed, using fallback URL:",s),s}}static async deleteFile(e){try{let t=new URL(e),r=t.pathname.substring(t.pathname.indexOf("/",1)+1);await n.removeObject(i,r),console.log(`File deleted from MinIO: ${r}`)}catch(e){throw console.error("Error deleting file from MinIO:",e),Error("Failed to delete file")}}static async listFiles(e="posts",t=100){try{let r=[],a=n.listObjects(i,`${e}/`,!0);return new Promise((e,s)=>{a.on("data",e=>{if(r.length<t){let t=o.useSSL?"https":"http",a=o.port!==(o.useSSL?443:80)?`:${o.port}`:"",s=`${t}://${o.endPoint}${a}/${i}/${e.name}`;r.push({name:e.name,size:e.size,lastModified:e.lastModified,url:s,fileName:e.name?.split("/").pop()||"",folder:e.name?.split("/").slice(0,-1).join("/")||""})}}),a.on("end",()=>e(r)),a.on("error",e=>s(e))})}catch(e){throw console.error("Error listing files:",e),Error("Failed to list files")}}static async getPresignedUrl(e,t=3600){try{return await n.presignedGetObject(i,e,t)}catch(e){throw console.error("Error generating presigned URL:",e),Error("Failed to generate presigned URL")}}static async getFileStream(e){try{let t=await n.getObject(i,e),r=[];return new Promise((e,a)=>{t.on("data",e=>r.push(e)),t.on("end",()=>e(Buffer.concat(r))),t.on("error",a)})}catch(e){throw console.error("Error getting file stream from MinIO:",e),Error("Failed to get file from MinIO")}}}l().catch(e=>{console.error("Critical error during MinIO initialization:",e)})},40060:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"001d2f242e1ebc0563880e1dfe8b6d738fc2076ffc":()=>a.Xp,"40396c047acd2e1f406bf08ca318567c133e740b3f":()=>a.$Y,"60aa6babc821942b6eceea76aa61c8a4f676069c5b":()=>a.jH,"60cdb4e58939c5ebe2ad11f732ca2e9f4afd0da297":()=>a.uF,"70058763efb0e19a94f7f5968947fb2774a4f5df9b":()=>a._K});var a=r(97405)},41204:e=>{"use strict";e.exports=require("string_decoder")},41412:(e,t,r)=>{Promise.resolve().then(r.bind(r,26083))},51828:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(37413),s=r(97405),o=r(34661),i=r.n(o),n=r(17530);let l=async()=>{let e=await (0,s.Xp)();console.log("resposne for category",e);let t=i()(e,"data.categories",[]).map(e=>({_id:e._id,name:e.name,slug:e.name.toLowerCase().replace(/\s+/g,"-"),description:e.description||""}));return(0,a.jsx)(n.default,{categories:t})}},55511:e=>{"use strict";e.exports=require("crypto")},55530:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(56037),s=r.n(a);let o=new a.Schema({title:{type:String,required:[!0,"Post title is required"],trim:!0,maxlength:[1e3,"Title cannot exceed 200 characters"]},slug:{type:String,trim:!0,lowercase:!0},canonicalUrl:{type:String,trim:!0,validate:{validator:e=>!e||/^https?:\/\/.+/.test(e),message:"Canonical URL must be a valid URL"}},existingUrl:{type:Boolean,default:!1},content:{type:String,required:[!0,"Post content is required"]},excerpt:{type:String,trim:!0,maxlength:[1e3,"Excerpt cannot exceed 300 characters"]},description:{type:String},author:{type:String,trim:!0},isBlog:{type:Boolean,default:!0},categories:[{type:s().Schema.Types.ObjectId,ref:"Category"}],tags:[{type:String,trim:!0,lowercase:!0}],metaTitle:{type:String,required:[!0,"Meta title is required"],maxlength:[1e3,"Meta title cannot exceed 60 characters"]},metaDescription:{type:String,required:[!0,"Meta description is required"],maxlength:[1e3,"Meta description cannot exceed 160 characters"]},metaKeywords:{type:String,required:[!0,"Meta keywords are required"],maxlength:[1e3,"Meta keywords cannot exceed 200 characters"]},banner:{type:String,required:[!0,"Banner image is required"],trim:!0},status:{type:String,enum:["draft","published","archived"],default:"draft"},isPublished:{type:Boolean,default:!1},publishedAt:{type:Date,default:null},views:{type:Number,default:0},readTime:{type:Number,default:1},isTopNews:{type:Boolean,default:!1}},{timestamps:!0});o.pre("save",function(e){if(this.isModified("title")&&!this.slug&&(this.slug=this.title.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,"")),this.isModified("content")){let e=(this.content||"").split(/\s+/).length;this.readTime=Math.ceil(e/200)}this.isModified("status")&&"published"===this.status&&!this.publishedAt&&(this.publishedAt=new Date,this.isPublished=!0),e()}),o.index({categories:1}),o.index({tags:1}),o.index({status:1}),o.index({isPublished:1}),o.index({publishedAt:-1}),o.index({"banner.title":1}),o.index({title:"text",content:"text",metaTitle:"text",metaDescription:"text"});let i=s().models.Post||s().model("Post",o)},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66136:e=>{"use strict";e.exports=require("timers")},71349:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"0002875283cbdce215571f9af7fbe61e7e3be8e32c":()=>a.HW,"0026ed18766329cb38cbf036b1b58cc4e238af4b63":()=>h,"004f2b5eeef171b19220f18158fad2880f8a8cf70a":()=>a.y4,"00bd484230662a391368281e6d8bac1469f7b246f1":()=>m,"00d6b6044cac2bc7b8d62ef179e12bb8d427e56b1a":()=>v,"40168a97068f09cbb2fc2fef96d6445bef8e5c9466":()=>a.DY,"4028b030361181c413d3fc466747cf702ccff9b189":()=>b,"4038792a88358da73fac1c13a273a76c89f53d3e2e":()=>y,"404a43fe39e2d0727fca93a9fec58abc5921f6408b":()=>a.iC,"407c1d9494e79c02f37662fbc2619c5ae3c2bbded2":()=>j,"408de5c0c7bb7fdff5408d6db4a77d8acb05b282d4":()=>S,"40b7e9a017084ee5487bcc5eac77e4b80827e85573":()=>a.Lx,"40db3833dad112ff1faa13e6562a2aeee0dc76a47a":()=>w,"60775d7582a533479853fd0ab1fbe6dd7b1300fc9b":()=>A});var a=r(19488),s=r(91199);r(42087);var o=r(7944),i=r(74208);async function n(e,t={}){let{baseUrl:r="https://www.freespin168.asia",nextOptions:a,headers:s,includeCookies:o=!0,customCookies:l,...c}=t,d=`${r}${e}`;console.log(d);let u={"Content-Type":"application/json",...s};if(o)try{let e=await (0,i.UL)(),t=e.get("session-token");t&&(u.Authorization=`Bearer ${t.value}`),l&&(u.Cookie=Object.entries(l).map(([e,t])=>`${e}=${t}`).join("; "));let r=e.getAll();if(r.length>0){let e=r.map(e=>`${e.name}=${e.value}`).join("; ");l||(u.Cookie=e)}}catch(e){console.warn("Cookies not available in this context:",e)}let p={...c,headers:u,next:{tags:a?.tags||[],revalidate:a?.revalidate}};try{let e=await fetch(d,p);if(console.log("res",e),!e.ok){let t="An error occurred",r={};try{t=(r=await e.json()).message||r.error||`HTTP ${e.status}: ${e.statusText}`}catch{t=`HTTP ${e.status}: ${e.statusText}`}return{status:e.status,message:t,errors:r.errors||void 0}}return e.json()}catch(e){return{status:0,message:e instanceof Error?e.message:"Network error occurred"}}}function l(e){return e&&"number"==typeof e.status&&"string"==typeof e.message}async function c(e,t={}){return n(e,{...t,includeCookies:!0})}async function d(e,t={}){return n(e,{...t,includeCookies:!1})}let u=e=>e?"?"+new URLSearchParams(e).toString():"",p=({endpoint:e,tags:t,queryParams:r,revalidate:a,requireAuth:s=!0})=>{let i=s?c:d;return{async getAll(){let s=await i(`${e}${u(r)}`,{nextOptions:{tags:[e,...t],revalidate:a}});return l(s)?{success:!1,message:s.message,status:s.status}:s},async getById(r){let a=await i(`${e}/${r}`,{nextOptions:{tags:[`${e}-${r}`,...t]}});return l(a)?{success:!1,message:a.message,status:a.status}:a},async createData(r){let a=r instanceof FormData?{"Content-Type":"multipart/form-data"}:{"Content-Type":"application/json"},s=await i(e,{method:"POST",body:r instanceof FormData?r:JSON.stringify(r),headers:a});return l(s)?{success:!1,message:s.message,status:s.status}:(t.forEach(e=>(0,o.revalidateTag)(e)),(0,o.revalidateTag)(e),s)},async updateData(r,a){let s=a instanceof FormData?void 0:{"Content-Type":"application/json"},n=await i(`${e}/${r}`,{method:"PATCH",body:a instanceof FormData?a:JSON.stringify(a),headers:s});return l(n)?{success:!1,message:n.message,status:n.status}:(t.forEach(e=>(0,o.revalidateTag)(e)),(0,o.revalidateTag)(e),(0,o.revalidateTag)(`${e}-${r}`),n)},async deleteDataById(r){let a=await i(`${e}/${r}`,{method:"DELETE"});return l(a)?{success:!1,message:a.message,status:a.status}:(t.forEach(e=>(0,o.revalidateTag)(e)),(0,o.revalidateTag)(e),(0,o.revalidateTag)(`${e}-${r}`),a)}}};var g=r(33331);let f=p({endpoint:"/api/categories",tags:["categories"],requireAuth:!0});async function m(){try{let e=await fetch("https://freespin168.asia/api/categories",{method:"GET",headers:{"Content-Type":"application/json"},credentials:"include"});if(!e.ok){let t=await e.text();throw Error(`HTTP error! status: ${e.status}, body: ${t}`)}return await e.json()}catch(e){throw console.error("Error fetching categories:",e),e}}async function h(){return m()}async function b(e){return await f.createData(e)}async function y(e){return await f.deleteDataById(e)}let x=p({endpoint:"/api/posts",tags:["posts"],requireAuth:!0});async function v(){return await x.getAll()}async function w(e){return await x.createData(e)}async function S(e){return await x.deleteDataById(e)}async function A(e,t){try{let r=await fetch(`https://freespin168.asia/api/posts/${e}/status`,{method:"PATCH",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({status:t})});if(!r.ok){let e=await r.text();throw Error(`HTTP error! status: ${r.status}, body: ${e}`)}return await r.json()}catch(e){return console.error("Error updating post status:",e),{success:!1,error:e instanceof Error?e.message:"Failed to update post status"}}}async function j(e){let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.category&&t.append("category",e.category),e?.tag&&t.append("tag",e.tag),e?.search&&t.append("search",e.search);let r=t.toString(),a="https://freespin168.asia",s=r?`${a}/api/public/posts?${r}`:`${a}/api/public/posts`;try{let e=await fetch(s,{method:"GET",headers:{"Content-Type":"application/json"},next:{tags:["public-posts"],revalidate:300}});if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);return await e.json()}catch(e){return console.error("Error fetching public posts:",e),{success:!1,error:"Failed to fetch posts"}}}(0,g.D)([m,h,b,y,v,w,S,A,j]),(0,s.A)(m,"00bd484230662a391368281e6d8bac1469f7b246f1",null),(0,s.A)(h,"0026ed18766329cb38cbf036b1b58cc4e238af4b63",null),(0,s.A)(b,"4028b030361181c413d3fc466747cf702ccff9b189",null),(0,s.A)(y,"4038792a88358da73fac1c13a273a76c89f53d3e2e",null),(0,s.A)(v,"00d6b6044cac2bc7b8d62ef179e12bb8d427e56b1a",null),(0,s.A)(w,"40db3833dad112ff1faa13e6562a2aeee0dc76a47a",null),(0,s.A)(S,"408de5c0c7bb7fdff5408d6db4a77d8acb05b282d4",null),(0,s.A)(A,"60775d7582a533479853fd0ab1fbe6dd7b1300fc9b",null),(0,s.A)(j,"407c1d9494e79c02f37662fbc2619c5ae3c2bbded2",null)},73024:e=>{"use strict";e.exports=require("node:fs")},73944:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(56037),s=r.n(a);let o=new a.Schema({name:{type:String,required:[!0,"Category name is required"],unique:!0,trim:!0,maxlength:[50,"Category name cannot exceed 50 characters"]},description:{type:String,maxlength:[200,"Description cannot exceed 200 characters"]},color:{type:String,default:"#6366f1",match:[/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,"Please enter a valid hex color"]},isActive:{type:Boolean,default:!0}},{timestamps:!0});o.index({isActive:1});let i=s().models.Category||s().model("Category",o)},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(56037),s=r.n(a);let o=process.env.MONGODB_URI;if(!o)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let i=global?.mongoose;i||(i=global.mongoose={conn:null,promise:null});let n=async function(){if(i.conn)return i.conn;i.promise||(i.promise=s().connect(o,{bufferCommands:!1}).then(e=>e));try{i.conn=await i.promise}catch(e){throw i.promise=null,e}return i.conn}},77029:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=r(65239),s=r(48088),o=r(88170),i=r.n(o),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["dashboard",{children:["blogs",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,51828)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\dashboard\\blogs\\categories\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,63144)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\dashboard\\blogs\\categories\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/dashboard/blogs/categories/page",pathname:"/dashboard/blogs/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var a=r(60687);r(43210);var s=r(4780);function o({className:e,type:t,...r}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},94735:e=>{"use strict";e.exports=require("events")},96882:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},97405:(e,t,r)=>{"use strict";r.d(t,{$Y:()=>h,Xp:()=>m,_K:()=>y,jH:()=>b,uF:()=>f});var a=r(67218);r(79130);var s=r(44999),o=r(62351),i=r(75745),n=r(55530),l=r(73944),c=r(36847),d=r(43205),u=r.n(d),p=r(17478);let g=process.env.JWT_SECRET||"your-secret-key";async function f(e,t){try{let r=await (0,s.UL)(),a=r.get("auth-token")?.value,l=r.get("user-info")?.value,c=null;if(a)try{c=u().verify(a,g)}catch(e){console.log("Token verification failed:",e)}if(!c&&l)try{let e=JSON.parse(decodeURIComponent(l));"admin"===e.role&&(c=e)}catch(e){console.log("Failed to parse user-info cookie:",e)}if(!c)return{success:!1,error:"Authentication required"};await (0,i.A)();let d=await n.A.findById(e);if(!d)return{success:!1,error:"Post not found"};d.status=t,"published"===t?(d.isPublished=!0,d.publishedAt||(d.publishedAt=new Date)):d.isPublished=!1;let p=await d.save();(0,o.revalidateTag)("public-posts"),(0,o.revalidatePath)("/en/blogs"),(0,o.revalidatePath)("/th/blogs");let f={_id:p._id.toString(),title:p.title,status:p.status,isPublished:p.isPublished,publishedAt:p.publishedAt?.toISOString(),createdAt:p.createdAt?.toISOString(),updatedAt:p.updatedAt?.toISOString()};return{success:!0,data:{post:f}}}catch(e){return console.error("Error updating post status:",e),{success:!1,error:"Internal server error"}}}async function m(){try{let e=await (0,s.UL)();console.log("=== CATEGORIES ACTION DEBUG ==="),console.log("All cookies:",e.getAll());let t=e.get("auth-token")?.value,r=e.get("user-info")?.value;console.log("Auth token:",t?"Found":"Not found"),console.log("User info cookie:",r?"Found":"Not found");let a=null;if(t)try{let e=u().verify(t,g);console.log("Token verified successfully for user:",e.userId),a=e}catch(e){console.log("Token verification failed:",e)}if(!a&&r)try{let e=JSON.parse(decodeURIComponent(r));if(console.log("User info from cookie:",e),"admin"!==e.role)return{success:!1,error:"Admin access required"};a=e,console.log("Admin access granted via user-info cookie")}catch(e){console.log("Failed to parse user-info cookie:",e)}if(!a)return{success:!1,error:"No valid authentication found"};await (0,i.A)();let o=(await l.A.find({isActive:!0}).sort({name:1}).lean()).map(e=>({_id:e._id.toString(),name:e.name,description:e.description||"",color:e.color||"#6366f1",isActive:e.isActive,createdAt:e.createdAt?.toISOString()||new Date().toISOString(),updatedAt:e.updatedAt?.toISOString()||new Date().toISOString()}));return{success:!0,data:{categories:o}}}catch(e){return console.error("Error fetching categories:",e),{success:!1,error:"Internal server error"}}}async function h(e){try{let t=await (0,s.UL)(),r=t.get("auth-token")?.value,a=t.get("user-info")?.value,o=null;if(r)try{o=u().verify(r,g)}catch(e){console.log("Token verification failed:",e)}if(!o&&a)try{let e=JSON.parse(decodeURIComponent(a));"admin"===e.role&&(o=e)}catch(e){console.log("Failed to parse user-info cookie:",e)}if(!o)return{success:!1,error:"Authentication required"};if(await (0,i.A)(),await l.A.findOne({name:{$regex:RegExp(`^${e.name}$`,"i")}}))return{success:!1,error:"Category already exists"};let n=new l.A(e);await n.save();let c={_id:n._id.toString(),name:n.name,description:n.description||"",color:n.color||"#6366f1",isActive:n.isActive,createdAt:n.createdAt?.toISOString()||new Date().toISOString(),updatedAt:n.updatedAt?.toISOString()||new Date().toISOString()};return{success:!0,message:"Category created successfully",data:{category:c}}}catch(e){return console.error("Error creating category:",e),{success:!1,error:"Internal server error"}}}async function b(e,t){console.log("hello");try{let r=await (0,s.UL)(),a=r.get("auth-token")?.value,o=r.get("user-info")?.value,l=null;if(a)try{l=u().verify(a,g)}catch(e){console.log("Token verification failed:",e)}if(!l&&o)try{let e=JSON.parse(decodeURIComponent(o));"admin"===e.role&&(l=e)}catch(e){console.log("Failed to parse user-info cookie:",e)}if(!l)return{success:!1,error:"Authentication required"};await (0,i.A)();let d="";if(t)try{let e=t.match(/^data:(.+);base64,(.+)$/);if(!e||3!==e.length)throw Error("Invalid base64 image format");let r=e[1],a=e[2],s=Buffer.from(a,"base64"),o=Date.now(),i=r.split("/")[1]||"jpg",n=`banner-${o}.${i}`;console.log("Uploading banner image:",{contentType:r,filename:n,bufferSize:s.length}),d=await c.Kd.uploadFile(s,n,r,"banners"),console.log("Banner uploaded successfully:",d)}catch(e){console.error("Error uploading banner:",e)}let p={...e,banner:d||e.banner||"",author:l.userId||l._id,status:"draft",isBlog:!0,isPublished:!1},f=new n.A(p);await f.save();let m={_id:f._id.toString(),title:f.title,slug:f.slug,excerpt:f.excerpt,content:f.content,banner:f.banner,status:f.status,isPublished:f.isPublished,publishedAt:f.publishedAt?.toISOString(),createdAt:f.createdAt?.toISOString(),updatedAt:f.updatedAt?.toISOString()};return{success:!0,message:"Post created successfully",data:{post:m}}}catch(e){return console.error("Error creating post:",e),{success:!1,error:"Internal server error"}}}async function y(e,t,r){try{let a=await (0,s.UL)(),l=a.get("auth-token")?.value,d=a.get("user-info")?.value,p=null;if(l)try{p=u().verify(l,g)}catch(e){console.log("Token verification failed:",e)}if(!p&&d)try{let e=JSON.parse(decodeURIComponent(d));"admin"===e.role&&(p=e)}catch(e){console.log("Failed to parse user-info cookie:",e)}if(!p)return{success:!1,error:"Authentication required"};await (0,i.A)();let f=await n.A.findOne({slug:e});if(!f)return{success:!1,error:"Post not found"};let m=t.banner?.image||f.banner?.image||f.banner||"";if(r)try{let e=r.match(/^data:(.+);base64,(.+)$/);if(!e||3!==e.length)throw Error("Invalid base64 image format");let t=e[1],a=e[2],s=Buffer.from(a,"base64"),o=Date.now(),i=t.split("/")[1]||"jpg",n=`banner-${o}.${i}`;console.log("Uploading banner image:",{contentType:t,filename:n,bufferSize:s.length}),m=await c.Kd.uploadFile(s,n,t,"banners"),console.log("Banner uploaded successfully:",m)}catch(e){console.error("Error uploading banner:",e)}let h={...{title:t.title,slug:t.slug,content:t.content,categories:t.categories,tags:t.tags,status:t.status,isPublished:t.isPublished,publishedAt:t.publishedAt,metaTitle:t.metaTitle,metaDescription:t.metaDescription,metaKeywords:t.metaKeywords},banner:m,seoTitle:t.metaTitle,seoDescription:t.metaDescription,metaKeywords:t.metaKeywords,author:p.userId||p._id},b=await n.A.findOneAndUpdate({slug:e},h,{new:!0,runValidators:!0}).populate("categories","name description color").lean();if(!b)return{success:!1,error:"Failed to update post"};let y={_id:b._id.toString(),title:b.title,slug:b.slug,content:b.content,banner:b.banner,categories:Array.isArray(b.categories)?b.categories.map(e=>({_id:e._id?.toString(),name:e.name,description:e.description,color:e.color})):[],tags:b.tags||[],seoTitle:b.seoTitle,seoDescription:b.seoDescription,metaKeywords:b.metaKeywords,status:b.status,isPublished:b.isPublished,publishedAt:b.publishedAt?.toISOString?.()||b.publishedAt,createdAt:b.createdAt?.toISOString?.()||b.createdAt,updatedAt:b.updatedAt?.toISOString?.()||b.updatedAt};return(0,o.revalidateTag)("public-posts"),(0,o.revalidatePath)("/en/blogs"),(0,o.revalidatePath)("/th/blogs"),{success:!0,message:"Post updated successfully",data:{post:y}}}catch(e){return console.error("Error updating post:",e),{success:!1,error:"Internal server error"}}}(0,p.D)([f,m,h,b,y]),(0,a.A)(f,"60cdb4e58939c5ebe2ad11f732ca2e9f4afd0da297",null),(0,a.A)(m,"001d2f242e1ebc0563880e1dfe8b6d738fc2076ffc",null),(0,a.A)(h,"40396c047acd2e1f406bf08ca318567c133e740b3f",null),(0,a.A)(b,"60aa6babc821942b6eceea76aa61c8a4f676069c5b",null),(0,a.A)(y,"70058763efb0e19a94f7f5968947fb2774a4f5df9b",null)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,6944,1658,9733,3205,40,4247,5021,7288,7335,1997,7116],()=>r(77029));module.exports=a})();