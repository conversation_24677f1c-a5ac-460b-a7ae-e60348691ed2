(()=>{var e={};e.id=7758,e.ids=[7758],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17063:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(56037),a=t.n(s),n=t(85663);let i=new s.Schema({username:{type:String,required:[!0,"Username is required"],unique:!0,trim:!0,minlength:[3,"Username must be at least 3 characters"],maxlength:[30,"Username cannot exceed 30 characters"]},email:{type:String,required:[!0,"Email is required"],unique:!0,lowercase:!0,trim:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Please enter a valid email"]},password:{type:String,required:[!0,"Password is required"],minlength:[6,"Password must be at least 6 characters"],select:!1},firstName:{type:String,trim:!0,maxlength:[50,"First name cannot exceed 50 characters"]},lastName:{type:String,trim:!0,maxlength:[50,"Last name cannot exceed 50 characters"]},bio:{type:String,maxlength:[500,"Bio cannot exceed 500 characters"]},avatar:{type:String,default:null},role:{type:String,enum:["user","admin","moderator"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1}},{timestamps:!0});i.pre("save",async function(e){if(!this.isModified("password"))return e();try{let r=await n.Ay.genSalt(12);this.password=await n.Ay.hash(this.password,r),e()}catch(r){e(r)}}),i.methods.comparePassword=async function(e){return n.Ay.compare(e,this.password)};let o=a().models.User||a().model("User",i)},25150:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>x,serverHooks:()=>y,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{POST:()=>g});var a=t(96559),n=t(48088),i=t(37719),o=t(32190),u=t(45697),c=t(43205),l=t.n(c),p=t(75745),d=t(17063);let m=u.z.object({email:u.z.string().email(),password:u.z.string().min(1)}),h=process.env.JWT_SECRET||"your-secret-key";async function g(e){try{await (0,p.A)();let r=await e.json(),{email:t,password:s}=m.parse(r),a=await d.A.findOne({email:t}).select("+password");if(!a||!a.isActive||!await a.comparePassword(s))return o.NextResponse.json({success:!1,error:"Invalid credentials"},{status:401});let n=l().sign({userId:a._id,email:a.email,username:a.username,role:a.role},h,{expiresIn:"7d"}),i=o.NextResponse.json({success:!0,message:"Login successful",data:{user:{id:a._id,username:a.username,email:a.email,firstName:a.firstName,lastName:a.lastName,role:a.role,avatar:a.avatar},token:n}});return i.cookies.set("auth-token",n,{httpOnly:!0,secure:!0,sameSite:"lax",maxAge:604800,path:"/"}),i}catch(e){if(e instanceof u.z.ZodError)return o.NextResponse.json({success:!1,error:"Validation failed",details:e.errors},{status:400});return console.error("Login error:",e),o.NextResponse.json({success:!1,error:"Login failed"},{status:500})}}let x=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\api\\auth\\login\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:w,serverHooks:y}=x;function v(){return(0,i.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:w})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75745:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(56037),a=t.n(s);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let i=global?.mongoose;i||(i=global.mongoose={conn:null,promise:null});let o=async function(){if(i.conn)return i.conn;i.promise||(i.promise=a().connect(n,{bufferCommands:!1}).then(e=>e));try{i.conn=await i.promise}catch(e){throw i.promise=null,e}return i.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,3205,5697,5663],()=>t(25150));module.exports=s})();