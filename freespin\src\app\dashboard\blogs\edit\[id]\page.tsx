// "use client";

// import { use } from "react";

// import {
//   cleanObject,
//   convertToBase64,
// } from "@/app/shared/cleanObject";

// import {
//   getCategoriesAction,
//   updatePostAction,
// } from "@/app/dashboard/blogs/actions";

// import { Button } from "@/components/ui/button";
// import { Eye, ImagePlus, Info, Loader2 } from "lucide-react";
// import type React from "react";
// import { useState, useEffect } from "react";
// import { Controller, useForm } from "react-hook-form";
// import { useRouter } from "next/navigation";
// import { toast } from "react-toastify";
// import { z } from "zod";
// import WysiwygEditor from "../../components/WysiwygEditor";
// // import {
// //   MultiSelect,
// //   type MultiSelectOption,
// // } from "../../"

// import {
//   MultiSelect,
//   type MultiSelectOption,
// } from "../../components/BlogPostEditor";

// // Form schema matching the API
// const formSchema = z.object({
//   title: z.string().min(1, "Title is required"),

//   slug: z.string().optional(),
//   // canonicalUrl: z
//   //   .string()
//   //   .url("Must be a valid URL")
//   //   .optional()
//   //   .or(z.literal("")),
//   // existingUrl: z.boolean().default(false),
//   content: z.string().min(1, "Content is required"),
//   isBlog: z.boolean().default(true),
//   categories: z.array(z.string()).default([]),
//   tags: z.array(z.string()).default([]),
//   metaTitle: z.string().min(1, "Meta title is required"),

//   metaDescription: z.string().min(1, "Meta description is required"),
//   metaKeywords: z.string().min(1, "Meta keywords are required"),

//   banner: z.object({
//     title: z.string().min(1, "Banner title is required"),

//     description: z.string().min(1, "Banner description is required"),
//     image: z.string().min(1, "Banner image is required"),
//     altText: z.string().min(1, "Alt text is required"),
//   }),
// });

// type FormData = z.infer<typeof formSchema>;

// // Field configuration for dynamic rendering
// const fieldConfigs = {
//   blogPost: [
//     {
//       name: "title" as const,
//       label: "Title",
//       type: "text",
//       placeholder: "Enter the title of the blog post",
//       tooltip: true,
//       required: true,
//     },
//     {
//       name: "slug" as const,
//       label: "Slug (Optional)",
//       type: "text",
//       placeholder: "Enter a unique slug (auto-generated if empty)",
//       tooltip: true,
//       required: false,
//     },
//   ],
//   seo: [
//     {
//       name: "metaTitle" as const,
//       label: "Meta Title",
//       type: "text",
//       placeholder: "Enter meta title ",
//       tooltip: true,
//       required: true,
//     },
//     {
//       name: "metaDescription" as const,
//       label: "Meta Description",
//       type: "textarea",
//       placeholder: "Enter meta description ",
//       rows: 3,
//       tooltip: true,
//       required: true,
//     },
//     {
//       name: "metaKeywords" as const,
//       label: "Meta Keywords (comma-separated)",
//       type: "text",
//       placeholder: "Enter meta keywords",
//       tooltip: true,
//       required: true,
//     },
//   ],
//   banner: [
//     {
//       name: "title" as const,
//       label: "Banner Title",
//       type: "text",
//       placeholder: "Enter banner title",
//       tooltip: true,
//       required: true,
//     },
//     {
//       name: "description" as const,
//       label: "Banner Description",
//       type: "textarea",
//       placeholder: "Enter banner description",
//       rows: 4,
//       tooltip: true,
//       required: true,
//     },
//     {
//       name: "altText" as const,
//       label: "Banner Image Alt Text",
//       type: "text",
//       placeholder: "Enter banner image alt text",
//       tooltip: true,
//       required: true,
//     },
//   ],
// };

// const InfoIcon = ({ className }: { className?: string }) => (
//   <Info className={`w-4 h-4 text-gray-400 ${className}`} />
// );

// const FormField = ({
//   label,
//   error,
//   children,
//   required = false,
//   tooltip = false,
// }: {
//   label: string;
//   error?: string;
//   children: React.ReactNode;
//   required?: boolean;
//   tooltip?: boolean;
// }) => (
//   <div className="mb-4">
//     <label className="block text-sm font-medium text-gray-700 mb-2">
//       {label}
//       {required && <span className="text-red-500 ml-1">*</span>}
//       {tooltip && <InfoIcon className="inline ml-1" />}
//     </label>
//     {children}
//     {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
//   </div>
// );

// const DynamicField = ({
//   config,
//   register,
//   error,
// }: {
//   config: any;
//   register: any;
//   error?: string;
// }) => {
//   return (
//     <FormField
//       label={config.label}
//       error={error}
//       tooltip={config.tooltip}
//       required={config.required}
//     >
//       {config.type === "textarea" ? (
//         <textarea
//           {...register(config.name)}
//           placeholder={config.placeholder}
//           rows={config.rows || 3}
//           className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
//         />
//       ) : (
//         <input
//           {...register(config.name)}
//           type={config.type}
//           placeholder={config.placeholder}
//           className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
//         />
//       )}
//     </FormField>
//   );
// };

// export default function EditBlogPost({
//   params,
// }: {
//   params: Promise<{ id: string; locale: string }>;
// }) {
//   const { id: postId, locale } = use(params);
//   const router = useRouter();
//   const [categories, setCategories] = useState<string[]>([]);
//   const [tags, setTags] = useState<string[]>([]);
//   const [availableCategories, setAvailableCategories] = useState<
//     MultiSelectOption[]
//   >([]);
//   const [isSubmitting, setIsSubmitting] = useState(false);
//   const [bannerImageFile, setBannerImageFile] = useState<File | null>(null);
//   const [initialData, setInitialData] = useState<FormData | null>(null);

//   const {
//     control,
//     register,
//     handleSubmit,
//     watch,
//     setValue,
//     reset,
//     formState: { errors },
//   } = useForm<FormData>({
//     defaultValues: {
//       title: "",
//       slug: "",
//       // canonicalUrl: "",
//       // existingUrl: false,
//       content: "",
//       isBlog: true,
//       categories: [],
//       tags: [],
//       metaTitle: "",
//       metaDescription: "",
//       metaKeywords: "",
//       banner: {
//         title: "",
//         description: "",
//         altText: "",
//         image: "",
//       },
//     },
//   });

//   // const existingUrl = watch("existingUrl");

//   // Fetch post data and categories on component mount
//   useEffect(() => {
//     const fetchPostData = async () => {
//       try {
//         // Fetch categories
//         const categoryResponse = await getCategoriesAction();
//         console.log("Categories response:", categoryResponse);

//         if (categoryResponse.success && categoryResponse.data) {
//           const categoriesData =
//             (categoryResponse.data as any).categories || [];
//           const categoryNames = categoriesData.map((cat: any) => ({
//             label: cat.name,
//             value: cat._id,
//           }));
//           setAvailableCategories(categoryNames);
//         } else {
//           console.warn("No categories data found");
//         }

//         // Fetch post data
//         if (postId) {
//           console.log("Fetching post with ID:", postId);
//           const response = await fetch(`/api/posts/${postId}`);
//           const result = await response.json();
//           console.log("Post API response:", result);

//           if (result.success && result.data?.post) {
//             const post = result.data.post;

//             // Transform categories - handle both object and string formats
//             let categoryIds: string[] = [];
//             if (Array.isArray(post.categories)) {
//               categoryIds = post.categories
//                 .map((cat: any) => {
//                   // If category is an object with _id, extract the _id
//                   if (typeof cat === "object" && cat._id) {
//                     return cat._id;
//                   }
//                   // If category is already a string (ID), use it directly
//                   if (typeof cat === "string") {
//                     return cat;
//                   }
//                   return "";
//                 })
//                 .filter(Boolean);
//             }

//             // Transform tags - handle both object and string formats
//             let tagValues: string[] = [];
//             if (Array.isArray(post.tags)) {
//               tagValues = post.tags
//                 .map((tag: any) => {
//                   // If tag is an object with name or value, extract it
//                   if (typeof tag === "object") {
//                     return tag.name || tag.value || tag._id || "";
//                   }
//                   // If tag is already a string, use it directly
//                   if (typeof tag === "string") {
//                     return tag;
//                   }
//                   return "";
//                 })
//                 .filter(Boolean);
//             }

//             const postData: FormData = {
//               title: post.title || "",
//               slug: post.slug || "",
//               // canonicalUrl: post.canonicalUrl || "",
//               // existingUrl: !!post.canonicalUrl,
//               content: post.content || "",
//               isBlog: post.isBlog ?? true,
//               categories: categoryIds,
//               tags: tagValues,
//               metaTitle: post.seoTitle || post.title || "",
//               metaDescription: post.seoDescription || "",
//               metaKeywords: post.metaKeywords || "",
//               banner: {
//                 title: post.title || "", // Use post title as banner title fallback
//                 description: post.metaDescription || post.seoDescription || "", // Use meta description as banner description fallback
//                 image:
//                   typeof post.banner === "string"
//                     ? post.banner
//                     : post.banner?.image || "", // Handle banner as string URL or object
//                 altText: post.title || "", // Use post title as alt text fallback
//               },
//             };

//             console.log("Prepared postData:", postData);
//             setInitialData(postData);
//             reset(postData);
//             setCategories(categoryIds);
//             setTags(tagValues);
//             setValue("categories", categoryIds);
//             setValue("tags", tagValues);
//           } else {
//             console.error("Post data not found in response:", result);
//             toast.error("Failed to load post data");
//           }
//         } else {
//           console.error("No postId provided");
//           toast.error("No post ID provided");
//         }
//       } catch (error) {
//         console.error("Failed to fetch data:", error);
//         toast.error("Failed to load post data");
//       }
//     };

//     fetchPostData();
//   }, [postId, reset, setValue]);

//   const onSubmit = async (data: FormData) => {
//     if (!postId) {
//       toast.error("No post ID provided");
//       return;
//     }

//     setIsSubmitting(true);
//     try {
//       console.log("Form data before cleaning:", data);

//       const cleanedData = cleanObject(data);
//       const bannerImageBase64 = bannerImageFile
//         ? ((await convertToBase64(bannerImageFile)) as string)
//         : data.banner.image;

//       const res = await updatePostAction(
//         postId,
//         cleanedData,
//         bannerImageBase64
//       );

//       console.log("Update API response:", res);

//       if (!res.success) {
//         console.error("API Error:", res);
//         toast.error(res?.message || "Failed to update post");
//       } else {
//         console.log("Post updated successfully:", res);
//         toast.success(res?.message || "Post updated successfully");
//         setTimeout(() => {
//           router.push(`/${locale}/dashboard/blogs`);
//         }, 1500);
//       }
//     } catch (error) {
//       console.error("Submission error:", error);
//       toast.error("An unexpected error occurred");
//     } finally {
//       setIsSubmitting(false);
//     }
//   };

//   const handleBannerImageChange = (
//     event: React.ChangeEvent<HTMLInputElement>
//   ) => {
//     const file = event.target.files?.[0];
//     if (file) {
//       console.log("Banner image selected:", {
//         name: file.name,
//         size: file.size,
//         type: file.type,
//       });

//       const allowedTypes = [
//         "image/jpeg",
//         "image/jpg",
//         "image/png",
//         "image/webp",
//         "image/gif",
//         "image/svg+xml",
//       ];
//       const maxSize = 10 * 1024 * 1024; // 10MB

//       if (!allowedTypes.includes(file.type)) {
//         toast.error(
//           `Invalid file type: ${file.type}. Only images are allowed.`
//         );
//         return;
//       }

//       if (file.size > maxSize) {
//         toast.error(
//           `File too large: ${(file.size / 1024 / 1024).toFixed(
//             2
//           )}MB. Maximum 10MB allowed.`
//         );
//         return;
//       }

//       if (file.size === 0) {
//         toast.error("File is empty");
//         return;
//       }

//       setBannerImageFile(file);
//       const previewUrl = URL.createObjectURL(file);
//       setValue("banner.image", previewUrl);
//     } else {
//       setBannerImageFile(null);
//       setValue("banner.image", initialData?.banner.image || "");
//     }
//   };

//   return (
//     <div className="min-h-screen bg-gray-50 p-6">
//       <div className="container px-3 mx-auto">
//         <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
//           {/* Blog Post Section */}
//           <div className="bg-white p-6 rounded-lg shadow-sm border">
//             <div className="flex items-center justify-between mb-6">
//               <div className="flex items-center gap-4">
//                 <span className="text-teal-600">Edit</span>
//                 <span className="text-gray-400">•</span>
//                 <span>Blog Post</span>
//               </div>
//               <div className="flex items-center gap-2">
//                 <button
//                   type="button"
//                   className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
//                   onClick={() => router.push(`/${locale}/dashboard/blogs`)}
//                 >
//                   Cancel
//                 </button>
//                 <button
//                   type="submit"
//                   disabled={isSubmitting}
//                   className="bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 disabled:opacity-50 flex items-center gap-2"
//                 >
//                   {isSubmitting && <Loader2 className="w-4 h-4 animate-spin" />}
//                   {isSubmitting ? "Updating..." : "Update Post"}
//                 </button>
//               </div>
//             </div>

//             <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
//               <div className="lg:col-span-2">
//                 <div className="space-y-6">
//                   <div>
//                     <h3 className="text-lg font-semibold mb-4">Post Details</h3>
//                     {fieldConfigs.blogPost.map((config) => (
//                       <DynamicField
//                         key={config.name}
//                         config={config}
//                         register={register}
//                         error={errors[config.name]?.message}
//                       />
//                     ))}
//                     {/* <FormField
//                       label="Canonical URL"
//                       error={errors.canonicalUrl?.message}
//                       tooltip
//                     >
//                       <div className="space-y-2">
//                         <div className="flex items-center gap-2">
//                           <input
//                             {...register("existingUrl")}
//                             type="checkbox"
//                             className="rounded"
//                           />
//                           <label className="text-sm">Existing Url</label>
//                         </div>
//                         <div className="flex items-center gap-2">
//                           <input
//                             {...register("canonicalUrl")}
//                             type="url"
//                             placeholder="https://example.com/blog/post/"
//                             disabled={!existingUrl}
//                             className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
//                           />
//                           <button
//                             type="button"
//                             disabled={!existingUrl}
//                             className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 flex items-center gap-1"
//                           >
//                             <Eye className="w-4 h-4" />
//                             Visit
//                           </button>
//                         </div>
//                       </div>
//                     </FormField> */}

//                     {/* Categories and Tags Section */}
//                     <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//                       <div>
//                         <MultiSelect
//                           label="Categories"
//                           placeholder="Search for categories..."
//                           options={availableCategories}
//                           selectedValues={categories}
//                           onChange={(values) => {
//                             setCategories(values);
//                             setValue("categories", values);
//                           }}
//                           allowCustom={true}
//                         />
//                       </div>
//                       <div>
//                         <MultiSelect
//                           label="Tags"
//                           placeholder="Search for tags..."
//                           options={[]}
//                           selectedValues={tags}
//                           onChange={(values) => {
//                             setTags(values);
//                             setValue("tags", values);
//                           }}
//                           allowCustom={true}
//                         />
//                       </div>
//                     </div>
//                   </div>

//                   <div>
//                     <h3 className="text-lg font-semibold mb-4">Blog Content</h3>
//                     <FormField
//                       label="Content"
//                       error={errors.content?.message}
//                       required
//                     >
//                       <Controller
//                         name="content"
//                         control={control}
//                         render={({ field }) => (
//                           <WysiwygEditor
//                             onChange={field.onChange}
//                             content={field.value}
//                             placeholder="Start writing your blog post content..."
//                           />
//                         )}
//                       />
//                     </FormField>
//                   </div>
//                 </div>
//               </div>

//               <div className="space-y-6">{/* Sidebar content */}</div>
//             </div>
//           </div>

//           {/* SEO Section */}
//           <div className="bg-white p-6 rounded-lg shadow-sm border">
//             <h2 className="text-lg font-semibold mb-6">SEO</h2>
//             <div className="space-y-4">
//               {fieldConfigs.seo.map((config) => (
//                 <DynamicField
//                   key={config.name}
//                   config={config}
//                   register={register}
//                   error={errors[config.name]?.message}
//                 />
//               ))}
//             </div>
//           </div>

//           {/* Banner Section */}
//           <div className="bg-white p-6 rounded-lg shadow-sm border">
//             <h2 className="text-lg font-semibold mb-6">Banner</h2>
//             <div className="space-y-4">
//               {fieldConfigs.banner.map((config) => (
//                 <DynamicField
//                   key={config.name}
//                   config={config}
//                   register={register}
//                   error={errors.banner?.[config.name]?.message}
//                 />
//               ))}
//               <div className="space-y-4">
//                 <label
//                   htmlFor="bannerImage"
//                   className="block text-sm font-medium text-gray-700 jakarta"
//                 >
//                   Banner Image
//                 </label>
//                 <div className="flex items-center gap-4">
//                   <Button
//                     type="button"
//                     variant="default"
//                     className="flex items-center gap-2 text-white transition-all duration-300 rounded-md shadow-md"
//                     onClick={() =>
//                       document.getElementById("bannerImage")?.click()
//                     }
//                   >
//                     <ImagePlus size={20} />
//                     <span>Choose Image</span>
//                   </Button>
//                   <input
//                     type="file"
//                     id="bannerImage"
//                     accept="image/*"
//                     onChange={handleBannerImageChange}
//                     className="hidden"
//                   />
//                 </div>

//                 {bannerImageFile ? (
//                   <div className="mt-4">
//                     <p className="text-sm text-gray-600 jakarta">
//                       Selected: {bannerImageFile.name} (
//                       {(bannerImageFile.size / 1024).toFixed(2)} KB)
//                     </p>
//                     <img
//                       src={
//                         URL.createObjectURL(bannerImageFile) ||
//                         "/placeholder.svg"
//                       }
//                       alt="Preview"
//                       className="mt-2 max-w-[200px] max-h-[200px] rounded-md shadow-lg object-cover"
//                     />
//                   </div>
//                 ) : initialData?.banner.image ? (
//                   <div className="mt-4">
//                     <p className="text-sm text-gray-600 jakarta">
//                       Current Image
//                     </p>
//                     <img
//                       src={initialData.banner.image || "/placeholder.svg"}
//                       alt={initialData.banner.altText}
//                       className="mt-2 max-w-[200px] max-h-[200px] rounded-md shadow-lg object-cover"
//                     />
//                   </div>
//                 ) : null}
//               </div>
//             </div>
//           </div>
//         </form>
//       </div>
//     </div>
//   );
// }

// Second

"use client";

import { use } from "react";

import {
  cleanObject,
  convertToBase64,
} from "@/app/shared/cleanObject";

import {
  getCategoriesAction,
  updatePostAction,
} from "@/app/dashboard/blogs/actions";

import { Button } from "@/components/ui/button";
import { Eye, ImagePlus, Info, Loader2 } from "lucide-react";
import type React from "react";
import { useState, useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";
import { z } from "zod";
import WysiwygEditor from "../../components/WysiwygEditor";
import {
  MultiSelect,
  type MultiSelectOption,
} from "../../components/BlogPostEditor";

// Form schema matching the API
const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  slug: z.string().optional(),
  content: z.string().min(1, "Content is required"),
  isBlog: z.boolean().default(true),
  categories: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
  metaTitle: z.string().min(1, "Meta title is required"),
  metaDescription: z.string().min(1, "Meta description is required"),
  metaKeywords: z.string().min(1, "Meta keywords are required"),
  banner: z.object({
    image: z.string().min(1, "Banner image is required"),
  }),
});

type FormData = z.infer<typeof formSchema>;

// Field configuration for dynamic rendering
const fieldConfigs = {
  blogPost: [
    {
      name: "title" as const,
      label: "Title",
      type: "text",
      placeholder: "Enter the title of the blog post",
      tooltip: true,
      required: true,
    },
    {
      name: "slug" as const,
      label: "Slug (Optional)",
      type: "text",
      placeholder: "Enter a unique slug (auto-generated if empty)",
      tooltip: true,
      required: false,
    },
  ],
  seo: [
    {
      name: "metaTitle" as const,
      label: "Meta Title",
      type: "text",
      placeholder: "Enter meta title ",
      tooltip: true,
      required: true,
    },
    {
      name: "metaDescription" as const,
      label: "Meta Description",
      type: "textarea",
      placeholder: "Enter meta description ",
      rows: 3,
      tooltip: true,
      required: true,
    },
    {
      name: "metaKeywords" as const,
      label: "Meta Keywords (comma-separated)",
      type: "text",
      placeholder: "Enter meta keywords",
      tooltip: true,
      required: true,
    },
  ],
  banner: [],
};

const InfoIcon = ({ className }: { className?: string }) => (
  <Info className={`w-4 h-4 text-gray-400 ${className}`} />
);

const FormField = ({
  label,
  error,
  children,
  required = false,
  tooltip = false,
}: {
  label: string;
  error?: string;
  children: React.ReactNode;
  required?: boolean;
  tooltip?: boolean;
}) => (
  <div className="mb-4">
    <label className="block text-sm font-medium text-gray-700 mb-2">
      {label}
      {required && <span className="text-red-500 ml-1">*</span>}
      {tooltip && <InfoIcon className="inline ml-1" />}
    </label>
    {children}
    {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
  </div>
);

const DynamicField = ({
  config,
  register,
  error,
}: {
  config: any;
  register: any;
  error?: string;
}) => {
  return (
    <FormField
      label={config.label}
      error={error}
      tooltip={config.tooltip}
      required={config.required}
    >
      {config.type === "textarea" ? (
        <textarea
          {...register(config.name)}
          placeholder={config.placeholder}
          rows={config.rows || 3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
        />
      ) : (
        <input
          {...register(config.name)}
          type={config.type}
          placeholder={config.placeholder}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      )}
    </FormField>
  );
};

export default function EditBlogPost({
  params,
}: {
  params: Promise<{ id: string; locale: string }>;
}) {
  const { id: postId, locale } = use(params);
  const router = useRouter();
  const [categories, setCategories] = useState<string[]>([]);
  const [tags, setTags] = useState<string[]>([]);
  const [availableCategories, setAvailableCategories] = useState<
    MultiSelectOption[]
  >([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bannerImageFile, setBannerImageFile] = useState<File | null>(null);
  const [initialData, setInitialData] = useState<FormData | null>(null);

  const {
    control,
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      title: "",
      slug: "",
      content: "",
      isBlog: true,
      categories: [],
      tags: [],
      metaTitle: "",
      metaDescription: "",
      metaKeywords: "",
      banner: {
        image: "",
      },
    },
  });

  // Fetch post data and categories on component mount
  useEffect(() => {
    const fetchPostData = async () => {
      try {
        // Fetch categories
        const categoryResponse = await getCategoriesAction();
        console.log("Categories response:", categoryResponse);

        if (categoryResponse.success && categoryResponse.data) {
          const categoriesData =
            (categoryResponse.data as any).categories || [];
          const categoryNames = categoriesData.map((cat: any) => ({
            label: cat.name,
            value: cat._id,
          }));
          setAvailableCategories(categoryNames);
        } else {
          console.warn("No categories data found");
        }

        // Fetch post data
        if (postId) {
          console.log("Fetching post with ID:", postId);
          const response = await fetch(`/api/posts/${postId}`);
          const result = await response.json();
          console.log("Post API response:", result);

          if (result.success && result.data?.post) {
            const post = result.data.post;

            // Transform categories - handle both object and string formats
            let categoryIds: string[] = [];
            if (Array.isArray(post.categories)) {
              categoryIds = post.categories
                .map((cat: any) => {
                  // If category is an object with _id, extract the _id
                  if (typeof cat === "object" && cat._id) {
                    return cat._id;
                  }
                  // If category is already a string (ID), use it directly
                  if (typeof cat === "string") {
                    return cat;
                  }
                  return "";
                })
                .filter(Boolean);
            }

            // Transform tags - handle both object and string formats
            let tagValues: string[] = [];
            if (Array.isArray(post.tags)) {
              tagValues = post.tags
                .map((tag: any) => {
                  // If tag is an object with name or value, extract it
                  if (typeof tag === "object") {
                    return tag.name || tag.value || tag._id || "";
                  }
                  // If tag is already a string, use it directly
                  if (typeof tag === "string") {
                    return tag;
                  }
                  return "";
                })
                .filter(Boolean);
            }

            const postData: FormData = {
              title: post.title || "",
              slug: post.slug || "",
              content: post.content || "",
              isBlog: post.isBlog ?? true,
              categories: categoryIds,
              tags: tagValues,
              metaTitle: post.seoTitle || post.title || "",
              metaDescription: post.metaDescription || "",
              metaKeywords: post.metaKeywords || "",
              banner: {
                image:
                  typeof post.banner === "string"
                    ? post.banner
                    : post.banner?.image || "",
              },
            };

            console.log("Prepared postData:", postData);
            setInitialData(postData);
            reset(postData);
            setCategories(categoryIds);
            setTags(tagValues);
            setValue("categories", categoryIds);
            setValue("tags", tagValues);
          } else {
            console.error("Post data not found in response:", result);
            toast.error("Failed to load post data");
          }
        } else {
          console.error("No postId provided");
          toast.error("No post ID provided");
        }
      } catch (error) {
        console.error("Failed to fetch data:", error);
        toast.error("Failed to load post data");
      }
    };

    fetchPostData();
  }, [postId, reset, setValue]);

  const onSubmit = async (data: FormData) => {
    if (!postId) {
      toast.error("No post ID provided");
      return;
    }

    setIsSubmitting(true);
    try {
      console.log("Form data before cleaning:", data);

      const cleanedData = cleanObject(data);
      const bannerImageBase64 = bannerImageFile
        ? ((await convertToBase64(bannerImageFile)) as string)
        : data.banner.image;

      const res = await updatePostAction(
        postId,
        cleanedData,
        bannerImageBase64
      );

      console.log("Update API response:", res);

      if (!res.success) {
        console.error("API Error:", res);
        toast.error(res?.message || "Failed to update post");
      } else {
        console.log("Post updated successfully:", res);
        toast.success(res?.message || "Post updated successfully");
        setTimeout(() => {
          router.push(`/${locale}/dashboard/blogs`);
        }, 1500);
      }
    } catch (error) {
      console.error("Submission error:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBannerImageChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      console.log("Banner image selected:", {
        name: file.name,
        size: file.size,
        type: file.type,
      });

      const allowedTypes = [
        "image/jpeg",
        "image/jpg",
        "image/png",
        "image/webp",
        "image/gif",
        "image/svg+xml",
      ];
      const maxSize = 10 * 1024 * 1024; // 10MB

      if (!allowedTypes.includes(file.type)) {
        toast.error(
          `Invalid file type: ${file.type}. Only images are allowed.`
        );
        return;
      }

      if (file.size > maxSize) {
        toast.error(
          `File too large: ${(file.size / 1024 / 1024).toFixed(
            2
          )}MB. Maximum 10MB allowed.`
        );
        return;
      }

      if (file.size === 0) {
        toast.error("File is empty");
        return;
      }

      setBannerImageFile(file);
      const previewUrl = URL.createObjectURL(file);
      setValue("banner.image", previewUrl);
    } else {
      setBannerImageFile(null);
      setValue("banner.image", initialData?.banner.image || "");
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="container px-3 mx-auto">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Blog Post Section */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <span className="text-teal-600">Edit</span>
                <span className="text-gray-400">•</span>
                <span>Blog Post</span>
              </div>
              <div className="flex items-center gap-2">
                <button
                  type="button"
                  className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                  onClick={() => router.push(`/${locale}/dashboard/blogs`)}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 disabled:opacity-50 flex items-center gap-2"
                >
                  {isSubmitting && <Loader2 className="w-4 h-4 animate-spin" />}
                  {isSubmitting ? "Updating..." : "Update Post"}
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Post Details</h3>
                    {fieldConfigs.blogPost.map((config) => (
                      <DynamicField
                        key={config.name}
                        config={config}
                        register={register}
                        error={errors[config.name]?.message}
                      />
                    ))}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <MultiSelect
                          label="Categories"
                          placeholder="Search for categories..."
                          options={availableCategories}
                          selectedValues={categories}
                          onChange={(values) => {
                            setCategories(values);
                            setValue("categories", values);
                          }}
                          allowCustom={true}
                        />
                      </div>
                      <div>
                        <MultiSelect
                          label="Tags"
                          placeholder="Search for tags..."
                          options={[]}
                          selectedValues={tags}
                          onChange={(values) => {
                            setTags(values);
                            setValue("tags", values);
                          }}
                          allowCustom={true}
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-4">Blog Content</h3>
                    <FormField
                      label="Content"
                      error={errors.content?.message}
                      required
                    >
                      <Controller
                        name="content"
                        control={control}
                        render={({ field }) => (
                          <WysiwygEditor
                            onChange={field.onChange}
                            content={field.value}
                            placeholder="Start writing your blog post content..."
                          />
                        )}
                      />
                    </FormField>
                  </div>
                </div>
              </div>

              <div className="space-y-6">{/* Sidebar content */}</div>
            </div>
          </div>

          {/* SEO Section */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-semibold mb-6">SEO</h2>
            <div className="space-y-4">
              {fieldConfigs.seo.map((config) => (
                <DynamicField
                  key={config.name}
                  config={config}
                  register={register}
                  error={errors[config.name]?.message}
                />
              ))}
            </div>
          </div>

          {/* Banner Section */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-semibold mb-6">Banner</h2>
            <div className="space-y-4">
              <div className="space-y-4">
                <label
                  htmlFor="bannerImage"
                  className="block text-sm font-medium text-gray-700 jakarta"
                >
                  Banner Image
                </label>
                <div className="flex items-center gap-4">
                  <Button
                    type="button"
                    variant="default"
                    className="flex items-center gap-2 text-white transition-all duration-300 rounded-md shadow-md"
                    onClick={() =>
                      document.getElementById("bannerImage")?.click()
                    }
                  >
                    <ImagePlus size={20} />
                    <span>Choose Image</span>
                  </Button>
                  <input
                    type="file"
                    id="bannerImage"
                    accept="image/*"
                    onChange={handleBannerImageChange}
                    className="hidden"
                  />
                </div>

                {bannerImageFile ? (
                  <div className="mt-4">
                    <p className="text-sm text-gray-600 jakarta">
                      Selected: {bannerImageFile.name} (
                      {(bannerImageFile.size / 1024).toFixed(2)} KB)
                    </p>
                    <img
                      src={
                        URL.createObjectURL(bannerImageFile) ||
                        "/placeholder.svg"
                      }
                      alt="Preview"
                      className="mt-2 max-w-[200px] max-h-[200px] rounded-md shadow-lg object-cover"
                    />
                  </div>
                ) : initialData?.banner.image ? (
                  <div className="mt-4">
                    <p className="text-sm text-gray-600 jakarta">
                      Current Image
                    </p>
                    <img
                      src={initialData.banner.image || "/placeholder.svg"}
                      alt="Banner"
                      className="mt-2 max-w-[200px] max-h-[200px] rounded-md shadow-lg object-cover"
                    />
                  </div>
                ) : null}
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
