import React from "react";

const Banner: React.FC<{
  url?: string;
  text?: string;
  secondTitleText?: string;
  secondText?: string;
}> = ({ url = "/bar.png", text, secondText, secondTitleText }) => {
  return (
    <div className="relative h-[80vh] flex items-center justify-center overflow-hidden">
      <div
        className="absolute inset-0 z-0 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${url})` }}
      />
      <div className="absolute inset-0 z-10 bg-gradient-to-b from-transparent to-black" />
      <div className="flex flex-col items-center gap-5">
        {secondTitleText && (
          <p className="z-20 md:text-6xl text-4xl text-white font-bold">
            {secondTitleText}
          </p>
        )}
        {secondText && (
          <p className="z-20 md:text-2xl text-2xl text-white font-semibold ">
            {secondText}
          </p>
        )}
        <p className="z-20 md:text-3xl text-2xl text-white font-bold ">
          {text}
        </p>
      </div>
    </div>
  );
};

export default Banner;
