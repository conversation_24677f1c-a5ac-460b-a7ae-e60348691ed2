(()=>{var e={};e.id=2316,e.ids=[2316],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29140:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>k,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>v});var s={};t.r(s),t.d(s,{GET:()=>d});var o=t(96559),n=t(48088),u=t(37719),a=t(32190),i=t(43205),p=t.n(i);let c=process.env.JWT_SECRET||"your-secret-key";async function d(e){try{let r=e.headers.get("authorization"),t=r?.replace("Bearer ","")||e.cookies.get("auth-token")?.value;if(!t)return a.NextResponse.json({success:!1,error:"No token provided"},{status:401});let s=p().verify(t,c);return a.NextResponse.json({success:!0,data:{message:"Access granted to protected resource",user:{userId:s.userId,email:s.email,role:s.role}}})}catch(e){if(e instanceof p().JsonWebTokenError)return a.NextResponse.json({success:!1,error:"Invalid token"},{status:401});return console.error("Protected route error:",e),a.NextResponse.json({success:!1,error:"Authentication failed"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/protected/route",pathname:"/api/protected",filename:"route",bundlePath:"app/api/protected/route"},resolvedPagePath:"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\api\\protected\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:v,serverHooks:f}=l;function k(){return(0,u.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:v})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,3205],()=>t(29140));module.exports=s})();