import React from "react";
import BasicHeader from "../../home/<USER>/BasicHeader";
import MainHeader from "./MainHeader";
import Image from "next/image";
import clsx from "clsx";

const Contents: React.FC<{
  header: string;
  contents: string[];
  src: string;
  reverse: boolean;
}> = ({ header, contents, src, reverse }) => {
  console.log("header", header, contents);
  return (
    <div className="py-6 grid grid-cols-1 md:grid-cols-2">
      <div
        className={clsx(
          "flex justify-center flex-col",
          reverse && "md:order-2"
        )}
      >
        {/* <BasicHeader text="About Us" className="text-start" /> */}
        <MainHeader text={header} className="text-white" />
        <div className="my-6 text-[#A9A7B0] space-y-3 text-justify">
          {contents?.map((item, index) => (
            <p key={index}>{item}</p>
          ))}
        </div>
      </div>
      <div
        className={clsx(
          "flex items-center justify-center",
          reverse && "md:order-1"
        )}
      >
        <Image
          src={src}
          alt="about"
          width={500}
          height={500}
          // className="mix-blend-overlay"
          className="border-transparent "
        />
      </div>
    </div>
  );
};

export default Contents;
