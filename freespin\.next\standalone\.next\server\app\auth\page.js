(()=>{var e={};e.id=8365,e.ids=[8365],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4833:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ToastProvider() from the server but ToastProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\shared\\ToastProvider.tsx","ToastProvider")},6507:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>n});var a=r(60687),s=r(93853);function n(){return(0,a.jsx)(s.N9,{"aria-label":"Toast notifications",position:"top-right",autoClose:3e3})}r(25806)},10541:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Web Studio Nepal\\\\FreeSpin168\\\\freespin\\\\src\\\\components\\\\GoogleAnalytics\\\\GoogleAnalytics.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\components\\GoogleAnalytics\\GoogleAnalytics.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19488:(e,t,r)=>{"use strict";r.d(t,{DY:()=>l,HW:()=>c,Lx:()=>o,iC:()=>d,y4:()=>i});var a=r(91199);r(42087);var s=r(74208),n=r(90141);async function o(e){try{let t=await fetch("https://freespin168.asia/api/auth/login",{method:"POST",body:JSON.stringify(e),headers:{"Content-Type":"application/json"}});if(!t.ok){let e=await t.json();return{success:!1,message:e.error||"Login failed"}}let r=await t.json();if(console.log("Login result:",r),r.success&&r.data){let e=await (0,s.UL)();if(e.set("session-token",r.data.token,{httpOnly:!0,secure:!0,sameSite:"lax",maxAge:604800,path:"/"}),r.data.user){let t=JSON.stringify({id:r.data.user.id,email:r.data.user.email,name:`${r.data.user.firstName||""} ${r.data.user.lastName||""}`.trim()||r.data.user.username,username:r.data.user.username,role:r.data.user.role});if(e.set("user-info",t,{httpOnly:!1,secure:!0,sameSite:"lax",maxAge:604800,path:"/"}),"undefined"!=typeof document){let e=new Date(Date.now()+6048e5).toUTCString();document.cookie=`user-info=${encodeURIComponent(t)}; expires=${e}; path=/; SameSite=lax`}}}return r}catch(e){return console.error("Login error:",e),{success:!1,message:"Network error. Please check your connection and try again."}}}async function i(){try{let e=await (0,s.UL)();e.delete("session-token"),e.delete("user-info");try{await fetch("https://freespin168.asia/api/auth/logout",{method:"POST",headers:{"Content-Type":"application/json"}})}catch(e){console.warn("Logout API call failed:",e)}return{success:!0,message:"Logged out successfully"}}catch(e){return console.error("Logout error:",e),{success:!1,message:"Logout failed"}}}async function l(e){try{let t=await fetch("https://freespin168.asia/api/auth/register",{method:"POST",body:JSON.stringify(e),headers:{"Content-Type":"application/json"}});if(!t.ok){let e=await t.json();return{success:!1,message:e.error||"Registration failed"}}let r=await t.json();if(r.success&&r.data){let e=await (0,s.UL)();e.set("session-token",r.data.token,{httpOnly:!0,secure:!0,sameSite:"lax",maxAge:604800,path:"/"}),r.data.user&&e.set("user-info",JSON.stringify({id:r.data.user.id,email:r.data.user.email,name:`${r.data.user.firstName||""} ${r.data.user.lastName||""}`.trim()||r.data.user.username,username:r.data.user.username,role:r.data.user.role}),{httpOnly:!1,secure:!0,sameSite:"lax",maxAge:604800,path:"/"})}return r}catch(e){return console.error("Registration error:",e),{success:!1,message:"Network error. Please check your connection and try again."}}}async function c(){try{let e=await (0,s.UL)(),t=e.get("user-info"),r=e.get("session-token");if(!t||!r)return null;return JSON.parse(t.value)}catch(e){return console.error("Error getting current user:",e),null}}async function d(e){let t=e.get("email"),r=e.get("password");if(!t||!r)return{success:!1,message:"Email and password are required"};let a=await o({email:t,password:r});return a.success&&(0,n.redirect)("/dashboard/blogs"),a}(0,r(33331).D)([o,i,l,c,d]),(0,a.A)(o,"40b7e9a017084ee5487bcc5eac77e4b80827e85573",null),(0,a.A)(i,"004f2b5eeef171b19220f18158fad2880f8a8cf70a",null),(0,a.A)(l,"40168a97068f09cbb2fc2fef96d6445bef8e5c9466",null),(0,a.A)(c,"0002875283cbdce215571f9af7fbe61e7e3be8e32c",null),(0,a.A)(d,"404a43fe39e2d0727fca93a9fec58abc5921f6408b",null)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30916:(e,t,r)=>{Promise.resolve().then(r.bind(r,45930))},33473:(e,t,r)=>{Promise.resolve().then(r.bind(r,4833)),Promise.resolve().then(r.bind(r,10541))},33873:e=>{"use strict";e.exports=require("path")},45930:(e,t,r)=>{"use strict";r.d(t,{default:()=>j});var a=r(60687),s=r(43210),n=r(27605),o=r(57335),i=r(9275),l=r(62688);let c=(0,l.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),d=(0,l.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),u=(0,l.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]);var p=r(58869);let m=(0,l.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var f=r(6475);let h=(0,f.createServerReference)("40b7e9a017084ee5487bcc5eac77e4b80827e85573",f.callServer,void 0,f.findSourceMapURL,"loginUser");var g=r(93853),b=r(16189);let x=i.z.object({email:i.z.string().min(1,"Email or username is required"),password:i.z.string().min(6,"Password must be at least 6 characters")}),y=({children:e,onClick:t,variant:r="primary",size:s="md",type:n="button",disabled:o=!1,className:i="",fullWidth:l=!1})=>(0,a.jsx)("button",{type:n,onClick:t,disabled:o,className:`
        inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 
        ${{primary:"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm",secondary:"bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",outline:"border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-blue-500",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500"}[r]} 
        ${{sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-sm",lg:"px-6 py-3 text-base"}[s]} 
        ${l?"w-full":""} 
        ${o?"opacity-50 cursor-not-allowed":""} 
        ${i}
      `,children:e}),v=({label:e,type:t="text",placeholder:r,error:n,icon:o,showPasswordToggle:i=!1,className:l="",...u})=>{let[p,m]=(0,s.useState)(!1);return(0,a.jsxs)("div",{className:`space-y-1 ${l}`,children:[e&&(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:e}),(0,a.jsxs)("div",{className:"relative",children:[o&&(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(o,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{type:i?p?"text":"password":t,placeholder:r,className:`
            block w-full rounded-lg border border-gray-300 px-3 py-2.5 text-sm 
            placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500
            ${o?"pl-10":""} 
            ${i?"pr-10":""} 
            ${n?"border-red-300 focus:border-red-500 focus:ring-red-500":""}
          `,...u}),i&&(0,a.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>m(!p),children:p?(0,a.jsx)(c,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,a.jsx)(d,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]}),n&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:n})]})},w=()=>{let[e,t]=(0,s.useState)(!1),r=(0,b.useRouter)(),{register:i,handleSubmit:l,formState:{errors:c}}=(0,n.mN)({resolver:(0,o.u)(x)}),d=async e=>{t(!0);try{let t=await h(e);if(!t.success)return void g.oR.error(t.message);g.oR.success(t.message),r.push("/dashboard/blogs")}catch(e){console.error("Login error:",e)}finally{t(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto h-12 w-12 bg-gray-800 rounded-full flex items-center justify-center",children:(0,a.jsx)(u,{className:"h-6 w-6 text-white"})}),(0,a.jsx)("h2",{className:"mt-6 text-3xl font-bold text-gray-900",children:"Sign in to your account"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Welcome back! Please sign in to your account."})]}),(0,a.jsx)("div",{className:"bg-white py-8 px-6 shadow-sm rounded-lg border",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(v,{label:"Email or Username",type:"text",placeholder:"Enter your email or username",icon:p.A,error:c.email?.message,...i("email")}),(0,a.jsx)(v,{label:"Password",type:"password",placeholder:"Enter your password",icon:m,showPasswordToggle:!0,error:c.password?.message,...i("password")}),(0,a.jsx)(y,{type:"submit",className:"bg-gray-800 hover:bg-gray-700 cursor-pointer",size:"lg",fullWidth:!0,disabled:e,onClick:l(d),children:e?"Signing in...":"Sign in"})]})})]})})};function j(){return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(w,{})})}},46625:(e,t,r)=>{Promise.resolve().then(r.bind(r,6507)),Promise.resolve().then(r.bind(r,81799))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65828:(e,t,r)=>{Promise.resolve().then(r.bind(r,67319))},66605:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},67319:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Web Studio Nepal\\\\FreeSpin168\\\\freespin\\\\src\\\\app\\\\auth\\\\AuthPage.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\auth\\AuthPage.tsx","default")},68305:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>n});var a=r(37413);r(61120);var s=r(67319);let n={title:"Blog Management Dashboard",alternates:{canonical:"https://www.freespin168.asia/auth"},description:"Blog Management Dashbaord",keywords:["Blog Management"]},o=()=>(0,a.jsx)("div",{children:(0,a.jsx)(s.default,{})})},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"236x252",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},81799:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var a=r(60687);r(43210);var s=r(72600);let n=()=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s.default,{src:"https://www.googletagmanager.com/gtag/js?id=G-0361KF7G8N",strategy:"afterInteractive"}),(0,a.jsx)(s.default,{id:"google-analytics",strategy:"afterInteractive",children:`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-0361KF7G8N');
          `})]})},85165:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},93392:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"0002875283cbdce215571f9af7fbe61e7e3be8e32c":()=>a.HW,"004f2b5eeef171b19220f18158fad2880f8a8cf70a":()=>a.y4,"40168a97068f09cbb2fc2fef96d6445bef8e5c9466":()=>a.DY,"404a43fe39e2d0727fca93a9fec58abc5921f6408b":()=>a.iC,"40b7e9a017084ee5487bcc5eac77e4b80827e85573":()=>a.Lx});var a=r(19488)},94311:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=r(65239),s=r(48088),n=r(88170),o=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["auth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,68305)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\auth\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\auth\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/auth/page",pathname:"/auth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var a=r(37413),s=r(22950),n=r.n(s),o=r(59758),i=r.n(o),l=r(41649),c=r.n(l),d=r(49840),u=r.n(d),p=r(22987),m=r.n(p),f=r(22376),h=r.n(f),g=r(68726),b=r.n(g);r(22638),r(23902),r(61135);var x=r(4833),y=r(10541);async function v({children:e}){return(0,a.jsxs)("html",{lang:"th",children:[(0,a.jsxs)("head",{children:[(0,a.jsx)("meta",{name:"language",content:"Thai"}),(0,a.jsx)("meta",{httpEquiv:"content-language",content:"th"})]}),(0,a.jsxs)("body",{className:`${h().variable} ${b().variable} ${n().variable} ${i().variable} ${c().variable} ${u().variable} ${m().variable} antialiased`,children:[e,(0,a.jsx)(x.ToastProvider,{}),(0,a.jsx)(y.default,{})]})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,6944,1658,40,4247,7335],()=>r(94311));module.exports=a})();