(()=>{var e={};e.id=2588,e.ids=[2588],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17063:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(56037),a=t.n(s),n=t(85663);let i=new s.Schema({username:{type:String,required:[!0,"Username is required"],unique:!0,trim:!0,minlength:[3,"Username must be at least 3 characters"],maxlength:[30,"Username cannot exceed 30 characters"]},email:{type:String,required:[!0,"Email is required"],unique:!0,lowercase:!0,trim:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Please enter a valid email"]},password:{type:String,required:[!0,"Password is required"],minlength:[6,"Password must be at least 6 characters"],select:!1},firstName:{type:String,trim:!0,maxlength:[50,"First name cannot exceed 50 characters"]},lastName:{type:String,trim:!0,maxlength:[50,"Last name cannot exceed 50 characters"]},bio:{type:String,maxlength:[500,"Bio cannot exceed 500 characters"]},avatar:{type:String,default:null},role:{type:String,enum:["user","admin","moderator"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1}},{timestamps:!0});i.pre("save",async function(e){if(!this.isModified("password"))return e();try{let r=await n.Ay.genSalt(12);this.password=await n.Ay.hash(this.password,r),e()}catch(r){e(r)}}),i.methods.comparePassword=async function(e){return n.Ay.compare(e,this.password)};let o=a().models.User||a().model("User",i)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75745:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(56037),a=t.n(s);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let i=global?.mongoose;i||(i=global.mongoose={conn:null,promise:null});let o=async function(){if(i.conn)return i.conn;i.promise||(i.promise=a().connect(n,{bufferCommands:!1}).then(e=>e));try{i.conn=await i.promise}catch(e){throw i.promise=null,e}return i.conn}},77838:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>h,serverHooks:()=>x,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>v});var s={};t.r(s),t.d(s,{GET:()=>m});var a=t(96559),n=t(48088),i=t(37719),o=t(32190),u=t(43205),c=t.n(u),l=t(75745),p=t(17063);let d=process.env.JWT_SECRET||"your-secret-key";async function m(e){try{let r=e.headers.get("authorization"),t=r?.replace("Bearer ","")||e.cookies.get("session-token")?.value;if(!t)return o.NextResponse.json({success:!1,error:"No authentication token provided"},{status:401});let s=c().verify(t,d);await (0,l.A)();let a=await p.A.findById(s.userId).select("_id email username role isActive firstName lastName");if(!a||!a.isActive)return o.NextResponse.json({success:!1,error:"User not found or inactive"},{status:401});return o.NextResponse.json({success:!0,user:{userId:a._id.toString(),email:a.email,username:a.username,role:a.role,firstName:a.firstName,lastName:a.lastName}})}catch(e){if(e instanceof c().JsonWebTokenError)return o.NextResponse.json({success:!1,error:"Invalid authentication token"},{status:401});return console.error("Authentication verification error:",e),o.NextResponse.json({success:!1,error:"Authentication verification failed"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/verify/route",pathname:"/api/auth/verify",filename:"route",bundlePath:"app/api/auth/verify/route"},resolvedPagePath:"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\api\\auth\\verify\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:v,serverHooks:x}=h;function y(){return(0,i.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:v})}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580,3205,5663],()=>t(77838));module.exports=s})();