import mongoose, { type Document, Schema } from "mongoose"

export interface IComment extends Document {
  content: string
  author: mongoose.Types.ObjectId
  post: mongoose.Types.ObjectId
  parentComment?: mongoose.Types.ObjectId
  replies: mongoose.Types.ObjectId[]
  isApproved: boolean
  likes: mongoose.Types.ObjectId[]
  createdAt: Date
  updatedAt: Date
}

const CommentSchema = new Schema<IComment>(
  {
    content: {
      type: String,
      required: [true, "Comment content is required"],
      trim: true,
      maxlength: [1000, "Comment cannot exceed 1000 characters"],
    },
    author: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: [true, "Author is required"],
    },
    post: {
      type: Schema.Types.ObjectId,
      ref: "Post",
      required: [true, "Post is required"],
    },
    parentComment: {
      type: Schema.Types.ObjectId,
      ref: "Comment",
      default: null,
    },
    replies: [
      {
        type: Schema.Types.ObjectId,
        ref: "Comment",
      },
    ],
    isApproved: {
      type: Boolean,
      default: true, // Auto-approve for now, can be changed for moderation
    },
    likes: [
      {
        type: Schema.Types.ObjectId,
        ref: "User",
      },
    ],
  },
  {
    timestamps: true,
  },
)

// Add reply to parent comment when saving
CommentSchema.post("save", async function () {
  if (this.parentComment) {
    await mongoose.model("Comment").findByIdAndUpdate(this.parentComment, { $addToSet: { replies: this._id } })
  }
})

CommentSchema.index({ post: 1 })
CommentSchema.index({ author: 1 })
CommentSchema.index({ parentComment: 1 })
CommentSchema.index({ isApproved: 1 })

export default mongoose.models.Comment || mongoose.model<IComment>("Comment", CommentSchema)
