import React from "react";
import MainHeader from "../../aboutus/components/MainHeader";
import BasicHeader from "../../home/<USER>/BasicHeader";
import { Check } from "lucide-react";

const Hero: React.FC<{
  t1: string;
  t2: string;
  text1: string;
  imageUrl?: string;
  text2: string;
  list?: { header: string; content: string }[];
}> = ({ t1, t2, text1, text2, list, imageUrl = "/about/se1.png" }) => {
  return (
    <section className="grid grid-cols-1 py-8 gap-x-4 lg:grid-cols-3 text-white">
      <div>
        {/* <div
          className="lg:w-full lg:h-full lg:mx-0 mx-auto md:w-[300px] md:h-[500px] w-[150px] h-[250px] bg-contain bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${imageUrl})` }}
        /> */}

        <div
          className="lg:w-[450px] lg:h-[450px] lg:mx-0 mx-auto md:w-[350px] md:h-[450px] w-[200px] h-[300px] bg-contain bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${imageUrl})` }}
        />
      </div>

      <div className="col-span-2 py-6 space-y-6">
        <div className="space-y-2">
          <MainHeader text={t1} />
          <p className="text-sm md:text-base text-custom-primary">{text1}</p>
        </div>
        <div className="space-y-2">
          <BasicHeader text={t2} className="text-start" />
          <div>
            {text2.split("_").map((item, index) => (
              <p
                key={index}
                className="md:text-base text-sm text-custom-primary"
              >
                {item}
              </p>
            ))}
          </div>
        </div>
        {Array.isArray(list) && (
          <ul className="grid md:grid-cols-2 grid-cols-1 gap-x-6 gap-y-4">
            {list.map((item, index) => {
              return (
                <li className="relative" key={index}>
                  <div className="space-y-4">
                    <h6 className="text-lg font-bold">{item.header}</h6>
                    <p className="text-sm text-custom-primary text-justify">
                      {item.content}
                    </p>
                  </div>
                  <span className="absolute top-2 -left-5 p-0.5 rounded-full bg-[#3159C6] text-black font-bold">
                    <Check size={10} />
                  </span>
                </li>
              );
            })}
          </ul>
        )}
      </div>
    </section>
  );
};

export default Hero;
