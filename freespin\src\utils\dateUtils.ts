import dayjs from "dayjs";

export function formatTimeAgo(date: string | Date): string {
  const now = dayjs();
  const postDate = dayjs(date);
  const diffInDays = now.diff(postDate, "day");
  
  if (diffInDays === 0) {
    return "Today";
  } else if (diffInDays === 1) {
    return "1 day ago";
  } else if (diffInDays < 7) {
    return `${diffInDays} days ago`;
  } else if (diffInDays < 30) {
    const weeks = Math.floor(diffInDays / 7);
    return weeks === 1 ? "1 week ago" : `${weeks} weeks ago`;
  } else if (diffInDays < 365) {
    const months = Math.floor(diffInDays / 30);
    return months === 1 ? "1 month ago" : `${months} months ago`;
  } else {
    const years = Math.floor(diffInDays / 365);
    return years === 1 ? "1 year ago" : `${years} years ago`;
  }
}

export function formatDate(date: string | Date): string {
  return dayjs(date).format("DD/MM/YYYY");
}
