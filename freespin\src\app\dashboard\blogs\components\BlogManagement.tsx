"use client";
import { updatePostStatusAction } from "../actions";
import dayjs from "dayjs";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Edit,
  Plus,
  Search,
  Trash2,
  CheckCircle,
  Clock,
  Star,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "react-toastify";
import { get } from "lodash";

export interface BlogPost {
  _id: string;
  title: string;
  categories: string[];
  // categories: {
  //   _id: string;
  //   name: string;
  //   description?: string;
  // }[];

  tags: string[];
  createdAt?: Date | string | null;
  status: "published" | "draft" | "archived";
  slug?: string;
  isTopNews?: boolean;
}

export interface BlogManagementProps {
  posts: BlogPost[];
  onCreatePost?: () => void;
  onPreviewPost?: (post: BlogPost) => void;
  onEditPost?: (post: BlogPost) => void;
  onDeletePost?: (post: BlogPost) => void;
  onStatusChange?: (post: BlogPost, newStatus: BlogPost["status"]) => void;
  isLoading?: boolean;
  className?: string;
}

const statusFilters = [
  { key: "all", label: "All" },
  { key: "published", label: "Published" },
  { key: "draft", label: "Draft" },
  { key: "archived", label: "Archived" },
] as const;

const statusColors = {
  published: "bg-green-100 text-green-800 hover:bg-green-200",
  draft: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
  archived: "bg-gray-100 text-gray-800 hover:bg-gray-200",
} as const;

export default function BlogManagement({
  posts,
  isLoading = false,
  className = "",
}: BlogManagementProps) {
  const router = useRouter();
  const [activeFilter, setActiveFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");

  const filteredPosts = posts.filter((post) => {
    const matchesFilter =
      activeFilter === "all" || post.status === activeFilter;
    const matchesSearch =
      searchQuery === "" ||
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.categories.some((cat) =>
        cat.toLowerCase().includes(searchQuery.toLowerCase())
      ) ||
      post.tags.some((tag) =>
        tag.toLowerCase().includes(searchQuery.toLowerCase())
      );

    return matchesFilter && matchesSearch;
  });

  const formatDate = (date: Date | string | null | undefined) => {
    if (!date) {
      return "Not published";
    }

    try {
      const dayjsDate = dayjs(date);
      if (!dayjsDate.isValid()) {
        return "Invalid date";
      }

      return dayjsDate.format("MMMM DD, YYYY");
    } catch (error) {
      return "Invalid date";
    }
  };

  const getStatusBadgeClass = (status: BlogPost["status"]) => {
    return `${statusColors[status]} border-0 font-medium`;
  };

  const deletePos = async (id: string) => {
    try {
      // Make direct API call with proper authentication
      const response = await fetch(`/api/posts/${id}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include", // Include cookies for authentication
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        toast.error(result.error || result.message || "Failed to delete post");
        return;
      }

      toast.success(result.message || "Post deleted successfully");

      // Refresh the page to update the list
      window.location.reload();
    } catch (error) {
      console.error("Delete error:", error);
      toast.error("Failed to delete post");
    }
  };

  const togglePostStatus = async (post: BlogPost) => {
    try {
      console.log("Toggling post status for:", post._id, "from", post.status);
      const newStatus = post.status === "published" ? "draft" : "published";
      console.log("New status will be:", newStatus);

      const res = await updatePostStatusAction(post._id, newStatus);
      console.log("API response:", res);

      if (!res.success) {
        toast.error(res.error || "Failed to update post status");
        return;
      }

      toast.success(`Post ${newStatus} successfully`);

      // Use Next.js router refresh instead of hard page reload
      router.refresh();
    } catch (error) {
      console.error("Error toggling post status:", error);
      toast.error("Failed to update post status");
    }
  };

  const toggleTopNews = async (post: BlogPost) => {
    try {
      const response = await fetch(`/api/posts/${post._id}/toggle-top-news`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include", // Add credentials for authentication
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        toast.error(result.message || "Failed to toggle top news status");
        return;
      }

      toast.success(result.message);

      // Refresh the page to show updated top news status for all posts
      window.location.reload();
    } catch (error) {
      console.error("Error toggling top news:", error);
      toast.error("Failed to toggle top news status");
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Blog Management</h1>
        <Button
          onClick={() => router.push("/dashboard/blogs/add")}
          className="bg-black hover:bg-gray-800"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Blog
        </Button>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            {/* Status Filter Tabs */}
            {/* <div className="flex items-center space-x-1">
              {statusFilters.map((filter) => (
                <Button
                  key={filter.key}
                  variant={activeFilter === filter.key ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setActiveFilter(filter.key)}
                  className={
                    activeFilter === filter.key
                      ? "bg-gray-100 text-gray-900 hover:bg-gray-200"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                  }
                >
                  {filter.label}
                </Button>
              ))}
            </div> */}

            {/* Search */}
            <div className="relative w-80">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-gray-50 border-gray-200 focus:bg-white"
              />
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          {/* Table */}
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="border-gray-200">
                  <TableHead className="font-semibold text-gray-900">
                    Title
                  </TableHead>
                  <TableHead className="font-semibold text-gray-900">
                    Categories
                  </TableHead>
                  <TableHead className="font-semibold text-gray-900">
                    Tags
                  </TableHead>
                  <TableHead className="font-semibold text-gray-900">
                    Status
                  </TableHead>
                  <TableHead className="font-semibold text-gray-900">
                    Top News
                  </TableHead>
                  <TableHead className="font-semibold text-gray-900">
                    Published
                  </TableHead>
                  <TableHead className="font-semibold text-gray-900 text-right">
                    Actions
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array.from({ length: 3 }).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                      </TableCell>
                      <TableCell>
                        <div className="h-4 bg-gray-200 rounded animate-pulse w-16"></div>
                      </TableCell>
                      <TableCell>
                        <div className="h-4 bg-gray-200 rounded animate-pulse w-16"></div>
                      </TableCell>
                      <TableCell>
                        <div className="h-6 bg-gray-200 rounded animate-pulse w-16"></div>
                      </TableCell>
                      <TableCell>
                        <div className="h-6 bg-gray-200 rounded animate-pulse w-16"></div>
                      </TableCell>
                      <TableCell>
                        <div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
                      </TableCell>
                      <TableCell>
                        <div className="flex justify-end space-x-2">
                          <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
                          <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
                          <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : filteredPosts.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={8}
                      className="text-center py-8 text-gray-500"
                    >
                      {searchQuery
                        ? "No posts found matching your search."
                        : "No posts found."}
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredPosts.map((post) => (
                    <TableRow key={post._id} className="hover:bg-gray-50">
                      <TableCell className="font-medium max-w-xs">
                        <div className="truncate" title={post.title}>
                          {post.title}
                        </div>
                      </TableCell>
                      <TableCell>
                        {post.categories.length > 0 ? (
                          <div className="flex flex-wrap gap-1">
                            {post.categories
                              .slice(0, 2)
                              .map((category, index) => (
                                <Badge
                                  key={index}
                                  variant="outline"
                                  className="text-xs"
                                >
                                  {get(category, "name", "N/A")}
                                </Badge>
                              ))}
                            {post.categories.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{post.categories.length - 2}
                              </Badge>
                            )}
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>

                      <TableCell>
                        {post.tags.length > 0 ? (
                          <div className="flex flex-wrap gap-1">
                            {post.tags.slice(0, 2).map((tag, index) => (
                              <Badge
                                key={index}
                                variant="secondary"
                                className="text-xs"
                              >
                                {tag}
                              </Badge>
                            ))}
                            {post.tags.length > 2 && (
                              <Badge variant="secondary" className="text-xs">
                                +{post.tags.length - 2}
                              </Badge>
                            )}
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusBadgeClass(post.status)}>
                          {post.status.charAt(0).toUpperCase() +
                            post.status.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Star
                            className={`h-4 w-4 ${
                              post.isTopNews
                                ? "text-yellow-500 fill-yellow-500"
                                : "text-gray-300"
                            }`}
                          />
                          <span className="ml-2 text-sm text-gray-600">
                            {post.isTopNews ? "Yes" : "No"}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="text-gray-600">
                        {formatDate(post.createdAt)}
                      </TableCell>

                      <TableCell>
                        <div className="flex items-center justify-end space-x-2">
                          {/* Top News Toggle Button */}
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              toggleTopNews(post);
                            }}
                            className={`h-8 px-3 ${
                              post.isTopNews
                                ? "text-yellow-600 hover:text-yellow-700 hover:bg-yellow-50"
                                : "text-gray-600 hover:text-gray-700 hover:bg-gray-50"
                            }`}
                            title={
                              post.isTopNews
                                ? "Remove from Top News"
                                : "Mark as Top News"
                            }
                          >
                            <Star
                              className={`h-4 w-4 mr-1 ${
                                post.isTopNews ? "fill-current" : ""
                              }`}
                            />
                            {post.isTopNews ? "Remove" : "Top News"}
                          </Button>

                          {/* Publish/Unpublish Button */}
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              togglePostStatus(post);
                            }}
                            className={`h-8 px-3 ${
                              post.status === "published"
                                ? "text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                                : "text-green-600 hover:text-green-700 hover:bg-green-50"
                            }`}
                          >
                            {post.status === "published" ? (
                              <>
                                <Clock className="h-4 w-4 mr-1" />
                                Unpublish
                              </>
                            ) : (
                              <>
                                <CheckCircle className="h-4 w-4 mr-1" />
                                Publish
                              </>
                            )}
                          </Button>
                          {/* Edit Button */}
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              router.push(`/dashboard/blogs/edit/${post.slug}`)
                            }
                            className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>

                          {/* Delete Button */}
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              deletePos(post._id);
                            }}
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
