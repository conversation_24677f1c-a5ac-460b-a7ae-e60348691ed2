import { NextResponse } from "next/server";
import { z } from "zod";
import connectDB from "@/lib/mongodb";
import Category from "@/models/Category";
import {
  withAdmin,
  withAuth,
  withLogging,
  type AuthenticatedRequest,
} from "@/lib/middleware";

const createCategorySchema = z.object({
  name: z.string().min(1, "Name is required").max(50, "Name too long").trim(),
  description: z.string().max(200, "Description too long").optional(),
  color: z
    .string()
    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, "Invalid color format")
    .optional(),
});

// GET /api/categories - Get all categories (Public endpoint)
export async function GET() {
  try {
    await connectDB();

    const categories = await Category.find({ isActive: true })
      .sort({ name: 1 })
      .lean();

    return NextResponse.json({
      success: true,
      data: { categories },
    });
  } catch (error) {
    console.error("Error fetching categories:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch categories" },
      { status: 500 }
    );
  }
}

// POST /api/categories - Create a new category (Admin only with middleware)
export const POST = withLogging(
  withAuth(async (request: AuthenticatedRequest, context: any) => {
    try {
      await connectDB();

      const body = await request.json();
      const validatedData = createCategorySchema.parse(body);

      // Check if category already exists (case-insensitive)
      const existingCategory = await Category.findOne({
        name: { $regex: new RegExp(`^${validatedData.name}$`, "i") },
      });

      if (existingCategory) {
        return NextResponse.json(
          { success: false, error: "Category already exists" },
          { status: 400 }
        );
      }

      // Create new category
      const category = new Category(validatedData);
      await category.save();

      return NextResponse.json(
        {
          success: true,
          message: "Category created successfully",
          data: { category },
          createdBy: request.user!.username,
        },
        { status: 201 }
      );
    } catch (error: any) {
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { success: false, error: "Validation failed", details: error.errors },
          { status: 400 }
        );
      }

      // Handle MongoDB duplicate key errors
      if (error.code === 11000 && error.keyPattern?.name) {
        return NextResponse.json(
          { success: false, error: "Category name already exists" },
          { status: 400 }
        );
      }

      console.error("Error creating category:", error);
      return NextResponse.json(
        { success: false, error: "Failed to create category" },
        { status: 500 }
      );
    }
  })
  // withAdmin(async (request: AuthenticatedRequest, context: any) => {
);
