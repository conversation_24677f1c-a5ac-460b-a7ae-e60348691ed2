(()=>{var e={};e.id=9492,e.ids=[9492],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4833:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ToastProvider() from the server but ToastProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\shared\\ToastProvider.tsx","ToastProvider")},6507:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>s});var n=r(60687),o=r(93853);function s(){return(0,n.jsx)(o.N9,{"aria-label":"Toast notifications",position:"top-right",autoClose:3e3})}r(25806)},10541:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Web Studio Nepal\\\\FreeSpin168\\\\freespin\\\\src\\\\components\\\\GoogleAnalytics\\\\GoogleAnalytics.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\components\\GoogleAnalytics\\GoogleAnalytics.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16523:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>c,tree:()=>l});var n=r(65239),o=r(48088),s=r(88170),i=r.n(s),a=r(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,p=[],u={require:r,loadChunk:()=>Promise.resolve()},c=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33473:(e,t,r)=>{Promise.resolve().then(r.bind(r,4833)),Promise.resolve().then(r.bind(r,10541))},33873:e=>{"use strict";e.exports=require("path")},46625:(e,t,r)=>{Promise.resolve().then(r.bind(r,6507)),Promise.resolve().then(r.bind(r,81799))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66605:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},81799:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});var n=r(60687);r(43210);var o=r(72600);let s=()=>(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.default,{src:"https://www.googletagmanager.com/gtag/js?id=G-0361KF7G8N",strategy:"afterInteractive"}),(0,n.jsx)(o.default,{id:"google-analytics",strategy:"afterInteractive",children:`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-0361KF7G8N');
          `})]})},85165:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>P});var n=r(37413),o=r(22950),s=r.n(o),i=r(59758),a=r.n(i),d=r(41649),l=r.n(d),p=r(49840),u=r.n(p),c=r(22987),v=r.n(c),h=r(22376),m=r.n(h),b=r(68726),f=r.n(b);r(22638),r(23902),r(61135);var g=r(4833),x=r(10541);async function P({children:e}){return(0,n.jsxs)("html",{lang:"th",children:[(0,n.jsxs)("head",{children:[(0,n.jsx)("meta",{name:"language",content:"Thai"}),(0,n.jsx)("meta",{httpEquiv:"content-language",content:"th"})]}),(0,n.jsxs)("body",{className:`${m().variable} ${f().variable} ${s().variable} ${a().variable} ${l().variable} ${u().variable} ${v().variable} antialiased`,children:[e,(0,n.jsx)(g.ToastProvider,{}),(0,n.jsx)(x.default,{})]})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,6944],()=>r(16523));module.exports=n})();