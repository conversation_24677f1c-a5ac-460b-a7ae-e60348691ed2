(()=>{var e={};e.id=5593,e.ids=[5593],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33772:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>u,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>g});var i={};r.r(i),r.d(i,{GET:()=>c});var s=r(96559),a=r(48088),n=r(37719),o=r(32190),l=r(75745),p=r(55530);async function c(e){try{await (0,l.A)();let{searchParams:t}=new URL(e.url),r=Number.parseInt(t.get("page")||"1"),i=Number.parseInt(t.get("limit")||"10"),s=t.get("category"),a=t.get("tag"),n=t.get("search"),c={status:"published",isPublished:!0,isBlog:!0};s&&(c.categories={$in:[s]}),a&&(c.tags={$in:[a]}),n&&(c.$or=[{title:{$regex:n,$options:"i"}},{description:{$regex:n,$options:"i"}},{metaTitle:{$regex:n,$options:"i"}},{metaDescription:{$regex:n,$options:"i"}}]);let u=(r-1)*i,d=await p.A.find(c).sort({publishedAt:-1,createdAt:-1}).skip(u).limit(i).select({title:1,description:1,banner:1,categories:1,tags:1,publishedAt:1,createdAt:1,readTime:1,views:1,metaTitle:1,metaDescription:1,_id:1}).lean(),g=await p.A.countDocuments(c);return o.NextResponse.json({success:!0,data:{posts:d,pagination:{page:r,limit:i,total:g,pages:Math.ceil(g/i),hasNext:r<Math.ceil(g/i),hasPrev:r>1}}})}catch(e){return console.error("Error fetching public posts:",e),o.NextResponse.json({success:!1,error:"Failed to fetch posts"},{status:500})}}let u=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/public/posts/route",pathname:"/api/public/posts",filename:"route",bundlePath:"app/api/public/posts/route"},resolvedPagePath:"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\api\\public\\posts\\route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:d,workUnitAsyncStorage:g,serverHooks:h}=u;function m(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:g})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55530:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var i=r(56037),s=r.n(i);let a=new i.Schema({title:{type:String,required:[!0,"Post title is required"],trim:!0,maxlength:[1e3,"Title cannot exceed 200 characters"]},slug:{type:String,trim:!0,lowercase:!0},canonicalUrl:{type:String,trim:!0,validate:{validator:e=>!e||/^https?:\/\/.+/.test(e),message:"Canonical URL must be a valid URL"}},existingUrl:{type:Boolean,default:!1},content:{type:String,required:[!0,"Post content is required"]},excerpt:{type:String,trim:!0,maxlength:[1e3,"Excerpt cannot exceed 300 characters"]},description:{type:String},author:{type:String,trim:!0},isBlog:{type:Boolean,default:!0},categories:[{type:s().Schema.Types.ObjectId,ref:"Category"}],tags:[{type:String,trim:!0,lowercase:!0}],metaTitle:{type:String,required:[!0,"Meta title is required"],maxlength:[1e3,"Meta title cannot exceed 60 characters"]},metaDescription:{type:String,required:[!0,"Meta description is required"],maxlength:[1e3,"Meta description cannot exceed 160 characters"]},metaKeywords:{type:String,required:[!0,"Meta keywords are required"],maxlength:[1e3,"Meta keywords cannot exceed 200 characters"]},banner:{type:String,required:[!0,"Banner image is required"],trim:!0},status:{type:String,enum:["draft","published","archived"],default:"draft"},isPublished:{type:Boolean,default:!1},publishedAt:{type:Date,default:null},views:{type:Number,default:0},readTime:{type:Number,default:1},isTopNews:{type:Boolean,default:!1}},{timestamps:!0});a.pre("save",function(e){if(this.isModified("title")&&!this.slug&&(this.slug=this.title.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,"")),this.isModified("content")){let e=(this.content||"").split(/\s+/).length;this.readTime=Math.ceil(e/200)}this.isModified("status")&&"published"===this.status&&!this.publishedAt&&(this.publishedAt=new Date,this.isPublished=!0),e()}),a.index({categories:1}),a.index({tags:1}),a.index({status:1}),a.index({isPublished:1}),a.index({publishedAt:-1}),a.index({"banner.title":1}),a.index({title:"text",content:"text",metaTitle:"text",metaDescription:"text"});let n=s().models.Post||s().model("Post",a)},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),s=r.n(i);let a=process.env.MONGODB_URI;if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let n=global?.mongoose;n||(n=global.mongoose={conn:null,promise:null});let o=async function(){if(n.conn)return n.conn;n.promise||(n.promise=s().connect(a,{bufferCommands:!1}).then(e=>e));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4447,580],()=>r(33772));module.exports=i})();