"use client";
// import React from "react";
// import Link from "next/link";
// import Image from "next/image";

// const Footer: React.FC = () => {
//   const siteMapLinks = [
//     { name: t("homepage"), href: "/" },
//     { name: "About", href: "/about" },
//     { name: "Casino", href: "/casino" },
//     { name: "Games", href: "/games" },
//     { name: "Blog", href: "/blog" },
//   ];

//   const quickLinks = [
//     { name: "J<PERSON><PERSON>", href: "/jili" },
//     { name: t("sexygaming"), href: "/sexy-gaming" },
//     { name: "PGSLOT", href: "/pgslot" },
//     { name: t("sagaming"), href: "/sa-gaming" },
//     { name: t("sboportal"), href: "/sbo-portal" },
//     { name: "LALIKA", href: "/lalika" },
//   ];

//   return (
//     <footer className="bg-black text-white py-12 px-6">
//       <div className="max-w-7xl mx-auto">
//         <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
//           {/* Logo and Description */}
//           <div className="lg:col-span-1">
//             <Image
//               src="/logo.png"
//               alt={t("websitelogo")}
//               height={100}
//               width={100}
//             />
//             <p className="text-gray-300 leading-relaxed text-sm max-w-md">
//               Freespin168 is one of Thailand's top-rated and most trusted online
//               gambling platforms. Since 2013, we've been a leading force in both
//               casino and sports betting. With over 8 years of excellence,
//               countless players across Thailand have recognized Freespin168 as a
//               premier destination for safe, exciting, and reliable online
//               betting.
//             </p>
//           </div>

//           {/* Site Map */}
//           <div className="lg:col-span-1">
//             <h3 className="text-white font-semibold text-lg mb-6">{t("sitemap")}</h3>
//             <ul className="space-y-3">
//               {siteMapLinks.map((link) => (
//                 <li key={link.name}>
//                   <Link
//                     href={link.href}
//                     className="text-gray-400 hover:text-white transition-colors duration-200 text-sm"
//                   >
//                     {link.name}
//                   </Link>
//                 </li>
//               ))}
//             </ul>
//           </div>

//           {/* Quick Links */}
//           <div className="lg:col-span-1">
//             <h3 className="text-white font-semibold text-lg mb-6">
//               Quick Links
//             </h3>
//             <ul className="space-y-3">
//               {quickLinks.map((link) => (
//                 <li key={link.name}>
//                   <Link
//                     href={link.href}
//                     className="text-gray-400 hover:text-white transition-colors duration-200 text-sm"
//                   >
//                     {link.name}
//                   </Link>
//                 </li>
//               ))}
//             </ul>
//           </div>
//         </div>

//         {/* Bottom Border */}
//         <div className="mt-12 pt-8 border-t border-gray-800">
//           <div className="text-center text-gray-500 text-xs">
//             © 2024 Freespin168. All rights reserved.
//           </div>
//         </div>
//       </div>
//     </footer>
//   );
// };

// export default Footer;

import React from "react";
import Link from "next/link";
import Image from "next/image";

const Footer: React.FC = () => {
  const siteMapLinks = [
    { name: "หน้าแรก", href: "/" },
    { name: "เกี่ยวกับ", href: "/aboutus" },
    { name: "คาสิโน", href: "/wm-casino" },
    { name: "เกม", href: "/pg-games" },
    { name: "บล็อก", href: "/blog" },
  ];

  const quickLinks = [
    { name: "JILI", href: "/jili" },
    // { name: t("sexygaming"), href: "/sexy" },
    { name: "PGSLOT", href: "/pg-games" },
    // { name: t("sagaming"), href: "/sa-gaming" },
    { name: "SBO Portal", href: "/sbo-bet" },
    { name: "LALIKA", href: "/lalika" },
  ];

  return (
    <footer className="bg-black text-white py-12 ">
      <div className="container mx-auto px-3 md:px-[4rem]">
        <div className="mt-12 pt-8 border-t border-gray-800"></div>
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 lg:gap-12">
          {/* Logo and Description */}
          <div className="lg:col-span-6">
            <div className="flex items-center mb-6">
              <Image
                src="/logo.png"
                alt="Website LOGO"
                height={100}
                width={100}
              />
            </div>
            <p className="text-gray-300 leading-relaxed jakarta text-sm md:text-base max-w-xl">
              Freespin168
              เป็นหนึ่งในแพลตฟอร์มการพนันออนไลน์ที่ได้รับความนิยมและความเชื่อถือสูงสุดในประเทศไทย
              ตั้งแต่ปี 2013 เราคือผู้นำในวงการคาสิโนและการเดิมพันกีฬา
              ด้วยประสบการณ์กว่า 8 ปีที่ยอดเยี่ยม
              ผู้เล่นจำนวนมากทั่วประเทศต่างยอมรับว่า Freespin168
              คือจุดหมายปลายทางชั้นนำสำหรับการเดิมพันออนไลน์ที่ปลอดภัย
              น่าตื่นเต้น และเชื่อถือได้
            </p>
          </div>

          {/* Site Map */}
          <div className="lg:col-span-3">
            <h3 className="text-white font-bold jakarta  text-sm md:text-lg lg:text-xl mb-6">
              แผนผังเว็บไซต์
            </h3>
            <ul className="space-y-3">
              {siteMapLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 jakarta text-sm lg:text-base hover:text-white transition-colors duration-200 "
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Quick Links */}
          <div className="lg:col-span-3">
            <h3 className="text-white font-bold jakarta  text-sm md:text-lg lg:text-xl mb-6">
              ลิงก์ด่วน
            </h3>
            <ul className="space-y-3">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors duration-200 jakarta text-sm lg:text-base"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Border */}
        <div className="mt-12 pt-8 border-t border-gray-800">
          <div className="text-center text-gray-500 text-xs">
            &copy; {new Date().getFullYear()} Freespin168. สงวนลิขสิทธิ์ทั้งหมด.
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
