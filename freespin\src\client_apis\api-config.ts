import { revalidateTag } from "next/cache";
import { authenticatedFetch, publicFetch, isApiError } from "./api-wrapper";

interface IProps {
  endpoint: string;
  tags: string[];
  queryParams?: Record<string, string>;
  revalidate?: number | false;
  requireAuth?: boolean;
}

export interface ApiResult<T> {
  success: boolean;
  data?: T;
  message: string;
  status?: number;
}

const buildQuery = (params?: Record<string, string>) =>
  params ? "?" + new URLSearchParams(params).toString() : "";

export const createApiConfig = <T, CreateInput = any, UpdateInput = any>({
  endpoint,
  tags,
  queryParams,
  revalidate,
  requireAuth = true, // Default to requiring auth
}: IProps) => {
  // Choose the appropriate fetch function based on auth requirement
  const fetchFn = requireAuth ? authenticatedFetch : publicFetch;

  return {
    async getAll(): Promise<ApiResult<T[]>> {
      const response = await fetchFn<ApiResult<T[]>>(
        `${endpoint}${buildQuery(queryParams)}`,
        {
          nextOptions: {
            tags: [endpoint, ...tags],
            revalidate,
          },
        }
      );

      if (isApiError(response)) {
        return {
          success: false,
          message: response.message,
          status: response.status,
        };
      }

      return response;
    },

    async getById(id: string): Promise<ApiResult<T>> {
      const response = await fetchFn<ApiResult<T>>(`${endpoint}/${id}`, {
        nextOptions: {
          tags: [`${endpoint}-${id}`, ...tags],
        },
      });

      if (isApiError(response)) {
        return {
          success: false,
          message: response.message,
          status: response.status,
        };
      }

      return response;
    },

    async createData(body: CreateInput): Promise<ApiResult<T>> {
      const headers =
        body instanceof FormData
          ? { "Content-Type": "multipart/form-data" }
          : { "Content-Type": "application/json" };

      const response = await fetchFn<ApiResult<T>>(endpoint, {
        method: "POST",
        body: body instanceof FormData ? body : JSON.stringify(body),
        headers,
      });

      if (isApiError(response)) {
        return {
          success: false,
          message: response.message,
          status: response.status,
        };
      }

      // Revalidate cache tags
      tags.forEach((tag) => revalidateTag(tag));
      revalidateTag(endpoint);

      return response;
    },

    async updateData(id: string, body: UpdateInput): Promise<ApiResult<T>> {
      const headers =
        body instanceof FormData
          ? undefined
          : { "Content-Type": "application/json" };

      const response = await fetchFn<ApiResult<T>>(`${endpoint}/${id}`, {
        method: "PATCH",
        body: body instanceof FormData ? body : JSON.stringify(body),
        headers,
      });

      if (isApiError(response)) {
        return {
          success: false,
          message: response.message,
          status: response.status,
        };
      }

      // Revalidate specific item and collection
      tags.forEach((tag) => revalidateTag(tag));
      revalidateTag(endpoint);
      revalidateTag(`${endpoint}-${id}`);

      return response;
    },

    async deleteDataById(id: string): Promise<ApiResult<null>> {
      const response = await fetchFn<ApiResult<null>>(`${endpoint}/${id}`, {
        method: "DELETE",
      });

      if (isApiError(response)) {
        return {
          success: false,
          message: response.message,
          status: response.status,
        };
      }

      // Revalidate cache tags
      tags.forEach((tag) => revalidateTag(tag));
      revalidateTag(endpoint);
      revalidateTag(`${endpoint}-${id}`);

      return response;
    },
  };
};
