"use client";
import React from "react";
import Slider from "react-slick";
import { slickSettings } from "./home.obj";
import { Button } from "@/components/ui/button";
import BasicHeader from "./BasicHeader";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Link from "next/link";

const CasinoCard = ({
  text,
  imgUrl,
  url,
}: {
  text: string;
  imgUrl: string;
  url: string;
}) => {
  return (
    <div>
      <div
        className="m-2 w-full h-[300px] bg-contain bg-center bg-no-repeat"
        style={{
          backgroundImage: `url(${imgUrl})`,
        }}
      />
      <div className="flex flex-col items-center justify-cener gap-y-2">
        <h6 className="text-2xl text-center  font-semibold">{text}</h6>
        <Link
          href={url}
          className="bg-[#07BC0C] px-4 py-1 rounded-full cursor-poiner mx-auto w-auto text-white hover:bg-[#07BC0C]/90"
        >
          Play now !
        </Link>
      </div>
    </div>
  );
};
const PrevArrow = ({
  onClick,
}: {
  onClick?: React.MouseEventHandler<HTMLButtonElement>;
}) => (
  <button
    className="absolute left-0 top-[40%] z-10 bg-fuchsia-600 p-2 rounded-full text-white"
    onClick={onClick}
  >
    <ChevronLeft />
  </button>
);

const NextArrow = ({
  onClick,
}: {
  onClick?: React.MouseEventHandler<HTMLButtonElement>;
}) => (
  <button
    className="absolute cursor-pointer right-0 top-[40%] z-10 bg-fuchsia-600 p-2 rounded-full text-white"
    onClick={onClick}
  >
    <ChevronRight />
  </button>
);
const OnlineCasino = () => {
  const obj = [
    { text: "Sexy", url: "/sexy", imgUrl: "/sexy/sexy.png" },
    { text: "SA Gaming", url: "/sa", imgUrl: "/sa/saimg.png" },
    {
      text: "Dream Gaming",
      url: "/dream-gaming",
      imgUrl: "/dream-gaming/girl.png",
    },
    {
      text: "Evolution Lobby",
      url: "/evolution-lobby",
      imgUrl: "/evolution-lobby/girl.png",
    },
    { text: "SV388 Portal", url: "/sv388", imgUrl: "/sv388/sv388.png" },
    { text: "AllBet Portal", url: "all-bets", imgUrl: "/all-bet.png" },
    {
      text: "Pretty Game Portal",
      url: "/pretty-game",
      imgUrl: "/pretty/girl.png",
    },
    {
      text: "Pragmatic Play",
      url: "/pragmatic-play",
      imgUrl: "/pragmatic/girl.png",
    },
    { text: "WmCasino", url: "/wm-casino", imgUrl: "/wmcasino.png" },
    { text: "Big Gaming", url: "/big-game", imgUrl: "/bigGame/girl.png" },
    {
      text: "OnGaming-Lobby",
      url: "/on-gaming-lobby",
      imgUrl: "/gaminglobby/girl.png",
    },
  ];

  const settings = {
    ...slickSettings,
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
  };

  return (
    <section className="py-12 container mx-auto px-3 md:px-[4rem] text-white bg-black">
      <BasicHeader text="คาสิโนออนไลน์" className="mb-7" />
      <div>
        <Slider {...settings}>
          {obj.map((item, index) => (
            <CasinoCard {...item} key={index} />
          ))}
        </Slider>
      </div>
    </section>
  );
};

export default OnlineCasino;
