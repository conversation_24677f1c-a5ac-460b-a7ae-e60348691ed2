(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/app/dashboard/blogs/data:940fed [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"607c10ea8628475a8cbfb06ec6fe9153598d2e744a":"updatePostStatusAction"},"src/app/dashboard/blogs/actions.ts",""] */ __turbopack_context__.s({
    "updatePostStatusAction": (()=>updatePostStatusAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var updatePostStatusAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("607c10ea8628475a8cbfb06ec6fe9153598d2e744a", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "updatePostStatusAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/badge.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Badge": (()=>Badge),
    "badgeVariants": (()=>badgeVariants)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slot/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
;
;
;
const badgeVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cva"])("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden", {
    variants: {
        variant: {
            default: "border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",
            secondary: "border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",
            destructive: "border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
            outline: "text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"
        }
    },
    defaultVariants: {
        variant: "default"
    }
});
function Badge({ className, variant, asChild = false, ...props }) {
    const Comp = asChild ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slot"] : "span";
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Comp, {
        "data-slot": "badge",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(badgeVariants({
            variant
        }), className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/badge.tsx",
        lineNumber: 38,
        columnNumber: 5
    }, this);
}
_c = Badge;
;
var _c;
__turbopack_context__.k.register(_c, "Badge");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/card.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Card": (()=>Card),
    "CardAction": (()=>CardAction),
    "CardContent": (()=>CardContent),
    "CardDescription": (()=>CardDescription),
    "CardFooter": (()=>CardFooter),
    "CardHeader": (()=>CardHeader),
    "CardTitle": (()=>CardTitle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
;
function Card({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
_c = Card;
function CardHeader({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-header",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 20,
        columnNumber: 5
    }, this);
}
_c1 = CardHeader;
function CardTitle({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-title",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("leading-none font-semibold", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 33,
        columnNumber: 5
    }, this);
}
_c2 = CardTitle;
function CardDescription({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-description",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 43,
        columnNumber: 5
    }, this);
}
_c3 = CardDescription;
function CardAction({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-action",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("col-start-2 row-span-2 row-start-1 self-start justify-self-end", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 53,
        columnNumber: 5
    }, this);
}
_c4 = CardAction;
function CardContent({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-content",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("px-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 66,
        columnNumber: 5
    }, this);
}
_c5 = CardContent;
function CardFooter({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-footer",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex items-center px-6 [.border-t]:pt-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 76,
        columnNumber: 5
    }, this);
}
_c6 = CardFooter;
;
var _c, _c1, _c2, _c3, _c4, _c5, _c6;
__turbopack_context__.k.register(_c, "Card");
__turbopack_context__.k.register(_c1, "CardHeader");
__turbopack_context__.k.register(_c2, "CardTitle");
__turbopack_context__.k.register(_c3, "CardDescription");
__turbopack_context__.k.register(_c4, "CardAction");
__turbopack_context__.k.register(_c5, "CardContent");
__turbopack_context__.k.register(_c6, "CardFooter");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/input.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Input": (()=>Input)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
;
function Input({ className, type, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
        type: type,
        "data-slot": "input",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm", "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]", "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/input.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
_c = Input;
;
var _c;
__turbopack_context__.k.register(_c, "Input");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/table.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Table": (()=>Table),
    "TableBody": (()=>TableBody),
    "TableCaption": (()=>TableCaption),
    "TableCell": (()=>TableCell),
    "TableFooter": (()=>TableFooter),
    "TableHead": (()=>TableHead),
    "TableHeader": (()=>TableHeader),
    "TableRow": (()=>TableRow)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
function Table({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "table-container",
        className: "relative w-full overflow-x-auto",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
            "data-slot": "table",
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("w-full caption-bottom text-sm", className),
            ...props
        }, void 0, false, {
            fileName: "[project]/src/components/ui/table.tsx",
            lineNumber: 13,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/table.tsx",
        lineNumber: 9,
        columnNumber: 5
    }, this);
}
_c = Table;
function TableHeader({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
        "data-slot": "table-header",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("[&_tr]:border-b", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/table.tsx",
        lineNumber: 24,
        columnNumber: 5
    }, this);
}
_c1 = TableHeader;
function TableBody({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
        "data-slot": "table-body",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("[&_tr:last-child]:border-0", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/table.tsx",
        lineNumber: 34,
        columnNumber: 5
    }, this);
}
_c2 = TableBody;
function TableFooter({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tfoot", {
        "data-slot": "table-footer",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("bg-muted/50 border-t font-medium [&>tr]:last:border-b-0", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/table.tsx",
        lineNumber: 44,
        columnNumber: 5
    }, this);
}
_c3 = TableFooter;
function TableRow({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
        "data-slot": "table-row",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/table.tsx",
        lineNumber: 57,
        columnNumber: 5
    }, this);
}
_c4 = TableRow;
function TableHead({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
        "data-slot": "table-head",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/table.tsx",
        lineNumber: 70,
        columnNumber: 5
    }, this);
}
_c5 = TableHead;
function TableCell({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
        "data-slot": "table-cell",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/table.tsx",
        lineNumber: 83,
        columnNumber: 5
    }, this);
}
_c6 = TableCell;
function TableCaption({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("caption", {
        "data-slot": "table-caption",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground mt-4 text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/table.tsx",
        lineNumber: 99,
        columnNumber: 5
    }, this);
}
_c7 = TableCaption;
;
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7;
__turbopack_context__.k.register(_c, "Table");
__turbopack_context__.k.register(_c1, "TableHeader");
__turbopack_context__.k.register(_c2, "TableBody");
__turbopack_context__.k.register(_c3, "TableFooter");
__turbopack_context__.k.register(_c4, "TableRow");
__turbopack_context__.k.register(_c5, "TableHead");
__turbopack_context__.k.register(_c6, "TableCell");
__turbopack_context__.k.register(_c7, "TableCaption");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/dashboard/blogs/components/BlogManagement.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>BlogManagement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$data$3a$940fed__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/dashboard/blogs/data:940fed [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/dayjs/dayjs.min.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/table.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$pen$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Edit$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/square-pen.js [app-client] (ecmascript) <export default as Edit>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-client] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-client] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js [app-client] (ecmascript) <export default as Trash2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-client] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/star.js [app-client] (ecmascript) <export default as Star>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2f$get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lodash/get.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
const statusFilters = [
    {
        key: "all",
        label: "All"
    },
    {
        key: "published",
        label: "Published"
    },
    {
        key: "draft",
        label: "Draft"
    },
    {
        key: "archived",
        label: "Archived"
    }
];
const statusColors = {
    published: "bg-green-100 text-green-800 hover:bg-green-200",
    draft: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
    archived: "bg-gray-100 text-gray-800 hover:bg-gray-200"
};
function BlogManagement({ posts, isLoading = false, className = "" }) {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [activeFilter, setActiveFilter] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("all");
    const [searchQuery, setSearchQuery] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const filteredPosts = posts.filter((post)=>{
        const matchesFilter = activeFilter === "all" || post.status === activeFilter;
        const matchesSearch = searchQuery === "" || post.title.toLowerCase().includes(searchQuery.toLowerCase()) || post.categories.some((cat)=>cat.toLowerCase().includes(searchQuery.toLowerCase())) || post.tags.some((tag)=>tag.toLowerCase().includes(searchQuery.toLowerCase()));
        return matchesFilter && matchesSearch;
    });
    const formatDate = (date)=>{
        if (!date) {
            return "Not published";
        }
        try {
            const dayjsDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$dayjs$2f$dayjs$2e$min$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(date);
            if (!dayjsDate.isValid()) {
                return "Invalid date";
            }
            return dayjsDate.format("MMMM DD, YYYY");
        } catch (error) {
            return "Invalid date";
        }
    };
    const getStatusBadgeClass = (status)=>{
        return `${statusColors[status]} border-0 font-medium`;
    };
    const deletePos = async (id)=>{
        try {
            // Make direct API call with proper authentication
            const response = await fetch(`/api/posts/${id}`, {
                method: "DELETE",
                headers: {
                    "Content-Type": "application/json"
                },
                credentials: "include"
            });
            const result = await response.json();
            if (!response.ok || !result.success) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(result.error || result.message || "Failed to delete post");
                return;
            }
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(result.message || "Post deleted successfully");
            // Refresh the page to update the list
            window.location.reload();
        } catch (error) {
            console.error("Delete error:", error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Failed to delete post");
        }
    };
    const togglePostStatus = async (post)=>{
        try {
            console.log("Toggling post status for:", post._id, "from", post.status);
            const newStatus = post.status === "published" ? "draft" : "published";
            console.log("New status will be:", newStatus);
            const res = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$data$3a$940fed__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["updatePostStatusAction"])(post._id, newStatus);
            console.log("API response:", res);
            if (!res.success) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(res.error || "Failed to update post status");
                return;
            }
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`Post ${newStatus} successfully`);
            // Use Next.js router refresh instead of hard page reload
            router.refresh();
        } catch (error) {
            console.error("Error toggling post status:", error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Failed to update post status");
        }
    };
    const toggleTopNews = async (post)=>{
        try {
            const response = await fetch(`/api/posts/${post._id}/toggle-top-news`, {
                method: "PATCH",
                headers: {
                    "Content-Type": "application/json"
                }
            });
            const result = await response.json();
            if (!result.success) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(result.message || "Failed to toggle top news status");
                return;
            }
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(result.message);
            router.refresh();
        } catch (error) {
            console.error("Error toggling top news:", error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("Failed to toggle top news status");
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `space-y-6 ${className}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-2xl font-bold text-gray-900",
                        children: "Blog Management"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                        lineNumber: 197,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                        onClick: ()=>router.push("/dashboard/blogs/add"),
                        className: "bg-black hover:bg-gray-800",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                className: "h-4 w-4 mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                lineNumber: 202,
                                columnNumber: 11
                            }, this),
                            "Create Blog"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                        lineNumber: 198,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                lineNumber: 196,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                        className: "pb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "relative w-80",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                        className: "absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                        lineNumber: 232,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                        placeholder: "Search...",
                                        value: searchQuery,
                                        onChange: (e)=>setSearchQuery(e.target.value),
                                        className: "pl-10 bg-gray-50 border-gray-200 focus:bg-white"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                        lineNumber: 233,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                lineNumber: 231,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                            lineNumber: 210,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                        lineNumber: 209,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                        className: "p-0",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "overflow-x-auto",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Table"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHeader"], {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                            className: "border-gray-200",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHead"], {
                                                    className: "font-semibold text-gray-900",
                                                    children: "Title"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                    lineNumber: 249,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHead"], {
                                                    className: "font-semibold text-gray-900",
                                                    children: "Categories"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                    lineNumber: 252,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHead"], {
                                                    className: "font-semibold text-gray-900",
                                                    children: "Tags"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                    lineNumber: 255,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHead"], {
                                                    className: "font-semibold text-gray-900",
                                                    children: "Status"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                    lineNumber: 258,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHead"], {
                                                    className: "font-semibold text-gray-900",
                                                    children: "Top News"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                    lineNumber: 261,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHead"], {
                                                    className: "font-semibold text-gray-900",
                                                    children: "Published"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                    lineNumber: 264,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableHead"], {
                                                    className: "font-semibold text-gray-900 text-right",
                                                    children: "Actions"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                    lineNumber: 267,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                            lineNumber: 248,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                        lineNumber: 247,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableBody"], {
                                        children: isLoading ? Array.from({
                                            length: 3
                                        }).map((_, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "h-4 bg-gray-200 rounded animate-pulse"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                            lineNumber: 277,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                        lineNumber: 276,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "h-4 bg-gray-200 rounded animate-pulse w-16"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                            lineNumber: 280,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                        lineNumber: 279,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "h-4 bg-gray-200 rounded animate-pulse w-16"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                            lineNumber: 283,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                        lineNumber: 282,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "h-6 bg-gray-200 rounded animate-pulse w-16"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                            lineNumber: 286,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                        lineNumber: 285,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "h-6 bg-gray-200 rounded animate-pulse w-16"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                            lineNumber: 289,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                        lineNumber: 288,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "h-4 bg-gray-200 rounded animate-pulse w-20"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                            lineNumber: 292,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                        lineNumber: 291,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex justify-end space-x-2",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "h-8 w-8 bg-gray-200 rounded animate-pulse"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                    lineNumber: 296,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "h-8 w-8 bg-gray-200 rounded animate-pulse"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                    lineNumber: 297,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "h-8 w-8 bg-gray-200 rounded animate-pulse"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                    lineNumber: 298,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                            lineNumber: 295,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                        lineNumber: 294,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, index, true, {
                                                fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                lineNumber: 275,
                                                columnNumber: 21
                                            }, this)) : filteredPosts.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                colSpan: 8,
                                                className: "text-center py-8 text-gray-500",
                                                children: searchQuery ? "No posts found matching your search." : "No posts found."
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                lineNumber: 305,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                            lineNumber: 304,
                                            columnNumber: 19
                                        }, this) : filteredPosts.map((post)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableRow"], {
                                                className: "hover:bg-gray-50",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                        className: "font-medium max-w-xs",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "truncate",
                                                            title: post.title,
                                                            children: post.title
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                            lineNumber: 318,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                        lineNumber: 317,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                        children: post.categories.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex flex-wrap gap-1",
                                                            children: [
                                                                post.categories.slice(0, 2).map((category, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                                                        variant: "outline",
                                                                        className: "text-xs",
                                                                        children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lodash$2f$get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(category, "name", "N/A")
                                                                    }, index, false, {
                                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                        lineNumber: 328,
                                                                        columnNumber: 33
                                                                    }, this)),
                                                                post.categories.length > 2 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                                                    variant: "outline",
                                                                    className: "text-xs",
                                                                    children: [
                                                                        "+",
                                                                        post.categories.length - 2
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                    lineNumber: 337,
                                                                    columnNumber: 31
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                            lineNumber: 324,
                                                            columnNumber: 27
                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-gray-400",
                                                            children: "-"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                            lineNumber: 343,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                        lineNumber: 322,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                        children: post.tags.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex flex-wrap gap-1",
                                                            children: [
                                                                post.tags.slice(0, 2).map((tag, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                                                        variant: "secondary",
                                                                        className: "text-xs",
                                                                        children: tag
                                                                    }, index, false, {
                                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                        lineNumber: 351,
                                                                        columnNumber: 31
                                                                    }, this)),
                                                                post.tags.length > 2 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                                                    variant: "secondary",
                                                                    className: "text-xs",
                                                                    children: [
                                                                        "+",
                                                                        post.tags.length - 2
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                    lineNumber: 360,
                                                                    columnNumber: 31
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                            lineNumber: 349,
                                                            columnNumber: 27
                                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-gray-400",
                                                            children: "-"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                            lineNumber: 366,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                        lineNumber: 347,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                                            className: getStatusBadgeClass(post.status),
                                                            children: post.status.charAt(0).toUpperCase() + post.status.slice(1)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                            lineNumber: 370,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                        lineNumber: 369,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__["Star"], {
                                                                    className: `h-4 w-4 ${post.isTopNews ? "text-yellow-500 fill-yellow-500" : "text-gray-300"}`
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                    lineNumber: 377,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "ml-2 text-sm text-gray-600",
                                                                    children: post.isTopNews ? "Yes" : "No"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                    lineNumber: 384,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                            lineNumber: 376,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                        lineNumber: 375,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                        className: "text-gray-600",
                                                        children: formatDate(post.createdAt)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                        lineNumber: 389,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$table$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TableCell"], {
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center justify-end space-x-2",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                    type: "button",
                                                                    variant: "outline",
                                                                    size: "sm",
                                                                    onClick: (e)=>{
                                                                        e.preventDefault();
                                                                        e.stopPropagation();
                                                                        toggleTopNews(post);
                                                                    },
                                                                    className: `h-8 px-3 ${post.isTopNews ? "text-yellow-600 hover:text-yellow-700 hover:bg-yellow-50" : "text-gray-600 hover:text-gray-700 hover:bg-gray-50"}`,
                                                                    title: post.isTopNews ? "Remove from Top News" : "Mark as Top News",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__["Star"], {
                                                                            className: `h-4 w-4 mr-1 ${post.isTopNews ? "fill-current" : ""}`
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                            lineNumber: 416,
                                                                            columnNumber: 29
                                                                        }, this),
                                                                        post.isTopNews ? "Remove" : "Top News"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                    lineNumber: 396,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                    type: "button",
                                                                    variant: "outline",
                                                                    size: "sm",
                                                                    onClick: (e)=>{
                                                                        e.preventDefault();
                                                                        e.stopPropagation();
                                                                        togglePostStatus(post);
                                                                    },
                                                                    className: `h-8 px-3 ${post.status === "published" ? "text-orange-600 hover:text-orange-700 hover:bg-orange-50" : "text-green-600 hover:text-green-700 hover:bg-green-50"}`,
                                                                    children: post.status === "published" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                                                                className: "h-4 w-4 mr-1"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                                lineNumber: 442,
                                                                                columnNumber: 33
                                                                            }, this),
                                                                            "Unpublish"
                                                                        ]
                                                                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                                                                                className: "h-4 w-4 mr-1"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                                lineNumber: 447,
                                                                                columnNumber: 33
                                                                            }, this),
                                                                            "Publish"
                                                                        ]
                                                                    }, void 0, true)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                    lineNumber: 425,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                    type: "button",
                                                                    variant: "outline",
                                                                    size: "sm",
                                                                    onClick: ()=>router.push(`/dashboard/blogs/edit/${post.slug}`),
                                                                    className: "h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$pen$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Edit$3e$__["Edit"], {
                                                                        className: "h-4 w-4"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                        lineNumber: 462,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                    lineNumber: 453,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                    type: "button",
                                                                    variant: "outline",
                                                                    size: "sm",
                                                                    onClick: (e)=>{
                                                                        e.preventDefault();
                                                                        e.stopPropagation();
                                                                        deletePos(post._id);
                                                                    },
                                                                    className: "h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                                                        className: "h-4 w-4"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                        lineNumber: 477,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                                    lineNumber: 466,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                            lineNumber: 394,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                        lineNumber: 393,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, post._id, true, {
                                                fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                                lineNumber: 316,
                                                columnNumber: 21
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                        lineNumber: 272,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                                lineNumber: 246,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                            lineNumber: 245,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                        lineNumber: 243,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
                lineNumber: 208,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/dashboard/blogs/components/BlogManagement.tsx",
        lineNumber: 194,
        columnNumber: 5
    }, this);
}
_s(BlogManagement, "5K7scsMz4IWkDdaXCCkItky4nr0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = BlogManagement;
var _c;
__turbopack_context__.k.register(_c, "BlogManagement");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_1035a0a6._.js.map