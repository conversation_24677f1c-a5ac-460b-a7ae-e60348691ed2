{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from \"mongoose\";\r\n\r\nconst MONGODB_URI = process.env.MONGODB_URI!;\r\n\r\nif (!MONGODB_URI) {\r\n  throw new Error(\r\n    \"Please define the MONGODB_URI environment variable inside .env.local\"\r\n  );\r\n}\r\n\r\n/**\r\n * Global is used here to maintain a cached connection across hot reloads\r\n * in development. This prevents connections growing exponentially\r\n * during API Route usage.\r\n */\r\n// @ts-expect-error //global can have type any\r\nlet cached = global?.mongoose;\r\n\r\nif (!cached) {\r\n  // @ts-expect-error //global mongoose can have type any\r\n  cached = global.mongoose = { conn: null, promise: null };\r\n}\r\n\r\nasync function connectDB() {\r\n  if (cached.conn) {\r\n    return cached.conn;\r\n  }\r\n\r\n  if (!cached.promise) {\r\n    const opts = {\r\n      bufferCommands: false,\r\n    };\r\n\r\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\r\n      return mongoose;\r\n    });\r\n  }\r\n\r\n  try {\r\n    cached.conn = await cached.promise;\r\n  } catch (e) {\r\n    cached.promise = null;\r\n    throw e;\r\n  }\r\n\r\n  return cached.conn;\r\n}\r\n\r\nexport default connectDB;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MACR;AAEJ;AAEA;;;;CAIC,GACD,8CAA8C;AAC9C,IAAI,SAAS,QAAQ;AAErB,IAAI,CAAC,QAAQ;IACX,uDAAuD;IACvD,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/models/Post.ts"], "sourcesContent": ["import mongoose, { type Document, Mongoose, Schema, Types } from \"mongoose\";\r\n\r\nexport interface IPost extends Document {\r\n  // Blog Post fields\r\n  title: string;\r\n  slug?: string;\r\n  canonicalUrl?: string;\r\n  existingUrl: boolean;\r\n  content: string; // Main content field\r\n  excerpt?: string; // Short description/excerpt\r\n  description: string; // For backward compatibility\r\n  isBlog: boolean;\r\n  categories: Types.ObjectId[];\r\n  tags: string[];\r\n  author?: string; // Author reference\r\n\r\n  // SEO fields\r\n  metaTitle: string;\r\n  metaDescription: string;\r\n  metaKeywords: string;\r\n\r\n  // FAQ fields\r\n  // faqs: {\r\n  //   question: string;\r\n  //   answer: string;\r\n  //   index: number;\r\n  // }[];\r\n\r\n  // Banner fields\r\n  banner: string;\r\n\r\n  // Additional blog functionality fields\r\n  status: \"draft\" | \"published\" | \"archived\";\r\n  isPublished: boolean;\r\n  publishedAt?: Date;\r\n  views: number;\r\n  readTime: number;\r\n  isTopNews: boolean;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\nconst PostSchema = new Schema<IPost>(\r\n  {\r\n    // Blog Post fields\r\n    title: {\r\n      type: String,\r\n      required: [true, \"Post title is required\"],\r\n      trim: true,\r\n      maxlength: [1000, \"Title cannot exceed 200 characters\"],\r\n    },\r\n\r\n    slug: {\r\n      type: String,\r\n      trim: true,\r\n      lowercase: true,\r\n    },\r\n\r\n    canonicalUrl: {\r\n      type: String,\r\n      trim: true,\r\n      validate: {\r\n        validator: (v: string) => {\r\n          if (!v) return true; // Allow empty string\r\n          return /^https?:\\/\\/.+/.test(v);\r\n        },\r\n        message: \"Canonical URL must be a valid URL\",\r\n      },\r\n    },\r\n    existingUrl: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n\r\n    content: {\r\n      type: String,\r\n      required: [true, \"Post content is required\"],\r\n    },\r\n\r\n    excerpt: {\r\n      type: String,\r\n      trim: true,\r\n      maxlength: [1000, \"Excerpt cannot exceed 300 characters\"],\r\n    },\r\n\r\n    description: {\r\n      type: String,\r\n      // Not required anymore since we have content field\r\n    },\r\n\r\n    author: {\r\n      type: String,\r\n      trim: true,\r\n    },\r\n    isBlog: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    categories: [\r\n      {\r\n        type: mongoose.Schema.Types.ObjectId,\r\n        ref: \"Category\",\r\n      },\r\n    ],\r\n    tags: [\r\n      {\r\n        type: String,\r\n        trim: true,\r\n        lowercase: true,\r\n      },\r\n    ],\r\n\r\n    // SEO fields\r\n    metaTitle: {\r\n      type: String,\r\n      required: [true, \"Meta title is required\"],\r\n      maxlength: [1000, \"Meta title cannot exceed 60 characters\"],\r\n    },\r\n    metaDescription: {\r\n      type: String,\r\n      required: [true, \"Meta description is required\"],\r\n      maxlength: [1000, \"Meta description cannot exceed 160 characters\"],\r\n    },\r\n    metaKeywords: {\r\n      type: String,\r\n      required: [true, \"Meta keywords are required\"],\r\n      maxlength: [1000, \"Meta keywords cannot exceed 200 characters\"],\r\n    },\r\n\r\n    // Banner fields\r\n    banner: {\r\n      type: String,\r\n      required: [true, \"Banner image is required\"],\r\n      trim: true,\r\n    },\r\n\r\n    // Additional blog functionality fields\r\n    status: {\r\n      type: String,\r\n      enum: [\"draft\", \"published\", \"archived\"],\r\n      default: \"draft\",\r\n    },\r\n    isPublished: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    publishedAt: {\r\n      type: Date,\r\n      default: null,\r\n    },\r\n    views: {\r\n      type: Number,\r\n      default: 0,\r\n    },\r\n    readTime: {\r\n      type: Number,\r\n      default: 1,\r\n    },\r\n    isTopNews: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  {\r\n    timestamps: true,\r\n  }\r\n);\r\n\r\n// Pre-save middleware\r\nPostSchema.pre(\"save\", function (next) {\r\n  // Generate slug from title if not provided\r\n  if (this.isModified(\"title\") && !this.slug) {\r\n    this.slug = this.title\r\n      .toLowerCase()\r\n      .replace(/[^a-z0-9]+/g, \"-\")\r\n      .replace(/^-+|-+$/g, \"\");\r\n  }\r\n\r\n  // Calculate read time (average 200 words per minute)\r\n  if (this.isModified(\"content\")) {\r\n    const wordCount = (this.content || \"\").split(/\\s+/).length;\r\n    this.readTime = Math.ceil(wordCount / 200);\r\n  }\r\n\r\n  // Set published date when status changes to published\r\n  if (\r\n    this.isModified(\"status\") &&\r\n    this.status === \"published\" &&\r\n    !this.publishedAt\r\n  ) {\r\n    this.publishedAt = new Date();\r\n    this.isPublished = true;\r\n  }\r\n\r\n  next();\r\n});\r\n\r\n// Create indexes for better query performance\r\nPostSchema.index({ categories: 1 });\r\nPostSchema.index({ tags: 1 });\r\nPostSchema.index({ status: 1 });\r\nPostSchema.index({ isPublished: 1 });\r\nPostSchema.index({ publishedAt: -1 });\r\nPostSchema.index({ \"banner.title\": 1 });\r\nPostSchema.index({\r\n  title: \"text\",\r\n  content: \"text\",\r\n  metaTitle: \"text\",\r\n  metaDescription: \"text\",\r\n}); // Text search index\r\n\r\nexport default mongoose.models.Post ||\r\n  mongoose.model<IPost>(\"Post\", PostSchema);\r\n"], "names": [], "mappings": ";;;AAAA;;AA0CA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAC3B;IACE,mBAAmB;IACnB,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,MAAM;QACN,WAAW;YAAC;YAAM;SAAqC;IACzD;IAEA,MAAM;QACJ,MAAM;QACN,MAAM;QACN,WAAW;IACb;IAEA,cAAc;QACZ,MAAM;QACN,MAAM;QACN,UAAU;YACR,WAAW,CAAC;gBACV,IAAI,CAAC,GAAG,OAAO,MAAM,qBAAqB;gBAC1C,OAAO,iBAAiB,IAAI,CAAC;YAC/B;YACA,SAAS;QACX;IACF;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IAEA,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;IAC9C;IAEA,SAAS;QACP,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAAuC;IAC3D;IAEA,aAAa;QACX,MAAM;IAER;IAEA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,YAAY;QACV;YACE,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;YACpC,KAAK;QACP;KACD;IACD,MAAM;QACJ;YACE,MAAM;YACN,MAAM;YACN,WAAW;QACb;KACD;IAED,aAAa;IACb,WAAW;QACT,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,WAAW;YAAC;YAAM;SAAyC;IAC7D;IACA,iBAAiB;QACf,MAAM;QACN,UAAU;YAAC;YAAM;SAA+B;QAChD,WAAW;YAAC;YAAM;SAAgD;IACpE;IACA,cAAc;QACZ,MAAM;QACN,UAAU;YAAC;YAAM;SAA6B;QAC9C,WAAW;YAAC;YAAM;SAA6C;IACjE;IAEA,gBAAgB;IAChB,QAAQ;QACN,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,MAAM;IACR;IAEA,uCAAuC;IACvC,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAS;YAAa;SAAW;QACxC,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IACA,OAAO;QACL,MAAM;QACN,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,SAAS;IACX;AACF,GACA;IACE,YAAY;AACd;AAGF,sBAAsB;AACtB,WAAW,GAAG,CAAC,QAAQ,SAAU,IAAI;IACnC,2CAA2C;IAC3C,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE;QAC1C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CACnB,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;IACzB;IAEA,qDAAqD;IACrD,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY;QAC9B,MAAM,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,EAAE,KAAK,CAAC,OAAO,MAAM;QAC1D,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,YAAY;IACxC;IAEA,sDAAsD;IACtD,IACE,IAAI,CAAC,UAAU,CAAC,aAChB,IAAI,CAAC,MAAM,KAAK,eAChB,CAAC,IAAI,CAAC,WAAW,EACjB;QACA,IAAI,CAAC,WAAW,GAAG,IAAI;QACvB,IAAI,CAAC,WAAW,GAAG;IACrB;IAEA;AACF;AAEA,8CAA8C;AAC9C,WAAW,KAAK,CAAC;IAAE,YAAY;AAAE;AACjC,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC7B,WAAW,KAAK,CAAC;IAAE,aAAa;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,aAAa,CAAC;AAAE;AACnC,WAAW,KAAK,CAAC;IAAE,gBAAgB;AAAE;AACrC,WAAW,KAAK,CAAC;IACf,OAAO;IACP,SAAS;IACT,WAAW;IACX,iBAAiB;AACnB,IAAI,oBAAoB;uCAET,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IACjC,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/lib/auth.ts"], "sourcesContent": ["import type { NextRequest } from \"next/server\";\r\nimport jwt from \"jsonwebtoken\";\r\n\r\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key\";\r\n\r\nexport interface AuthUser {\r\n  userId: string;\r\n  email: string;\r\n  username: string;\r\n  role: string;\r\n}\r\n\r\nexport interface AuthResult {\r\n  success: boolean;\r\n  user?: AuthUser;\r\n  error?: string;\r\n}\r\n\r\nexport async function verifyAuth(request: NextRequest): Promise<AuthResult> {\r\n  try {\r\n    // Debug: Log all cookies and headers\r\n    console.log(\"=== AUTH DEBUG ===\");\r\n    console.log(\"All cookies:\", request.cookies.getAll());\r\n    console.log(\"Authorization header:\", request.headers.get(\"authorization\"));\r\n    console.log(\"Cookie header:\", request.headers.get(\"cookie\"));\r\n\r\n    // Get token from Authorization header or cookies\r\n    const authHeader = request.headers.get(\"authorization\");\r\n    const authTokenCookie = request.cookies.get(\"auth-token\")?.value;\r\n    const sessionTokenCookie = request.cookies.get(\"session-token\")?.value;\r\n\r\n    console.log(\"Auth header token:\", authHeader?.replace(\"Bearer \", \"\"));\r\n    console.log(\"Auth token cookie:\", authTokenCookie);\r\n    console.log(\"Session token cookie:\", sessionTokenCookie);\r\n\r\n    const token =\r\n      authHeader?.replace(\"Bearer \", \"\") ||\r\n      authTokenCookie ||\r\n      sessionTokenCookie;\r\n\r\n    console.log(\"Final token:\", token ? \"Found\" : \"Not found\");\r\n    console.log(\"==================\");\r\n\r\n    if (!token) {\r\n      return { success: false, error: \"No authentication token provided\" };\r\n    }\r\n\r\n    // Verify token\r\n    const decoded = jwt.verify(token, JWT_SECRET) as AuthUser;\r\n\r\n    return { success: true, user: decoded };\r\n  } catch (error) {\r\n    if (error instanceof jwt.JsonWebTokenError) {\r\n      return { success: false, error: \"Invalid authentication token\" };\r\n    }\r\n\r\n    return { success: false, error: \"Authentication failed\" };\r\n  }\r\n}\r\n\r\nexport function requireAuth(roles?: string[]) {\r\n  return async (request: NextRequest) => {\r\n    const authResult = await verifyAuth(request);\r\n\r\n    if (!authResult.success) {\r\n      return authResult;\r\n    }\r\n\r\n    if (roles && !roles.includes(authResult.user!.role)) {\r\n      return { success: false, error: \"Insufficient permissions\" };\r\n    }\r\n\r\n    return authResult;\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAetC,eAAe,WAAW,OAAoB;IACnD,IAAI;QACF,qCAAqC;QACrC,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,gBAAgB,QAAQ,OAAO,CAAC,MAAM;QAClD,QAAQ,GAAG,CAAC,yBAAyB,QAAQ,OAAO,CAAC,GAAG,CAAC;QACzD,QAAQ,GAAG,CAAC,kBAAkB,QAAQ,OAAO,CAAC,GAAG,CAAC;QAElD,iDAAiD;QACjD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,MAAM,kBAAkB,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAC3D,MAAM,qBAAqB,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB;QAEjE,QAAQ,GAAG,CAAC,sBAAsB,YAAY,QAAQ,WAAW;QACjE,QAAQ,GAAG,CAAC,sBAAsB;QAClC,QAAQ,GAAG,CAAC,yBAAyB;QAErC,MAAM,QACJ,YAAY,QAAQ,WAAW,OAC/B,mBACA;QAEF,QAAQ,GAAG,CAAC,gBAAgB,QAAQ,UAAU;QAC9C,QAAQ,GAAG,CAAC;QAEZ,IAAI,CAAC,OAAO;YACV,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAmC;QACrE;QAEA,eAAe;QACf,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAElC,OAAO;YAAE,SAAS;YAAM,MAAM;QAAQ;IACxC,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,uIAAA,CAAA,UAAG,CAAC,iBAAiB,EAAE;YAC1C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA+B;QACjE;QAEA,OAAO;YAAE,SAAS;YAAO,OAAO;QAAwB;IAC1D;AACF;AAEO,SAAS,YAAY,KAAgB;IAC1C,OAAO,OAAO;QACZ,MAAM,aAAa,MAAM,WAAW;QAEpC,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO;QACT;QAEA,IAAI,SAAS,CAAC,MAAM,QAAQ,CAAC,WAAW,IAAI,CAAE,IAAI,GAAG;YACnD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA2B;QAC7D;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/api/posts/%5Bid%5D/toggle-top-news/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\r\nimport connectDB from \"@/lib/mongodb\";\r\nimport Post from \"@/models/Post\";\r\nimport { verifyAuth } from \"@/lib/auth\";\r\nimport { isValidObjectId } from \"mongoose\";\r\n\r\nexport async function PATCH(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const { id } = await params;\r\n\r\n    // Validate ObjectId\r\n    if (!isValidObjectId(id)) {\r\n      return NextResponse.json(\r\n        { success: false, message: \"Invalid post ID\" },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Verify authentication\r\n    const authResult = await verifyAuth(request);\r\n    if (!authResult.success) {\r\n      return NextResponse.json(\r\n        {\r\n          success: false,\r\n          message: authResult.error || \"Authentication required\",\r\n        },\r\n        { status: 401 }\r\n      );\r\n    }\r\n\r\n    // Check if user has admin role (optional - you can remove this if any authenticated user can toggle top news)\r\n    if (authResult.user?.role !== \"admin\") {\r\n      return NextResponse.json(\r\n        { success: false, message: \"Admin access required\" },\r\n        { status: 403 }\r\n      );\r\n    }\r\n\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Find the post\r\n    const post = await Post.findById(id);\r\n    if (!post) {\r\n      return NextResponse.json(\r\n        { success: false, message: \"Post not found\" },\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    // If the post is currently not top news and we're marking it as top news,\r\n    // first remove any existing top news\r\n    if (!post.isTopNews) {\r\n      // Remove top news status from all other posts\r\n      await Post.updateMany(\r\n        { _id: { $ne: id }, isTopNews: true },\r\n        { $set: { isTopNews: false } }\r\n      );\r\n\r\n      // Mark this post as top news\r\n      post.isTopNews = true;\r\n      await post.save();\r\n\r\n      return NextResponse.json({\r\n        success: true,\r\n        message:\r\n          \"Post marked as top news (previous top news removed automatically)\",\r\n        data: {\r\n          postId: post._id,\r\n          isTopNews: post.isTopNews,\r\n        },\r\n      });\r\n    } else {\r\n      // If it's already top news, just remove it\r\n      post.isTopNews = false;\r\n      await post.save();\r\n\r\n      return NextResponse.json({\r\n        success: true,\r\n        message: \"Post removed from top news\",\r\n        data: {\r\n          postId: post._id,\r\n          isTopNews: post.isTopNews,\r\n        },\r\n      });\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error toggling top news status:\", error);\r\n    return NextResponse.json(\r\n      { success: false, message: \"Internal server error\" },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,eAAe,MACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QAErB,oBAAoB;QACpB,IAAI,CAAC,CAAA,GAAA,yGAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;YACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAkB,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,aAAa,MAAM,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE;QACpC,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,SAAS,WAAW,KAAK,IAAI;YAC/B,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,8GAA8G;QAC9G,IAAI,WAAW,IAAI,EAAE,SAAS,SAAS;YACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAwB,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,gBAAgB;QAChB,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS;YAAiB,GAC5C;gBAAE,QAAQ;YAAI;QAElB;QAEA,0EAA0E;QAC1E,qCAAqC;QACrC,IAAI,CAAC,KAAK,SAAS,EAAE;YACnB,8CAA8C;YAC9C,MAAM,uHAAA,CAAA,UAAI,CAAC,UAAU,CACnB;gBAAE,KAAK;oBAAE,KAAK;gBAAG;gBAAG,WAAW;YAAK,GACpC;gBAAE,MAAM;oBAAE,WAAW;gBAAM;YAAE;YAG/B,6BAA6B;YAC7B,KAAK,SAAS,GAAG;YACjB,MAAM,KAAK,IAAI;YAEf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SACE;gBACF,MAAM;oBACJ,QAAQ,KAAK,GAAG;oBAChB,WAAW,KAAK,SAAS;gBAC3B;YACF;QACF,OAAO;YACL,2CAA2C;YAC3C,KAAK,SAAS,GAAG;YACjB,MAAM,KAAK,IAAI;YAEf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;gBACT,MAAM;oBACJ,QAAQ,KAAK,GAAG;oBAChB,WAAW,KAAK,SAAS;gBAC3B;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS;QAAwB,GACnD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}