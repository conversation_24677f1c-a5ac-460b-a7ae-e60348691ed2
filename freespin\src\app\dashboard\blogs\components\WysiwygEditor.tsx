// "use client";

// import { LexicalComposer } from "@lexical/react/LexicalComposer";
// import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
// import { ContentEditable } from "@lexical/react/LexicalContentEditable";
// import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
// import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin";
// import { LinkPlugin } from "@lexical/react/LexicalLinkPlugin";
// import { ListPlugin } from "@lexical/react/LexicalListPlugin";
// import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
// import { HeadingNode, QuoteNode } from "@lexical/rich-text";
// import { ListItemNode, ListNode } from "@lexical/list";
// import { AutoLinkNode, LinkNode } from "@lexical/link";
// import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
// import { OnChangePlugin } from "@lexical/react/LexicalOnChangePlugin";
// import {
//   Bold,
//   Italic,
//   Underline,
//   List,
//   ListOrdered,
//   Undo,
//   Redo,
//   Link as LinkIcon,
// } from "lucide-react";
// import { useCallback, useState, useEffect } from "react";
// import {
//   $getSelection,
//   $isRangeSelection,
//   FORMAT_TEXT_COMMAND,
//   UNDO_COMMAND,
//   REDO_COMMAND,
//   EditorState,
//   TextFormatType,
//   $getRoot,
// } from "lexical";
// import { $generateHtmlFromNodes } from "@lexical/html";
// import {
//   $createHeadingNode,
//   $createQuoteNode,
//   $isHeadingNode,
// } from "@lexical/rich-text";
// import {
//   INSERT_UNORDERED_LIST_COMMAND,
//   INSERT_ORDERED_LIST_COMMAND,
//   $isListNode,
// } from "@lexical/list";
// import { TOGGLE_LINK_COMMAND } from "@lexical/link";

// interface WysiwygEditorProps {
//   content: string;
//   onChange: (content: string) => void;
//   placeholder?: string;
// }

// // Lexical theme configuration
// const theme = {
//   ltr: "ltr",
//   rtl: "rtl",
//   placeholder: "text-gray-400 text-sm",
//   paragraph: "mb-2",
//   quote: "border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4",
//   heading: {
//     h1: "text-3xl font-bold mb-4",
//     h2: "text-2xl font-bold mb-3",
//     h3: "text-xl font-bold mb-2",
//   },
//   list: {
//     nested: {
//       listitem: "list-none",
//     },
//     ol: "list-decimal list-inside mb-2",
//     ul: "list-disc list-inside mb-2",
//     listitem: "mb-1",
//   },
//   link: "text-blue-600 underline hover:text-blue-800",
//   text: {
//     bold: "font-bold",
//     italic: "italic",
//     underline: "underline",
//     strikethrough: "line-through",
//   },
// };

// // Error boundary fallback
// function onError(error: Error) {
//   console.error(error);
// }

// // Initial editor configuration
// const initialConfig = {
//   namespace: "WysiwygEditor",
//   theme,
//   onError,
//   nodes: [
//     HeadingNode,
//     ListNode,
//     ListItemNode,
//     QuoteNode,
//     AutoLinkNode,
//     LinkNode,
//   ],
// };

// // Toolbar component
// function ToolbarPlugin() {
//   const [editor] = useLexicalComposerContext();
//   const [isBold, setIsBold] = useState(false);
//   const [isItalic, setIsItalic] = useState(false);
//   const [isUnderline, setIsUnderline] = useState(false);
//   const [blockType, setBlockType] = useState("paragraph");

//   const updateToolbar = useCallback(() => {
//     const selection = $getSelection();
//     if ($isRangeSelection(selection)) {
//       setIsBold(selection.hasFormat("bold"));
//       setIsItalic(selection.hasFormat("italic"));
//       setIsUnderline(selection.hasFormat("underline"));

//       const anchorNode = selection.anchor.getNode();
//       const element =
//         anchorNode.getKey() === "root"
//           ? anchorNode
//           : anchorNode.getTopLevelElementOrThrow();

//       if ($isListNode(element)) {
//         const type = element.getListType();
//         setBlockType(type === "bullet" ? "ul" : "ol");
//       } else {
//         const type = $isHeadingNode(element)
//           ? element.getTag()
//           : element.getType();
//         setBlockType(type);
//       }
//     }
//   }, []);

//   useEffect(() => {
//     return editor.registerUpdateListener(({ editorState }) => {
//       editorState.read(() => {
//         updateToolbar();
//       });
//     });
//   }, [updateToolbar, editor]);

//   const formatText = (format: TextFormatType) => {
//     editor.dispatchCommand(FORMAT_TEXT_COMMAND, format);
//   };

//   const formatHeading = (headingSize: string) => {
//     editor.update(() => {
//       const selection = $getSelection();
//       if ($isRangeSelection(selection)) {
//         if (blockType !== headingSize) {
//           const heading = $createHeadingNode(headingSize as any);
//           selection.insertNodes([heading]);
//         }
//       }
//     });
//   };

//   const formatQuote = () => {
//     editor.update(() => {
//       const selection = $getSelection();
//       if ($isRangeSelection(selection)) {
//         const quote = $createQuoteNode();
//         selection.insertNodes([quote]);
//       }
//     });
//   };

//   const formatBulletList = () => {
//     editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
//   };

//   const formatNumberedList = () => {
//     editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
//   };

//   const insertLink = () => {
//     const url = prompt("Enter URL:");
//     if (url) {
//       editor.dispatchCommand(TOGGLE_LINK_COMMAND, url);
//     }
//   };

//   return (
//     <div className="flex flex-wrap items-center gap-1 p-2 border-b border-gray-300 bg-gray-50">
//       {/* Text Formatting */}
//       <button
//         type="button"
//         onClick={() => formatText("bold")}
//         className={`p-2 rounded hover:bg-gray-200 ${
//           isBold ? "bg-gray-300" : ""
//         }`}
//         title="Bold"
//       >
//         <Bold className="w-4 h-4" />
//       </button>

//       <button
//         type="button"
//         onClick={() => formatText("italic")}
//         className={`p-2 rounded hover:bg-gray-200 ${
//           isItalic ? "bg-gray-300" : ""
//         }`}
//         title="Italic"
//       >
//         <Italic className="w-4 h-4" />
//       </button>

//       <button
//         type="button"
//         onClick={() => formatText("underline")}
//         className={`p-2 rounded hover:bg-gray-200 ${
//           isUnderline ? "bg-gray-300" : ""
//         }`}
//         title="Underline"
//       >
//         <Underline className="w-4 h-4" />
//       </button>

//       <div className="w-px h-6 bg-gray-300 mx-1" />

//       {/* Block Type */}
//       <select
//         onChange={(e) => {
//           const value = e.target.value;
//           if (value === "quote") {
//             formatQuote();
//           } else if (value.startsWith("h")) {
//             formatHeading(value);
//           }
//         }}
//         value={blockType}
//         className="px-2 py-1 border border-gray-300 rounded text-sm"
//       >
//         <option value="paragraph">Paragraph</option>
//         <option value="h1">Heading 1</option>
//         <option value="h2">Heading 2</option>
//         <option value="h3">Heading 3</option>
//         <option value="quote">Quote</option>
//       </select>

//       <div className="w-px h-6 bg-gray-300 mx-1" />

//       {/* Lists */}
//       <button
//         type="button"
//         onClick={formatBulletList}
//         className="p-2 rounded hover:bg-gray-200"
//         title="Bullet List"
//       >
//         <List className="w-4 h-4" />
//       </button>

//       <button
//         type="button"
//         onClick={formatNumberedList}
//         className="p-2 rounded hover:bg-gray-200"
//         title="Numbered List"
//       >
//         <ListOrdered className="w-4 h-4" />
//       </button>

//       <div className="w-px h-6 bg-gray-300 mx-1" />

//       {/* Link */}
//       <button
//         type="button"
//         onClick={insertLink}
//         className="p-2 rounded hover:bg-gray-200"
//         title="Add Link"
//       >
//         <LinkIcon className="w-4 h-4" />
//       </button>

//       <div className="w-px h-6 bg-gray-300 mx-1" />

//       {/* Undo/Redo */}
//       <button
//         type="button"
//         onClick={() => editor.dispatchCommand(UNDO_COMMAND, undefined)}
//         className="p-2 rounded hover:bg-gray-200"
//         title="Undo"
//       >
//         <Undo className="w-4 h-4" />
//       </button>

//       <button
//         type="button"
//         onClick={() => editor.dispatchCommand(REDO_COMMAND, undefined)}
//         className="p-2 rounded hover:bg-gray-200"
//         title="Redo"
//       >
//         <Redo className="w-4 h-4" />
//       </button>
//     </div>
//   );
// }

// // Main WYSIWYG Editor Component
// const WysiwygEditor = ({
//   onChange,
//   placeholder = "Start writing...",
// }: Omit<WysiwygEditorProps, "content">) => {
//   return (
//     <div className="border border-gray-300 rounded-lg overflow-hidden">
//       <LexicalComposer initialConfig={initialConfig}>
//         <ToolbarPlugin />
//         <div className="relative">
//           <RichTextPlugin
//             contentEditable={
//               <ContentEditable
//                 className="min-h-[300px] p-4 focus:outline-none prose max-w-none"
//                 style={{ resize: "none" }}
//               />
//             }
//             placeholder={
//               <div className="absolute top-4 left-4 text-gray-400 pointer-events-none">
//                 {placeholder}
//               </div>
//             }
//             ErrorBoundary={LexicalErrorBoundary}
//           />
//           <OnChangePlugin
//             onChange={(editorState: EditorState, editor) => {
//               editorState.read(() => {
//                 // Generate HTML from the editor state to preserve formatting
//                 const htmlContent = $generateHtmlFromNodes(editor, null);
//                 onChange(htmlContent);
//               });
//             }}
//           />
//           <HistoryPlugin />
//           <AutoFocusPlugin />
//           <LinkPlugin />
//           <ListPlugin />
//         </div>
//       </LexicalComposer>
//     </div>
//   );
// };

// export default WysiwygEditor;

//  Second

// Complete WYSIWYG Editor with Fixed Content Prop Handling

"use client";

import "./lexical-editor.css";
import { LexicalComposer } from "@lexical/react/LexicalComposer";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { LinkPlugin } from "@lexical/react/LexicalLinkPlugin";
import { ListPlugin } from "@lexical/react/LexicalListPlugin";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { HeadingNode, QuoteNode } from "@lexical/rich-text";
import { ListItemNode, ListNode } from "@lexical/list";
import { AutoLinkNode, LinkNode } from "@lexical/link";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { OnChangePlugin } from "@lexical/react/LexicalOnChangePlugin";
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Undo,
  Redo,
  Link as LinkIcon,
} from "lucide-react";
import { useCallback, useState, useEffect } from "react";
import {
  $getSelection,
  $isRangeSelection,
  FORMAT_TEXT_COMMAND,
  UNDO_COMMAND,
  REDO_COMMAND,
  EditorState,
  TextFormatType,
  $getRoot,
  $createParagraphNode,
  $createTextNode,
} from "lexical";
import { $generateHtmlFromNodes, $generateNodesFromDOM } from "@lexical/html";
import {
  $createHeadingNode,
  $createQuoteNode,
  $isHeadingNode,
} from "@lexical/rich-text";
import {
  INSERT_UNORDERED_LIST_COMMAND,
  INSERT_ORDERED_LIST_COMMAND,
  $isListNode,
} from "@lexical/list";
import { TOGGLE_LINK_COMMAND } from "@lexical/link";

interface WysiwygEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
}

// Lexical theme configuration
const theme = {
  ltr: "ltr",
  rtl: "rtl",
  placeholder: "text-gray-400 text-sm",
  paragraph: "mb-2",
  quote: "border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4",
  heading: {
    h1: "text-3xl font-bold mb-4",
    h2: "text-2xl font-bold mb-3",
    h3: "text-xl font-bold mb-2",
  },
  list: {
    nested: {
      listitem: "list-none",
    },
    ol: "list-decimal list-inside mb-2",
    ul: "list-disc list-inside mb-2",
    listitem: "mb-1",
  },
  link: "text-blue-600 underline hover:text-blue-800",
  text: {
    bold: "font-bold",
    italic: "italic",
    underline: "underline",
    strikethrough: "line-through",
  },
};

function onError(error: Error) {
  console.error(error);
}

function ToolbarPlugin() {
  const [editor] = useLexicalComposerContext();
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [isUnderline, setIsUnderline] = useState(false);
  const [blockType, setBlockType] = useState("paragraph");

  const updateToolbar = useCallback(() => {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      setIsBold(selection.hasFormat("bold"));
      setIsItalic(selection.hasFormat("italic"));
      setIsUnderline(selection.hasFormat("underline"));

      const anchorNode = selection.anchor.getNode();
      const element =
        anchorNode.getKey() === "root"
          ? anchorNode
          : anchorNode.getTopLevelElementOrThrow();

      if ($isListNode(element)) {
        const type = element.getListType();
        setBlockType(type === "bullet" ? "ul" : "ol");
      } else {
        const type = $isHeadingNode(element)
          ? element.getTag()
          : element.getType();
        setBlockType(type);
      }
    }
  }, []);

  useEffect(() => {
    return editor.registerUpdateListener(({ editorState }) => {
      editorState.read(() => {
        updateToolbar();
      });
    });
  }, [updateToolbar, editor]);

  const formatText = (format: TextFormatType) => {
    editor.dispatchCommand(FORMAT_TEXT_COMMAND, format);
  };

  const formatHeading = (headingSize: string) => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        if (blockType !== headingSize) {
          const heading = $createHeadingNode(headingSize as any);
          selection.insertNodes([heading]);
        }
      }
    });
  };

  const formatQuote = () => {
    editor.update(() => {
      const selection = $getSelection();
      if ($isRangeSelection(selection)) {
        const quote = $createQuoteNode();
        selection.insertNodes([quote]);
      }
    });
  };

  const formatBulletList = () => {
    editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
  };

  const formatNumberedList = () => {
    editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
  };

  const insertLink = () => {
    const url = prompt("Enter URL:");
    if (url) {
      editor.dispatchCommand(TOGGLE_LINK_COMMAND, url);
    }
  };

  return (
    <div
      className="flex flex-wrap items-center gap-1 p-2 border-b border-gray-300 bg-gray-50"
      style={{
        display: "flex",
        flexDirection: "row",
        flexWrap: "wrap",
        alignItems: "center",
        gap: "4px",
        padding: "8px",
      }}
    >
      <button
        type="button"
        onClick={() => formatText("bold")}
        className={`p-2 rounded hover:bg-gray-200 ${
          isBold ? "bg-gray-300" : ""
        }`}
        title="Bold"
      >
        <Bold className="w-4 h-4" />
      </button>

      <button
        type="button"
        onClick={() => formatText("italic")}
        className={`p-2 rounded hover:bg-gray-200 ${
          isItalic ? "bg-gray-300" : ""
        }`}
        title="Italic"
      >
        <Italic className="w-4 h-4" />
      </button>

      <button
        type="button"
        onClick={() => formatText("underline")}
        className={`p-2 rounded hover:bg-gray-200 ${
          isUnderline ? "bg-gray-300" : ""
        }`}
        title="Underline"
      >
        <Underline className="w-4 h-4" />
      </button>

      <div className="w-px h-6 bg-gray-300 mx-1" />

      <select
        onChange={(e) => {
          const value = e.target.value;
          if (value === "quote") {
            formatQuote();
          } else if (value.startsWith("h")) {
            formatHeading(value);
          }
        }}
        value={blockType}
        className="px-2 py-1 border border-gray-300 rounded text-sm"
      >
        <option value="paragraph">Paragraph</option>
        <option value="h1">Heading 1</option>
        <option value="h2">Heading 2</option>
        <option value="h3">Heading 3</option>
        <option value="quote">Quote</option>
      </select>

      <div className="w-px h-6 bg-gray-300 mx-1" />

      <button
        type="button"
        onClick={formatBulletList}
        className="p-2 rounded hover:bg-gray-200"
        title="Bullet List"
      >
        <List className="w-4 h-4" />
      </button>

      <button
        type="button"
        onClick={formatNumberedList}
        className="p-2 rounded hover:bg-gray-200"
        title="Numbered List"
      >
        <ListOrdered className="w-4 h-4" />
      </button>

      <div className="w-px h-6 bg-gray-300 mx-1" />

      <button
        type="button"
        onClick={insertLink}
        className="p-2 rounded hover:bg-gray-200"
        title="Add Link"
      >
        <LinkIcon className="w-4 h-4" />
      </button>

      <div className="w-px h-6 bg-gray-300 mx-1" />

      <button
        type="button"
        onClick={() => editor.dispatchCommand(UNDO_COMMAND, undefined)}
        className="p-2 rounded hover:bg-gray-200"
        title="Undo"
      >
        <Undo className="w-4 h-4" />
      </button>

      <button
        type="button"
        onClick={() => editor.dispatchCommand(REDO_COMMAND, undefined)}
        className="p-2 rounded hover:bg-gray-200"
        title="Redo"
      >
        <Redo className="w-4 h-4" />
      </button>
    </div>
  );
}

// Plugin to update editor content when content prop changes
function ContentUpdatePlugin({ content }: { content: string }) {
  const [editor] = useLexicalComposerContext();
  const [isFirstLoad, setIsFirstLoad] = useState(true);

  useEffect(() => {
    // Only update on first load or when content actually changes
    if (isFirstLoad && content && content.trim() !== "") {
      editor.update(() => {
        try {
          // Fix vertical stacking issue by detecting and merging single-character paragraphs
          let cleanedContent = content;

          // Pattern to detect single character paragraphs that cause vertical stacking
          const singleCharPattern = /<p[^>]*><span[^>]*>(.)<\/span><\/p>/g;
          const matches = [...content.matchAll(singleCharPattern)];

          if (matches.length > 1) {
            // If we have multiple single-character paragraphs, merge them
            const characters = matches.map((match) => match[1]).join("");
            // Replace all single-character paragraphs with one paragraph containing all characters
            cleanedContent = content.replace(singleCharPattern, "");
            cleanedContent =
              `<p class="mb-2" dir="ltr"><span style="white-space: pre-wrap;">${characters}</span></p>` +
              cleanedContent;
          }

          // Additional cleaning
          cleanedContent = cleanedContent
            .replace(/\s+/g, " ") // Replace multiple whitespace with single space
            .replace(/>\s+</g, "><") // Remove whitespace between tags
            .trim();

          const parser = new DOMParser();
          const dom = parser.parseFromString(cleanedContent, "text/html");

          // Ensure all text nodes maintain proper spacing
          const walker = document.createTreeWalker(
            dom.body,
            NodeFilter.SHOW_TEXT
          );

          let textNode;
          while ((textNode = walker.nextNode())) {
            if (textNode.textContent) {
              // Ensure text content flows normally
              textNode.textContent = textNode.textContent.replace(/\s+/g, " ");
            }
          }

          const nodes = $generateNodesFromDOM(editor, dom);
          const root = $getRoot();
          root.clear();
          root.append(...nodes);

          // Force a re-render to ensure proper text flow
          setTimeout(() => {
            editor.update(() => {
              const root = $getRoot();
              root.markDirty();
            });
          }, 100);
        } catch (error) {
          console.error("Error updating editor content:", error);
          // Fallback: create a simple paragraph with the content
          const root = $getRoot();
          root.clear();

          // Strip HTML tags for fallback
          const textContent = content
            .replace(/<[^>]*>/g, " ")
            .replace(/\s+/g, " ")
            .trim();
          if (textContent) {
            const paragraph = $createParagraphNode();
            paragraph.append($createTextNode(textContent));
            root.append(paragraph);
          }
        }
      });
      setIsFirstLoad(false);
    }
  }, [content, editor, isFirstLoad]);

  return null;
}

const WysiwygEditor = ({
  content,
  onChange,
  placeholder = "Start writing...",
}: WysiwygEditorProps) => {
  const initialConfig = {
    namespace: "WysiwygEditor",
    theme,
    onError,
    nodes: [
      HeadingNode,
      ListNode,
      ListItemNode,
      QuoteNode,
      AutoLinkNode,
      LinkNode,
    ],
  };

  return (
    <div className="border border-gray-300 rounded-lg overflow-hidden lexical-editor">
      <LexicalComposer key="wysiwyg-editor" initialConfig={initialConfig}>
        <ToolbarPlugin />
        <ContentUpdatePlugin content={content} />
        <div
          className="relative lexical-content-area"
          style={{
            direction: "ltr",
            textAlign: "left",
            writingMode: "horizontal-tb",
          }}
        >
          <RichTextPlugin
            contentEditable={
              <ContentEditable
                className="min-h-[300px] p-4 focus:outline-none"
                style={{
                  minHeight: "300px",
                  padding: "16px",
                  whiteSpace: "normal",
                  wordWrap: "break-word",
                  textAlign: "left",
                  direction: "ltr",
                  writingMode: "horizontal-tb",
                  unicodeBidi: "normal",
                  display: "block",
                  width: "100%",
                  boxSizing: "border-box",
                  fontFamily: "inherit",
                  fontSize: "16px",
                  lineHeight: "1.6",
                  color: "inherit",
                  background: "transparent",
                  border: "none",
                  outline: "none",
                }}
                spellCheck={true}
                dir="ltr"
              />
            }
            placeholder={
              <div className="absolute top-4 left-4 text-gray-400 pointer-events-none">
                {placeholder}
              </div>
            }
            ErrorBoundary={LexicalErrorBoundary}
          />
          <OnChangePlugin
            onChange={(editorState: EditorState, editor) => {
              editorState.read(() => {
                const htmlContent = $generateHtmlFromNodes(editor, null);
                onChange(htmlContent);
              });
            }}
          />
          <HistoryPlugin />
          <LinkPlugin />
          <ListPlugin />
        </div>
      </LexicalComposer>
    </div>
  );
};

export default WysiwygEditor;
