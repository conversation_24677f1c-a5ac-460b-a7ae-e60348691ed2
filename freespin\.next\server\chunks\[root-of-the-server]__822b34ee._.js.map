{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from \"mongoose\";\r\n\r\nconst MONGODB_URI = process.env.MONGODB_URI!;\r\n\r\nif (!MONGODB_URI) {\r\n  throw new Error(\r\n    \"Please define the MONGODB_URI environment variable inside .env.local\"\r\n  );\r\n}\r\n\r\n/**\r\n * Global is used here to maintain a cached connection across hot reloads\r\n * in development. This prevents connections growing exponentially\r\n * during API Route usage.\r\n */\r\n// @ts-expect-error //global can have type any\r\nlet cached = global?.mongoose;\r\n\r\nif (!cached) {\r\n  // @ts-expect-error //global mongoose can have type any\r\n  cached = global.mongoose = { conn: null, promise: null };\r\n}\r\n\r\nasync function connectDB() {\r\n  if (cached.conn) {\r\n    return cached.conn;\r\n  }\r\n\r\n  if (!cached.promise) {\r\n    const opts = {\r\n      bufferCommands: false,\r\n    };\r\n\r\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\r\n      return mongoose;\r\n    });\r\n  }\r\n\r\n  try {\r\n    cached.conn = await cached.promise;\r\n  } catch (e) {\r\n    cached.promise = null;\r\n    throw e;\r\n  }\r\n\r\n  return cached.conn;\r\n}\r\n\r\nexport default connectDB;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MACR;AAEJ;AAEA;;;;CAIC,GACD,8CAA8C;AAC9C,IAAI,SAAS,QAAQ;AAErB,IAAI,CAAC,QAAQ;IACX,uDAAuD;IACvD,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/models/Category.ts"], "sourcesContent": ["import mongoose, { type Document, Schema } from \"mongoose\";\r\n\r\nexport interface ICategory extends Document {\r\n  name: string;\r\n  description?: string;\r\n  color?: string;\r\n  isActive: boolean;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\nconst CategorySchema = new Schema<ICategory>(\r\n  {\r\n    name: {\r\n      type: String,\r\n      required: [true, \"Category name is required\"],\r\n      unique: true,\r\n      trim: true,\r\n      maxlength: [50, \"Category name cannot exceed 50 characters\"],\r\n    },\r\n    description: {\r\n      type: String,\r\n      maxlength: [200, \"Description cannot exceed 200 characters\"],\r\n    },\r\n    color: {\r\n      type: String,\r\n      default: \"#6366f1\",\r\n      match: [\r\n        /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,\r\n        \"Please enter a valid hex color\",\r\n      ],\r\n    },\r\n    isActive: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n  },\r\n  {\r\n    timestamps: true,\r\n  }\r\n);\r\n\r\nCategorySchema.index({ isActive: 1 });\r\n\r\nexport default mongoose.models.Category ||\r\n  mongoose.model<ICategory>(\"Category\", CategorySchema);\r\n"], "names": [], "mappings": ";;;AAAA;;AAWA,MAAM,iBAAiB,IAAI,yGAAA,CAAA,SAAM,CAC/B;IACE,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAA4B;QAC7C,QAAQ;QACR,MAAM;QACN,WAAW;YAAC;YAAI;SAA4C;IAC9D;IACA,aAAa;QACX,MAAM;QACN,WAAW;YAAC;YAAK;SAA2C;IAC9D;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,OAAO;YACL;YACA;SACD;IACH;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;AACF,GACA;IACE,YAAY;AACd;AAGF,eAAe,KAAK,CAAC;IAAE,UAAU;AAAE;uCAEpB,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,QAAQ,IACrC,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAY,YAAY", "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/models/User.ts"], "sourcesContent": ["import mongoose, { type Document, Schema } from \"mongoose\";\r\nimport bcrypt from \"bcryptjs\";\r\n\r\nexport interface I<PERSON>ser extends Document {\r\n  username: string;\r\n  email: string;\r\n  password: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n  bio?: string;\r\n  avatar?: string;\r\n  role: \"user\" | \"admin\" | \"moderator\";\r\n  isActive: boolean;\r\n  emailVerified: boolean;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n  comparePassword(candidatePassword: string): Promise<boolean>;\r\n}\r\n\r\nconst UserSchema = new Schema<IUser>(\r\n  {\r\n    username: {\r\n      type: String,\r\n      required: [true, \"Username is required\"],\r\n      unique: true,\r\n      trim: true,\r\n      minlength: [3, \"Username must be at least 3 characters\"],\r\n      maxlength: [30, \"Username cannot exceed 30 characters\"],\r\n    },\r\n    email: {\r\n      type: String,\r\n      required: [true, \"Email is required\"],\r\n      unique: true,\r\n      lowercase: true,\r\n      trim: true,\r\n      match: [\r\n        /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/,\r\n        \"Please enter a valid email\",\r\n      ],\r\n    },\r\n    password: {\r\n      type: String,\r\n      required: [true, \"Password is required\"],\r\n      minlength: [6, \"Password must be at least 6 characters\"],\r\n      select: false, // Don't include password in queries by default\r\n    },\r\n    firstName: {\r\n      type: String,\r\n      trim: true,\r\n      maxlength: [50, \"First name cannot exceed 50 characters\"],\r\n    },\r\n    lastName: {\r\n      type: String,\r\n      trim: true,\r\n      maxlength: [50, \"Last name cannot exceed 50 characters\"],\r\n    },\r\n    bio: {\r\n      type: String,\r\n      maxlength: [500, \"Bio cannot exceed 500 characters\"],\r\n    },\r\n    avatar: {\r\n      type: String,\r\n      default: null,\r\n    },\r\n    role: {\r\n      type: String,\r\n      enum: [\"user\", \"admin\", \"moderator\"],\r\n      default: \"user\",\r\n    },\r\n    isActive: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    emailVerified: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  {\r\n    timestamps: true,\r\n  }\r\n);\r\n\r\n// Hash password before saving\r\nUserSchema.pre(\"save\", async function (next) {\r\n  if (!this.isModified(\"password\")) return next();\r\n\r\n  try {\r\n    const salt = await bcrypt.genSalt(12);\r\n    this.password = await bcrypt.hash(this.password, salt);\r\n    next();\r\n  } catch (error: any) {\r\n    next(error);\r\n  }\r\n});\r\n\r\n// Compare password method\r\nUserSchema.methods.comparePassword = async function (\r\n  candidatePassword: string\r\n): Promise<boolean> {\r\n  return bcrypt.compare(candidatePassword, this.password);\r\n};\r\n\r\nexport default mongoose.models.User ||\r\n  mongoose.model<IUser>(\"User\", UserSchema);\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAkBA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAC3B;IACE,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,QAAQ;QACR,MAAM;QACN,WAAW;YAAC;YAAG;SAAyC;QACxD,WAAW;YAAC;YAAI;SAAuC;IACzD;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,WAAW;QACX,MAAM;QACN,OAAO;YACL;YACA;SACD;IACH;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,WAAW;YAAC;YAAG;SAAyC;QACxD,QAAQ;IACV;IACA,WAAW;QACT,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAI;SAAyC;IAC3D;IACA,UAAU;QACR,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAI;SAAwC;IAC1D;IACA,KAAK;QACH,MAAM;QACN,WAAW;YAAC;YAAK;SAAmC;IACtD;IACA,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,MAAM;QACJ,MAAM;QACN,MAAM;YAAC;YAAQ;YAAS;SAAY;QACpC,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,eAAe;QACb,MAAM;QACN,SAAS;IACX;AACF,GACA;IACE,YAAY;AACd;AAGF,8BAA8B;AAC9B,WAAW,GAAG,CAAC,QAAQ,eAAgB,IAAI;IACzC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,MAAM,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACjD;IACF,EAAE,OAAO,OAAY;QACnB,KAAK;IACP;AACF;AAEA,0BAA0B;AAC1B,WAAW,OAAO,CAAC,eAAe,GAAG,eACnC,iBAAyB;IAEzB,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IACjC,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/lib/middleware.ts"], "sourcesContent": ["import { type NextRequest, NextResponse } from \"next/server\";\r\nimport jwt from \"jsonwebtoken\";\r\nimport connectDB from \"@/lib/mongodb\";\r\nimport User from \"@/models/User\";\r\n\r\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key\";\r\n\r\nexport interface AuthenticatedRequest extends NextRequest {\r\n  user?: {\r\n    userId: string;\r\n    email: string;\r\n    username: string;\r\n    role: string;\r\n  };\r\n}\r\n\r\n// Helper function to get client IP address\r\nfunction getClientIP(request: NextRequest): string {\r\n  const forwarded = request.headers.get(\"x-forwarded-for\");\r\n  const realIP = request.headers.get(\"x-real-ip\");\r\n  const cfConnectingIP = request.headers.get(\"cf-connecting-ip\");\r\n\r\n  if (forwarded) {\r\n    return forwarded.split(\",\")[0].trim();\r\n  }\r\n  if (realIP) {\r\n    return realIP;\r\n  }\r\n  if (cfConnectingIP) {\r\n    return cfConnectingIP;\r\n  }\r\n\r\n  return \"unknown\";\r\n}\r\n\r\n// Higher-order function that wraps API route handlers with authentication\r\nexport function withAuth(\r\n  handler: (\r\n    request: AuthenticatedRequest,\r\n    context: any\r\n  ) => Promise<NextResponse>\r\n) {\r\n  return async (request: NextRequest, context: any) => {\r\n    try {\r\n      // Get token from Authorization header or cookie\r\n      const authHeader = request.headers.get(\"authorization\");\r\n      const token =\r\n        authHeader?.replace(\"Bearer \", \"\") ||\r\n        request.cookies.get(\"session-token\")?.value;\r\n\r\n      if (!token) {\r\n        return NextResponse.json(\r\n          { success: false, error: \"Authentication required\" },\r\n          { status: 401 }\r\n        );\r\n      }\r\n\r\n      // Verify JWT token\r\n      const decoded = jwt.verify(token, JWT_SECRET) as any;\r\n\r\n      // Optional: Verify user still exists in database\r\n      await connectDB();\r\n      const user = await User.findById(decoded.userId).select(\r\n        \"_id email username role isActive\"\r\n      );\r\n\r\n      if (!user || !user.isActive) {\r\n        return NextResponse.json(\r\n          { success: false, error: \"User not found or inactive\" },\r\n          { status: 401 }\r\n        );\r\n      }\r\n      // Add user info to request object\r\n      (request as AuthenticatedRequest).user = {\r\n        userId: user._id.toString(),\r\n        email: user.email,\r\n        username: user.username,\r\n        role: user.role,\r\n      };\r\n\r\n      // Call the original handler with authenticated request\r\n      return handler(request as AuthenticatedRequest, context);\r\n    } catch (error) {\r\n      if (error instanceof jwt.JsonWebTokenError) {\r\n        return NextResponse.json(\r\n          { success: false, error: \"Invalid authentication token\" },\r\n          { status: 401 }\r\n        );\r\n      }\r\n\r\n      console.error(\"Authentication middleware error:\", error);\r\n      return NextResponse.json(\r\n        { success: false, error: \"Authentication failed\" },\r\n        { status: 500 }\r\n      );\r\n    }\r\n  };\r\n}\r\n\r\n// Higher-order function that wraps API route handlers with role-based authorization\r\nexport function withRole(allowedRoles: string[]) {\r\n  return (\r\n    handler: (\r\n      request: AuthenticatedRequest,\r\n      context: any\r\n    ) => Promise<NextResponse>\r\n  ) =>\r\n    withAuth(async (request: AuthenticatedRequest, context: any) => {\r\n      const user = request.user!;\r\n\r\n      if (!allowedRoles.includes(user.role)) {\r\n        return NextResponse.json(\r\n          {\r\n            success: false,\r\n            error: `Access denied. Required roles: ${allowedRoles.join(\r\n              \", \"\r\n            )}. Your role: ${user.role}`,\r\n          },\r\n          { status: 403 }\r\n        );\r\n      }\r\n\r\n      // User has required role, proceed with handler\r\n      return handler(request, context);\r\n    });\r\n}\r\n\r\n// Middleware for admin-only routes\r\nexport const withAdmin = withRole([\"admin\"]);\r\n\r\n// Middleware for admin and editor routes\r\nexport const withEditor = withRole([\"admin\", \"editor\"]);\r\n\r\n// Middleware for admin, editor, and author routes\r\nexport const withAuthor = withRole([\"admin\", \"editor\", \"author\"]);\r\n\r\n// Optional: Rate limiting middleware\r\nexport function withRateLimit(\r\n  maxRequests = 100,\r\n  windowMs: number = 15 * 60 * 1000\r\n) {\r\n  const requests = new Map<string, { count: number; resetTime: number }>();\r\n\r\n  return (\r\n      handler: (request: NextRequest, context: any) => Promise<NextResponse>\r\n    ) =>\r\n    async (request: NextRequest, context: any) => {\r\n      const ip = getClientIP(request);\r\n      const now = Date.now();\r\n\r\n      // Clean up old entries\r\n      for (const [key, value] of requests.entries()) {\r\n        if (now > value.resetTime) {\r\n          requests.delete(key);\r\n        }\r\n      }\r\n\r\n      // Get or create request count for this IP\r\n      const requestData = requests.get(ip) || {\r\n        count: 0,\r\n        resetTime: now + windowMs,\r\n      };\r\n\r\n      if (now > requestData.resetTime) {\r\n        // Reset window\r\n        requestData.count = 1;\r\n        requestData.resetTime = now + windowMs;\r\n      } else {\r\n        requestData.count++;\r\n      }\r\n\r\n      requests.set(ip, requestData);\r\n\r\n      // Check if rate limit exceeded\r\n      if (requestData.count > maxRequests) {\r\n        return NextResponse.json(\r\n          {\r\n            success: false,\r\n            error: \"Rate limit exceeded. Please try again later.\",\r\n            retryAfter: Math.ceil((requestData.resetTime - now) / 1000),\r\n          },\r\n          { status: 429 }\r\n        );\r\n      }\r\n\r\n      return handler(request, context);\r\n    };\r\n}\r\n\r\n// Logging middleware\r\nexport function withLogging(\r\n  handler: (request: NextRequest, context: any) => Promise<NextResponse>\r\n) {\r\n  return async (request: NextRequest, context: any) => {\r\n    const start = Date.now();\r\n    const method = request.method;\r\n    const url = request.url;\r\n\r\n    console.log(`[${new Date().toISOString()}] ${method} ${url} - Started`);\r\n\r\n    try {\r\n      const response = await handler(request, context);\r\n      const duration = Date.now() - start;\r\n\r\n      console.log(\r\n        `[${new Date().toISOString()}] ${method} ${url} - ${\r\n          response.status\r\n        } (${duration}ms)`\r\n      );\r\n\r\n      return response;\r\n    } catch (error) {\r\n      const duration = Date.now() - start;\r\n      console.error(\r\n        `[${new Date().toISOString()}] ${method} ${url} - Error (${duration}ms):`,\r\n        error\r\n      );\r\n      throw error;\r\n    }\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;AACA;;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAW7C,2CAA2C;AAC3C,SAAS,YAAY,OAAoB;IACvC,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;IACtC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;IACnC,MAAM,iBAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAE3C,IAAI,WAAW;QACb,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IACrC;IACA,IAAI,QAAQ;QACV,OAAO;IACT;IACA,IAAI,gBAAgB;QAClB,OAAO;IACT;IAEA,OAAO;AACT;AAGO,SAAS,SACd,OAG0B;IAE1B,OAAO,OAAO,SAAsB;QAClC,IAAI;YACF,gDAAgD;YAChD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;YACvC,MAAM,QACJ,YAAY,QAAQ,WAAW,OAC/B,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB;YAExC,IAAI,CAAC,OAAO;gBACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAA0B,GACnD;oBAAE,QAAQ;gBAAI;YAElB;YAEA,mBAAmB;YACnB,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;YAElC,iDAAiD;YACjD,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;YACd,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,QAAQ,MAAM,EAAE,MAAM,CACrD;YAGF,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;gBAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAA6B,GACtD;oBAAE,QAAQ;gBAAI;YAElB;YACA,kCAAkC;YACjC,QAAiC,IAAI,GAAG;gBACvC,QAAQ,KAAK,GAAG,CAAC,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,MAAM,KAAK,IAAI;YACjB;YAEA,uDAAuD;YACvD,OAAO,QAAQ,SAAiC;QAClD,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,uIAAA,CAAA,UAAG,CAAC,iBAAiB,EAAE;gBAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAA+B,GACxD;oBAAE,QAAQ;gBAAI;YAElB;YAEA,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAwB,GACjD;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF;AAGO,SAAS,SAAS,YAAsB;IAC7C,OAAO,CACL,UAKA,SAAS,OAAO,SAA+B;YAC7C,MAAM,OAAO,QAAQ,IAAI;YAEzB,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;gBACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBACE,SAAS;oBACT,OAAO,CAAC,+BAA+B,EAAE,aAAa,IAAI,CACxD,MACA,aAAa,EAAE,KAAK,IAAI,EAAE;gBAC9B,GACA;oBAAE,QAAQ;gBAAI;YAElB;YAEA,+CAA+C;YAC/C,OAAO,QAAQ,SAAS;QAC1B;AACJ;AAGO,MAAM,YAAY,SAAS;IAAC;CAAQ;AAGpC,MAAM,aAAa,SAAS;IAAC;IAAS;CAAS;AAG/C,MAAM,aAAa,SAAS;IAAC;IAAS;IAAU;CAAS;AAGzD,SAAS,cACd,cAAc,GAAG,EACjB,WAAmB,KAAK,KAAK,IAAI;IAEjC,MAAM,WAAW,IAAI;IAErB,OAAO,CACH,UAEF,OAAO,SAAsB;YAC3B,MAAM,KAAK,YAAY;YACvB,MAAM,MAAM,KAAK,GAAG;YAEpB,uBAAuB;YACvB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,SAAS,OAAO,GAAI;gBAC7C,IAAI,MAAM,MAAM,SAAS,EAAE;oBACzB,SAAS,MAAM,CAAC;gBAClB;YACF;YAEA,0CAA0C;YAC1C,MAAM,cAAc,SAAS,GAAG,CAAC,OAAO;gBACtC,OAAO;gBACP,WAAW,MAAM;YACnB;YAEA,IAAI,MAAM,YAAY,SAAS,EAAE;gBAC/B,eAAe;gBACf,YAAY,KAAK,GAAG;gBACpB,YAAY,SAAS,GAAG,MAAM;YAChC,OAAO;gBACL,YAAY,KAAK;YACnB;YAEA,SAAS,GAAG,CAAC,IAAI;YAEjB,+BAA+B;YAC/B,IAAI,YAAY,KAAK,GAAG,aAAa;gBACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBACE,SAAS;oBACT,OAAO;oBACP,YAAY,KAAK,IAAI,CAAC,CAAC,YAAY,SAAS,GAAG,GAAG,IAAI;gBACxD,GACA;oBAAE,QAAQ;gBAAI;YAElB;YAEA,OAAO,QAAQ,SAAS;QAC1B;AACJ;AAGO,SAAS,YACd,OAAsE;IAEtE,OAAO,OAAO,SAAsB;QAClC,MAAM,QAAQ,KAAK,GAAG;QACtB,MAAM,SAAS,QAAQ,MAAM;QAC7B,MAAM,MAAM,QAAQ,GAAG;QAEvB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,EAAE,EAAE,OAAO,CAAC,EAAE,IAAI,UAAU,CAAC;QAEtE,IAAI;YACF,MAAM,WAAW,MAAM,QAAQ,SAAS;YACxC,MAAM,WAAW,KAAK,GAAG,KAAK;YAE9B,QAAQ,GAAG,CACT,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,EAAE,EAAE,OAAO,CAAC,EAAE,IAAI,GAAG,EAChD,SAAS,MAAM,CAChB,EAAE,EAAE,SAAS,GAAG,CAAC;YAGpB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,WAAW,KAAK,GAAG,KAAK;YAC9B,QAAQ,KAAK,CACX,CAAC,CAAC,EAAE,IAAI,OAAO,WAAW,GAAG,EAAE,EAAE,OAAO,CAAC,EAAE,IAAI,UAAU,EAAE,SAAS,IAAI,CAAC,EACzE;YAEF,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 497, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/api/categories/route.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\r\nimport { z } from \"zod\";\r\nimport connectDB from \"@/lib/mongodb\";\r\nimport Category from \"@/models/Category\";\r\nimport {\r\n  withAdmin,\r\n  withAuth,\r\n  withLogging,\r\n  type AuthenticatedRequest,\r\n} from \"@/lib/middleware\";\r\n\r\nconst createCategorySchema = z.object({\r\n  name: z.string().min(1, \"Name is required\").max(50, \"Name too long\").trim(),\r\n  description: z.string().max(200, \"Description too long\").optional(),\r\n  color: z\r\n    .string()\r\n    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, \"Invalid color format\")\r\n    .optional(),\r\n});\r\n\r\n// GET /api/categories - Get all categories (Public endpoint)\r\nexport async function GET() {\r\n  try {\r\n    await connectDB();\r\n\r\n    const categories = await Category.find({ isActive: true })\r\n      .sort({ name: 1 })\r\n      .lean();\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      data: { categories },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Error fetching categories:\", error);\r\n    return NextResponse.json(\r\n      { success: false, error: \"Failed to fetch categories\" },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// POST /api/categories - Create a new category (Admin only with middleware)\r\nexport const POST = withLogging(\r\n  withAuth(async (request: AuthenticatedRequest, context: any) => {\r\n    try {\r\n      await connectDB();\r\n\r\n      const body = await request.json();\r\n      const validatedData = createCategorySchema.parse(body);\r\n\r\n      // Check if category already exists (case-insensitive)\r\n      const existingCategory = await Category.findOne({\r\n        name: { $regex: new RegExp(`^${validatedData.name}$`, \"i\") },\r\n      });\r\n\r\n      if (existingCategory) {\r\n        return NextResponse.json(\r\n          { success: false, error: \"Category already exists\" },\r\n          { status: 400 }\r\n        );\r\n      }\r\n\r\n      // Create new category\r\n      const category = new Category(validatedData);\r\n      await category.save();\r\n\r\n      return NextResponse.json(\r\n        {\r\n          success: true,\r\n          message: \"Category created successfully\",\r\n          data: { category },\r\n          createdBy: request.user!.username,\r\n        },\r\n        { status: 201 }\r\n      );\r\n    } catch (error: any) {\r\n      if (error instanceof z.ZodError) {\r\n        return NextResponse.json(\r\n          { success: false, error: \"Validation failed\", details: error.errors },\r\n          { status: 400 }\r\n        );\r\n      }\r\n\r\n      // Handle MongoDB duplicate key errors\r\n      if (error.code === 11000 && error.keyPattern?.name) {\r\n        return NextResponse.json(\r\n          { success: false, error: \"Category name already exists\" },\r\n          { status: 400 }\r\n        );\r\n      }\r\n\r\n      console.error(\"Error creating category:\", error);\r\n      return NextResponse.json(\r\n        { success: false, error: \"Failed to create category\" },\r\n        { status: 500 }\r\n      );\r\n    }\r\n  })\r\n  // withAdmin(async (request: AuthenticatedRequest, context: any) => {\r\n);\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;AAOA,MAAM,uBAAuB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,IAAI,iBAAiB,IAAI;IACzE,aAAa,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,wBAAwB,QAAQ;IACjE,OAAO,mLAAA,CAAA,IAAC,CACL,MAAM,GACN,KAAK,CAAC,sCAAsC,wBAC5C,QAAQ;AACb;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,aAAa,MAAM,2HAAA,CAAA,UAAQ,CAAC,IAAI,CAAC;YAAE,UAAU;QAAK,GACrD,IAAI,CAAC;YAAE,MAAM;QAAE,GACf,IAAI;QAEP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBAAE;YAAW;QACrB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAA6B,GACtD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,MAAM,OAAO,CAAA,GAAA,0HAAA,CAAA,cAAW,AAAD,EAC5B,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,SAA+B;IAC7C,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,qBAAqB,KAAK,CAAC;QAEjD,sDAAsD;QACtD,MAAM,mBAAmB,MAAM,2HAAA,CAAA,UAAQ,CAAC,OAAO,CAAC;YAC9C,MAAM;gBAAE,QAAQ,IAAI,OAAO,CAAC,CAAC,EAAE,cAAc,IAAI,CAAC,CAAC,CAAC,EAAE;YAAK;QAC7D;QAEA,IAAI,kBAAkB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA0B,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,MAAM,WAAW,IAAI,2HAAA,CAAA,UAAQ,CAAC;QAC9B,MAAM,SAAS,IAAI;QAEnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT,SAAS;YACT,MAAM;gBAAE;YAAS;YACjB,WAAW,QAAQ,IAAI,CAAE,QAAQ;QACnC,GACA;YAAE,QAAQ;QAAI;IAElB,EAAE,OAAO,OAAY;QACnB,IAAI,iBAAiB,mLAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;gBAAqB,SAAS,MAAM,MAAM;YAAC,GACpE;gBAAE,QAAQ;YAAI;QAElB;QAEA,sCAAsC;QACtC,IAAI,MAAM,IAAI,KAAK,SAAS,MAAM,UAAU,EAAE,MAAM;YAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA+B,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAA4B,GACrD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}