"use client";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Eye,
  EyeOff,
  User,
  // Mail, // Commented out - used for register
  Lock,
  // UserPlus, // Commented out - used for register
  LogIn,
  // Github, // Commented out - used for register
  // Globe, // Commented out - used for register
} from "lucide-react";
import { loginUser } from "@/client_apis/auth";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";

// Zod validation schemas
const loginSchema = z.object({
  email: z.string().min(1, "Email or username is required"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  // rememberMe: z.boolean().optional(),
});

// Register schema (commented out)
// const registerSchema = z
//   .object({
//     username: z
//       .string()
//       .min(3, "Username must be at least 3 characters")
//       .max(30, "Username cannot exceed 30 characters")
//       .regex(
//         /^[a-zA-Z0-9_]+$/,
//         "Username can only contain letters, numbers, and underscores"
//       ),
//     email: z.string().email("Please enter a valid email address").toLowerCase(),
//     password: z
//       .string()
//       .min(6, "Password must be at least 6 characters")
//       .regex(
//         /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
//         "Password must contain at least one uppercase letter, one lowercase letter, and one number"
//       ),
//     confirmPassword: z.string(),
//     firstName: z
//       .string()
//       .max(50, "First name cannot exceed 50 characters")
//       .optional()
//       .or(z.literal("")),
//     lastName: z
//       .string()
//       .max(50, "Last name cannot exceed 50 characters")
//       .optional()
//       .or(z.literal("")),
//     agreeToTerms: z
//       .boolean()
//       .refine(
//         (val) => val === true,
//         "You must agree to the terms and conditions"
//       ),
//   })
//   .refine((data) => data.password === data.confirmPassword, {
//     message: "Passwords don't match",
//     path: ["confirmPassword"],
//   });

type LoginFormData = {
  email: string;
  password: string;
  // rememberMe?: boolean;
};

// Register form data type (commented out)
// type RegisterFormData = z.infer<typeof registerSchema>;

// Reusable UI Components
const Button = ({
  children,
  onClick,
  variant = "primary",
  size = "md",
  type = "button",
  disabled = false,
  className = "",
  fullWidth = false,
}: {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: "primary" | "secondary" | "outline" | "ghost";
  size?: "sm" | "md" | "lg";
  type?: "button" | "submit";
  disabled?: boolean;
  className?: string;
  fullWidth?: boolean;
}) => {
  const baseClasses =
    "inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2";

  const variants = {
    primary:
      "bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm",
    secondary: "bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",
    outline:
      "border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-blue-500",
    ghost: "text-gray-700 hover:bg-gray-100 focus:ring-gray-500",
  };

  const sizes = {
    sm: "px-3 py-2 text-sm",
    md: "px-4 py-2.5 text-sm",
    lg: "px-6 py-3 text-base",
  };

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`
        ${baseClasses} 
        ${variants[variant]} 
        ${sizes[size]} 
        ${fullWidth ? "w-full" : ""} 
        ${disabled ? "opacity-50 cursor-not-allowed" : ""} 
        ${className}
      `}
    >
      {children}
    </button>
  );
};

const Input = ({
  label,
  type = "text",
  placeholder,
  error,
  icon: Icon,
  showPasswordToggle = false,
  className = "",
  ...props
}: {
  label?: string;
  type?: string;
  placeholder?: string;
  error?: string;
  icon?: React.ComponentType<{ className?: string }>;
  showPasswordToggle?: boolean;
  className?: string;
  [key: string]: any;
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const inputType = showPasswordToggle
    ? showPassword
      ? "text"
      : "password"
    : type;

  return (
    <div className={`space-y-1 ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
        </label>
      )}
      <div className="relative">
        {Icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Icon className="h-5 w-5 text-gray-400" />
          </div>
        )}
        <input
          type={inputType}
          placeholder={placeholder}
          className={`
            block w-full rounded-lg border border-gray-300 px-3 py-2.5 text-sm 
            placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500
            ${Icon ? "pl-10" : ""} 
            ${showPasswordToggle ? "pr-10" : ""} 
            ${
              error
                ? "border-red-300 focus:border-red-500 focus:ring-red-500"
                : ""
            }
          `}
          {...props}
        />
        {showPasswordToggle && (
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
            ) : (
              <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
            )}
          </button>
        )}
      </div>
      {error && <p className="text-sm text-red-600">{error}</p>}
    </div>
  );
};

const Checkbox = ({
  label,
  error,
  ...props
}: {
  label: string;
  error?: string;
  [key: string]: any;
}) => (
  <div className="space-y-1">
    <div className="flex items-center">
      <input
        type="checkbox"
        className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
        {...props}
      />
      <label className="ml-2 block text-sm text-gray-700">{label}</label>
    </div>
    {error && <p className="text-sm text-red-600">{error}</p>}
  </div>
);

// Login Component
const LoginPage = () => {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    try {
      const res = await loginUser(data);

      if (!res.success) {
        toast.error(res.message);
        return;
      }
      toast.success(res.message);

      // Redirect to dashboard after successful login
      router.push("/dashboard/blogs");
    } catch (error) {
      console.error("Login error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 bg-gray-800 rounded-full flex items-center justify-center">
            <LogIn className="h-6 w-6 text-white" />
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Sign in to your account
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Welcome back! Please sign in to your account.
          </p>
        </div>

        <div className="bg-white py-8 px-6 shadow-sm rounded-lg border">
          <div className="space-y-6">
            <Input
              label="Email or Username"
              type="text"
              placeholder="Enter your email or username"
              icon={User}
              error={errors.email?.message}
              {...register("email")}
            />

            <Input
              label="Password"
              type="password"
              placeholder="Enter your password"
              icon={Lock}
              showPasswordToggle
              error={errors.password?.message}
              {...register("password")}
            />

            {/* <div className="flex items-center justify-between">
              <Checkbox label="Remember me" {...register("rememberMe")} />
              <button className="text-sm text-blue-600 hover:text-blue-500">
                Forgot your password?
              </button>
            </div> */}

            <Button
              type="submit"
              className="bg-gray-800 hover:bg-gray-700 cursor-pointer"
              size="lg"
              fullWidth
              disabled={isLoading}
              onClick={handleSubmit(onSubmit)}
            >
              {isLoading ? "Signing in..." : "Sign in"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
// Register Component (commented out)
// interface IResponse {
//   success: boolean;
//   message: string;
// }
// const RegisterPage = ({ onSwitchToLogin }: { onSwitchToLogin: () => void }) => {
//   const [isLoading, setIsLoading] = useState(false);

//   const {
//     register,
//     handleSubmit,
//     formState: { errors },
//     watch,
//     reset,
//   } = useForm<RegisterFormData>({
//     resolver: zodResolver(registerSchema),
//   });

//   const onSubmit = async (data: RegisterFormData) => {
//     setIsLoading(true);
//     try {
//       const { confirmPassword, agreeToTerms, ...submitData } = data;

//       const res = await fetch("/api/auth/register", {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//         },
//         body: JSON.stringify({ ...submitData, role: "admin" }),
//       });

//       const { success, message }: IResponse = await res.json();

//       if (!success) {
//         toast.error(message);
//         return;
//       }

//       toast.success(message);
//       reset();
//     } catch (error) {
//       console.error("Registration error:", error);
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   return (
//     <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
//       <div className="max-w-md w-full space-y-8">
//         <div className="text-center">
//           <div className="mx-auto h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
//             <UserPlus className="h-6 w-6 text-white" />
//           </div>
//           <h2 className="mt-6 text-3xl font-bold text-gray-900">
//             Create your account
//           </h2>
//           <p className="mt-2 text-sm text-gray-600">
//             Already have an account?{" "}
//             <button
//               onClick={onSwitchToLogin}
//               className="font-medium text-blue-600 hover:text-blue-500"
//             >
//               Sign in here
//             </button>
//           </p>
//         </div>

//         <div className="bg-white py-8 px-6 shadow-sm rounded-lg border">
//           <div className="space-y-6">
//             <Input
//               label="Username"
//               type="text"
//               placeholder="Choose a unique username"
//               icon={User}
//               error={errors.username?.message}
//               {...register("username")}
//             />

//             <Input
//               label="Email Address"
//               type="email"
//               placeholder="Enter your email address"
//               icon={Mail}
//               error={errors.email?.message}
//               {...register("email")}
//             />

//             <div className="grid grid-cols-2 gap-4">
//               <Input
//                 label="First Name"
//                 type="text"
//                 placeholder="First name"
//                 error={errors.firstName?.message}
//                 {...register("firstName")}
//               />
//               <Input
//                 label="Last Name"
//                 type="text"
//                 placeholder="Last name"
//                 error={errors.lastName?.message}
//                 {...register("lastName")}
//               />
//             </div>

//             <Input
//               label="Password"
//               type="password"
//               placeholder="Create a strong password"
//               icon={Lock}
//               showPasswordToggle
//               error={errors.password?.message}
//               {...register("password")}
//             />

//             <Input
//               label="Confirm Password"
//               type="password"
//               placeholder="Confirm your password"
//               icon={Lock}
//               showPasswordToggle
//               error={errors.confirmPassword?.message}
//               {...register("confirmPassword")}
//             />

//             <Checkbox
//               label="I agree to the Terms of Service and Privacy Policy"
//               error={errors.agreeToTerms?.message}
//               {...register("agreeToTerms")}
//             />

//             <Button
//               type="submit"
//               variant="primary"
//               size="lg"
//               fullWidth
//               disabled={isLoading}
//               onClick={handleSubmit(onSubmit)}
//             >
//               {isLoading ? "Creating account..." : "Create account"}
//             </Button>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// Main Auth Component (register functionality commented out)
export default function AuthPages() {
  // const [currentPage, setCurrentPage] = useState<"login" | "register">("login");

  return (
    <>
      <LoginPage />
      {/* Register page commented out */}
      {/* {currentPage === "login" ? (
        <LoginPage onSwitchToRegister={() => setCurrentPage("register")} />
      ) : (
        <RegisterPage onSwitchToLogin={() => setCurrentPage("login")} />
      )} */}
    </>
  );
}
