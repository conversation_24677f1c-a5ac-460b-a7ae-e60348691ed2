"use client";
import Image from "next/image";
import Link from "next/link";
import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  <PERSON>ubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarTrigger,
} from "@/components/ui/menubar";

interface NavLink {
  title: string;
  href: string;
  subLinks?: { title: string; href: string }[];
}

const Navbar: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [openMenus, setOpenMenus] = useState<{ [key: string]: boolean }>({});
  const [openMobileSubMenus, setOpenMobileSubMenus] = useState<{
    [key: string]: boolean;
  }>({});

  const navLinks: NavLink[] = [
    { title: "บ้าน ", href: "/" },
    { title: "เกี่ยวกับเรา", href: "/aboutus" },
    {
      title: "คาสิโน",
      href: "/wm-casino",
      subLinks: [
        { title: "เซ็กซี่", href: "/sexy" },
        { title: "SA Gaming", href: "/sa" },
        { title: "Dream Gaming", href: "/dream-gaming" },
        { title: "Evolution Lobby", href: "/evolution-lobby" },
        { title: "SV388 Portal", href: "/sv388" },
        { title: "AllBet Portal", href: "/all-bets" },
        { title: "PrettyGame Portal", href: "/pretty-game" },
        { title: "Pragmatic Play Casino", href: "/pragmatic-play" },
        { title: "WM Casino", href: "/wm-casino" },
        { title: "BigGame", href: "/big-game" },
        { title: "On Gaming Lobby", href: "/on-gaming-lobby" },
      ],
    },
    {
      title: "เกม",
      href: "/",
      subLinks: [
        { title: "JILI", href: "/jili-games" },
        { title: "PGSLOT", href: "/pg-games" },
        { title: "SBO Portal", href: "/sbo-bet" },
        { title: "LALIKA", href: "/lalika" },
      ],
    },
    { title: "บล็อก", href: "/blogs" },
  ];

  const toggleMobileSubMenu = (title: string) => {
    setOpenMobileSubMenus((prev) => ({
      ...prev,
      [title]: !prev[title],
    }));
  };

  const drawerVariants = {
    open: { x: 0, transition: { duration: 0.3 } },
    closed: { x: "100%", transition: { duration: 0.3 } },
  };

  const hamburgerLine = {
    closed: { rotate: 0, translateY: 0 },
    open: (i: number) => ({
      rotate: i === 1 ? 45 : -45,
      translateY: i === 1 ? 6 : -6,
    }),
  };

  const underlineVariants = {
    hidden: { scaleX: 0, originX: 0.5 },
    visible: {
      scaleX: 1,
      originX: 0.5,
      transition: { duration: 0.3, ease: "easeOut" },
    },
  };

  const menuVariants = {
    hidden: { opacity: 0, y: -10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.2, ease: "easeOut" },
    },
  };

  const subMenuVariants = {
    hidden: { height: 0, opacity: 0 },
    visible: {
      height: "auto",
      opacity: 1,
      transition: { duration: 0.2, ease: "easeOut" },
    },
  };

  return (
    <div className="absolute top-0 left-0 w-full bg-transparent  container mx-auto px-3 md:px-[4rem] z-40">
      <div className="hidden md:flex items-center justify-between py-4">
        <div className="flex items-center gap-8 text-sm md:text-base text-white font-bold">
          <Link href="/">
            <Image
              src="/logo.png"
              alt="FreeSpin Logo"
              priority
              width={60}
              height={60}
            />
          </Link>
          {navLinks.map((item, index) => (
            <div key={index} className="relative">
              {item.subLinks ? (
                <Menubar className="border-none bg-transparent">
                  <MenubarMenu>
                    <MenubarTrigger
                      className="text-base cursor-pointer bg-transparent hover:bg-transparent focus:bg-transparent data-[state=open]:text-white data-[state=open]:bg-transparent"
                      onPointerEnter={() => {
                        setOpenMenus((prev) => ({
                          ...prev,
                          [item.title]: true,
                        }));
                      }}
                      onPointerLeave={() => {
                        setOpenMenus((prev) => ({
                          ...prev,
                          [item.title]: false,
                        }));
                      }}
                    >
                      {item.title}
                    </MenubarTrigger>
                    <motion.div
                      variants={menuVariants}
                      initial="hidden"
                      animate={openMenus[item.title] ? "visible" : "hidden"}
                      className="z-[200] absolute left-0"
                    >
                      {/* <MenubarContent className="w-48 bg-black text-white outline-none rounded-md"> */}
                      <MenubarContent className="w-48 bg-[#1C1F26] text-gray-300 rounded-xl py-2 px-0 shadow-none border-none space-y-1">
                        {item.subLinks.map((subLink, subIndex) => (
                          <MenubarItem key={subIndex} asChild>
                            {/* <Link
                              href={subLink.href}
                              className="w-full px-4 py-2 hover:bg-[#DEA933] hover:text-black"
                            > */}
                            <Link
                              href={subLink.href}
                              className="block w-full px-4 py-3 text-left text-gray-300 hover:bg-[#DEA933] hover:text-black transition-colors duration-200"
                            >
                              {subLink.title}
                            </Link>
                          </MenubarItem>
                        ))}
                      </MenubarContent>
                    </motion.div>
                  </MenubarMenu>
                </Menubar>
              ) : (
                <Link className="text-base" href={item.href}>
                  {item.title}
                </Link>
              )}
              <motion.div
                className="absolute bottom-[-4px] left-0 right-0 h-[2px] bg-[#DEA933]"
                whileHover="visible"
                initial="hidden"
                variants={underlineVariants}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Mobile Menu */}
      <div className="flex md:hidden items-center justify-between py-4">
        <Link href="/">
          <Image src="/logo.png" alt="FreeSpin Logo" width={40} height={40} />
        </Link>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex flex-col justify-center items-center w-10 h-10 relative z-50"
        >
          <motion.span
            className="bg-white h-0.5 w-6 mb-1.5"
            custom={1}
            animate={isOpen ? "open" : "closed"}
            variants={hamburgerLine}
          />
          <motion.span
            className={`bg-white h-0.5 w-6 ${isOpen ? "hidden" : "block"}`}
          />
          <motion.span
            className="bg-white h-0.5 w-6 mt-1.5"
            custom={2}
            animate={isOpen ? "open" : "closed"}
            variants={hamburgerLine}
          />
        </button>
      </div>

      <AnimatePresence>
        {isOpen && (
          <>
            <motion.div
              className="fixed inset-0 bg-black/50 z-30 md:hidden"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsOpen(false)}
            />
            <motion.div
              className="fixed top-0 right-0 h-full w-[80%] bg-black z-40 md:hidden flex flex-col items-center justify-center gap-4"
              variants={drawerVariants}
              initial="closed"
              animate="open"
              exit="closed"
            >
              {navLinks.map((item, index) => (
                <div key={index} className="w-full text-center">
                  {item.subLinks ? (
                    <>
                      <button
                        onClick={() => toggleMobileSubMenu(item.title)}
                        className="text-white text-lg font-bold w-full py-2 flex justify-center items-center gap-2"
                      >
                        {item.title}
                        <motion.span
                          animate={{
                            rotate: openMobileSubMenus[item.title] ? 180 : 0,
                          }}
                          transition={{ duration: 0.2 }}
                        >
                          ▼
                        </motion.span>
                      </button>
                      <AnimatePresence>
                        {openMobileSubMenus[item.title] && (
                          <motion.div
                            variants={subMenuVariants}
                            initial="hidden"
                            animate="visible"
                            exit="hidden"
                            className="flex flex-col items-center bg-gray-900 w-full"
                          >
                            {item.subLinks.map((subLink, subIndex) => (
                              <Link
                                key={subIndex}
                                href={subLink.href}
                                className="text-white text-base py-2 w-full text-center hover:bg-[#DEA933] hover:text-black"
                                onClick={() => setIsOpen(false)}
                              >
                                {subLink.title}
                              </Link>
                            ))}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </>
                  ) : (
                    <motion.div
                      className="relative"
                      whileHover="visible"
                      initial="hidden"
                    >
                      <Link
                        href={item.href}
                        className="text-white text-lg font-bold py-2 block"
                        onClick={() => setIsOpen(false)}
                      >
                        {item.title}
                      </Link>
                      <motion.div
                        className="absolute bottom-[-4px] left-0 right-0 h-[2px] bg-[#DEA933]"
                        variants={underlineVariants}
                      />
                    </motion.div>
                  )}
                </div>
              ))}
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Navbar;
