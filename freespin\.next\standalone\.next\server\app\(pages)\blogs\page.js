(()=>{var e={};e.id=3120,e.ids=[3120],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5611:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(60687),a=r(49384);r(43210);let i=({className:e,text:t})=>(0,s.jsx)("h2",{className:(0,a.A)("text-2xl font-bold text-center text-white",e),children:t})},10444:(e,t,r)=>{"use strict";r.d(t,{BlogsPageClient:()=>j});var s=r(60687),a=r(43210),i=r(26001),n=r(88920),o=r(44493),l=r(29523),d=r(96834),c=r(85814),u=r.n(c),h=r(5611),p=r(85668),g=r.n(p);function m(e){return g()(e).format("DD/MM/YYYY")}let f=()=>{let[e,t]=(0,a.useState)(!1),r=(0,a.useMemo)(()=>Array.from({length:6},(e,t)=>({id:t,left:[20,40,60,80,30,70][t]||50,top:[10,30,50,70,20,80][t]||50,delay:.3*t,duration:3+t%3})),[]);return(0,a.useEffect)(()=>{t(!0)},[]),(0,s.jsxs)("div",{className:"absolute inset-0 z-30 flex items-center justify-center bg-gradient-to-br from-gray-900 via-gray-800 to-black",children:[(0,s.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,s.jsx)(i.P.div,{className:"absolute inset-0",animate:{background:["radial-gradient(circle at 20% 50%, #3b82f6 0%, transparent 50%)","radial-gradient(circle at 80% 50%, #8b5cf6 0%, transparent 50%)","radial-gradient(circle at 40% 80%, #06b6d4 0%, transparent 50%)","radial-gradient(circle at 20% 50%, #3b82f6 0%, transparent 50%)"]},transition:{duration:4,repeat:Number.POSITIVE_INFINITY,ease:"easeInOut"}})}),(0,s.jsxs)("div",{className:"relative z-10 flex flex-col items-center",children:[(0,s.jsxs)("div",{className:"relative w-20 h-20 mb-6",children:[(0,s.jsx)(i.P.div,{className:"absolute inset-0 border-4 border-transparent border-t-blue-500 border-r-purple-500 rounded-full",animate:{rotate:360},transition:{duration:1.5,repeat:Number.POSITIVE_INFINITY,ease:"linear"}}),(0,s.jsx)(i.P.div,{className:"absolute inset-2 border-4 border-transparent border-b-cyan-500 border-l-green-500 rounded-full",animate:{rotate:-360},transition:{duration:2,repeat:Number.POSITIVE_INFINITY,ease:"linear"}}),(0,s.jsx)(i.P.div,{className:"absolute inset-4 border-4 border-transparent border-t-orange-500 border-r-pink-500 rounded-full",animate:{rotate:360},transition:{duration:1,repeat:Number.POSITIVE_INFINITY,ease:"linear"}})]}),(0,s.jsx)("div",{className:"flex space-x-2 mb-4",children:[0,1,2].map(e=>(0,s.jsx)(i.P.div,{className:"w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full",animate:{scale:[1,1.5,1],opacity:[.5,1,.5]},transition:{duration:1.5,repeat:Number.POSITIVE_INFINITY,delay:.2*e,ease:"easeInOut"}},e))}),(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},className:"text-center",children:[(0,s.jsx)(i.P.p,{className:"text-white font-medium text-lg mb-2",animate:{opacity:[.5,1,.5]},transition:{duration:2,repeat:Number.POSITIVE_INFINITY,ease:"easeInOut"},children:"Loading News"}),(0,s.jsx)("p",{className:"text-gray-400 text-sm",children:"Preparing your content..."})]}),(0,s.jsx)(i.P.div,{className:"w-32 h-1 bg-gray-700 rounded-full mt-4 overflow-hidden",initial:{opacity:0},animate:{opacity:1},transition:{delay:1},children:(0,s.jsx)(i.P.div,{className:"h-full bg-gradient-to-r from-blue-500 via-purple-500 to-cyan-500 rounded-full",animate:{x:["-100%","100%"]},transition:{duration:2,repeat:Number.POSITIVE_INFINITY,ease:"easeInOut"}})})]}),e&&r.map(e=>(0,s.jsx)(i.P.div,{className:"absolute w-2 h-2 bg-white/20 rounded-full",style:{left:`${e.left}%`,top:`${e.top}%`},animate:{y:[0,-20,0],opacity:[0,1,0],scale:[.5,1,.5]},transition:{duration:e.duration,repeat:Number.POSITIVE_INFINITY,delay:e.delay,ease:"easeInOut"}},e.id))]})};function x({topNewsPosts:e=[]}){let[t,r]=(0,a.useState)(!1),[c,p]=(0,a.useState)(!1),g=e[0];if(!g)return(0,s.jsxs)("div",{children:[(0,s.jsx)(h.A,{text:"Top News",className:"mb-6 text-start"}),(0,s.jsx)(o.Zp,{className:"relative flex flex-col justify-center items-center border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0",children:(0,s.jsxs)("div",{className:"text-center text-gray-400",children:[(0,s.jsx)("p",{children:"No top news available"}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Mark a blog post as “Top News” to display it here"})]})})]});let x=g.banner||"/blogs/blg1.png";return(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:" text-lg md:text-xl lg:text-2xl mb-6 text-start",children:"Explore News & Blogs from Freespin168"}),(0,s.jsx)(u(),{href:`/blogs/${g.slug}`,children:(0,s.jsxs)(o.Zp,{className:"relative flex flex-col justify-end border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0 cursor-pointer hover:scale-[1.02] transition-transform duration-300",children:[(0,s.jsxs)("div",{className:"absolute inset-0 z-0",children:[(0,s.jsx)("img",{src:x||"/placeholder.svg",alt:g.title,className:`w-full h-full object-cover transition-opacity duration-500 ${t?"opacity-100":"opacity-0"}`,onLoad:()=>r(!0),onError:()=>{p(!0),r(!0)}}),c&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-gray-800 via-gray-900 to-black"})]}),(0,s.jsx)(n.N,{children:!t&&(0,s.jsx)(i.P.div,{initial:{opacity:1},exit:{opacity:0},transition:{duration:.5},children:(0,s.jsx)(f,{})})}),(0,s.jsx)(n.N,{children:t&&(0,s.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,ease:"easeOut"},children:(0,s.jsxs)(o.Wu,{className:"p-6 relative z-20 h-auto",children:[(0,s.jsx)("div",{className:"absolute inset-0 z-10 backdrop-blur-md bg-black/30"}),(0,s.jsxs)("div",{className:"relative z-20",children:[(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.4,delay:.2},className:"flex items-center gap-2 mb-3",children:[(0,s.jsx)(d.E,{variant:"secondary",className:"bg-orange-600 text-white hover:bg-orange-700",children:"HOT"}),(0,s.jsx)("span",{className:"text-sm text-gray-400",children:m(g.publishedAt||g.createdAt)})]}),(0,s.jsx)(i.P.h3,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.4,delay:.3},className:"text-xl font-bold mb-4 text-white leading-tight line-clamp-3",children:g.title}),(0,s.jsx)(i.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.4,delay:.5},children:(0,s.jsx)(l.$,{className:"bg-[#07BC0C] cursor-pointer text-white hover:bg-[#06A50B] transition-colors",children:"Read More"})})]})]})})})]})})]})}var b=r(30474);function v({posts:e,onLoadMore:t,isLoading:r}){return console.log("posts",e),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-6 text-white",children:"Latest Blogs"}),(0,s.jsxs)("div",{className:"flex flex-col  gap-4 w-full",children:[e.length>0?e.map(e=>(0,s.jsx)(u(),{href:`/blogs/${e.slug}`,children:(0,s.jsx)(o.Zp,{className:"bg-gray-800 border-gray-700 hover:bg-gray-750 p-0 transition-colors cursor-pointer",children:(0,s.jsxs)(o.Wu,{className:"flex p-0",children:[(0,s.jsx)("div",{className:"relative size-24 flex-shrink-0",children:(0,s.jsx)(b.default,{src:e.banner||"/blogs/latest1.png",alt:e.title,fill:!0,className:"object-cover rounded-l-lg"})}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,s.jsx)("div",{className:"p-4 pb-2",children:(0,s.jsx)("h3",{className:"font-semibold text-white text-sm leading-tight mb-2 line-clamp-2",children:e.title})}),(0,s.jsx)("div",{className:"mx-4 h-[1px]  bg-[#4F5259] "}),(0,s.jsx)("div",{className:"p-4 pt-2",children:(0,s.jsxs)("div",{className:"flex items-center gap-3 justify-between",children:[(0,s.jsx)("span",{className:"text-xs text-gray-400",children:function(e){let t=g()(),r=g()(e),s=t.diff(r,"day");if(0===s)return"Today";if(1===s)return"1 day ago";if(s<7)return`${s} days ago`;if(s<30){let e=Math.floor(s/7);return 1===e?"1 week ago":`${e} weeks ago`}if(s<365){let e=Math.floor(s/30);return 1===e?"1 month ago":`${e} months ago`}else{let e=Math.floor(s/365);return 1===e?"1 year ago":`${e} years ago`}}(e.publishedAt||e.createdAt)}),(0,s.jsx)(d.E,{variant:"outline",className:"text-xs border-orange-600 text-orange-400",children:e.categories[0]||"BLOG"})]})})]})]})})},e._id)):(0,s.jsx)("div",{className:"text-center text-gray-400 py-8",children:r?"Loading posts...":"No blog posts available."}),t&&(0,s.jsx)("div",{className:"flex items-center justify-center mt-2",children:(0,s.jsx)(l.$,{className:"bg-[#07BC0C] cursor-pointer text-white hover:bg-[#06A50B] transition-colors",onClick:t,disabled:r,children:r?"Loading...":"Load More"})})]})]})}var y=r(47033),w=r(14952);function N({text:e="Explore",pagination:t=!1,posts:r,postsPerPage:i=4}){let[n,d]=(0,a.useState)(1),c=Math.ceil(r.length/i),p=(n-1)*i,g=r.slice(p,p+i),f=e=>{d(e)};return(0,s.jsxs)("div",{children:[(0,s.jsx)(h.A,{text:e,className:"text-start mb-6"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:g.length>0?g.map(e=>(0,s.jsx)(u(),{href:`/blogs/${e.slug}`,children:(0,s.jsxs)(o.Zp,{className:"bg-gray-800 border-gray-700 py-0 overflow-hidden hover:bg-gray-750 transition-colors cursor-pointer",children:[(0,s.jsxs)("div",{className:"relative h-48 w-full",children:[(0,s.jsx)(b.default,{src:e.banner||"/placeholder.svg",alt:e.title,fill:!0,className:"object-cover"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"})]}),(0,s.jsxs)(o.Wu,{className:"px-6 py-2",children:[(0,s.jsx)("div",{className:"flex items-center gap-2 mb-3",children:(0,s.jsx)("span",{className:"text-sm text-gray-400",children:m(e.publishedAt||e.createdAt)})}),(0,s.jsx)("h3",{className:"text-lg font-bold mb-3 text-white leading-tight",children:e.title})]})]})},e._id)):(0,s.jsx)("div",{className:"col-span-2 text-center text-gray-400 py-8",children:"No posts available."})}),t&&(0,s.jsx)("div",{className:"mt-8 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(l.$,{variant:"ghost",size:"icon",onClick:()=>{n>1&&d(n-1)},disabled:n<=1,className:n<=1?"bg-[#131925] text-gray-500 cursor-not-allowed rounded-full w-10 h-10 opacity-50":"bg-[#131925] text-white hover:bg-[#A945F1] rounded-full w-10 h-10 transition-all duration-200",children:(0,s.jsx)(y.A,{className:"h-5 w-5"})}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mx-4",children:[Array.from({length:Math.min(c,7)},(e,t)=>{let r=t+1,a=r===n;return(0,s.jsx)(l.$,{variant:"ghost",size:"icon",onClick:()=>f(r),className:a?"bg-[#A945F1] text-[#310267] hover:bg-[#8B37C7] rounded-full w-10 h-10 font-semibold":"bg-[#131925] text-white hover:bg-[#A945F1] hover:text-[#310267] transition-all duration-200 rounded-full w-10 h-10",children:r},r)}),c>7&&(0,s.jsx)("span",{className:"text-gray-400 px-2",children:"..."})]}),(0,s.jsx)(l.$,{variant:"ghost",size:"icon",onClick:()=>{n<c&&d(n+1)},disabled:n>=c,className:n>=c?"bg-[#131925] text-gray-500 cursor-not-allowed rounded-full w-10 h-10 opacity-50":"bg-[#131925] text-white hover:bg-[#A945F1] rounded-full w-10 h-10 transition-all duration-200",children:(0,s.jsx)(w.A,{className:"h-5 w-5"})})]})})]})}function j({initialLatestPosts:e,explorePosts:t,topNewsPosts:r}){let[i,n]=(0,a.useState)(e),[o,l]=(0,a.useState)(!1),[d,c]=(0,a.useState)(1),u=async()=>{if(!o){l(!0);try{let e=d+1,t=await fetch(`/api/public/posts?page=${e}&limit=6`);if(t.ok){let r=await t.json();if(r.success&&r.data?.posts){let t=r.data.posts;n(e=>[...e,...t]),c(e)}}}catch(e){console.error("Error loading more posts:",e)}finally{l(!1)}}};return(0,s.jsx)("div",{className:"min-h-screen bg-black text-white",children:(0,s.jsx)("div",{className:"container mx-auto px-3 md:px-[4rem] pt-24 py-8",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12",children:[(0,s.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,s.jsx)(x,{topNewsPosts:r}),(0,s.jsx)(N,{pagination:!0,posts:t,postsPerPage:4})]}),(0,s.jsx)("div",{className:"lg:col-span-1",children:(0,s.jsx)(v,{posts:i,onLoadMore:u,isLoading:o})})]})})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14952:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19786:(e,t,r)=>{Promise.resolve().then(r.bind(r,36113))},24224:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});var s=r(49384);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=s.$,n=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:n,defaultVariants:o}=t,l=Object.keys(n).map(e=>{let t=null==r?void 0:r[e],s=null==o?void 0:o[e];if(null===t)return null;let i=a(t)||a(s);return n[e][i]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return i(e,l,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...d}[t]):({...o,...d})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},24407:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["(pages)",{children:["blogs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,83098)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\(pages)\\blogs\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,35299)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\(pages)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\(pages)\\blogs\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(pages)/blogs/page",pathname:"/blogs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(60687);r(43210);var a=r(8730),i=r(24224),n=r(4780);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:i=!1,...l}){let d=i?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:r,className:e})),...l})}},33873:e=>{"use strict";e.exports=require("path")},36113:(e,t,r)=>{"use strict";r.d(t,{BlogsPageClient:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call BlogsPageClient() from the server but BlogsPageClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\(pages)\\blogs\\components\\BlogsPageClient.tsx","BlogsPageClient")},44493:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>o,Zp:()=>i,aR:()=>n});var s=r(60687);r(43210);var a=r(4780);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},47033:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},55530:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(56037),a=r.n(s);let i=new s.Schema({title:{type:String,required:[!0,"Post title is required"],trim:!0,maxlength:[1e3,"Title cannot exceed 200 characters"]},slug:{type:String,trim:!0,lowercase:!0},canonicalUrl:{type:String,trim:!0,validate:{validator:e=>!e||/^https?:\/\/.+/.test(e),message:"Canonical URL must be a valid URL"}},existingUrl:{type:Boolean,default:!1},content:{type:String,required:[!0,"Post content is required"]},excerpt:{type:String,trim:!0,maxlength:[1e3,"Excerpt cannot exceed 300 characters"]},description:{type:String},author:{type:String,trim:!0},isBlog:{type:Boolean,default:!0},categories:[{type:a().Schema.Types.ObjectId,ref:"Category"}],tags:[{type:String,trim:!0,lowercase:!0}],metaTitle:{type:String,required:[!0,"Meta title is required"],maxlength:[1e3,"Meta title cannot exceed 60 characters"]},metaDescription:{type:String,required:[!0,"Meta description is required"],maxlength:[1e3,"Meta description cannot exceed 160 characters"]},metaKeywords:{type:String,required:[!0,"Meta keywords are required"],maxlength:[1e3,"Meta keywords cannot exceed 200 characters"]},banner:{type:String,required:[!0,"Banner image is required"],trim:!0},status:{type:String,enum:["draft","published","archived"],default:"draft"},isPublished:{type:Boolean,default:!1},publishedAt:{type:Date,default:null},views:{type:Number,default:0},readTime:{type:Number,default:1},isTopNews:{type:Boolean,default:!1}},{timestamps:!0});i.pre("save",function(e){if(this.isModified("title")&&!this.slug&&(this.slug=this.title.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,"")),this.isModified("content")){let e=(this.content||"").split(/\s+/).length;this.readTime=Math.ceil(e/200)}this.isModified("status")&&"published"===this.status&&!this.publishedAt&&(this.publishedAt=new Date,this.isPublished=!0),e()}),i.index({categories:1}),i.index({tags:1}),i.index({status:1}),i.index({isPublished:1}),i.index({publishedAt:-1}),i.index({"banner.title":1}),i.index({title:"text",content:"text",metaTitle:"text",metaDescription:"text"});let n=a().models.Post||a().model("Post",i)},56037:e=>{"use strict";e.exports=require("mongoose")},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:i="",children:n,iconNode:c,...u},h)=>(0,s.createElement)("svg",{ref:h,...d,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:o("lucide",i),...!n&&!l(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(n)?n:[n]])),u=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...i},l)=>(0,s.createElement)(c,{ref:l,iconNode:t,className:o(`lucide-${a(n(e))}`,`lucide-${e}`,r),...i}));return r.displayName=n(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73944:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(56037),a=r.n(s);let i=new s.Schema({name:{type:String,required:[!0,"Category name is required"],unique:!0,trim:!0,maxlength:[50,"Category name cannot exceed 50 characters"]},description:{type:String,maxlength:[200,"Description cannot exceed 200 characters"]},color:{type:String,default:"#6366f1",match:[/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,"Please enter a valid hex color"]},isActive:{type:Boolean,default:!0}},{timestamps:!0});i.index({isActive:1});let n=a().models.Category||a().model("Category",i)},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(56037),a=r.n(s);let i=process.env.MONGODB_URI;if(!i)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let n=global?.mongoose;n||(n=global.mongoose={conn:null,promise:null});let o=async function(){if(n.conn)return n.conn;n.promise||(n.promise=a().connect(i,{bufferCommands:!1}).then(e=>e));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}},79551:e=>{"use strict";e.exports=require("url")},82834:(e,t,r)=>{Promise.resolve().then(r.bind(r,10444))},83098:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,dynamic:()=>l,metadata:()=>c,revalidate:()=>d});var s=r(37413);r(61120);var a=r(36113),i=r(75745),n=r(55530),o=r(73944);let l="force-dynamic",d=0,c={title:"Casino News & Gaming Blog | Latest Updates & Reviews",alternates:{canonical:"https://www.freespin168.asia/blogs"},description:"Stay updated with the latest casino news, game reviews, and industry insights. Read expert analysis and tips from FreeSpin168's gaming blog.",keywords:["casino news","gaming blog","casino reviews","online casino updates"]},u=async({searchParams:e})=>{try{await (0,i.A)(),o.A;let e=n.A;console.log("=== BLOGS PAGE FRESH LOAD ==="),console.log("Timestamp:",new Date().toISOString());let t=await e.find({isBlog:!0}).lean();console.log("=== BLOGS PAGE DEBUG ==="),console.log("Total blog posts in database:",t.length),console.log("Posts by status:",t.reduce((e,t)=>(e[t.status]=(e[t.status]||0)+1,e),{})),console.log("Published posts:",t.filter(e=>"published"===e.status&&e.isPublished).length);let r=await e.find({status:"published",isPublished:!0,isBlog:!0}).populate("categories","name description").sort({publishedAt:-1,createdAt:-1}).limit(6).lean();console.log("Latest posts found:",r.length);let l=await e.find({status:"published",isPublished:!0,isBlog:!0}).sort({publishedAt:-1,createdAt:-1}).lean(),d=await e.find({status:"published",isPublished:!0,isBlog:!0,isTopNews:!0}).sort({publishedAt:-1,createdAt:-1}).limit(5).lean();console.log("=== FRESH DATA FETCHED ==="),console.log("Latest posts:",r.length),console.log("Explore posts:",l.length),console.log("Top news posts:",d.length),console.log("Top news titles:",d.map(e=>e.title));let c=e=>e.map(e=>({_id:e._id.toString(),title:e.title,description:e.excerpt||e.description||e.content?.substring(0,200)||"",banner:e.banner||"",slug:e?.slug||e?._id,categories:e.categories?.map(e=>e.name)||[],tags:e.tags||[],publishedAt:e.publishedAt?.toISOString()||e.createdAt?.toISOString()||"",createdAt:e.createdAt?.toISOString()||"",readTime:e.readTime||5,views:e.views||0,metaTitle:e.metaTitle||e.title||"",metaDescription:e.metaDescription||e.excerpt||e.description||""})),u=c(r),h=c(l),p=c(d);return(0,s.jsx)(a.BlogsPageClient,{initialLatestPosts:u,explorePosts:h,topNewsPosts:p})}catch(e){return console.error("Error fetching posts:",e),(0,s.jsx)(a.BlogsPageClient,{initialLatestPosts:[],explorePosts:[],topNewsPosts:[]})}}},85668:function(e){e.exports=function(){"use strict";var e="millisecond",t="second",r="minute",s="hour",a="week",i="month",n="quarter",o="year",l="date",d="Invalid Date",c=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,u=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,h=function(e,t,r){var s=String(e);return!s||s.length>=t?e:""+Array(t+1-s.length).join(r)+e},p="en",g={};g[p]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||t[0])+"]"}};var m="$isDayjsObject",f=function(e){return e instanceof y||!(!e||!e[m])},x=function e(t,r,s){var a;if(!t)return p;if("string"==typeof t){var i=t.toLowerCase();g[i]&&(a=i),r&&(g[i]=r,a=i);var n=t.split("-");if(!a&&n.length>1)return e(n[0])}else{var o=t.name;g[o]=t,a=o}return!s&&a&&(p=a),a||!s&&p},b=function(e,t){if(f(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new y(r)},v={s:h,z:function(e){var t=-e.utcOffset(),r=Math.abs(t);return(t<=0?"+":"-")+h(Math.floor(r/60),2,"0")+":"+h(r%60,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var s=12*(r.year()-t.year())+(r.month()-t.month()),a=t.clone().add(s,i),n=r-a<0,o=t.clone().add(s+(n?-1:1),i);return+(-(s+(r-a)/(n?a-o:o-a))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(d){return({M:i,y:o,w:a,d:"day",D:l,h:s,m:r,s:t,ms:e,Q:n})[d]||String(d||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};v.l=x,v.i=f,v.w=function(e,t){return b(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var y=function(){function h(e){this.$L=x(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[m]=!0}var p=h.prototype;return p.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if(v.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var s=t.match(c);if(s){var a=s[2]-1||0,i=(s[7]||"0").substring(0,3);return r?new Date(Date.UTC(s[1],a,s[3]||1,s[4]||0,s[5]||0,s[6]||0,i)):new Date(s[1],a,s[3]||1,s[4]||0,s[5]||0,s[6]||0,i)}}return new Date(t)}(e),this.init()},p.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},p.$utils=function(){return v},p.isValid=function(){return this.$d.toString()!==d},p.isSame=function(e,t){var r=b(e);return this.startOf(t)<=r&&r<=this.endOf(t)},p.isAfter=function(e,t){return b(e)<this.startOf(t)},p.isBefore=function(e,t){return this.endOf(t)<b(e)},p.$g=function(e,t,r){return v.u(e)?this[t]:this.set(r,e)},p.unix=function(){return Math.floor(this.valueOf()/1e3)},p.valueOf=function(){return this.$d.getTime()},p.startOf=function(e,n){var d=this,c=!!v.u(n)||n,u=v.p(e),h=function(e,t){var r=v.w(d.$u?Date.UTC(d.$y,t,e):new Date(d.$y,t,e),d);return c?r:r.endOf("day")},p=function(e,t){return v.w(d.toDate()[e].apply(d.toDate("s"),(c?[0,0,0,0]:[23,59,59,999]).slice(t)),d)},g=this.$W,m=this.$M,f=this.$D,x="set"+(this.$u?"UTC":"");switch(u){case o:return c?h(1,0):h(31,11);case i:return c?h(1,m):h(0,m+1);case a:var b=this.$locale().weekStart||0,y=(g<b?g+7:g)-b;return h(c?f-y:f+(6-y),m);case"day":case l:return p(x+"Hours",0);case s:return p(x+"Minutes",1);case r:return p(x+"Seconds",2);case t:return p(x+"Milliseconds",3);default:return this.clone()}},p.endOf=function(e){return this.startOf(e,!1)},p.$set=function(a,n){var d,c=v.p(a),u="set"+(this.$u?"UTC":""),h=((d={}).day=u+"Date",d[l]=u+"Date",d[i]=u+"Month",d[o]=u+"FullYear",d[s]=u+"Hours",d[r]=u+"Minutes",d[t]=u+"Seconds",d[e]=u+"Milliseconds",d)[c],p="day"===c?this.$D+(n-this.$W):n;if(c===i||c===o){var g=this.clone().set(l,1);g.$d[h](p),g.init(),this.$d=g.set(l,Math.min(this.$D,g.daysInMonth())).$d}else h&&this.$d[h](p);return this.init(),this},p.set=function(e,t){return this.clone().$set(e,t)},p.get=function(e){return this[v.p(e)]()},p.add=function(e,n){var l,d=this;e=Number(e);var c=v.p(n),u=function(t){var r=b(d);return v.w(r.date(r.date()+Math.round(t*e)),d)};if(c===i)return this.set(i,this.$M+e);if(c===o)return this.set(o,this.$y+e);if("day"===c)return u(1);if(c===a)return u(7);var h=((l={})[r]=6e4,l[s]=36e5,l[t]=1e3,l)[c]||1,p=this.$d.getTime()+e*h;return v.w(p,this)},p.subtract=function(e,t){return this.add(-1*e,t)},p.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||d;var s=e||"YYYY-MM-DDTHH:mm:ssZ",a=v.z(this),i=this.$H,n=this.$m,o=this.$M,l=r.weekdays,c=r.months,h=r.meridiem,p=function(e,r,a,i){return e&&(e[r]||e(t,s))||a[r].slice(0,i)},g=function(e){return v.s(i%12||12,e,"0")},m=h||function(e,t,r){var s=e<12?"AM":"PM";return r?s.toLowerCase():s};return s.replace(u,function(e,s){return s||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return v.s(t.$y,4,"0");case"M":return o+1;case"MM":return v.s(o+1,2,"0");case"MMM":return p(r.monthsShort,o,c,3);case"MMMM":return p(c,o);case"D":return t.$D;case"DD":return v.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return p(r.weekdaysMin,t.$W,l,2);case"ddd":return p(r.weekdaysShort,t.$W,l,3);case"dddd":return l[t.$W];case"H":return String(i);case"HH":return v.s(i,2,"0");case"h":return g(1);case"hh":return g(2);case"a":return m(i,n,!0);case"A":return m(i,n,!1);case"m":return String(n);case"mm":return v.s(n,2,"0");case"s":return String(t.$s);case"ss":return v.s(t.$s,2,"0");case"SSS":return v.s(t.$ms,3,"0");case"Z":return a}return null}(e)||a.replace(":","")})},p.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},p.diff=function(e,l,d){var c,u=this,h=v.p(l),p=b(e),g=(p.utcOffset()-this.utcOffset())*6e4,m=this-p,f=function(){return v.m(u,p)};switch(h){case o:c=f()/12;break;case i:c=f();break;case n:c=f()/3;break;case a:c=(m-g)/6048e5;break;case"day":c=(m-g)/864e5;break;case s:c=m/36e5;break;case r:c=m/6e4;break;case t:c=m/1e3;break;default:c=m}return d?c:v.a(c)},p.daysInMonth=function(){return this.endOf(i).$D},p.$locale=function(){return g[this.$L]},p.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),s=x(e,t,!0);return s&&(r.$L=s),r},p.clone=function(){return v.w(this.$d,this)},p.toDate=function(){return new Date(this.valueOf())},p.toJSON=function(){return this.isValid()?this.toISOString():null},p.toISOString=function(){return this.$d.toISOString()},p.toString=function(){return this.$d.toUTCString()},h}(),w=y.prototype;return b.prototype=w,[["$ms",e],["$s",t],["$m",r],["$H",s],["$W","day"],["$M",i],["$y",o],["$D",l]].forEach(function(e){w[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),b.extend=function(e,t){return e.$i||(e(t,y,b),e.$i=!0),b},b.locale=x,b.isDayjs=f,b.unix=function(e){return b(1e3*e)},b.en=g[p],b.Ls=g,b.p={},b}()},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(60687);r(43210);var a=r(8730),i=r(24224),n=r(4780);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...i}){let l=r?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(o({variant:t}),e),...i})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,6944,1658,9733,5036,8378],()=>r(24407));module.exports=s})();