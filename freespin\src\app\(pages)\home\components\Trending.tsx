"use client";

import React from "react";
import Slider from "react-slick";
import {
  ArrowLeft as FaArrowLeft,
  ArrowRight as FaArrowRight,
} from "lucide-react";
import BasicHeader from "./BasicHeader";

type TrendingCard = {
  date: string;
  title: string;
  description: string;
  imageUrl: string;
};

const PrevArrow = ({ onClick }: any) => (
  <button
    className="absolute left-[-10px] top-[40%] z-10 bg-fuchsia-600 p-2 rounded-full text-white"
    onClick={onClick}
  >
    <FaArrowLeft />
  </button>
);

const NextArrow = ({ onClick }: any) => (
  <button
    className="absolute cursor-pointer right-[-10px] top-[40%] z-10 bg-fuchsia-600 p-2 rounded-full text-white"
    onClick={onClick}
  >
    <FaArrowRight />
  </button>
);

const Trending: React.FC = () => {
  const trendingData: TrendingCard[] = [
    {
      date: "06/02/2025",
      title:
        "ทำไม 918Kiss ประเทศไทยถึงโดดเด่นในฐานะตัวเลือกอันดับหนึ่งของคนรักสล็อตออนไลน์",
      description:
        "918Kiss ประเทศไทยได้รับชื่อเสียงในฐานะผู้ให้บริการเกมสล็อตหลากหลาย พร้อมประสบการณ์ใช้งานที่ราบรื่นและรางวัลสุดตื่นเต้น ด้วยกราฟิกคุณภาพสูง เกมเล่นลื่นไหล และระบบความปลอดภัยที่เชื่อถือได้ จึงไม่แปลกใจที่ 918Kiss ยังคงเป็นที่ชื่นชอบของนักเล่นสล็อตออนไลน์ทั่วประเทศไทย",
      imageUrl: "/f88c8a10-0091-41d4-809e-54ec51006305.png",
    },
    {
      date: "06/02/2025",
      title:
        "สัมผัสประสบการณ์เดิมพันกีฬาและคาสิโนออนไลน์กับ UFABET ได้ที่ ChokD99.com",
      description:
        "สนุกกับการเดิมพันกีฬาระดับพรีเมียมและเกมคาสิโนออนไลน์สุดเร้าใจกับ UFABET ที่ ChokD99.com ตั้งแต่พนันกีฬาสดไปจนถึงเกมยอดนิยมอย่างสล็อต บาคาร่า และรูเล็ต สัมผัสประสบการณ์เกมที่น่าเชื่อถือและมีคุณภาพครบจบในที่เดียว",
      imageUrl: "/f88c8a10-0091-41d4-809e-54ec51006305.png",
    },
    {
      date: "06/02/2025",
      title: "สัมผัสสุดยอดประสบการณ์คาสิโนออนไลน์กับ 918Kiss ประเทศไทย",
      description:
        "ก้าวเข้าสู่โลกแห่งการเล่นเกมสุดเร้าใจ ดีไซน์ทันสมัย และรางวัลสุดคุ้มกับ 918Kiss ประเทศไทย ตั้งแต่สล็อตคลาสสิกไปจนถึงเกมคาสิโนแอ็กชันสุดมัน เพลิดเพลินกับประสบการณ์เล่นเกมที่ลื่นไหลและปลอดภัย ได้รับความไว้วางใจจากผู้เล่นทั่วประเทศ",
      imageUrl: "/f88c8a10-0091-41d4-809e-54ec51006305.png",
    },
  ];

  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
    responsive: [
      {
        breakpoint: 1024,
        settings: { slidesToShow: 2 },
      },
      {
        breakpoint: 768,
        settings: { slidesToShow: 1 },
      },
    ],
  };

  return (
    <section className="bg-black py-6">
      <div className="container mx-auto px-3 md:px-[4rem]  py-10 ">
        <BasicHeader text="สิ่งที่กำลังเป็นที่นิยม" className="mb-8" />
        <Slider {...settings}>
          {trendingData.map((item, index) => (
            <div key={index} className="px-3 max-h-[395px] h-full">
              <div className="rounded-2xl overflow-hidden shadow-lg bg-[#111] text-white">
                <img
                  src="/homepage/blog.png"
                  alt="Casino Card"
                  className="w-full h-56 object-cover"
                />
                <div className="p-4">
                  <p className="text-sm text-purple-300">{item.date}</p>
                  <h3 className="text-lg font-semibold mt-2">{item.title}</h3>
                  <p className="text-sm text-gray-400 mt-2 truncate">
                    {item.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </Slider>
      </div>
    </section>
  );
};

export default Trending;
