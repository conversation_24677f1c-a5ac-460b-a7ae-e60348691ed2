import React from "react";
import Image from "next/image";
import { notFound } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { BlogsSection } from "../components/BlogSection";
import { RelatedBlogsSection } from "./components/RelatedBlogsSection";
import connectDB from "@/lib/mongodb";
import Post from "@/models/Post";
import Category from "@/models/Category";
import { formatDate } from "@/utils/dateUtils";
import { PublicBlogPost } from "@/client_apis/api/blog";
import { isValidObjectId } from "mongoose";
import { Metadata } from "next";

interface BlogPageProps {
  params: Promise<{
    locale: string;
    id: string;
  }>;
}
export async function generateMetadata({
  params,
}: BlogPageProps): Promise<Metadata> {
  const { id, locale } = await params;
  const canonicalUrl = `https://www.freespin168.asia/${locale}/blogs/${id}`;

  try {
    await connectDB();

    const post = await Post.findOne({
      slug: id,
      status: "published",
      isPublished: true,
      isBlog: true,
    }).lean();

    if (!post) {
      return {
        title: "Blog Post Not Found",
        alternates: {
          canonical: canonicalUrl,
        },
        description: "The requested blog post could not be found.",
      };
    }

    return {
      title: (post as any).metaTitle || (post as any).title,
      description:
        (post as any).metaDescription ||
        (post as any).excerpt ||
        (post as any).description,
      alternates: {
        canonical: canonicalUrl,
      },
      openGraph: {
        title: (post as any).metaTitle || (post as any).title,
        description:
          (post as any).metaDescription ||
          (post as any).excerpt ||
          (post as any).description,
        images: (post as any).banner ? [{ url: (post as any).banner }] : [],
        type: "article",
        publishedTime: (post as any).publishedAt?.toISOString(),
      },
      twitter: {
        card: "summary_large_image",
        title: (post as any).metaTitle || (post as any).title,
        description:
          (post as any).metaDescription ||
          (post as any).excerpt ||
          (post as any).description,
        images: (post as any).banner ? [(post as any).banner] : [],
      },
    };
  } catch (error) {
    console.error("Error generating metadata:", error);
    return {
      title: "Blog Post",
      description: "Read our latest blog post.",
      alternates: {
        canonical: canonicalUrl,
      },
    };
  }
}
const page = async ({ params }: BlogPageProps) => {
  const { locale, id } = await params;

  try {
    // Connect to database
    await connectDB();

    // Find the blog post by ID
    const post = await Post.findOne({
      slug: id,
      status: "published",
      isPublished: true,
      isBlog: true,
    })
      .populate("categories", "name description")
      .lean();

    if (!post) {
      notFound();
    }

    // Type cast for better TypeScript support
    const typedPost = post as any;

    // Get related posts (same categories, excluding current post)
    const relatedPosts = await Post.find({
      slug: { $ne: id },
      status: "published",
      isPublished: true,
      isBlog: true,
      categories: { $in: typedPost.categories || [] },
    })
      .populate("categories", "name description")
      .sort({ publishedAt: -1, createdAt: -1 })
      .limit(6) // Increased to show more related posts
      .lean();

    console.log("=== RELATED POSTS DEBUG ===");
    console.log("Current post categories:", typedPost.categories);
    console.log("Related posts found:", relatedPosts.length);

    // Get latest posts for sidebar
    const latestPosts = await Post.find({
      slug: { $ne: id },
      status: "published",
      isPublished: true,
      isBlog: true,
    })
      .populate("categories", "name description")
      .sort({ publishedAt: -1, createdAt: -1 })
      .limit(6)
      .lean();

    // Serialize posts for client components
    const serializePosts = (posts: any[]): PublicBlogPost[] => {
      return posts.map((p) => ({
        _id: p._id.toString(),
        title: p.title,
        description:
          p.excerpt || p.description || p.content?.substring(0, 200) || "",
        banner: p.banner || "",
        slug: p.slug || "",
        categories: p.categories?.map((cat: any) => cat.name || cat) || [],
        tags: p.tags || [],
        publishedAt:
          p.publishedAt?.toISOString() || p.createdAt?.toISOString() || "",
        createdAt: p.createdAt?.toISOString() || "",
        readTime: p.readTime || 5,
        views: p.views || 0,
        metaTitle: p.metaTitle || p.title || "",
        metaDescription: p.metaDescription || p.excerpt || p.description || "",
      }));
    };

    const serializedRelatedPosts = serializePosts(relatedPosts);
    const serializedLatestPosts = serializePosts(latestPosts);

    // Serialize the main post categories for safe client-side usage
    const serializedMainPost = {
      ...typedPost,
      categories:
        typedPost.categories?.map((cat: any) => ({
          name: cat.name || cat,
          description: cat.description || "",
        })) || [],
    };

    return (
      <div className="min-h-screen bg-black text-white">
        <div className="container mx-auto px-4 pt-24 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            <div className="lg:col-span-2 space-y-6">
              <div className="relative w-full space-y-4">
                {/* Banner Image */}
                {serializedMainPost.banner && (
                  <div className="h-[400px] rounded-3xl w-full z-0 overflow-hidden">
                    <Image
                      src={serializedMainPost.banner}
                      alt={serializedMainPost.title}
                      width={800}
                      height={400}
                      className="w-full h-full object-cover"
                      priority
                    />
                  </div>
                )}

                {/* Article Content */}
                <article className="prose prose-invert prose-lg max-w-none">
                  {/* Date */}
                  <div className="mb-6">
                    <time className="text-gray-400 text-sm font-medium">
                      {formatDate(
                        serializedMainPost.publishedAt?.toISOString() ||
                          serializedMainPost.createdAt?.toISOString()
                      )}
                    </time>
                  </div>

                  {/* Title */}
                  <h1 className="text-3xl font-bold mb-4 text-white leading-tight">
                    {serializedMainPost.title}
                  </h1>

                  {/* Categories */}
                  {serializedMainPost.categories &&
                    serializedMainPost.categories.length > 0 && (
                      <div className="flex items-center gap-2 mb-6">
                        {serializedMainPost.categories.map(
                          (
                            category: { name: string; description: string },
                            index: number
                          ) => (
                            <Badge
                              key={index}
                              variant="outline"
                              className="border-orange-600 text-orange-400"
                            >
                              {category.name}
                            </Badge>
                          )
                        )}
                      </div>
                    )}

                  {/* Content */}
                  <div
                    className="prose prose-invert prose-lg max-w-none text-gray-300 leading-relaxed text-justify"
                    dangerouslySetInnerHTML={{
                      __html:
                        serializedMainPost.content ||
                        serializedMainPost.description ||
                        "",
                    }}
                  />

                  {/* Tags */}
                  {serializedMainPost.tags &&
                    serializedMainPost.tags.length > 0 && (
                      <div className="mt-8 pt-8 border-t border-gray-700">
                        <h3 className="text-lg font-semibold mb-4">Tags</h3>
                        <div className="flex flex-wrap gap-2">
                          {serializedMainPost.tags.map(
                            (tag: string, index: number) => (
                              <Badge
                                key={index}
                                variant="secondary"
                                className="bg-gray-700 text-gray-300"
                              >
                                #{tag}
                              </Badge>
                            )
                          )}
                        </div>
                      </div>
                    )}
                </article>
              </div>
              <div className="w-full h-[1px] bg-gray-400" />
              <RelatedBlogsSection
                text="Related Blogs"
                posts={serializedRelatedPosts}
              />
            </div>
            <div className="lg:col-span-1">
              <BlogsSection posts={serializedLatestPosts} />
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Error fetching blog post:", error);
    notFound();
  }
};

export default page;
