// import { MagicMarquee } from "@/components/magicui/marquee";
// import Image from "next/image";
// import React from "react";

// const HeroSection: React.FC = () => {
//   const images = [
//     "/homepage/marquee1.png",
//     "/homepage/marquee2.png",
//     "/homepage/marquee3.png",
//     "/homepage/marquee4.png",
//     "/homepage/marquee5.png",
//     "/homepage/marquee6.png",
//     "/homepage/marquee7.png",
//   ];

//   const texts = [
//     "Slot",
//     "Casino",
//     "Sport",
//     "Lotto",
//     "Fishing",
//     "Table",
//     "Keno",
//     t("hitsbrand"),
//   ];

//   return (
//     <div className="relative w-full h-screen overflow-hidden">
//       {/* Video Background */}
//       <video
//         className="absolute top-0 left-0 w-full h-full object-cover"
//         autoPlay
//         loop
//         muted
//         playsInline
//       >
//         <source src="/homepage/video.mp4" type="video/mp4" />
//         Your browser does not support the video tag.
//       </video>

//       {/* Overlay for better text readability */}
//       <div className="absolute inset-0 bg-black/50"></div>

//       {/* Text Content */}
//       <div className="relative z-20 flex flex-col items-start mt-[8rem] md:mt-0 md:justify-center h-full text-center text-white px-3 md:px-[4rem] lg:px-[6rem]">
//         <h1 className="text-2xl md:text-4xl lg:text-5xl sm:text-3xl font-bold mb-4">
//           Next-Level Thrill-
//         </h1>
//         <p className="text-2xl md:text-4xl lg:text-5xl sm:text-3xl  font-bold mb-4">
//           Mobile Sports
//         </p>
//         <div className="flex flex-col md:flex-row items-center gap-4">
//           <p className="text-2xl md:text-4xl lg:text-5xl sm:text-3xl font-bold mb-4">
//             Games Built for
//           </p>
//           <Image
//             src="/homepage/Fun.png"
//             alt={t("funlogo")}
//             width={70}
//             height={40}
//           />
//         </div>
//       </div>

//       <div className="hidden md:flex absolute top-[80%] left-15 w-full z-10 transform -rotate-300 origin-top-right">
//         <MagicMarquee pauseOnHover className="[--duration:40s]">
//           {images.map((src, index) => (
//             <div
//               key={index}
//               className="flex-shrink-0 mx-4"
//               style={{ width: 350, height: 350, position: "relative" }}
//             >
//               <img
//                 src={src}
//                 alt={`Marquee image ${index + 1}`}
//                 className="w-full h-full object-cover rounded-lg transform -rotate-90 opacity-0.9"
//               />
//             </div>
//           ))}
//         </MagicMarquee>
//       </div>

//       <div className="flex absolute bottom-0 left-0 w-full z-10 ">
//         <MagicMarquee pauseOnHover className="[--duration:10s]">
//           {texts.map((review, index) => (
//             <div
//               key={index}
//               className="text-sm py-2 px-4  bg-[#171A24] text-[#EFEFEF] rounded-xl"
//             >
//               {review}
//             </div>
//           ))}
//         </MagicMarquee>
//       </div>
//     </div>
//   );
// };

// export default HeroSection;

// Second

import { MagicMarquee } from "@/components/magicui/marquee";
import Image from "next/image";
import React from "react";

const HeroSection: React.FC = () => {
  const images = [
    "/homepage/marquee1.png",
    "/homepage/marquee2.png",
    "/homepage/marquee3.png",
    "/homepage/marquee4.png",
    "/homepage/marquee5.png",
    "/homepage/marquee6.png",
    "/homepage/marquee7.png",
  ];

  const texts = [
    "สล็อต",
    "คาสิโน",
    "กีฬา",
    "ล็อตโต้",
    "ตกปลา",
    "โต๊ะ",
    "คีโน่",
    "HitsBrand",
  ];

  return (
    <div className="relative w-full h-screen overflow-hidden">
      {/* Video Background */}
      <video
        className="absolute top-0 left-0 w-full h-full object-cover"
        autoPlay
        loop
        muted
        playsInline
      >
        <source src="/homepage/video.mp4" type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Overlay for better text readability */}
      <div className="absolute inset-0 bg-black/50"></div>

      {/* Text Content */}
      {/* <div className="relative z-20 flex flex-col lg:items-start mt-[23rem] md:mt-0 md:justify-center h-full text-center text-white px-3 pr-[8rem] md:pr-0 md:px-[4rem] lg:px-[6rem]">
        {/* <p className="text-4xl pr-14 md:pr-0 text-nowrap md:text-4xl lg:text-5xl sm:text-3xl  font-bold mb-4">
          {t("title2")}
          </p> */}
      {/* <div className="flex   items-center gap-4"> */}
      {/* <p className="text-4xl text-nowrap md:text-4xl lg:text-5xl sm:text-3xl font-bold mb-4">
            {t("title3")}
            </p> */}
      {/* <h1 className="text-4xl md:text-4xl lg:text-5xl text-wrap sm:text-3xl font-bold mb-4"> */}
      {/* Your Premier Online Casino in Thailand for Online Gambling, Betting */}
      {/* </h1> */}
      {/* <Image */}
      {/* src="/homepage/Fun.png" */}
      {/* alt="Fun Logo" */}
      {/* width={70} */}
      {/* height={40} */}
      {/* /> */}
      {/* </div> */}
      {/* </div>  */}

      <div className="relative z-20 flex flex-col lg:items-start mt-[23rem] md:mt-0 md:justify-center h-full text-left text-white px-3 pr-[8rem] md:pr-0 md:px-[4rem] lg:px-[6rem]">
        <div className="flex items-start gap-4">
          <h2 className="text-4xl md:text-4xl lg:text-5xl sm:text-3xl font-bold mb-4 break-words max-w-4xl">
            คาสิโนออนไลน์ชั้นนำของคุณในประเทศไทยสำหรับการพนันออนไลน์และการเดิมพัน
          </h2>
          {/* <Image
            src="/homepage/Fun.png"
            alt="Fun Logo"
            width={70}
            height={40}
          /> */}
        </div>
      </div>

      <div className=" flex absolute top-[50%] md:top-[80%] left-15 w-full z-10 transform -rotate-297 md:-rotate-300 origin-top-right">
        <MagicMarquee pauseOnHover className="[--duration:40s]">
          {images.map((src, index) => (
            <div
              key={index}
              className="relative w-full h-full flex-shrink-0 mx-4"
              style={{ width: 350, height: 350, position: "relative" }}
            >
              <Image
                src={src}
                alt={`Marquee image ${index + 1}`}
                fill={true}
                className="-rotate-90"
              />
            </div>
          ))}
        </MagicMarquee>
      </div>

      <div className="flex absolute -bottom-2 left-0 w-full z-10 ">
        <MagicMarquee pauseOnHover className="[--duration:10s]">
          {texts.map((review, index) => (
            <div
              key={index}
              className="text-sm py-2 px-4  bg-[#171A24] text-[#EFEFEF] rounded-xl"
            >
              {review}
            </div>
          ))}
        </MagicMarquee>
      </div>
    </div>
  );
};

export default HeroSection;
