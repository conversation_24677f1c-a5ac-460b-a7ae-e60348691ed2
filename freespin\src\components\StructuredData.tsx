import React from "react";

interface StructuredDataProps {
  type?: "website" | "organization" | "casino" | "game";
  title?: string;
  description?: string;
  url?: string;
  image?: string;
  gameName?: string;
  gameProvider?: string;
}

export default function StructuredData({
  type = "website",
  title = "FreeSpin168 - คาสิโนออนไลน์ในประเทศไทย",
  description = "สำรวจคาสิโนออนไลน์ที่ดีที่สุดในประเทศไทยกับ FreeSpin168.asia เพลิดเพลินกับการเล่นเกมที่ปลอดภัย แพลตฟอร์มที่ได้รับการตรวจสอบ และอัปเดตฟรีสปินรายวัน ทั้งหมดในที่เดียว",
  url = "https://www.freespin168.asia",
  image = "https://www.freespin168.asia/logo.png",
  gameName,
  gameProvider,
}: StructuredDataProps) {
  const getStructuredData = () => {
    const baseData = {
      "@context": "https://schema.org",
      "@type": type === "website" ? "WebSite" : "Organization",
      name: title,
      description,
      url,
      image,
      sameAs: ["https://www.freespin168.asia"],
    };

    if (type === "organization") {
      return {
        ...baseData,
        "@type": "Organization",
        foundingDate: "2013",
        address: {
          "@type": "PostalAddress",
          addressCountry: "TH",
        },
        contactPoint: {
          "@type": "ContactPoint",
          contactType: "customer service",
          availableLanguage: ["English", "Thai", "Chinese"],
        },
        offers: {
          "@type": "Offer",
          category: "Online Casino Games",
          availability: "https://schema.org/InStock",
        },
      };
    }

    if (type === "casino") {
      return {
        ...baseData,
        "@type": "EntertainmentBusiness",
        category: "Online Casino",
        priceRange: "$$",
        paymentAccepted: ["Credit Card", "Bank Transfer", "E-Wallet"],
        currenciesAccepted: "THB",
        availableLanguage: ["English", "Thai", "Chinese"],
        aggregateRating: {
          "@type": "AggregateRating",
          ratingValue: "4.5",
          reviewCount: "1000",
          bestRating: "5",
          worstRating: "1",
        },
      };
    }

    if (type === "game" && gameName && gameProvider) {
      return {
        ...baseData,
        "@type": "Game",
        name: gameName,
        description: `เล่น ${gameName} โดย ${gameProvider} ที่ FreeSpin168. ${description}`,
        gameItem: {
          "@type": "Thing",
          name: gameName,
        },
        publisher: {
          "@type": "Organization",
          name: gameProvider,
        },
        applicationCategory: "Casino Game",
        operatingSystem: "Web Browser, iOS, Android",
      };
    }

    return {
      ...baseData,
      potentialAction: {
        "@type": "SearchAction",
        target: {
          "@type": "EntryPoint",
          urlTemplate:
            "https://www.freespin168.asia/search?q={search_term_string}",
        },
        "query-input": "required name=search_term_string",
      },
    };
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(getStructuredData()),
      }}
    />
  );
}
