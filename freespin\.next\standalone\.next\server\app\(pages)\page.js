"use strict";(()=>{var e={};e.id=562,e.ids=[562],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8103:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>p});var n=t(65239),o=t(48088),i=t(88170),s=t.n(i),a=t(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let p={children:["",{children:["(pages)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,52276)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\(pages)\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,35299)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\(pages)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\(pages)\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(pages)/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},52276:(e,r,t)=>{t.r(r),t.d(r,{default:()=>a,generateMetadata:()=>s});var n=t(37413),o=t(96446),i=t(89920);async function s(){return(0,i.X)({title:"คาสิโนออนไลน์ในประเทศไทย | ฟรีสปินที่เชื่อถือได้และรีวิวเกม – FreeSpin168",description:"สำรวจคาสิโนออนไลน์ที่ดีที่สุดในประเทศไทยกับ FreeSpin168.asia เพลิดเพลินกับการเล่นเกมที่ปลอดภัย แพลตฟอร์มที่ได้รับการตรวจสอบ และอัปเดตฟรีสปินรายวัน ทั้งหมดในที่เดียว",keywords:["คาสิโนออนไลน์ในประเทศไทย","ฟรีสปิน","เกมคาสิโน","FreeSpin168"],path:"/home",locale:"th",type:"website"})}function a(){return(0,n.jsx)("div",{children:(0,n.jsx)(o.default,{})})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[4447,6944,1658,9733,5036,6343,8974,6486,8378,9592],()=>t(8103));module.exports=n})();