import React from "react";
import { Card } from "@/components/ui/card";
import Image from "next/image";
import Link from "next/link";
import { PublicBlogPost } from "@/client_apis/api/blog";
import { formatDate } from "@/utils/dateUtils";
import BasicHeader from "../../../home/<USER>/BasicHeader";

interface RelatedBlogsSectionProps {
  posts: PublicBlogPost[];
  text?: string;
}

export function RelatedBlogsSection({
  posts,
  text = "Related Blogs",
}: RelatedBlogsSectionProps) {
  return (
    <div>
      <BasicHeader text={text} />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
        {posts.length > 0 ? (
          posts.map((post) => (
            <Link key={post._id} href={`/blogs/${post._id}`}>
              <Card className="bg-gray-800 border-gray-700 overflow-hidden hover:bg-gray-750 transition-colors cursor-pointer group flex flex-col h-full p-0">
                {/* Banner Image - Takes most space */}
                <div className="relative flex-1 w-full min-h-[250px]">
                  <Image
                    src={post.banner || "/placeholder.svg"}
                    alt={post.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                </div>

                {/* Content at bottom */}
                <div className="p-4 mt-auto">
                  {/* Date */}
                  <div className="mb-2">
                    <time className="text-sm text-gray-400 font-medium">
                      {formatDate(post.publishedAt || post.createdAt)}
                    </time>
                  </div>

                  {/* Title */}
                  <h3 className="text-lg font-bold text-white leading-tight line-clamp-2 group-hover:text-gray-200 transition-colors">
                    {post.title}
                  </h3>

                  {/* Categories (optional) */}
                  {post.categories && post.categories.length > 0 && (
                    <div className="mt-2">
                      <span className="inline-block px-2 py-1 text-xs bg-orange-600/20 text-orange-400 rounded-md">
                        {post.categories[0]}
                      </span>
                    </div>
                  )}
                </div>
              </Card>
            </Link>
          ))
        ) : (
          <div className="col-span-2 text-center py-8">
            <p className="text-gray-400">
              No related blogs found in this category.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
