// "use client";
// import { motion, useAnimation } from "framer-motion";
// import { useEffect, useRef } from "react";

// interface MarqueeProps {
//   texts: string[];
//   speed?: number;
//   direction?: "left" | "right";
//   className?: string;
// }

// const MarqueeText: React.FC<MarqueeProps> = ({
  //   texts,
//   speed = 100,
//   direction = "left",
//   className = "",
// }) => {
//   const controls = useAnimation();
//   const containerRef = useRef<HTMLDivElement>(null);

//   useEffect(() => {
//     const containerWidth = containerRef.current?.scrollWidth || 1000;
//     const animationDuration = containerWidth / speed;

//     const startAnimation = async () => {
//       await controls.start({
//         x: direction === "left" ? -containerWidth / 2 : containerWidth / 2,
//         transition: {
//           x: {
//             repeat: Infinity,
//             repeatType: "loop",
//             duration: animationDuration,
//             ease: "linear",
//           },
//         },
//       });
//     };

//     startAnimation();
//   }, [controls, direction, speed]);

//   return (
//     <div className={`w-full overflow-hidden bg-transparent ${className}`}>
//       <motion.div
//         ref={containerRef}
//         className="flex"
//         animate={controls}
//         style={{ width: "max-content" }}
//       >
//         {[...texts, ...texts].map((text, index) => (
//           <div
//             key={index}
//             className="flex-shrink-0 mx-4 bg-[#171A24] text-[#EFEFEF] rounded-xl"
//             style={{ whiteSpace: "nowrap", padding: "10px" }}
//           >
//             {text}
//           </div>
//         ))}
//       </motion.div>
//     </div>
//   );
// };

// export default MarqueeText;

// "use client";
// import { motion, useAnimation } from "framer-motion";
// import { useEffect, useRef } from "react";

// interface MarqueeProps {
//   texts: string[];
//   speed?: number;
//   direction?: "left" | "right";
//   className?: string;
// }

// const MarqueeText: React.FC<MarqueeProps> = ({
//   texts,
//   speed = 100,
//   direction = "left",
//   className = "",
// }) => {
//   const controls = useAnimation();
//   const containerRef = useRef<HTMLDivElement>(null);

//   useEffect(() => {
//     const containerWidth = containerRef.current?.scrollWidth || 1000;
//     const animationDuration = containerWidth / speed;

//     const startAnimation = async () => {
//       await controls.start({
//         x: direction === "left" ? -containerWidth : containerWidth,
//         transition: {
//           x: {
//             repeat: Infinity,
//             repeatType: "loop",
//             duration: animationDuration,
//             ease: "linear",
//           },
//         },
//       });
//     };

//     startAnimation();
//   }, [controls, direction, speed]);

//   return (
//     <div className={`w-full overflow-hidden bg-transparent ${className}`}>
//       <motion.div
//         ref={containerRef}
//         className="flex"
//         animate={controls}
//         style={{ width: "max-content" }}
//       >
//         {[...texts, ...texts].map((text, index) => (
//           <div
//             key={index}
//             className="flex-shrink-0 mx-4 bg-[#171A24] text-[#EFEFEF] rounded-xl"
//             style={{
//               whiteSpace: "nowrap",
//               padding: "10px",
//             }}
//           >
//             {text}
//           </div>
//         ))}
//       </motion.div>
//     </div>
//   );
// };

// export default MarqueeText;

// "use client";
// import { motion, useAnimation } from "framer-motion";
// import { useEffect, useRef, useState } from "react";

// interface MarqueeProps {
//   texts: string[];
//   speed?: number;
//   direction?: "left" | "right";
//   className?: string;
// }

// const MarqueeText: React.FC<MarqueeProps> = ({
//   texts,
//   speed = 100,
//   direction = "left",
//   className = "",
// }) => {
//   const controls = useAnimation();
//   const containerRef = useRef<HTMLDivElement>(null);
//   const [contentWidth, setContentWidth] = useState(0);

//   useEffect(() => {
//     const updateContentWidth = () => {
//       if (containerRef.current) {
//         setContentWidth(containerRef.current.scrollWidth / 2); // Divide by 2 since we duplicate texts
//       }
//     };

//     updateContentWidth();
//     window.addEventListener("resize", updateContentWidth);

//     return () => window.removeEventListener("resize", updateContentWidth);
//   }, [texts]);

//   useEffect(() => {
//     if (contentWidth === 0) return;

//     const animationDuration = contentWidth / speed;
//     const startPosition = direction === "left" ? 0 : -contentWidth;
//     const endPosition = direction === "left" ? -contentWidth : 0;

//     const startAnimation = async () => {
//       await controls.start({
//         x: endPosition,
//         transition: {
//           x: {
//             repeat: Infinity,
//             repeatType: "loop",
//             duration: animationDuration,
//             ease: "linear",
//           },
//         },
//       });
//     };

//     controls.set({ x: startPosition });
//     startAnimation();
//   }, [controls, direction, speed, contentWidth]);

//   return (
//     <div className={`w-full overflow-hidden bg-transparent ${className}`}>
//       <motion.div
//         ref={containerRef}
//         className="flex"
//         animate={controls}
//         style={{ display: "inline-flex", willChange: "transform" }}
//       >
//         {[...texts, ...texts].map((text, index) => (
//           <div
//             key={index}
//             className="flex-shrink-0 mx-4 bg-[#171A24] text-[#EFEFEF] rounded-xl"
//             style={{
//               whiteSpace: "nowrap",
//               padding: "10px",
//             }}
//           >
//             {text}
//           </div>
//         ))}
//       </motion.div>
//     </div>
//   );
// };

// export default MarqueeText;

"use client";
import { motion, useAnimation } from "framer-motion";
import { useEffect, useRef, useState } from "react";

;
interface MarqueeProps {
  texts: string[];
  speed?: number;
  direction?: "left" | "right";
  className?: string;
}

const MarqueeText: React.FC<MarqueeProps> = ({
  texts,
  speed = 100,
  direction = "left",
  className = "",
}) => {
  const controls = useAnimation();
  const containerRef = useRef<HTMLDivElement>(null);
  const [contentWidth, setContentWidth] = useState(0);

  useEffect(() => {
    const updateContentWidth = () => {
      if (containerRef.current) {
        // Calculate the width of a single set of texts (not the duplicated ones)
        const singleSetWidth = Array.from(containerRef.current.children)
          .slice(0, texts.length)
          .reduce((acc, child) => acc + (child as HTMLElement).offsetWidth, 0);
        setContentWidth(singleSetWidth);
      }
    };

    updateContentWidth();
    window.addEventListener("resize", updateContentWidth);

    return () => window.removeEventListener("resize", updateContentWidth);
  }, [texts]);

  useEffect(() => {
    if (contentWidth === 0) return;

    const animationDuration = contentWidth / speed;
    const startPosition = direction === "left" ? 0 : -contentWidth;
    const endPosition = direction === "left" ? -contentWidth : contentWidth;

    const startAnimation = async () => {
      await controls.start({
        x: endPosition,
        transition: {
          x: {
            repeat: Infinity,
            repeatType: "loop",
            duration: animationDuration,
            ease: "linear",
          },
        },
      });
    };

    controls.set({ x: startPosition });
    startAnimation();
  }, [controls, direction, speed, contentWidth]);

  return (
    <div className={`w-full overflow-hidden bg-transparent ${className}`}>
      <motion.div
        ref={containerRef}
        className="flex"
        animate={controls}
        style={{ display: "inline-flex", willChange: "transform" }}
      >
        {/* Render three sets of texts to ensure seamless looping */}
        {[...texts, ...texts, ...texts].map((text, index) => (
          <div
            key={index}
            className="flex-shrink-0 mx-4 bg-[#171A24] text-[#EFEFEF] rounded-xl"
            style={{
              whiteSpace: "nowrap",
              padding: "10px",
            }}
          >
            {text}
          </div>
        ))}
      </motion.div>
    </div>
  );
};

export default MarqueeText;
