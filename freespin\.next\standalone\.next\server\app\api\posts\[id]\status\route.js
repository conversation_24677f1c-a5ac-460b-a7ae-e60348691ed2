(()=>{var e={};e.id=1200,e.ids=[1200],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4629:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>g});var r={};s.r(r),s.d(r,{PATCH:()=>d});var i=s(96559),o=s(48088),n=s(37719),a=s(32190),u=s(75745),c=s(55530),l=s(12909);async function d(e,{params:t}){try{let s=await (0,l._)(e);if(!s.success)return a.NextResponse.json({success:!1,error:s.error},{status:401});if(s.user?.role!=="admin")return a.NextResponse.json({success:!1,error:"Admin access required"},{status:403});await (0,u.A)();let{id:r}=await t,{status:i}=await e.json();if(!["draft","published","archived"].includes(i))return a.NextResponse.json({success:!1,error:"Invalid status value"},{status:400});let o=await c.A.findById(r);if(!o)return a.NextResponse.json({success:!1,error:"Post not found"},{status:404});return o.status=i,"published"===i?(o.isPublished=!0,o.publishedAt||(o.publishedAt=new Date)):(o.isPublished=!1,"draft"===i&&(o.publishedAt=null)),await o.save(),a.NextResponse.json({success:!0,message:`Post ${i} successfully`,data:{post:o}})}catch(e){return console.error("Error updating post status:",e),a.NextResponse.json({success:!1,error:"Failed to update post status"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/posts/[id]/status/route",pathname:"/api/posts/[id]/status",filename:"route",bundlePath:"app/api/posts/[id]/status/route"},resolvedPagePath:"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\api\\posts\\[id]\\status\\route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:h,workUnitAsyncStorage:g,serverHooks:m}=p;function x(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:g})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12909:(e,t,s)=>{"use strict";s.d(t,{_:()=>n});var r=s(43205),i=s.n(r);let o=process.env.JWT_SECRET||"your-secret-key";async function n(e){try{console.log("=== AUTH DEBUG ==="),console.log("All cookies:",e.cookies.getAll()),console.log("Authorization header:",e.headers.get("authorization")),console.log("Cookie header:",e.headers.get("cookie"));let t=e.headers.get("authorization"),s=e.cookies.get("auth-token")?.value,r=e.cookies.get("session-token")?.value;console.log("Auth header token:",t?.replace("Bearer ","")),console.log("Auth token cookie:",s),console.log("Session token cookie:",r);let n=t?.replace("Bearer ","")||s||r;if(console.log("Final token:",n?"Found":"Not found"),console.log("=================="),!n)return{success:!1,error:"No authentication token provided"};let a=i().verify(n,o);return{success:!0,user:a}}catch(e){if(e instanceof i().JsonWebTokenError)return{success:!1,error:"Invalid authentication token"};return{success:!1,error:"Authentication failed"}}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55530:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(56037),i=s.n(r);let o=new r.Schema({title:{type:String,required:[!0,"Post title is required"],trim:!0,maxlength:[1e3,"Title cannot exceed 200 characters"]},slug:{type:String,trim:!0,lowercase:!0},canonicalUrl:{type:String,trim:!0,validate:{validator:e=>!e||/^https?:\/\/.+/.test(e),message:"Canonical URL must be a valid URL"}},existingUrl:{type:Boolean,default:!1},content:{type:String,required:[!0,"Post content is required"]},excerpt:{type:String,trim:!0,maxlength:[1e3,"Excerpt cannot exceed 300 characters"]},description:{type:String},author:{type:String,trim:!0},isBlog:{type:Boolean,default:!0},categories:[{type:i().Schema.Types.ObjectId,ref:"Category"}],tags:[{type:String,trim:!0,lowercase:!0}],metaTitle:{type:String,required:[!0,"Meta title is required"],maxlength:[1e3,"Meta title cannot exceed 60 characters"]},metaDescription:{type:String,required:[!0,"Meta description is required"],maxlength:[1e3,"Meta description cannot exceed 160 characters"]},metaKeywords:{type:String,required:[!0,"Meta keywords are required"],maxlength:[1e3,"Meta keywords cannot exceed 200 characters"]},banner:{type:String,required:[!0,"Banner image is required"],trim:!0},status:{type:String,enum:["draft","published","archived"],default:"draft"},isPublished:{type:Boolean,default:!1},publishedAt:{type:Date,default:null},views:{type:Number,default:0},readTime:{type:Number,default:1},isTopNews:{type:Boolean,default:!1}},{timestamps:!0});o.pre("save",function(e){if(this.isModified("title")&&!this.slug&&(this.slug=this.title.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,"")),this.isModified("content")){let e=(this.content||"").split(/\s+/).length;this.readTime=Math.ceil(e/200)}this.isModified("status")&&"published"===this.status&&!this.publishedAt&&(this.publishedAt=new Date,this.isPublished=!0),e()}),o.index({categories:1}),o.index({tags:1}),o.index({status:1}),o.index({isPublished:1}),o.index({publishedAt:-1}),o.index({"banner.title":1}),o.index({title:"text",content:"text",metaTitle:"text",metaDescription:"text"});let n=i().models.Post||i().model("Post",o)},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75745:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(56037),i=s.n(r);let o=process.env.MONGODB_URI;if(!o)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let n=global?.mongoose;n||(n=global.mongoose={conn:null,promise:null});let a=async function(){if(n.conn)return n.conn;n.promise||(n.promise=i().connect(o,{bufferCommands:!1}).then(e=>e));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,580,3205],()=>s(4629));module.exports=r})();