#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Files that need function name fixes
const files = [
  'src/app/[locale]/all-bets/page.tsx',
  'src/app/[locale]/big-game/page.tsx',
  'src/app/[locale]/evolution-lobby/page.tsx',
  'src/app/[locale]/jili-games/page.tsx',
  'src/app/[locale]/lalika/page.tsx',
  'src/app/[locale]/on-gaming-lobby/page.tsx',
  'src/app/[locale]/pg-games/page.tsx',
  'src/app/[locale]/pragmatic-play/page.tsx',
  'src/app/[locale]/pretty-game/page.tsx',
  'src/app/[locale]/sa/page.tsx',
  'src/app/[locale]/sbo-bet/page.tsx',
  'src/app/[locale]/sexy/page.tsx',
  'src/app/[locale]/sv388/page.tsx',
  'src/app/[locale]/wm-casino/page.tsx'
];

// Special case for dream-gaming (uses 'index' instead of 'page')
const dreamGamingFile = 'src/app/[locale]/dream-gaming/page.tsx';

function fixComponentNames() {
  console.log('🔧 Fixing React component function names...');
  
  // Fix regular page components
  files.forEach(filePath => {
    try {
      const fullPath = path.join(process.cwd(), filePath);
      let content = fs.readFileSync(fullPath, 'utf8');
      
      // Replace 'const page = () => {' with 'const Page = () => {'
      content = content.replace(/const page = \(\) => \{/g, 'const Page = () => {');
      
      // Replace 'export default page;' with 'export default Page;'
      content = content.replace(/export default page;/g, 'export default Page;');
      
      fs.writeFileSync(fullPath, content);
      console.log(`✅ Fixed: ${filePath}`);
    } catch (error) {
      console.log(`❌ Error fixing ${filePath}:`, error.message);
    }
  });
  
  // Fix dream-gaming special case
  try {
    const fullPath = path.join(process.cwd(), dreamGamingFile);
    let content = fs.readFileSync(fullPath, 'utf8');
    
    // Replace 'const index = () => {' with 'const Index = () => {'
    content = content.replace(/const index = \(\) => \{/g, 'const Index = () => {');
    
    // Replace 'export default index;' with 'export default Index;'
    content = content.replace(/export default index;/g, 'export default Index;');
    
    fs.writeFileSync(fullPath, content);
    console.log(`✅ Fixed: ${dreamGamingFile}`);
  } catch (error) {
    console.log(`❌ Error fixing ${dreamGamingFile}:`, error.message);
  }
  
  console.log('🎉 Component name fixes completed!');
}

fixComponentNames();
