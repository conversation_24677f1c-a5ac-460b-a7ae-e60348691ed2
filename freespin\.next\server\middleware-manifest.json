{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|sitemap.xml|robots.txt|.*\\.).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|.*\\.).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "FrNNN+HBdWeLONyYct2yC9KZ/WQp8bWC8ijse9tJFWk=", "__NEXT_PREVIEW_MODE_ID": "575557a0541307a1075e75b9314937d4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4a55e0429d3270ee8dd51f8d2e7c058221a0dc50a95bfabe3d0ba28d3c4c06ad", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "86b414d0c3eda776d7807307c90c93ae6599f2b51d5aa34ad0a8bf6908910051"}}}, "sortedMiddleware": ["/"], "functions": {}}