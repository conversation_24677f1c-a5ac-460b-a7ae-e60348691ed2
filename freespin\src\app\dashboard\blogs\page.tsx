import { getPosts } from "@/client_apis/api/blog";
import BlogManagement, { BlogPost } from "./components/BlogManagement";
import get from "lodash/get";
export default async function AdminPage() {
  const pos = await getPosts();
  const postsData = get(pos, "data.posts", []);
  return (
    <div className="container mx-auto px-4 py-8">
      <BlogManagement posts={postsData as Array<BlogPost>} />
    </div>
  );
}
