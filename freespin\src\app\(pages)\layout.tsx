import React from "react";
import Navbar from "../shared/Navbar";
import Footer from "../shared/Footer";
import StructuredData from "@/components/StructuredData";
import { Metadata } from "next";

export const metadata: Metadata = {
  title:
    "คาสิโนออนไลน์ในประเทศไทย | ฟรีสปินที่เชื่อถือได้และรีวิวเกม – FreeSpin168",
  alternates: {
    canonical: "https://freespin168.asia/",
  },
  description:
    "สำรวจคาสิโนออนไลน์ที่ดีที่สุดในประเทศไทยกับ FreeSpin168.asia เพลิดเพลินกับการเล่นเกมที่ปลอดภัย แพลตฟอร์มที่ได้รับการตรวจสอบ และอัปเดตฟรีสปินรายวัน ทั้งหมดในที่เดียว",
  keywords: ["คาสิโนออนไลน์ในประเทศไทย", "ฟรีสปิน", "เกมคาสิโน", "FreeSpin168"],
};
export default function PagesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div>
      <StructuredData type="organization" />
      <Navbar />
      {children}
      <Footer />
    </div>
  );
}
