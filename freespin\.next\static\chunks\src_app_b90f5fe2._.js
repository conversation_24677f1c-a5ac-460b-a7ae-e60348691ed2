(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/app/shared/cleanObject.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "cleanObject": (()=>cleanObject),
    "convertToBase64": (()=>convertToBase64)
});
function cleanObject(obj) {
    if (typeof obj !== "object" || obj === null) return obj;
    const cleaned = {};
    for (const [key, value] of Object.entries(obj)){
        if (value === undefined || value === null || value === "" || typeof value === "object" && !Array.isArray(value) && Object.keys(cleanObject(value)).length === 0) {
            continue;
        }
        if (Array.isArray(value)) {
            const cleanedArray = value.map((item)=>typeof item === "object" ? cleanObject(item) : item).filter((item)=>typeof item === "object" ? Object.keys(item).length > 0 : item !== null && item !== undefined && item !== "");
            if (cleanedArray.length > 0) {
                cleaned[key] = cleanedArray;
            }
        } else if (typeof value === "object") {
            const cleanedObj = cleanObject(value);
            if (Object.keys(cleanedObj).length > 0) {
                cleaned[key] = cleanedObj;
            }
        } else {
            cleaned[key] = value;
        }
    }
    return cleaned;
}
const convertToBase64 = (file)=>{
    return new Promise((resolve, reject)=>{
        const reader = new FileReader();
        reader.readAsDataURL(file); // This encodes file to base64
        reader.onload = ()=>resolve(reader.result);
        reader.onerror = (error)=>reject(error);
    });
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/dashboard/blogs/data:ab07ba [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00aaf3229ad66ce8c0e05f422008f0d7c0448605e4":"getCategoriesAction"},"src/app/dashboard/blogs/actions.ts",""] */ __turbopack_context__.s({
    "getCategoriesAction": (()=>getCategoriesAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var getCategoriesAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("00aaf3229ad66ce8c0e05f422008f0d7c0448605e4", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "getCategoriesAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/dashboard/blogs/data:cb08fa [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"60e82a0f1ab060d924d48393e65f78bcf87b8015d6":"createPostAction"},"src/app/dashboard/blogs/actions.ts",""] */ __turbopack_context__.s({
    "createPostAction": (()=>createPostAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var createPostAction = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("60e82a0f1ab060d924d48393e65f78bcf87b8015d6", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "createPostAction"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// "use client";
// import { LexicalComposer } from "@lexical/react/LexicalComposer";
// import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
// import { ContentEditable } from "@lexical/react/LexicalContentEditable";
// import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
// import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin";
// import { LinkPlugin } from "@lexical/react/LexicalLinkPlugin";
// import { ListPlugin } from "@lexical/react/LexicalListPlugin";
// import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
// import { HeadingNode, QuoteNode } from "@lexical/rich-text";
// import { ListItemNode, ListNode } from "@lexical/list";
// import { AutoLinkNode, LinkNode } from "@lexical/link";
// import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
// import { OnChangePlugin } from "@lexical/react/LexicalOnChangePlugin";
// import {
//   Bold,
//   Italic,
//   Underline,
//   List,
//   ListOrdered,
//   Undo,
//   Redo,
//   Link as LinkIcon,
// } from "lucide-react";
// import { useCallback, useState, useEffect } from "react";
// import {
//   $getSelection,
//   $isRangeSelection,
//   FORMAT_TEXT_COMMAND,
//   UNDO_COMMAND,
//   REDO_COMMAND,
//   EditorState,
//   TextFormatType,
//   $getRoot,
// } from "lexical";
// import { $generateHtmlFromNodes } from "@lexical/html";
// import {
//   $createHeadingNode,
//   $createQuoteNode,
//   $isHeadingNode,
// } from "@lexical/rich-text";
// import {
//   INSERT_UNORDERED_LIST_COMMAND,
//   INSERT_ORDERED_LIST_COMMAND,
//   $isListNode,
// } from "@lexical/list";
// import { TOGGLE_LINK_COMMAND } from "@lexical/link";
// interface WysiwygEditorProps {
//   content: string;
//   onChange: (content: string) => void;
//   placeholder?: string;
// }
// // Lexical theme configuration
// const theme = {
//   ltr: "ltr",
//   rtl: "rtl",
//   placeholder: "text-gray-400 text-sm",
//   paragraph: "mb-2",
//   quote: "border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4",
//   heading: {
//     h1: "text-3xl font-bold mb-4",
//     h2: "text-2xl font-bold mb-3",
//     h3: "text-xl font-bold mb-2",
//   },
//   list: {
//     nested: {
//       listitem: "list-none",
//     },
//     ol: "list-decimal list-inside mb-2",
//     ul: "list-disc list-inside mb-2",
//     listitem: "mb-1",
//   },
//   link: "text-blue-600 underline hover:text-blue-800",
//   text: {
//     bold: "font-bold",
//     italic: "italic",
//     underline: "underline",
//     strikethrough: "line-through",
//   },
// };
// // Error boundary fallback
// function onError(error: Error) {
//   console.error(error);
// }
// // Initial editor configuration
// const initialConfig = {
//   namespace: "WysiwygEditor",
//   theme,
//   onError,
//   nodes: [
//     HeadingNode,
//     ListNode,
//     ListItemNode,
//     QuoteNode,
//     AutoLinkNode,
//     LinkNode,
//   ],
// };
// // Toolbar component
// function ToolbarPlugin() {
//   const [editor] = useLexicalComposerContext();
//   const [isBold, setIsBold] = useState(false);
//   const [isItalic, setIsItalic] = useState(false);
//   const [isUnderline, setIsUnderline] = useState(false);
//   const [blockType, setBlockType] = useState("paragraph");
//   const updateToolbar = useCallback(() => {
//     const selection = $getSelection();
//     if ($isRangeSelection(selection)) {
//       setIsBold(selection.hasFormat("bold"));
//       setIsItalic(selection.hasFormat("italic"));
//       setIsUnderline(selection.hasFormat("underline"));
//       const anchorNode = selection.anchor.getNode();
//       const element =
//         anchorNode.getKey() === "root"
//           ? anchorNode
//           : anchorNode.getTopLevelElementOrThrow();
//       if ($isListNode(element)) {
//         const type = element.getListType();
//         setBlockType(type === "bullet" ? "ul" : "ol");
//       } else {
//         const type = $isHeadingNode(element)
//           ? element.getTag()
//           : element.getType();
//         setBlockType(type);
//       }
//     }
//   }, []);
//   useEffect(() => {
//     return editor.registerUpdateListener(({ editorState }) => {
//       editorState.read(() => {
//         updateToolbar();
//       });
//     });
//   }, [updateToolbar, editor]);
//   const formatText = (format: TextFormatType) => {
//     editor.dispatchCommand(FORMAT_TEXT_COMMAND, format);
//   };
//   const formatHeading = (headingSize: string) => {
//     editor.update(() => {
//       const selection = $getSelection();
//       if ($isRangeSelection(selection)) {
//         if (blockType !== headingSize) {
//           const heading = $createHeadingNode(headingSize as any);
//           selection.insertNodes([heading]);
//         }
//       }
//     });
//   };
//   const formatQuote = () => {
//     editor.update(() => {
//       const selection = $getSelection();
//       if ($isRangeSelection(selection)) {
//         const quote = $createQuoteNode();
//         selection.insertNodes([quote]);
//       }
//     });
//   };
//   const formatBulletList = () => {
//     editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
//   };
//   const formatNumberedList = () => {
//     editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
//   };
//   const insertLink = () => {
//     const url = prompt("Enter URL:");
//     if (url) {
//       editor.dispatchCommand(TOGGLE_LINK_COMMAND, url);
//     }
//   };
//   return (
//     <div className="flex flex-wrap items-center gap-1 p-2 border-b border-gray-300 bg-gray-50">
//       {/* Text Formatting */}
//       <button
//         type="button"
//         onClick={() => formatText("bold")}
//         className={`p-2 rounded hover:bg-gray-200 ${
//           isBold ? "bg-gray-300" : ""
//         }`}
//         title="Bold"
//       >
//         <Bold className="w-4 h-4" />
//       </button>
//       <button
//         type="button"
//         onClick={() => formatText("italic")}
//         className={`p-2 rounded hover:bg-gray-200 ${
//           isItalic ? "bg-gray-300" : ""
//         }`}
//         title="Italic"
//       >
//         <Italic className="w-4 h-4" />
//       </button>
//       <button
//         type="button"
//         onClick={() => formatText("underline")}
//         className={`p-2 rounded hover:bg-gray-200 ${
//           isUnderline ? "bg-gray-300" : ""
//         }`}
//         title="Underline"
//       >
//         <Underline className="w-4 h-4" />
//       </button>
//       <div className="w-px h-6 bg-gray-300 mx-1" />
//       {/* Block Type */}
//       <select
//         onChange={(e) => {
//           const value = e.target.value;
//           if (value === "quote") {
//             formatQuote();
//           } else if (value.startsWith("h")) {
//             formatHeading(value);
//           }
//         }}
//         value={blockType}
//         className="px-2 py-1 border border-gray-300 rounded text-sm"
//       >
//         <option value="paragraph">Paragraph</option>
//         <option value="h1">Heading 1</option>
//         <option value="h2">Heading 2</option>
//         <option value="h3">Heading 3</option>
//         <option value="quote">Quote</option>
//       </select>
//       <div className="w-px h-6 bg-gray-300 mx-1" />
//       {/* Lists */}
//       <button
//         type="button"
//         onClick={formatBulletList}
//         className="p-2 rounded hover:bg-gray-200"
//         title="Bullet List"
//       >
//         <List className="w-4 h-4" />
//       </button>
//       <button
//         type="button"
//         onClick={formatNumberedList}
//         className="p-2 rounded hover:bg-gray-200"
//         title="Numbered List"
//       >
//         <ListOrdered className="w-4 h-4" />
//       </button>
//       <div className="w-px h-6 bg-gray-300 mx-1" />
//       {/* Link */}
//       <button
//         type="button"
//         onClick={insertLink}
//         className="p-2 rounded hover:bg-gray-200"
//         title="Add Link"
//       >
//         <LinkIcon className="w-4 h-4" />
//       </button>
//       <div className="w-px h-6 bg-gray-300 mx-1" />
//       {/* Undo/Redo */}
//       <button
//         type="button"
//         onClick={() => editor.dispatchCommand(UNDO_COMMAND, undefined)}
//         className="p-2 rounded hover:bg-gray-200"
//         title="Undo"
//       >
//         <Undo className="w-4 h-4" />
//       </button>
//       <button
//         type="button"
//         onClick={() => editor.dispatchCommand(REDO_COMMAND, undefined)}
//         className="p-2 rounded hover:bg-gray-200"
//         title="Redo"
//       >
//         <Redo className="w-4 h-4" />
//       </button>
//     </div>
//   );
// }
// // Main WYSIWYG Editor Component
// const WysiwygEditor = ({
//   onChange,
//   placeholder = "Start writing...",
// }: Omit<WysiwygEditorProps, "content">) => {
//   return (
//     <div className="border border-gray-300 rounded-lg overflow-hidden">
//       <LexicalComposer initialConfig={initialConfig}>
//         <ToolbarPlugin />
//         <div className="relative">
//           <RichTextPlugin
//             contentEditable={
//               <ContentEditable
//                 className="min-h-[300px] p-4 focus:outline-none prose max-w-none"
//                 style={{ resize: "none" }}
//               />
//             }
//             placeholder={
//               <div className="absolute top-4 left-4 text-gray-400 pointer-events-none">
//                 {placeholder}
//               </div>
//             }
//             ErrorBoundary={LexicalErrorBoundary}
//           />
//           <OnChangePlugin
//             onChange={(editorState: EditorState, editor) => {
//               editorState.read(() => {
//                 // Generate HTML from the editor state to preserve formatting
//                 const htmlContent = $generateHtmlFromNodes(editor, null);
//                 onChange(htmlContent);
//               });
//             }}
//           />
//           <HistoryPlugin />
//           <AutoFocusPlugin />
//           <LinkPlugin />
//           <ListPlugin />
//         </div>
//       </LexicalComposer>
//     </div>
//   );
// };
// export default WysiwygEditor;
//  Second
// Complete WYSIWYG Editor with Fixed Content Prop Handling
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposer$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalComposer.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalRichTextPlugin$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalContentEditable$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalContentEditable.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalHistoryPlugin$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalLinkPlugin$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalLinkPlugin.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalListPlugin$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalListPlugin.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalErrorBoundary$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$rich$2d$text$2f$LexicalRichText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/rich-text/LexicalRichText.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/list/LexicalList.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/link/LexicalLink.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalComposerContext.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalOnChangePlugin$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bold$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bold$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bold.js [app-client] (ecmascript) <export default as Bold>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$italic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Italic$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/italic.js [app-client] (ecmascript) <export default as Italic>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$underline$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Underline$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/underline.js [app-client] (ecmascript) <export default as Underline>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$list$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__List$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/list.js [app-client] (ecmascript) <export default as List>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$list$2d$ordered$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ListOrdered$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/list-ordered.js [app-client] (ecmascript) <export default as ListOrdered>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$undo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Undo$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/undo.js [app-client] (ecmascript) <export default as Undo>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$redo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Redo$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/redo.js [app-client] (ecmascript) <export default as Redo>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Link$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/link.js [app-client] (ecmascript) <export default as Link>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lexical/Lexical.dev.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$html$2f$LexicalHtml$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@lexical/html/LexicalHtml.dev.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// Lexical theme configuration
const theme = {
    ltr: "ltr",
    rtl: "rtl",
    placeholder: "text-gray-400 text-sm",
    paragraph: "mb-2",
    quote: "border-l-4 border-gray-300 pl-4 italic text-gray-600 my-4",
    heading: {
        h1: "text-3xl font-bold mb-4",
        h2: "text-2xl font-bold mb-3",
        h3: "text-xl font-bold mb-2"
    },
    list: {
        nested: {
            listitem: "list-none"
        },
        ol: "list-decimal list-inside mb-2",
        ul: "list-disc list-inside mb-2",
        listitem: "mb-1"
    },
    link: "text-blue-600 underline hover:text-blue-800",
    text: {
        bold: "font-bold",
        italic: "italic",
        underline: "underline",
        strikethrough: "line-through"
    }
};
function onError(error) {
    console.error(error);
}
function ToolbarPlugin() {
    _s();
    const [editor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"])();
    const [isBold, setIsBold] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isItalic, setIsItalic] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isUnderline, setIsUnderline] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [blockType, setBlockType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("paragraph");
    const updateToolbar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ToolbarPlugin.useCallback[updateToolbar]": ()=>{
            const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
                setIsBold(selection.hasFormat("bold"));
                setIsItalic(selection.hasFormat("italic"));
                setIsUnderline(selection.hasFormat("underline"));
                const anchorNode = selection.anchor.getNode();
                const element = anchorNode.getKey() === "root" ? anchorNode : anchorNode.getTopLevelElementOrThrow();
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isListNode"])(element)) {
                    const type = element.getListType();
                    setBlockType(type === "bullet" ? "ul" : "ol");
                } else {
                    const type = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$rich$2d$text$2f$LexicalRichText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isHeadingNode"])(element) ? element.getTag() : element.getType();
                    setBlockType(type);
                }
            }
        }
    }["ToolbarPlugin.useCallback[updateToolbar]"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ToolbarPlugin.useEffect": ()=>{
            return editor.registerUpdateListener({
                "ToolbarPlugin.useEffect": ({ editorState })=>{
                    editorState.read({
                        "ToolbarPlugin.useEffect": ()=>{
                            updateToolbar();
                        }
                    }["ToolbarPlugin.useEffect"]);
                }
            }["ToolbarPlugin.useEffect"]);
        }
    }["ToolbarPlugin.useEffect"], [
        updateToolbar,
        editor
    ]);
    const formatText = (format)=>{
        editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FORMAT_TEXT_COMMAND"], format);
    };
    const formatHeading = (headingSize)=>{
        editor.update(()=>{
            const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
                if (blockType !== headingSize) {
                    const heading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$rich$2d$text$2f$LexicalRichText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createHeadingNode"])(headingSize);
                    selection.insertNodes([
                        heading
                    ]);
                }
            }
        });
    };
    const formatQuote = ()=>{
        editor.update(()=>{
            const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getSelection"])();
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$isRangeSelection"])(selection)) {
                const quote = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$rich$2d$text$2f$LexicalRichText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createQuoteNode"])();
                selection.insertNodes([
                    quote
                ]);
            }
        });
    };
    const formatBulletList = ()=>{
        editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INSERT_UNORDERED_LIST_COMMAND"], undefined);
    };
    const formatNumberedList = ()=>{
        editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INSERT_ORDERED_LIST_COMMAND"], undefined);
    };
    const insertLink = ()=>{
        const url = prompt("Enter URL:");
        if (url) {
            editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TOGGLE_LINK_COMMAND"], url);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-wrap items-center gap-1 p-2 border-b border-gray-300 bg-gray-50",
        style: {
            display: "flex",
            flexDirection: "row",
            flexWrap: "wrap",
            alignItems: "center",
            gap: "4px",
            padding: "8px"
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                type: "button",
                onClick: ()=>formatText("bold"),
                className: `p-2 rounded hover:bg-gray-200 ${isBold ? "bg-gray-300" : ""}`,
                title: "Bold",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bold$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bold$3e$__["Bold"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                    lineNumber: 542,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                lineNumber: 534,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                type: "button",
                onClick: ()=>formatText("italic"),
                className: `p-2 rounded hover:bg-gray-200 ${isItalic ? "bg-gray-300" : ""}`,
                title: "Italic",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$italic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Italic$3e$__["Italic"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                    lineNumber: 553,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                lineNumber: 545,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                type: "button",
                onClick: ()=>formatText("underline"),
                className: `p-2 rounded hover:bg-gray-200 ${isUnderline ? "bg-gray-300" : ""}`,
                title: "Underline",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$underline$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Underline$3e$__["Underline"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                    lineNumber: 564,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                lineNumber: 556,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-px h-6 bg-gray-300 mx-1"
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                lineNumber: 567,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                onChange: (e)=>{
                    const value = e.target.value;
                    if (value === "quote") {
                        formatQuote();
                    } else if (value.startsWith("h")) {
                        formatHeading(value);
                    }
                },
                value: blockType,
                className: "px-2 py-1 border border-gray-300 rounded text-sm",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                        value: "paragraph",
                        children: "Paragraph"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                        lineNumber: 581,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                        value: "h1",
                        children: "Heading 1"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                        lineNumber: 582,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                        value: "h2",
                        children: "Heading 2"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                        lineNumber: 583,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                        value: "h3",
                        children: "Heading 3"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                        lineNumber: 584,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                        value: "quote",
                        children: "Quote"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                        lineNumber: 585,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                lineNumber: 569,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-px h-6 bg-gray-300 mx-1"
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                lineNumber: 588,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                type: "button",
                onClick: formatBulletList,
                className: "p-2 rounded hover:bg-gray-200",
                title: "Bullet List",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$list$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__List$3e$__["List"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                    lineNumber: 596,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                lineNumber: 590,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                type: "button",
                onClick: formatNumberedList,
                className: "p-2 rounded hover:bg-gray-200",
                title: "Numbered List",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$list$2d$ordered$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ListOrdered$3e$__["ListOrdered"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                    lineNumber: 605,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                lineNumber: 599,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-px h-6 bg-gray-300 mx-1"
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                lineNumber: 608,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                type: "button",
                onClick: insertLink,
                className: "p-2 rounded hover:bg-gray-200",
                title: "Add Link",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Link$3e$__["Link"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                    lineNumber: 616,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                lineNumber: 610,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-px h-6 bg-gray-300 mx-1"
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                lineNumber: 619,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                type: "button",
                onClick: ()=>editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UNDO_COMMAND"], undefined),
                className: "p-2 rounded hover:bg-gray-200",
                title: "Undo",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$undo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Undo$3e$__["Undo"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                    lineNumber: 627,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                lineNumber: 621,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                type: "button",
                onClick: ()=>editor.dispatchCommand(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REDO_COMMAND"], undefined),
                className: "p-2 rounded hover:bg-gray-200",
                title: "Redo",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$redo$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Redo$3e$__["Redo"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                    lineNumber: 636,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                lineNumber: 630,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
        lineNumber: 523,
        columnNumber: 5
    }, this);
}
_s(ToolbarPlugin, "OyazkNGbBvNW+Q9GGOZiuhTb2Kw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"]
    ];
});
_c = ToolbarPlugin;
// Plugin to update editor content when content prop changes
function ContentUpdatePlugin({ content }) {
    _s1();
    const [editor] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"])();
    const [isFirstLoad, setIsFirstLoad] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ContentUpdatePlugin.useEffect": ()=>{
            // Only update on first load or when content actually changes
            if (isFirstLoad && content && content.trim() !== "") {
                editor.update({
                    "ContentUpdatePlugin.useEffect": ()=>{
                        try {
                            const parser = new DOMParser();
                            const dom = parser.parseFromString(content, "text/html");
                            const nodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$html$2f$LexicalHtml$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$generateNodesFromDOM"])(editor, dom);
                            const root = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getRoot"])();
                            root.clear();
                            root.append(...nodes);
                        } catch (error) {
                            console.error("Error updating editor content:", error);
                            // Fallback: just set plain text
                            const root = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$getRoot"])();
                            root.clear();
                            const paragraph = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createParagraphNode"])();
                            paragraph.append((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lexical$2f$Lexical$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$createTextNode"])(content));
                            root.append(paragraph);
                        }
                    }
                }["ContentUpdatePlugin.useEffect"]);
                setIsFirstLoad(false);
            }
        }
    }["ContentUpdatePlugin.useEffect"], [
        content,
        editor,
        isFirstLoad
    ]);
    return null;
}
_s1(ContentUpdatePlugin, "9Kr6Yv8zM/miEvkbLkUxhL4k6VA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposerContext$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLexicalComposerContext"]
    ];
});
_c1 = ContentUpdatePlugin;
const WysiwygEditor = ({ content, onChange, placeholder = "Start writing..." })=>{
    const initialConfig = {
        namespace: "WysiwygEditor",
        theme,
        onError,
        nodes: [
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$rich$2d$text$2f$LexicalRichText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HeadingNode"],
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ListNode"],
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$list$2f$LexicalList$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ListItemNode"],
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$rich$2d$text$2f$LexicalRichText$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QuoteNode"],
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AutoLinkNode"],
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$link$2f$LexicalLink$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LinkNode"]
        ]
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "border border-gray-300 rounded-lg overflow-hidden lexical-editor",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalComposer$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LexicalComposer"], {
            initialConfig: initialConfig,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ToolbarPlugin, {}, void 0, false, {
                    fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                    lineNumber: 697,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ContentUpdatePlugin, {
                    content: content
                }, void 0, false, {
                    fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                    lineNumber: 698,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative lexical-content-area",
                    style: {
                        direction: "ltr",
                        textAlign: "left",
                        writingMode: "horizontal-tb"
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalRichTextPlugin$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RichTextPlugin"], {
                            contentEditable: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalContentEditable$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ContentEditable"], {
                                className: "min-h-[300px] p-4 focus:outline-none",
                                style: {
                                    minHeight: "300px",
                                    padding: "16px",
                                    whiteSpace: "normal",
                                    wordWrap: "break-word",
                                    textAlign: "left",
                                    direction: "ltr",
                                    writingMode: "horizontal-tb",
                                    unicodeBidi: "normal",
                                    display: "block",
                                    width: "100%",
                                    boxSizing: "border-box",
                                    fontFamily: "inherit",
                                    fontSize: "16px",
                                    lineHeight: "1.6",
                                    color: "inherit",
                                    background: "transparent",
                                    border: "none",
                                    outline: "none"
                                },
                                spellCheck: true,
                                dir: "ltr"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                                lineNumber: 709,
                                columnNumber: 15
                            }, void 0),
                            placeholder: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute top-4 left-4 text-gray-400 pointer-events-none",
                                children: placeholder
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                                lineNumber: 736,
                                columnNumber: 15
                            }, void 0),
                            ErrorBoundary: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalErrorBoundary$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LexicalErrorBoundary"]
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                            lineNumber: 707,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalOnChangePlugin$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OnChangePlugin"], {
                            onChange: (editorState, editor)=>{
                                editorState.read(()=>{
                                    const htmlContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$html$2f$LexicalHtml$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["$generateHtmlFromNodes"])(editor, null);
                                    onChange(htmlContent);
                                });
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                            lineNumber: 742,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalHistoryPlugin$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["HistoryPlugin"], {}, void 0, false, {
                            fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                            lineNumber: 750,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalLinkPlugin$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LinkPlugin"], {}, void 0, false, {
                            fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                            lineNumber: 751,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$lexical$2f$react$2f$LexicalListPlugin$2e$dev$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ListPlugin"], {}, void 0, false, {
                            fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                            lineNumber: 752,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
                    lineNumber: 699,
                    columnNumber: 9
                }, this)
            ]
        }, "wysiwyg-editor", true, {
            fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
            lineNumber: 696,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx",
        lineNumber: 695,
        columnNumber: 5
    }, this);
};
_c2 = WysiwygEditor;
const __TURBOPACK__default__export__ = WysiwygEditor;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "ToolbarPlugin");
__turbopack_context__.k.register(_c1, "ContentUpdatePlugin");
__turbopack_context__.k.register(_c2, "WysiwygEditor");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// "use client";
// import {
//   cleanObject,
//   convertToBase64,
// } from "@/app/shared/cleanObject";
// import { getCategoriesAction, createPostAction } from "../actions";
// import { Button } from "@/components/ui/button";
// import { Eye, ImagePlus, Info, Loader2, Plus } from "lucide-react";
// import type React from "react";
// import { useState, useEffect } from "react";
// import { Controller, useForm } from "react-hook-form";
// import { useRouter } from "next/navigation";
// import { toast } from "react-toastify";
// import { z } from "zod";
// import WysiwygEditor from "./WysiwygEditor";
// // Form schema matching the API
// const formSchema = z.object({
//   title: z.string().min(1, "Title is required"),
//   slug: z.string().optional(),
//   // canonicalUrl: z
//   //   .string()
//   //   .url("Must be a valid URL")
//   //   .optional()
//   //   .or(z.literal("")),
//   // existingUrl: z.boolean().default(false),
//   content: z.string().min(1, "Content is required"),
//   isBlog: z.boolean().default(true),
//   categories: z.array(z.string()).default([]),
//   tags: z.array(z.string()).default([]),
//   metaTitle: z.string().min(1, "Meta title is required"),
//   metaDescription: z.string().min(1, "Meta description is required"),
//   metaKeywords: z.string().min(1, "Meta keywords are required"),
//   banner: z.object({
//     title: z.string().min(1, "Banner title is required"),
//     description: z.string().min(1, "Banner description is required"),
//     image: z.string().min(1, "Banner image is required"),
//     altText: z.string().min(1, "Alt text is required"),
//   }),
// });
// type FormData = z.infer<typeof formSchema>;
// // Field configuration for dynamic rendering
// const fieldConfigs = {
//   blogPost: [
//     {
//       name: "title" as const,
//       label: "Title",
//       type: "text",
//       placeholder: "Enter the title of the blog post",
//       tooltip: true,
//       required: true,
//     },
//     {
//       name: "slug" as const,
//       label: "Slug (Optional)",
//       type: "text",
//       placeholder: "Enter a unique slug (auto-generated if empty)",
//       tooltip: true,
//       required: false,
//     },
//   ],
//   seo: [
//     {
//       name: "metaTitle" as const,
//       label: "Meta Title",
//       type: "text",
//       placeholder: "Enter meta title ",
//       tooltip: true,
//       required: true,
//     },
//     {
//       name: "metaDescription" as const,
//       label: "Meta Description",
//       type: "textarea",
//       placeholder: "Enter meta description",
//       rows: 3,
//       tooltip: true,
//       required: true,
//     },
//     {
//       name: "metaKeywords" as const,
//       label: "Meta Keywords (comma-separated)",
//       type: "text",
//       placeholder: "Enter meta keywords ",
//       tooltip: true,
//       required: true,
//     },
//   ],
//   banner: [
//     {
//       name: "title" as const,
//       label: "Banner Title",
//       type: "text",
//       placeholder: "Enter banner title",
//       tooltip: true,
//       required: true,
//     },
//     {
//       name: "description" as const,
//       label: "Banner Description",
//       type: "textarea",
//       placeholder: "Enter banner description",
//       rows: 4,
//       tooltip: true,
//       required: true,
//     },
//     {
//       name: "altText" as const,
//       label: "Banner Image Alt Text",
//       type: "text",
//       placeholder: "Enter banner image alt text",
//       tooltip: true,
//       required: true,
//     },
//   ],
// };
// const InfoIcon = ({ className }: { className?: string }) => (
//   <Info className={`w-4 h-4 text-gray-400 ${className}`} />
// );
// const FormField = ({
//   label,
//   error,
//   children,
//   required = false,
//   tooltip = false,
// }: {
//   label: string;
//   error?: string;
//   children: React.ReactNode;
//   required?: boolean;
//   tooltip?: boolean;
// }) => (
//   <div className="mb-4">
//     <label className="block text-sm font-medium text-gray-700 mb-2">
//       {label}
//       {required && <span className="text-red-500 ml-1">*</span>}
//       {tooltip && <InfoIcon className="inline ml-1" />}
//     </label>
//     {children}
//     {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
//   </div>
// );
// const DynamicField = ({
//   config,
//   register,
//   error,
// }: {
//   config: any;
//   register: any;
//   error?: string;
// }) => {
//   return (
//     <FormField
//       label={config.label}
//       error={error}
//       tooltip={config.tooltip}
//       required={config.required}
//     >
//       {config.type === "textarea" ? (
//         <textarea
//           {...register(config.name)}
//           placeholder={config.placeholder}
//           // placeholder="Enter meta description"
//           rows={config.rows || 3}
//           className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
//         />
//       ) : (
//         <input
//           {...register(config.name)}
//           type={config.type}
//           placeholder={config.placeholder}
//           className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
//         />
//       )}
//     </FormField>
//   );
// };
// // Multi-select component for categories and tags
// export interface MultiSelectOption {
//   label: string;
//   value: string;
// }
// export interface MultiSelectProps {
//   label: string;
//   placeholder: string;
//   options: MultiSelectOption[]; // Accepts array of objects
//   selectedValues: string[]; // Still stores only the 'value'
//   onChange: (values: string[]) => void;
//   onAddNew?: () => void;
//   allowCustom?: boolean;
// }
// export const MultiSelect = ({
//   label,
//   placeholder,
//   options,
//   selectedValues,
//   onChange,
//   onAddNew,
//   allowCustom = true,
// }: MultiSelectProps) => {
//   const [isOpen, setIsOpen] = useState(false);
//   const [searchTerm, setSearchTerm] = useState("");
//   // Filter options by label
//   const filteredOptions = options.filter(
//     (option) =>
//       option.label.toLowerCase().includes(searchTerm.toLowerCase()) &&
//       !selectedValues.includes(option.value)
//   );
//   const handleSelect = (option: MultiSelectOption) => {
//     if (!selectedValues.includes(option.value)) {
//       onChange([...selectedValues, option.value]);
//     }
//     setSearchTerm("");
//     setIsOpen(false);
//   };
//   const handleRemove = (value: string) => {
//     onChange(selectedValues.filter((item) => item !== value));
//   };
//   const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
//     if (e.key === "Enter" && searchTerm.trim() && allowCustom) {
//       e.preventDefault();
//       const newOption = searchTerm.trim();
//       if (!selectedValues.includes(newOption)) {
//         onChange([...selectedValues, newOption]);
//         setSearchTerm("");
//         setIsOpen(false);
//       }
//     }
//   };
//   // Helper to get label by value
//   const getLabelByValue = (value: string) => {
//     return options.find((option) => option.value === value)?.label || value;
//   };
//   return (
//     <div className="relative">
//       <div className="flex items-center justify-between mb-3">
//         <h3 className="font-semibold">{label}</h3>
//         {onAddNew && (
//           <button
//             type="button"
//             onClick={onAddNew}
//             className="bg-black text-white px-3 py-1 rounded text-sm flex items-center gap-1"
//           >
//             <Plus className="w-3 h-3" />
//             Add New
//           </button>
//         )}
//       </div>
//       {/* Selected items */}
//       {selectedValues.length > 0 && (
//         <div className="flex flex-wrap gap-2 mb-3">
//           {selectedValues.map((value, index) => (
//             <span
//               key={index}
//               className="bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm flex items-center gap-1"
//             >
//               {getLabelByValue(value)}
//               <button
//                 type="button"
//                 onClick={() => handleRemove(value)}
//                 className="text-blue-600 hover:text-blue-800 ml-1"
//               >
//                 ×
//               </button>
//             </span>
//           ))}
//         </div>
//       )}
//       {/* Search input */}
//       <div className="relative">
//         <input
//           type="text"
//           placeholder={placeholder}
//           value={searchTerm}
//           onChange={(e) => {
//             setSearchTerm(e.target.value);
//             setIsOpen(true);
//           }}
//           onFocus={() => setIsOpen(true)}
//           onKeyDown={handleKeyDown}
//           className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
//         />
//         {/* Dropdown */}
//         {isOpen &&
//           (filteredOptions.length > 0 ||
//             (allowCustom && searchTerm.trim())) && (
//             <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto">
//               {filteredOptions.map((option, index) => (
//                 <div
//                   key={index}
//                   onClick={() => handleSelect(option)}
//                   className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm"
//                 >
//                   {option.label}
//                 </div>
//               ))}
//               {allowCustom && searchTerm.trim() && (
//                 <div
//                   onClick={() =>
//                     handleSelect({
//                       label: searchTerm.trim(),
//                       value: searchTerm.trim(),
//                     })
//                   }
//                   className="px-3 py-2 hover:bg-green-50 cursor-pointer text-sm border-t border-gray-200 text-green-700"
//                 >
//                   Add &quot;{searchTerm.trim()}&quot;
//                 </div>
//               )}
//             </div>
//           )}
//       </div>
//       {/* Click outside to close */}
//       {isOpen && (
//         <div className="fixed inset-0 z-5" onClick={() => setIsOpen(false)} />
//       )}
//     </div>
//   );
// };
// // Original DynamicContentManager (preserved for backward compatibility)
// export default function DynamicContentManager() {
//   const router = useRouter();
//   const [categories, setCategories] = useState<string[]>([]);
//   const [tags, setTags] = useState<string[]>([]);
//   const [availableCategories, setAvailableCategories] = useState<
//     MultiSelectOption[]
//   >([]);
//   const [isSubmitting, setIsSubmitting] = useState(false);
//   const [bannerImageFile, setBannerImageFile] = useState<File | null>(null);
//   const {
//     control,
//     register,
//     handleSubmit,
//     watch,
//     setValue,
//     formState: { errors },
//   } = useForm<FormData>({
//     defaultValues: {
//       isBlog: true,
//       // existingUrl: false,
//       categories: [],
//       tags: [],
//       content: "",
//       banner: {
//         title: "",
//         description: "",
//         altText: "",
//         image: "",
//       },
//     },
//   });
//   // const existingUrl = watch("existingUrl");
//   // Fetch available categories on component mount
//   useEffect(() => {
//     const fetchCategories = async () => {
//       try {
//         const response = await getCategoriesAction();
//         if (response.success && response.data) {
//           const categoriesData = (response.data as any).categories || [];
//           const categoryNames = categoriesData.map((cat: any) => ({
//             label: cat.name,
//             value: cat._id,
//           }));
//           setAvailableCategories(categoryNames);
//         }
//       } catch (error) {
//         console.error("Failed to fetch categories:", error);
//       }
//     };
//     fetchCategories();
//   }, []);
//   const onSubmit = async (data: FormData) => {
//     setIsSubmitting(true);
//     try {
//       console.log("Form data before cleaning:", data);
//       // Clean and format the data
//       const formattedData = cleanObject(data);
//       const bannerImageBase64 = bannerImageFile
//         ? ((await convertToBase64(bannerImageFile)) as string)
//         : "";
//       const res = await createPostAction(formattedData, bannerImageBase64);
//       if (!res.success) {
//         console.error("API Error:", res);
//         toast.error(res?.message || "Failed to create post");
//       } else {
//         console.log("Post created successfully:", res);
//         toast.success(res?.message || "Post created successfully");
//         // Redirect to blogs management page after successful creation
//         setTimeout(() => {
//           router.push("/dashboard/blogs");
//         }, 1500); // Wait 1.5 seconds to show the success toast
//       }
//     } catch (error) {
//       console.error("Submission error:", error);
//       toast.error("An unexpected error occurred");
//     } finally {
//       setIsSubmitting(false);
//     }
//   };
//   const handleBannerImageChange = (
//     event: React.ChangeEvent<HTMLInputElement>
//   ) => {
//     const file = event.target.files?.[0];
//     if (file) {
//       console.log("Banner image selected:", {
//         name: file.name,
//         size: file.size,
//         type: file.type,
//       });
//       // Validate file
//       const allowedTypes = [
//         "image/jpeg",
//         "image/jpg",
//         "image/png",
//         "image/webp",
//         "image/gif",
//         "image/svg+xml",
//       ];
//       const maxSize = 10 * 1024 * 1024; // 10MB
//       if (!allowedTypes.includes(file.type)) {
//         toast.error(
//           `Invalid file type: ${file.type}. Only images are allowed.`
//         );
//         return;
//       }
//       if (file.size > maxSize) {
//         toast.error(
//           `File too large: ${(file.size / 1024 / 1024).toFixed(
//             2
//           )}MB. Maximum 10MB allowed.`
//         );
//         return;
//       }
//       if (file.size === 0) {
//         toast.error("File is empty");
//         return;
//       }
//       setBannerImageFile(file);
//       // Optionally create a preview URL
//       const previewUrl = URL.createObjectURL(file);
//       setValue("banner.image", previewUrl); // For preview purposes
//     } else {
//       setBannerImageFile(null);
//       setValue("banner.image", "");
//     }
//   };
//   return (
//     <div className="min-h-screen bg-gray-50 p-6">
//       <div className="container px-3 mx-auto">
//         <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
//           {/* Blog Post Section */}
//           <div className="bg-white p-6 rounded-lg shadow-sm border">
//             <div className="flex items-center justify-between mb-6">
//               <div className="flex items-center gap-4">
//                 <span className="text-teal-600">Create</span>
//                 <span className="text-gray-400">•</span>
//                 <span>Blog Post</span>
//               </div>
//               <div className="flex items-center gap-2">
//                 <button
//                   onClick={() => router.push("/dashboard/blogs")}
//                   type="button"
//                   className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
//                 >
//                   Discard
//                 </button>
//                 <button
//                   type="submit"
//                   disabled={isSubmitting}
//                   className="bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 disabled:opacity-50 flex items-center gap-2"
//                 >
//                   {isSubmitting && <Loader2 className="w-4 h-4 animate-spin" />}
//                   {isSubmitting ? "Creating..." : "Create Post"}
//                 </button>
//               </div>
//             </div>
//             <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
//               <div className="lg:col-span-2">
//                 <div className="space-y-6">
//                   <div>
//                     <h3 className="text-lg font-semibold mb-4">Post Details</h3>
//                     {fieldConfigs.blogPost.map((config) => (
//                       <DynamicField
//                         key={config.name}
//                         config={config}
//                         register={register}
//                         error={errors[config.name]?.message}
//                       />
//                     ))}
//                     {/* <FormField
//                       label="Canonical URL"
//                       error={errors.canonicalUrl?.message}
//                       tooltip
//                     >
//                       <div className="space-y-2">
//                         <div className="flex items-center gap-2">
//                           <input
//                             {...register("existingUrl")}
//                             type="checkbox"
//                             className="rounded"
//                           />
//                           <label className="text-sm">Existing Url</label>
//                         </div>
//                         <div className="flex items-center gap-2">
//                           <input
//                             {...register("canonicalUrl")}
//                             type="url"
//                             placeholder="https://example.com/blog/post/"
//                             disabled={!existingUrl}
//                             className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
//                           />
//                           <button
//                             type="button"
//                             disabled={!existingUrl}
//                             className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 flex items-center gap-1"
//                           >
//                             <Eye className="w-4 h-4" />
//                             Visit
//                           </button>
//                         </div>
//                       </div>
//                     </FormField> */}
//                     {/* Categories and Tags Section */}
//                     <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//                       <div>
//                         <MultiSelect
//                           label="Categories"
//                           placeholder="Search for categories..."
//                           options={availableCategories}
//                           selectedValues={categories}
//                           onChange={(values) => {
//                             setCategories(values);
//                             setValue("categories", values);
//                           }}
//                           allowCustom={true}
//                         />
//                       </div>
//                       <div>
//                         <MultiSelect
//                           label="Tags"
//                           placeholder="Search for tags..."
//                           options={[]} // Tags are usually custom, so no predefined options
//                           selectedValues={tags}
//                           onChange={(values) => {
//                             setTags(values);
//                             setValue("tags", values);
//                           }}
//                           allowCustom={true}
//                         />
//                       </div>
//                     </div>
//                   </div>
//                   <div>
//                     <h3 className="text-lg font-semibold mb-4">Blog Content</h3>
//                     <FormField
//                       label="Content"
//                       error={errors.content?.message}
//                       required
//                     >
//                       <Controller
//                         name="content"
//                         control={control}
//                         render={({ field }) => (
//                           <WysiwygEditor
//                             content={field.value || ""}
//                             onChange={field.onChange}
//                             placeholder="Start writing your blog post content..."
//                           />
//                         )}
//                       />
//                     </FormField>
//                   </div>
//                 </div>
//               </div>
//               <div className="space-y-6">
//                 {/* Sidebar content removed as requested */}
//               </div>
//             </div>
//           </div>
//           {/* SEO Section */}
//           <div className="bg-white p-6 rounded-lg shadow-sm border">
//             <h2 className="text-lg font-semibold mb-6">SEO</h2>
//             <div className="space-y-4">
//               {fieldConfigs.seo.map((config) => (
//                 <DynamicField
//                   key={config.name}
//                   config={config}
//                   register={register}
//                   error={errors[config.name]?.message}
//                 />
//               ))}
//             </div>
//           </div>
//           {/* Banner Section */}
//           <div className="bg-white p-6 rounded-lg shadow-sm border">
//             <h2 className="text-lg font-semibold mb-6">Banner</h2>
//             <div className="space-y-4">
//               {fieldConfigs.banner.map((config) => (
//                 <DynamicField
//                   key={config.name}
//                   config={config}
//                   register={register}
//                   error={errors.banner?.[config.name]?.message}
//                 />
//               ))}
//               <div className="space-y-4">
//                 <label
//                   htmlFor="bannerImage"
//                   className="block text-sm font-medium text-gray-700 jakarta"
//                 >
//                   Banner Image
//                 </label>
//                 <div className="flex items-center gap-4">
//                   <Button
//                     type="button"
//                     variant="default"
//                     className="flex items-center gap-2 text-white transition-all duration-300 rounded-md shadow-md"
//                     onClick={() =>
//                       document.getElementById("bannerImage")?.click()
//                     }
//                   >
//                     <ImagePlus size={20} />
//                     <span>Choose Image</span>
//                   </Button>
//                   <input
//                     type="file"
//                     id="bannerImage"
//                     accept="image/*"
//                     onChange={handleBannerImageChange}
//                     className="hidden"
//                   />
//                 </div>
//                 {bannerImageFile && (
//                   <div className="mt-4">
//                     <p className="text-sm text-gray-600 jakarta">
//                       Selected: {bannerImageFile.name} (
//                       {(bannerImageFile.size / 1024).toFixed(2)} KB)
//                     </p>
//                     <img
//                       src={URL.createObjectURL(bannerImageFile)}
//                       alt="Preview"
//                       className="mt-2 max-w-[200px] max-h-[200px] rounded-md shadow-lg object-cover"
//                     />
//                   </div>
//                 )}
//               </div>
//             </div>
//           </div>
//         </form>
//       </div>
//     </div>
//   );
// }
// Second
__turbopack_context__.s({
    "MultiSelect": (()=>MultiSelect),
    "default": (()=>DynamicContentManager)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$shared$2f$cleanObject$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/shared/cleanObject.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$data$3a$ab07ba__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/dashboard/blogs/data:ab07ba [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$data$3a$cb08fa__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/src/app/dashboard/blogs/data:cb08fa [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2d$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ImagePlus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/image-plus.js [app-client] (ecmascript) <export default as ImagePlus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/info.js [app-client] (ecmascript) <export default as Info>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-client] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-client] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$components$2f$WysiwygEditor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/dashboard/blogs/components/WysiwygEditor.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
// Form schema matching the API
const formSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    title: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "Title is required"),
    slug: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    // canonicalUrl: z
    //   .string()
    //   .url("Must be a valid URL")
    //   .optional()
    //   .or(z.literal("")),
    // existingUrl: z.boolean().default(false),
    content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "Content is required"),
    isBlog: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().default(true),
    categories: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()).default([]),
    tags: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()).default([]),
    metaTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "Meta title is required"),
    metaDescription: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "Meta description is required"),
    metaKeywords: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "Meta keywords are required"),
    banner: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        image: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "Banner image is required")
    })
});
// Field configuration for dynamic rendering
const fieldConfigs = {
    blogPost: [
        {
            name: "title",
            label: "Title",
            type: "text",
            placeholder: "Enter the title of the blog post",
            tooltip: true,
            required: true
        },
        {
            name: "slug",
            label: "Slug (Optional)",
            type: "text",
            placeholder: "Enter a unique slug (auto-generated if empty)",
            tooltip: true,
            required: false
        }
    ],
    seo: [
        {
            name: "metaTitle",
            label: "Meta Title",
            type: "text",
            placeholder: "Enter meta title ",
            tooltip: true,
            required: true
        },
        {
            name: "metaDescription",
            label: "Meta Description",
            type: "textarea",
            placeholder: "Enter meta description",
            rows: 3,
            tooltip: true,
            required: true
        },
        {
            name: "metaKeywords",
            label: "Meta Keywords (comma-separated)",
            type: "text",
            placeholder: "Enter meta keywords ",
            tooltip: true,
            required: true
        }
    ],
    banner: []
};
const InfoIcon = ({ className })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$info$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Info$3e$__["Info"], {
        className: `w-4 h-4 text-gray-400 ${className}`
    }, void 0, false, {
        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
        lineNumber: 802,
        columnNumber: 3
    }, this);
_c = InfoIcon;
const FormField = ({ label, error, children, required = false, tooltip = false })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "mb-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                className: "block text-sm font-medium text-gray-700 mb-2",
                children: [
                    label,
                    required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-red-500 ml-1",
                        children: "*"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                        lineNumber: 821,
                        columnNumber: 20
                    }, this),
                    tooltip && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(InfoIcon, {
                        className: "inline ml-1"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                        lineNumber: 822,
                        columnNumber: 19
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                lineNumber: 819,
                columnNumber: 5
            }, this),
            children,
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-red-500 text-sm mt-1",
                children: error
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                lineNumber: 825,
                columnNumber: 15
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
        lineNumber: 818,
        columnNumber: 3
    }, this);
_c1 = FormField;
const DynamicField = ({ config, register, error })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
        label: config.label,
        error: error,
        tooltip: config.tooltip,
        required: config.required,
        children: config.type === "textarea" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
            ...register(config.name),
            placeholder: config.placeholder,
            // placeholder="Enter meta description"
            rows: config.rows || 3,
            className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
        }, void 0, false, {
            fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
            lineNumber: 846,
            columnNumber: 9
        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
            ...register(config.name),
            type: config.type,
            placeholder: config.placeholder,
            className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        }, void 0, false, {
            fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
            lineNumber: 854,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
        lineNumber: 839,
        columnNumber: 5
    }, this);
};
_c2 = DynamicField;
const MultiSelect = ({ label, placeholder, options, selectedValues, onChange, onAddNew, allowCustom = true })=>{
    _s();
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [searchTerm, setSearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    // Filter options by label
    const filteredOptions = options.filter((option)=>option.label.toLowerCase().includes(searchTerm.toLowerCase()) && !selectedValues.includes(option.value));
    const handleSelect = (option)=>{
        if (!selectedValues.includes(option.value)) {
            onChange([
                ...selectedValues,
                option.value
            ]);
        }
        setSearchTerm("");
        setIsOpen(false);
    };
    const handleRemove = (value)=>{
        onChange(selectedValues.filter((item)=>item !== value));
    };
    const handleKeyDown = (e)=>{
        if (e.key === "Enter" && searchTerm.trim() && allowCustom) {
            e.preventDefault();
            const newOption = searchTerm.trim();
            if (!selectedValues.includes(newOption)) {
                onChange([
                    ...selectedValues,
                    newOption
                ]);
                setSearchTerm("");
                setIsOpen(false);
            }
        }
    };
    // Helper to get label by value
    const getLabelByValue = (value)=>{
        return options.find((option)=>option.value === value)?.label || value;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between mb-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "font-semibold",
                        children: label
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                        lineNumber: 932,
                        columnNumber: 9
                    }, this),
                    onAddNew && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: onAddNew,
                        className: "bg-black text-white px-3 py-1 rounded text-sm flex items-center gap-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                className: "w-3 h-3"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                lineNumber: 939,
                                columnNumber: 13
                            }, this),
                            "Add New"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                        lineNumber: 934,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                lineNumber: 931,
                columnNumber: 7
            }, this),
            selectedValues.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-wrap gap-2 mb-3",
                children: selectedValues.map((value, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm flex items-center gap-1",
                        children: [
                            getLabelByValue(value),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                type: "button",
                                onClick: ()=>handleRemove(value),
                                className: "text-blue-600 hover:text-blue-800 ml-1",
                                children: "×"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                lineNumber: 954,
                                columnNumber: 15
                            }, this)
                        ]
                    }, index, true, {
                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                        lineNumber: 949,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                lineNumber: 947,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        type: "text",
                        placeholder: placeholder,
                        value: searchTerm,
                        onChange: (e)=>{
                            setSearchTerm(e.target.value);
                            setIsOpen(true);
                        },
                        onFocus: ()=>setIsOpen(true),
                        onKeyDown: handleKeyDown,
                        className: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                        lineNumber: 968,
                        columnNumber: 9
                    }, this),
                    isOpen && (filteredOptions.length > 0 || allowCustom && searchTerm.trim()) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto",
                        children: [
                            filteredOptions.map((option, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    onClick: ()=>handleSelect(option),
                                    className: "px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm",
                                    children: option.label
                                }, index, false, {
                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                    lineNumber: 987,
                                    columnNumber: 17
                                }, this)),
                            allowCustom && searchTerm.trim() && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                onClick: ()=>handleSelect({
                                        label: searchTerm.trim(),
                                        value: searchTerm.trim()
                                    }),
                                className: "px-3 py-2 hover:bg-green-50 cursor-pointer text-sm border-t border-gray-200 text-green-700",
                                children: [
                                    'Add "',
                                    searchTerm.trim(),
                                    '"'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                lineNumber: 996,
                                columnNumber: 17
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                        lineNumber: 985,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                lineNumber: 967,
                columnNumber: 7
            }, this),
            isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 z-5",
                onClick: ()=>setIsOpen(false)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                lineNumber: 1014,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
        lineNumber: 930,
        columnNumber: 5
    }, this);
};
_s(MultiSelect, "ZBdbP2XiOJNfdd+CXqrKn6ipFjM=");
_c3 = MultiSelect;
function DynamicContentManager() {
    _s1();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [categories, setCategories] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [tags, setTags] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [availableCategories, setAvailableCategories] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isSubmitting, setIsSubmitting] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [bannerImageFile, setBannerImageFile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const { control, register, handleSubmit, watch, setValue, formState: { errors } } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        defaultValues: {
            isBlog: true,
            // existingUrl: false,
            categories: [],
            tags: [],
            content: "",
            banner: {
                image: ""
            }
        }
    });
    // const existingUrl = watch("existingUrl");
    // Fetch available categories on component mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DynamicContentManager.useEffect": ()=>{
            const fetchCategories = {
                "DynamicContentManager.useEffect.fetchCategories": async ()=>{
                    try {
                        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$data$3a$ab07ba__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["getCategoriesAction"])();
                        if (response.success && response.data) {
                            const categoriesData = response.data.categories || [];
                            const categoryNames = categoriesData.map({
                                "DynamicContentManager.useEffect.fetchCategories.categoryNames": (cat)=>({
                                        label: cat.name,
                                        value: cat._id
                                    })
                            }["DynamicContentManager.useEffect.fetchCategories.categoryNames"]);
                            setAvailableCategories(categoryNames);
                        }
                    } catch (error) {
                        console.error("Failed to fetch categories:", error);
                    }
                }
            }["DynamicContentManager.useEffect.fetchCategories"];
            fetchCategories();
        }
    }["DynamicContentManager.useEffect"], []);
    const onSubmit = async (data)=>{
        setIsSubmitting(true);
        try {
            console.log("Form data before cleaning:", data);
            // Clean and format the data
            const formattedData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$shared$2f$cleanObject$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cleanObject"])(data);
            const bannerImageBase64 = bannerImageFile ? await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$shared$2f$cleanObject$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertToBase64"])(bannerImageFile) : "";
            const res = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$data$3a$cb08fa__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["createPostAction"])(formattedData, bannerImageBase64);
            if (!res.success) {
                console.error("API Error:", res);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(res?.message || "Failed to create post");
            } else {
                console.log("Post created successfully:", res);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(res?.message || "Post created successfully");
                // Redirect to blogs management page after successful creation
                setTimeout(()=>{
                    router.push("/dashboard/blogs");
                }, 1500); // Wait 1.5 seconds to show the success toast
            }
        } catch (error) {
            console.error("Submission error:", error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("An unexpected error occurred");
        } finally{
            setIsSubmitting(false);
        }
    };
    const handleBannerImageChange = (event)=>{
        const file = event.target.files?.[0];
        if (file) {
            console.log("Banner image selected:", {
                name: file.name,
                size: file.size,
                type: file.type
            });
            // Validate file
            const allowedTypes = [
                "image/jpeg",
                "image/jpg",
                "image/png",
                "image/webp",
                "image/gif",
                "image/svg+xml"
            ];
            const maxSize = 10 * 1024 * 1024; // 10MB
            if (!allowedTypes.includes(file.type)) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(`Invalid file type: ${file.type}. Only images are allowed.`);
                return;
            }
            if (file.size > maxSize) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(`File too large: ${(file.size / 1024 / 1024).toFixed(2)}MB. Maximum 10MB allowed.`);
                return;
            }
            if (file.size === 0) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error("File is empty");
                return;
            }
            setBannerImageFile(file);
            // Optionally create a preview URL
            const previewUrl = URL.createObjectURL(file);
            setValue("banner.image", previewUrl); // For preview purposes
        } else {
            setBannerImageFile(null);
            setValue("banner.image", "");
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50 p-6",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "container px-3 mx-auto",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                onSubmit: handleSubmit(onSubmit),
                className: "space-y-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white p-6 rounded-lg shadow-sm border",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-between mb-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-teal-600",
                                                children: "Create"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                lineNumber: 1169,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-gray-400",
                                                children: "•"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                lineNumber: 1170,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: "Blog Post"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                lineNumber: 1171,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                        lineNumber: 1168,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center gap-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>router.push("/dashboard/blogs"),
                                                type: "button",
                                                className: "px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",
                                                children: "Discard"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                lineNumber: 1174,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                type: "submit",
                                                disabled: isSubmitting,
                                                className: "bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 disabled:opacity-50 flex items-center gap-2",
                                                children: [
                                                    isSubmitting && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2$3e$__["Loader2"], {
                                                        className: "w-4 h-4 animate-spin"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                        lineNumber: 1186,
                                                        columnNumber: 36
                                                    }, this),
                                                    isSubmitting ? "Creating..." : "Create Post"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                lineNumber: 1181,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                        lineNumber: 1173,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                lineNumber: 1167,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-1 lg:grid-cols-3 gap-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "lg:col-span-2",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-6",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "text-lg font-semibold mb-4",
                                                            children: "Post Details"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                            lineNumber: 1196,
                                                            columnNumber: 21
                                                        }, this),
                                                        fieldConfigs.blogPost.map((config)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DynamicField, {
                                                                config: config,
                                                                register: register,
                                                                error: errors[config.name]?.message
                                                            }, config.name, false, {
                                                                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                                lineNumber: 1198,
                                                                columnNumber: 23
                                                            }, this)),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(MultiSelect, {
                                                                        label: "Categories",
                                                                        placeholder: "Search for categories...",
                                                                        options: availableCategories,
                                                                        selectedValues: categories,
                                                                        onChange: (values)=>{
                                                                            setCategories(values);
                                                                            setValue("categories", values);
                                                                        },
                                                                        allowCustom: true
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                                        lineNumber: 1242,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                                    lineNumber: 1241,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(MultiSelect, {
                                                                        label: "Tags",
                                                                        placeholder: "Search for tags...",
                                                                        options: [],
                                                                        selectedValues: tags,
                                                                        onChange: (values)=>{
                                                                            setTags(values);
                                                                            setValue("tags", values);
                                                                        },
                                                                        allowCustom: true
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                                        lineNumber: 1255,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                                    lineNumber: 1254,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                            lineNumber: 1240,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                    lineNumber: 1195,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "text-lg font-semibold mb-4",
                                                            children: "Blog Content"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                            lineNumber: 1271,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(FormField, {
                                                            label: "Content",
                                                            error: errors.content?.message,
                                                            required: true,
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Controller"], {
                                                                name: "content",
                                                                control: control,
                                                                render: ({ field })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$dashboard$2f$blogs$2f$components$2f$WysiwygEditor$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                        content: field.value || "",
                                                                        onChange: field.onChange,
                                                                        placeholder: "Start writing your blog post content..."
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                                        lineNumber: 1281,
                                                                        columnNumber: 27
                                                                    }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                                lineNumber: 1277,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                            lineNumber: 1272,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                    lineNumber: 1270,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                            lineNumber: 1194,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                        lineNumber: 1193,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-6"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                        lineNumber: 1293,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                lineNumber: 1192,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                        lineNumber: 1166,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white p-6 rounded-lg shadow-sm border",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-lg font-semibold mb-6",
                                children: "SEO"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                lineNumber: 1301,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-4",
                                children: fieldConfigs.seo.map((config)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DynamicField, {
                                        config: config,
                                        register: register,
                                        error: errors[config.name]?.message
                                    }, config.name, false, {
                                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                        lineNumber: 1304,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                lineNumber: 1302,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                        lineNumber: 1300,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white p-6 rounded-lg shadow-sm border",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-lg font-semibold mb-6",
                                children: "Banner"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                lineNumber: 1316,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            htmlFor: "bannerImage",
                                            className: "block text-sm font-medium text-gray-700 jakarta",
                                            children: "Banner Image"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                            lineNumber: 1319,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                    type: "button",
                                                    variant: "default",
                                                    className: "flex items-center gap-2 text-white transition-all duration-300 rounded-md shadow-md",
                                                    onClick: ()=>document.getElementById("bannerImage")?.click(),
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$image$2d$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ImagePlus$3e$__["ImagePlus"], {
                                                            size: 20
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                            lineNumber: 1334,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            children: "Choose Image"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                            lineNumber: 1335,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                    lineNumber: 1326,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                    type: "file",
                                                    id: "bannerImage",
                                                    accept: "image/*",
                                                    onChange: handleBannerImageChange,
                                                    className: "hidden"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                    lineNumber: 1337,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                            lineNumber: 1325,
                                            columnNumber: 17
                                        }, this),
                                        bannerImageFile && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "mt-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm text-gray-600 jakarta",
                                                    children: [
                                                        "Selected: ",
                                                        bannerImageFile.name,
                                                        " (",
                                                        (bannerImageFile.size / 1024).toFixed(2),
                                                        " KB)"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                    lineNumber: 1348,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                    src: URL.createObjectURL(bannerImageFile) || "/placeholder.svg",
                                                    alt: "Preview",
                                                    className: "mt-2 max-w-[200px] max-h-[200px] rounded-md shadow-lg object-cover"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                                    lineNumber: 1352,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                            lineNumber: 1347,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                    lineNumber: 1318,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                                lineNumber: 1317,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                        lineNumber: 1315,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
                lineNumber: 1164,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
            lineNumber: 1163,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/dashboard/blogs/components/BlogPostEditor.tsx",
        lineNumber: 1162,
        columnNumber: 5
    }, this);
}
_s1(DynamicContentManager, "b19cJvkxQJtXRMpfD99nu6/VrL4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"]
    ];
});
_c4 = DynamicContentManager;
var _c, _c1, _c2, _c3, _c4;
__turbopack_context__.k.register(_c, "InfoIcon");
__turbopack_context__.k.register(_c1, "FormField");
__turbopack_context__.k.register(_c2, "DynamicField");
__turbopack_context__.k.register(_c3, "MultiSelect");
__turbopack_context__.k.register(_c4, "DynamicContentManager");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_app_b90f5fe2._.js.map