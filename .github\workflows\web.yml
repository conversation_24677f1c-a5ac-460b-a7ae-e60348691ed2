name: CI/CD for Web Deployment (VPS)
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: 'freespin/package-lock.json'
      - name: Build Docker Image
        run: |
          cd freespin
          docker build -t freespin168:latest .
      - name: Save Docker Image
        run: |
          docker save freespin168:latest | gzip > freespin168-docker.tar.gz
      - name: Setup SSH Key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p 22 -H ${{ secrets.VPS_HOST }} >> ~/.ssh/known_hosts
      - name: Deploy Docker Image to VPS
        env:
          VPS_HOST: ${{ secrets.VPS_HOST }}
          VPS_USER: ${{ secrets.VPS_USER }}
        run: |
          # Copy Docker image to VPS
          scp -P 22 freespin168-docker.tar.gz $VPS_USER@$VPS_HOST:/tmp/
          # SSH into the VPS and deploy with Docker
          ssh -p 22 $VPS_USER@$VPS_HOST << 'EOF'
            # Create directory for public files on VPS if it doesn't exist
            mkdir -p /var/www/freespin168/public

            # Load the Docker image
            docker load < /tmp/freespin168-docker.tar.gz
            rm /tmp/freespin168-docker.tar.gz

            # Stop and remove existing container if it exists
            docker stop freespin168 || true
            docker rm freespin168 || true

            # Run the new container with volume mounting
            docker run -d \
              --name freespin168 \
              --restart unless-stopped \
              -p 3000:3000 \
              -v /home/<USER>/freespin168/public:/app/public \
              freespin168:latest

            # Clean up old images (keep only the latest)
            docker image prune -f
          EOF
      - name: Cleanup SSH Key
        if: always()
        run: rm -f ~/.ssh/id_rsa
