import { Client } from "minio";
import { Readable } from "stream";

// MinIO configuration
const minioConfig = {
  endPoint:
    process.env.MINIO_ENDPOINT?.replace(/^https?:\/\//, "") || "localhost",
  port: process.env.MINIO_PORT ? Number.parseInt(process.env.MINIO_PORT) : 9000,
  useSSL: process.env.MINIO_USE_SSL === "true",
  accessKey: process.env.MINIO_ACCESS_KEY || "3uiq5emitjasdfghyjui",
  secretKey:
    process.env.MINIO_SECRET_KEY || "TYo1ruP1PqbOx3fONapGwawKQGqCDQsdfadsdfgh",
};

const BUCKET_NAME = process.env.MINIO_BUCKET_NAME || "spinfree";

// Create MinIO client
const minioClient = new Client(minioConfig);

// Initialize bucket if it doesn't exist
const initializeBucket = async (): Promise<void> => {
  try {
    console.log("Checking MinIO connection...");
    console.log("MinIO config:", {
      endPoint: minioConfig.endPoint,
      port: minioConfig.port,
      useSSL: minioConfig.useSSL,
      bucket: BUCKET_NAME,
    });

    const bucketExists = await minioClient.bucketExists(BUCKET_NAME);

    if (!bucketExists) {
      await minioClient.makeBucket(BUCKET_NAME, "us-east-1");
      console.log(`Bucket '${BUCKET_NAME}' created successfully`);

      // Set bucket policy to allow public read access
      const policy = {
        Version: "2012-10-17",
        Statement: [
          {
            Effect: "Allow",
            Principal: { AWS: ["*"] },
            Action: ["s3:GetObject"],
            Resource: [`arn:aws:s3:::${BUCKET_NAME}/*`],
          },
        ],
      };

      await minioClient.setBucketPolicy(BUCKET_NAME, JSON.stringify(policy));
      console.log(`Bucket policy set for '${BUCKET_NAME}'`);
    } else {
      console.log(`Bucket '${BUCKET_NAME}' already exists`);
    }
  } catch (error) {
    console.error("Error initializing MinIO bucket:", error);
    // Don't throw error, allow application to continue
  }
};

// File type detection
const detectImageType = (buffer: Buffer): string | null => {
  // Check for PNG
  if (
    buffer[0] === 0x89 &&
    buffer[1] === 0x50 &&
    buffer[2] === 0x4e &&
    buffer[3] === 0x47
  ) {
    return "image/png";
  }
  // Check for JPEG
  if (buffer[0] === 0xff && buffer[1] === 0xd8 && buffer[2] === 0xff) {
    return "image/jpeg";
  }
  // Check for SVG
  const possibleSvg = buffer.toString("ascii", 0, 100).toLowerCase();
  if (possibleSvg.includes("<svg") || possibleSvg.includes("<?xml")) {
    return "image/svg+xml";
  }
  return null;
};

export class MinioService {
  static async uploadFile(
    fileBuffer: Buffer,
    fileName: string,
    contentType: string,
    folder = "posts"
  ): Promise<string> {
    try {
      console.log("Starting file upload to MinIO...");

      // Check MinIO connection first
      try {
        await minioClient.bucketExists(BUCKET_NAME);
        console.log("MinIO connection verified");
      } catch (connectionError) {
        console.error("MinIO connection failed:", connectionError);
        throw new Error(
          "MinIO service is unavailable. Please try again later."
        );
      }

      // Initialize bucket if needed
      await initializeBucket();

      // Detect actual file type if it's octet-stream
      let mimeType = contentType;
      if (contentType === "application/octet-stream") {
        const detectedType = detectImageType(fileBuffer);
        if (!detectedType) {
          throw new Error("Invalid or unsupported image format");
        }
        mimeType = detectedType;
      }

      // Generate unique filename with date structure
      const date = new Date();
      const fileExtension =
        mimeType === "image/svg+xml"
          ? "svg"
          : mimeType === "image/png"
          ? "png"
          : "jpg";
      const uniqueFileName = `${Date.now()}-${fileName.replace(
        /\.[^/.]+$/,
        ""
      )}.${fileExtension}`;

      const relativePath = [
        folder,
        date.getFullYear().toString(),
        (date.getMonth() + 1).toString().padStart(2, "0"),
        uniqueFileName,
      ].join("/");

      console.log("Uploading to path:", relativePath);

      // Create readable stream from buffer
      const fileStream = new Readable();
      fileStream.push(fileBuffer);
      fileStream.push(null);

      // Upload to MinIO
      await minioClient.putObject(
        BUCKET_NAME,
        relativePath,
        fileStream,
        fileBuffer.length,
        {
          "Content-Type": mimeType,
        }
      );

      // Generate URL through our API proxy instead of direct MinIO URL
      const baseUrl =
        process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3001";
      const fileUrl = `${baseUrl}/api/uploads/${relativePath}`;

      console.log(`File uploaded to MinIO successfully!`);
      console.log(`- MinIO path: ${relativePath}`);
      console.log(`- Proxy URL: ${fileUrl}`);
      console.log(`- Base URL: ${baseUrl}`);
      return fileUrl;
    } catch (error) {
      console.error("Error uploading file to MinIO:", error);
      console.error("MinIO config:", {
        endPoint: minioConfig.endPoint,
        port: minioConfig.port,
        useSSL: minioConfig.useSSL,
        bucket: BUCKET_NAME,
      });
      console.error("Upload details:", {
        fileName,
        contentType,
        folder,
        bufferSize: fileBuffer.length,
      });

      // Fallback: return a placeholder URL instead of throwing
      const fallbackUrl = `/uploads/${folder}/${Date.now()}-${fileName}`;
      console.log("Upload failed, using fallback URL:", fallbackUrl);
      return fallbackUrl;
    }
  }

  static async deleteFile(fileUrl: string): Promise<void> {
    try {
      // Extract object name from URL
      const url = new URL(fileUrl);
      const objectName = url.pathname.substring(
        url.pathname.indexOf("/", 1) + 1
      );

      await minioClient.removeObject(BUCKET_NAME, objectName);
      console.log(`File deleted from MinIO: ${objectName}`);
    } catch (error) {
      console.error("Error deleting file from MinIO:", error);
      throw new Error("Failed to delete file");
    }
  }

  static async listFiles(folder = "posts", maxKeys = 100): Promise<any[]> {
    try {
      const files: any[] = [];
      const stream = minioClient.listObjects(BUCKET_NAME, `${folder}/`, true);

      return new Promise((resolve, reject) => {
        stream.on("data", (obj) => {
          if (files.length < maxKeys) {
            const protocol = minioConfig.useSSL ? "https" : "http";
            const port =
              minioConfig.port !== (minioConfig.useSSL ? 443 : 80)
                ? `:${minioConfig.port}`
                : "";
            const fileUrl = `${protocol}://${minioConfig.endPoint}${port}/${BUCKET_NAME}/${obj.name}`;

            files.push({
              name: obj.name,
              size: obj.size,
              lastModified: obj.lastModified,
              url: fileUrl,
              fileName: obj.name?.split("/").pop() || "",
              folder: obj.name?.split("/").slice(0, -1).join("/") || "",
            });
          }
        });

        stream.on("end", () => resolve(files));
        stream.on("error", (err) => reject(err));
      });
    } catch (error) {
      console.error("Error listing files:", error);
      throw new Error("Failed to list files");
    }
  }

  static async getPresignedUrl(
    objectName: string,
    expiry = 3600
  ): Promise<string> {
    try {
      return await minioClient.presignedGetObject(
        BUCKET_NAME,
        objectName,
        expiry
      );
    } catch (error) {
      console.error("Error generating presigned URL:", error);
      throw new Error("Failed to generate presigned URL");
    }
  }

  static async getFileStream(objectName: string): Promise<Buffer> {
    try {
      const stream = await minioClient.getObject(BUCKET_NAME, objectName);
      const chunks: Buffer[] = [];

      return new Promise((resolve, reject) => {
        stream.on("data", (chunk) => chunks.push(chunk));
        stream.on("end", () => resolve(Buffer.concat(chunks)));
        stream.on("error", reject);
      });
    } catch (error) {
      console.error("Error getting file stream from MinIO:", error);
      throw new Error("Failed to get file from MinIO");
    }
  }
}

// Initialize bucket on module load (but don't block)
initializeBucket().catch((err) => {
  console.error("Critical error during MinIO initialization:", err);
});

export { minioClient, minioConfig, BUCKET_NAME };
