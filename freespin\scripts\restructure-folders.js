const fs = require("fs");
const path = require("path");

// Main directories to process
const APP_DIR = path.join(__dirname, "..", "src", "app");
const LOCALE_DIR = path.join(APP_DIR, "[locale]");

// Function to ensure directory exists
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// Function to copy files and directories
function copyRecursive(src, dest) {
  const stats = fs.statSync(src);

  if (stats.isDirectory()) {
    ensureDirectoryExists(dest);
    const entries = fs.readdirSync(src);

    for (const entry of entries) {
      const srcPath = path.join(src, entry);
      const destPath = path.join(dest, entry);
      copyRecursive(srcPath, destPath);
    }
  } else {
    // Read file content and update imports if needed
    let content = fs.readFileSync(src, "utf8");

    // Update import paths that reference [locale]
    content = content.replace(/@\/app\/\[locale\]\//g, "@/app/");

    // Write the file with updated content
    fs.writeFileSync(dest, content);
    console.log(`Copied and updated: ${dest}`);
  }
}

// Function to remove directory
function removeDirectory(dir) {
  if (fs.existsSync(dir)) {
    fs.rmSync(dir, { recursive: true, force: true });
    console.log(`Removed: ${dir}`);
  }
}

// Main function
function main() {
  console.log("Starting folder restructuring...");

  // Check if [locale] directory exists
  if (!fs.existsSync(LOCALE_DIR)) {
    console.log("No [locale] directory found. Exiting.");
    return;
  }

  // Copy all content from [locale] to app directory
  const entries = fs.readdirSync(LOCALE_DIR);

  for (const entry of entries) {
    const srcPath = path.join(LOCALE_DIR, entry);
    const destPath = path.join(APP_DIR, entry);

    // Skip if it's a file that already exists at the destination
    if (fs.existsSync(destPath) && !fs.statSync(destPath).isDirectory()) {
      console.log(`Skipping existing file: ${destPath}`);
      continue;
    }

    copyRecursive(srcPath, destPath);
  }

  // Remove [locale] directory after copying
  removeDirectory(LOCALE_DIR);

  console.log("Folder restructuring completed!");
}

main();
