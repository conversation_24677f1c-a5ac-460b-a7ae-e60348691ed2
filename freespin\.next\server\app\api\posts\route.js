const CHUNK_PUBLIC_PATH = "server/app/api/posts/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_c57f909e._.js");
runtime.loadChunk("server/chunks/node_modules_zod_dist_esm_54374b3b._.js");
runtime.loadChunk("server/chunks/node_modules_xmlbuilder_lib_b1c583b3._.js");
runtime.loadChunk("server/chunks/node_modules_minio_dist_esm_8222fc9e._.js");
runtime.loadChunk("server/chunks/node_modules_async_dist_async_mjs_f862774d._.js");
runtime.loadChunk("server/chunks/node_modules_lodash_lodash_d273580f.js");
runtime.loadChunk("server/chunks/node_modules_mime-db_9ebaabbe._.js");
runtime.loadChunk("server/chunks/node_modules_50248eaf._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__a9933d9d._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/posts/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/posts/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/posts/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
