{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/dashboard/blogs/components/lexical-editor.css"], "sourcesContent": ["/* Lexical Editor Styles */\n.lexical-editor {\n  font-family: inherit;\n  font-size: 16px;\n  line-height: 1.6;\n  color: inherit;\n  direction: ltr !important;\n  text-align: left !important;\n  writing-mode: horizontal-tb !important;\n  unicode-bidi: normal !important;\n}\n\n/* Force horizontal text layout - but only for content area, not toolbar */\n.lexical-content-area [contenteditable],\n.lexical-content-area [data-lexical-editor] {\n  direction: ltr !important;\n  text-align: left !important;\n  writing-mode: horizontal-tb !important;\n  unicode-bidi: normal !important;\n  white-space: normal !important;\n  word-wrap: break-word !important;\n  overflow-wrap: break-word !important;\n}\n\n/* Ensure toolbar stays horizontal */\n.lexical-editor .flex {\n  display: flex !important;\n  flex-direction: row !important;\n  flex-wrap: wrap !important;\n}\n\n/* Toolbar specific styling */\n.lexical-editor .flex button,\n.lexical-editor .flex select {\n  display: inline-flex !important;\n  flex-direction: row !important;\n  align-items: center !important;\n  justify-content: center !important;\n}\n\n/* Toolbar container */\n.lexical-editor > div:first-child {\n  display: flex !important;\n  flex-direction: row !important;\n  flex-wrap: wrap !important;\n  align-items: center !important;\n}\n\n/* Specific styling for content area only */\n.lexical-content-area [contenteditable] {\n  white-space: normal !important;\n  word-wrap: break-word !important;\n  overflow-wrap: break-word !important;\n  text-align: left !important;\n  direction: ltr !important;\n  writing-mode: horizontal-tb !important;\n  unicode-bidi: normal !important;\n  font-family: inherit !important;\n  font-size: inherit !important;\n  line-height: inherit !important;\n  color: inherit !important;\n  background: transparent !important;\n  border: none !important;\n  outline: none !important;\n  resize: none !important;\n  display: block !important;\n  width: 100% !important;\n  box-sizing: border-box !important;\n}\n\n/* Content area paragraphs and text elements */\n.lexical-content-area [contenteditable] p,\n.lexical-content-area [contenteditable] div,\n.lexical-content-area [contenteditable] span,\n.lexical-content-area [contenteditable] h1,\n.lexical-content-area [contenteditable] h2,\n.lexical-content-area [contenteditable] h3,\n.lexical-content-area [contenteditable] h4,\n.lexical-content-area [contenteditable] h5,\n.lexical-content-area [contenteditable] h6,\n.lexical-content-area [contenteditable] ul,\n.lexical-content-area [contenteditable] ol,\n.lexical-content-area [contenteditable] li {\n  white-space: normal !important;\n  word-wrap: break-word !important;\n  overflow-wrap: break-word !important;\n  text-align: left !important;\n  direction: ltr !important;\n  writing-mode: horizontal-tb !important;\n  unicode-bidi: normal !important;\n  display: block !important;\n}\n\n/* Ensure inline elements flow properly */\n.lexical-content-area [contenteditable] span,\n.lexical-content-area [contenteditable] strong,\n.lexical-content-area [contenteditable] em,\n.lexical-content-area [contenteditable] b,\n.lexical-content-area [contenteditable] i {\n  display: inline !important;\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;;AAYA;;;;;;;;;;AAYA;;;;;AAOA;;;;;;;AASA;;;;;;AAQA;;;;;;;;;;;;;;;;;;;;;AAsBA;;;;;;;;;;;AAuBA", "debugId": null}}]}