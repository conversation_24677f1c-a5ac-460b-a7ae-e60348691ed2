{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/dashboard/blogs/components/lexical-editor.css"], "sourcesContent": ["/* Lexical Editor Styles */\n.lexical-editor {\n  font-family: inherit;\n  font-size: 16px;\n  line-height: 1.6;\n  color: inherit;\n  direction: ltr !important;\n  text-align: left !important;\n  writing-mode: horizontal-tb !important;\n  unicode-bidi: normal !important;\n}\n\n/* Force horizontal text layout */\n.lexical-editor,\n.lexical-editor *,\n.lexical-editor [contenteditable],\n.lexical-editor [data-lexical-editor] {\n  direction: ltr !important;\n  text-align: left !important;\n  writing-mode: horizontal-tb !important;\n  unicode-bidi: normal !important;\n  white-space: normal !important;\n  word-wrap: break-word !important;\n  overflow-wrap: break-word !important;\n  display: block !important;\n}\n\n.lexical-editor [contenteditable] {\n  white-space: normal !important;\n  word-wrap: break-word !important;\n  overflow-wrap: break-word !important;\n  text-align: left !important;\n  direction: ltr !important;\n  writing-mode: horizontal-tb !important;\n  unicode-bidi: normal !important;\n  font-family: inherit !important;\n  font-size: inherit !important;\n  line-height: inherit !important;\n  color: inherit !important;\n  background: transparent !important;\n  border: none !important;\n  outline: none !important;\n  resize: none !important;\n  display: block !important;\n  width: 100% !important;\n  box-sizing: border-box !important;\n}\n\n.lexical-editor p {\n  margin: 0 0 1rem 0;\n  white-space: normal !important;\n  word-wrap: break-word !important;\n  overflow-wrap: break-word !important;\n  text-align: left !important;\n  direction: ltr !important;\n  writing-mode: horizontal-tb !important;\n  unicode-bidi: normal !important;\n}\n\n.lexical-editor div {\n  white-space: normal !important;\n  word-wrap: break-word !important;\n  overflow-wrap: break-word !important;\n  text-align: left !important;\n  direction: ltr !important;\n  writing-mode: horizontal-tb !important;\n  unicode-bidi: normal !important;\n}\n\n/* Ensure text flows horizontally */\n.lexical-editor * {\n  white-space: normal !important;\n  word-wrap: break-word !important;\n  overflow-wrap: break-word !important;\n  text-align: left !important;\n  direction: ltr !important;\n  writing-mode: horizontal-tb !important;\n  unicode-bidi: normal !important;\n}\n\n/* Override any potential conflicting styles */\n.lexical-editor [data-lexical-editor] {\n  white-space: normal !important;\n  word-wrap: break-word !important;\n  overflow-wrap: break-word !important;\n  text-align: left !important;\n  direction: ltr !important;\n  writing-mode: horizontal-tb !important;\n  unicode-bidi: normal !important;\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;;AAYA;;;;;;;;;;;AAcA;;;;;;;;;;;;;;;;;;;;;AAqBA;;;;;;;;;;;AAWA", "debugId": null}}]}