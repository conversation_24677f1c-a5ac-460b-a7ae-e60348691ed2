(()=>{var e={};e.id=5758,e.ids=[5758],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9108:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>u,serverHooks:()=>d,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var o={};t.r(o),t.d(o,{GET:()=>c});var s=t(96559),n=t(48088),i=t(37719),a=t(32190),l=t(36847);async function c(e,{params:r}){try{let{path:t}=await r,o=t.join("/");console.log("=== IMAGE PROXY REQUEST ==="),console.log("Requested path:",o),console.log("Full request URL:",e.url);try{let e=await l.Kd.getPresignedUrl(o,3600);return console.log("Generated presigned URL:",e),a.NextResponse.redirect(e)}catch(e){console.error("MinIO failed to generate presigned URL:",e);try{let e=await l.Kd.getFileStream(o),r=o.split(".").pop()?.toLowerCase(),t="application/octet-stream";switch(r){case"jpg":case"jpeg":t="image/jpeg";break;case"png":t="image/png";break;case"gif":t="image/gif";break;case"webp":t="image/webp";break;case"svg":t="image/svg+xml"}return new a.NextResponse(e,{headers:{"Content-Type":t,"Cache-Control":"public, max-age=31536000, immutable"}})}catch(e){return console.error("Failed to stream file from MinIO:",e),new a.NextResponse("Image not found",{status:404})}}}catch(e){return console.error("Error serving image:",e),new a.NextResponse("Internal server error",{status:500})}}let u=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/uploads/[...path]/route",pathname:"/api/uploads/[...path]",filename:"route",bundlePath:"app/api/uploads/[...path]/route"},resolvedPagePath:"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\api\\uploads\\[...path]\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:d}=u;function f(){return(0,i.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36847:(e,r,t)=>{"use strict";t.d(r,{Kd:()=>u});var o=t(67288),s=t(27910);let n={endPoint:process.env.MINIO_ENDPOINT?.replace(/^https?:\/\//,"")||"localhost",port:process.env.MINIO_PORT?Number.parseInt(process.env.MINIO_PORT):9e3,useSSL:"true"===process.env.MINIO_USE_SSL,accessKey:process.env.MINIO_ACCESS_KEY||"3uiq5emitjasdfghyjui",secretKey:process.env.MINIO_SECRET_KEY||"TYo1ruP1PqbOx3fONapGwawKQGqCDQsdfadsdfgh"},i=process.env.MINIO_BUCKET_NAME||"spinfree",a=new o.Kj(n),l=async()=>{try{if(console.log("Checking MinIO connection..."),console.log("MinIO config:",{endPoint:n.endPoint,port:n.port,useSSL:n.useSSL,bucket:i}),await a.bucketExists(i))console.log(`Bucket '${i}' already exists`);else{await a.makeBucket(i,"us-east-1"),console.log(`Bucket '${i}' created successfully`);let e={Version:"2012-10-17",Statement:[{Effect:"Allow",Principal:{AWS:["*"]},Action:["s3:GetObject"],Resource:[`arn:aws:s3:::${i}/*`]}]};await a.setBucketPolicy(i,JSON.stringify(e)),console.log(`Bucket policy set for '${i}'`)}}catch(e){console.error("Error initializing MinIO bucket:",e)}},c=e=>{if(137===e[0]&&80===e[1]&&78===e[2]&&71===e[3])return"image/png";if(255===e[0]&&216===e[1]&&255===e[2])return"image/jpeg";let r=e.toString("ascii",0,100).toLowerCase();return r.includes("<svg")||r.includes("<?xml")?"image/svg+xml":null};class u{static async uploadFile(e,r,t,o="posts"){try{console.log("Starting file upload to MinIO...");try{await a.bucketExists(i),console.log("MinIO connection verified")}catch(e){throw console.error("MinIO connection failed:",e),Error("MinIO service is unavailable. Please try again later.")}await l();let n=t;if("application/octet-stream"===t){let r=c(e);if(!r)throw Error("Invalid or unsupported image format");n=r}let u=new Date,p="image/svg+xml"===n?"svg":"image/png"===n?"png":"jpg",g=`${Date.now()}-${r.replace(/\.[^/.]+$/,"")}.${p}`,d=[o,u.getFullYear().toString(),(u.getMonth()+1).toString().padStart(2,"0"),g].join("/");console.log("Uploading to path:",d);let f=new s.Readable;f.push(e),f.push(null),await a.putObject(i,d,f,e.length,{"Content-Type":n});let m="https://freespin168.asia",h=`${m}/api/uploads/${d}`;return console.log("File uploaded to MinIO successfully!"),console.log(`- MinIO path: ${d}`),console.log(`- Proxy URL: ${h}`),console.log(`- Base URL: ${m}`),h}catch(a){console.error("Error uploading file to MinIO:",a),console.error("MinIO config:",{endPoint:n.endPoint,port:n.port,useSSL:n.useSSL,bucket:i}),console.error("Upload details:",{fileName:r,contentType:t,folder:o,bufferSize:e.length});let s=`/uploads/${o}/${Date.now()}-${r}`;return console.log("Upload failed, using fallback URL:",s),s}}static async deleteFile(e){try{let r=new URL(e),t=r.pathname.substring(r.pathname.indexOf("/",1)+1);await a.removeObject(i,t),console.log(`File deleted from MinIO: ${t}`)}catch(e){throw console.error("Error deleting file from MinIO:",e),Error("Failed to delete file")}}static async listFiles(e="posts",r=100){try{let t=[],o=a.listObjects(i,`${e}/`,!0);return new Promise((e,s)=>{o.on("data",e=>{if(t.length<r){let r=n.useSSL?"https":"http",o=n.port!==(n.useSSL?443:80)?`:${n.port}`:"",s=`${r}://${n.endPoint}${o}/${i}/${e.name}`;t.push({name:e.name,size:e.size,lastModified:e.lastModified,url:s,fileName:e.name?.split("/").pop()||"",folder:e.name?.split("/").slice(0,-1).join("/")||""})}}),o.on("end",()=>e(t)),o.on("error",e=>s(e))})}catch(e){throw console.error("Error listing files:",e),Error("Failed to list files")}}static async getPresignedUrl(e,r=3600){try{return await a.presignedGetObject(i,e,r)}catch(e){throw console.error("Error generating presigned URL:",e),Error("Failed to generate presigned URL")}}static async getFileStream(e){try{let r=await a.getObject(i,e),t=[];return new Promise((e,o)=>{r.on("data",e=>t.push(e)),r.on("end",()=>e(Buffer.concat(t))),r.on("error",o)})}catch(e){throw console.error("Error getting file stream from MinIO:",e),Error("Failed to get file from MinIO")}}}l().catch(e=>{console.error("Critical error during MinIO initialization:",e)})},41204:e=>{"use strict";e.exports=require("string_decoder")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45158:(e,r,t)=>{var o=t(79428),s=o.Buffer;function n(e,r){for(var t in e)r[t]=e[t]}function i(e,r,t){return s(e,r,t)}s.from&&s.alloc&&s.allocUnsafe&&s.allocUnsafeSlow?e.exports=o:(n(o,r),r.Buffer=i),i.prototype=Object.create(s.prototype),n(s,i),i.from=function(e,r,t){if("number"==typeof e)throw TypeError("Argument must not be a number");return s(e,r,t)},i.alloc=function(e,r,t){if("number"!=typeof e)throw TypeError("Argument must be a number");var o=s(e);return void 0!==r?"string"==typeof t?o.fill(r,t):o.fill(r):o.fill(0),o},i.allocUnsafe=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return s(e)},i.allocUnsafeSlow=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return o.SlowBuffer(e)}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66136:e=>{"use strict";e.exports=require("timers")},73024:e=>{"use strict";e.exports=require("node:fs")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[4447,580,7288],()=>t(9108));module.exports=o})();