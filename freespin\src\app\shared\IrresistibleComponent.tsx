"use client";
import MainHeader from "../(pages)/aboutus/components/MainHeader";
import BasicHeader from "../(pages)/home/<USER>/BasicHeader";
import { CircleCheck } from "lucide-react";
import Image from "next/image";
import React from "react";

interface IProps {
  topHeading?: string;
  src?: string;
  altImage?: string;
  textData?: ITextData[];
}

interface ITextData {
  titleText: string;
  text: string;
}

const IrresistibleComponent = ({
  topHeading = "5 เหผลต้านทานไม่ได้ในการเล่น SA Gaming",
  src = "/casino.png",
  altImage = "casino",
  textData,
}: IProps) => {
  const defaultTextData = [
    {
      titleText: "ลเลอร์สดในเวลา",
      text: "ความตื่นเต้นของการเล่นลเลอร์สดในสภาพแวดล้อมสม",
    },
    {
      titleText: "เกมที่สามารถเล่นได้ทุกที่",
      text: "เพลิดเพลินประสบการณ์การเล่นเกมที่ราบรืennesบนทุกที่ ไม่ว่าอยู่ไหน",
    },
    {
      titleText: "เกมคลาสกหลากหลาย",
      text: "จากเกมคลาสกมากมาย รวมถึงบาคาร่า ล็อก แบล็คแจ็ค และอื่นๆมากมาย",
    },
    {
      titleText: "หลายภาษา",
      text: "ใช้งานได้ในภาษาไทยและอังกฤษ ทำให้เล่นจากทั่วโลกสามารถเพลิดเพลินเล่นเกม",
    },
  ];

  const finalTextData = textData || defaultTextData;
  return (
    <section className="bg-black  ps-8 text-justify">
      <div className="container mx-auto px-3 md:px-[4rem]o">
        <MainHeader text={topHeading} className="text-white text-center pb-8" />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
          <div className="flex items-center justify-start">
            <Image
              src={src}
              alt={altImage || `alt imgs`}
              height={400}
              width={450}
            />
          </div>
          <div className="space-y-4">
            {finalTextData.map((item, index) => (
              <div key={index}>
                <div className="relative">
                  <div className="bg-[#3159C6] absolute top-0 -left-7 md:-left-8 rounded-full">
                    <CircleCheck />
                  </div>
                  <BasicHeader
                    text={item.titleText}
                    className="text-white text-start font-semibold"
                  />
                </div>
                <p className="text-[#A9A7B0] jakarta text-base">{item.text}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default IrresistibleComponent;
