(()=>{var e={};e.id=4532,e.ids=[4532],e.modules={3244:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var a=t(37413),n=t(60832),i=t(78963),s=t(53384);let o=["Take a visual journey through our SA Gaming collection with captivating game screenshots that highlight the premium live casino experience. From crystal-clear HD streaming to sleek interfaces and professional live dealers, SA Gaming delivers immersive gameplay at its finest.",`They say a picture is worth a thousand words—and our gallery speaks volumes. Explore our curated visuals showcasing elegant tables, real-time action, and high-quality graphics that bring each game to life.`,`Available in multiple languages including English, Thai, Chinese, and more—SA Gaming ensures players around the world enjoy a smooth, localized, and world-class gaming experience.`],l=({header:e,imgList:r,contents:t=o,className:l="lg:grid-cols-3 grid sm:grid-cols-2 grid-cols-1 gap-4"})=>(0,a.jsxs)("section",{className:"text-white px-2 lg:px-0 py-12",children:[(0,a.jsx)(n.A,{text:e,className:"text-center mb-10"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:l,children:r?.map((e,r)=>(0,a.jsx)(i.Zp,{className:"w-full h-full bg-[#FFFFFF48]",children:(0,a.jsx)(i.Wu,{children:(0,a.jsx)(s.default,{src:e,alt:"image",width:500,height:500})})},r))}),(0,a.jsx)("div",{className:"space-y-2 py-8 text-[#A9A7B0]",children:t?.map((e,r)=>(0,a.jsx)("p",{className:"text-base text-justify",children:e},r))})]})]})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9816:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p,metadata:()=>l});var a=t(37413),n=t(10201),i=t(93097),s=t(3244),o=t(7519);t(61120);let l={title:"Top Dream Gaming Online Casino in Thailand | Bonuses & Live Tables",alternates:{canonical:"https://www.freespin168.asia/dream-gaming"},description:"Unlock the best bonuses for Dream Gaming online casino in Thailand. Play baccarat, roulette, and more with high-quality live streaming at FreeSpin168.",keywords:["Dream gaming online casino in thailand"]},d={t1:"เล่นเกมคาสิโนออนไลน์ด้วย FreeSpin168",text1:`เตรียมพร้อมที่จะดื่มด่ำกับโลกแห่งความบันเทิงคาสิโนออนไลน์ที่น่าตื่นเต้นด้วย FreeSpin168ตั้งแต่ตัวแทนจำหน่ายสดไปจนถึงการเล่นเกมแบบโต้ตอบ FreeSpin168 นำเสนอแพลตฟอร์มพรีเมี่ยมสำหรับผู้เล่นทุกระดับประสบการณ์ไม่ว่าคุณจะเป็นนักเล่นเกมทั่วไปหรือผู้เล่นเดิมพันสูง FreeSpin168 มอบสภาพแวดล้อมที่น่าตื่นเต้นและปลอดภัยเพื่อเพลิดเพลินกับเกมโปรดของคุณ`,t2:`ทำไม DreamGaming จึงเป็นตัวเลือกที่ถูกต้องใน FreeSpin168`,text2:`Dreamgaming บน Freespin168 นำการผสมผสานที่ลงตัวของนวัตกรรมและความถูกต้องให้กับประสบการณ์คาสิโนออนไลน์ของคุณด้วยเกมตัวแทนจำหน่ายสดคุณภาพสูงเกมเพลย์ที่รวดเร็วและปลอดภัยและการสนับสนุนสำหรับหลายภาษารวมถึงไทยอังกฤษและจีนได้รับการออกแบบมาเพื่อส่งมอบความบันเทิงที่น่าตื่นเต้นและเข้าถึงได้ไม่ว่าคุณจะรัก Baccarat, Roulette หรือ Dragon Tiger, DreamGaming เสนอการเล่นที่ไร้รอยต่อโดยแพลตฟอร์มที่เชื่อถือได้ของ FreeSpin168 และโปรโมชั่นพิเศษ`},p=()=>(0,a.jsxs)("main",{className:"bg-black",children:[(0,a.jsx)(i.A,{text:"คาสิโนออนไลน์ Dreamgaming",url:"/about/banner.png"}),(0,a.jsxs)("div",{className:"container mx-auto pt-6 md:px-0 px-3",children:[(0,a.jsx)(n.A,{...d}),(0,a.jsx)(s.A,{header:"ภาพหน้าจอ SV388 พอร์ทัล",imgList:["/dream-gaming/sc1.png","/dream-gaming/sc2.png","/dream-gaming/sc3.png"]}),(0,a.jsx)(o.default,{topHeading:"5 เหตุผลที่ไม่อาจต้านทานได้ในการเล่นพอร์ทัล SV388"})]})]})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var a=t(75986),n=t(8974);function i(...e){return(0,n.QP)((0,a.$)(e))}},16549:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,46533,23)),Promise.resolve().then(t.bind(t,14808))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22629:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,49603,23)),Promise.resolve().then(t.bind(t,7519))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63217:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>s.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>m,tree:()=>d});var a=t(65239),n=t(48088),i=t(88170),s=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["(pages)",{children:["dream-gaming",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,9816)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\(pages)\\dream-gaming\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,35299)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\(pages)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\(pages)\\dream-gaming\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(pages)/dream-gaming/page",pathname:"/dream-gaming",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},78963:(e,r,t)=>{"use strict";t.d(r,{Wu:()=>s,Zp:()=>i});var a=t(37413);t(61120);var n=t(10974);function i({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function s({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...r})}},79551:e=>{"use strict";e.exports=require("url")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4447,6944,1658,9733,5036,6343,8974,8378,1426],()=>t(63217));module.exports=a})();