// import React from "react";
// import Image from "next/image";
// import BasicHeader from "./BasicHeader";
// ;

// const AboutUs = () => {

//   return (
//     <section className="bg-black text-white">
//       <div className="container px-7   py-6 grid grid-cols-1 md:grid-cols-2">
//         <div className="flex justify-center flex-col">
//           <BasicHeader text={t("header")} className="text-start" />
//           <h3 className="font-bold  md:text-4xl sm:text-3xl text-2xl">
//             {t("title")}
//           </h3>
//           <div className="my-6 text-[#A9A7B0] space-y-3 text-justify">
//             <p>{t("text1")}</p>
//             <p>{t("text2")}</p>
//             <p>{t("text3")}</p>
//           </div>
//         </div>
//         <div className="flex items-center justify-center">
//           <Image
//             src="/homepage/about.png"
//             alt="about"
//             width={500}
//             height={500}
//             className="border-transparent "
//           />
//         </div>
//       </div>
//     </section>
//   );
// };

// export default AboutUs;
