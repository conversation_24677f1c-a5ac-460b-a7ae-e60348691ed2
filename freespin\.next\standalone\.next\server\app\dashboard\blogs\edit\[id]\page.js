(()=>{var e={};e.id=390,e.ids=[390],e.modules={1097:(e,r,t)=>{Promise.resolve().then(t.bind(t,54866))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35945:(e,r,t)=>{Promise.resolve().then(t.bind(t,65536))},41204:e=>{"use strict";e.exports=require("string_decoder")},54866:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>N});var s=t(60687),a=t(43210),i=t(98301);t(60150);var o=t(6475);let n=(0,o.createServerReference)("70058763efb0e19a94f7f5968947fb2774a4f5df9b",o.callServer,void 0,o.findSourceMapURL,"updatePostAction");var l=t(29523),d=t(96882),c=t(41862),p=t(83002),m=t(27605),u=t(16189),g=t(93853),x=t(9275),h=t(28095),b=t(28932);x.z.object({title:x.z.string().min(1,"Title is required"),slug:x.z.string().optional(),content:x.z.string().min(1,"Content is required"),isBlog:x.z.boolean().default(!0),categories:x.z.array(x.z.string()).default([]),tags:x.z.array(x.z.string()).default([]),metaTitle:x.z.string().min(1,"Meta title is required"),metaDescription:x.z.string().min(1,"Meta description is required"),metaKeywords:x.z.string().min(1,"Meta keywords are required"),banner:x.z.object({image:x.z.string().min(1,"Banner image is required")})});let f={blogPost:[{name:"title",label:"Title",type:"text",placeholder:"Enter the title of the blog post",tooltip:!0,required:!0},{name:"slug",label:"Slug (Optional)",type:"text",placeholder:"Enter a unique slug (auto-generated if empty)",tooltip:!0,required:!1}],seo:[{name:"metaTitle",label:"Meta Title",type:"text",placeholder:"Enter meta title ",tooltip:!0,required:!0},{name:"metaDescription",label:"Meta Description",type:"textarea",placeholder:"Enter meta description ",rows:3,tooltip:!0,required:!0},{name:"metaKeywords",label:"Meta Keywords (comma-separated)",type:"text",placeholder:"Enter meta keywords",tooltip:!0,required:!0}]},j=({className:e})=>(0,s.jsx)(d.A,{className:`w-4 h-4 text-gray-400 ${e}`}),v=({label:e,error:r,children:t,required:a=!1,tooltip:i=!1})=>(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[e,a&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"}),i&&(0,s.jsx)(j,{className:"inline ml-1"})]}),t,r&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:r})]}),y=({config:e,register:r,error:t})=>(0,s.jsx)(v,{label:e.label,error:t,tooltip:e.tooltip,required:e.required,children:"textarea"===e.type?(0,s.jsx)("textarea",{...r(e.name),placeholder:e.placeholder,rows:e.rows||3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"}):(0,s.jsx)("input",{...r(e.name),type:e.type,placeholder:e.placeholder,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})});function N({params:e}){let{id:r}=(0,a.use)(e),t=(0,u.useRouter)(),[o,d]=(0,a.useState)([]),[x,j]=(0,a.useState)([]),[N,w]=(0,a.useState)([]),[q,S]=(0,a.useState)(!1),[P,z]=(0,a.useState)(null),[C,k]=(0,a.useState)(null),{control:R,register:_,handleSubmit:E,watch:A,setValue:B,reset:F,formState:{errors:I}}=(0,m.mN)({defaultValues:{title:"",slug:"",content:"",isBlog:!0,categories:[],tags:[],metaTitle:"",metaDescription:"",metaKeywords:"",banner:{image:""}}}),M=async e=>{if(!r)return void g.oR.error("No post ID provided");S(!0);try{console.log("Form data before cleaning:",e);let s=(0,i.j)(e),a=P?await (0,i.e)(P):e.banner.image,o=await n(r,s,a);console.log("Update API response:",o),o.success?(console.log("Post updated successfully:",o),g.oR.success(o?.message||"Post updated successfully"),setTimeout(()=>{t.push("/dashboard/blogs")},1500)):(console.error("API Error:",o),g.oR.error(o?.message||"Failed to update post"))}catch(e){console.error("Submission error:",e),g.oR.error("An unexpected error occurred")}finally{S(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,s.jsx)("div",{className:"container px-3 mx-auto",children:(0,s.jsxs)("form",{onSubmit:E(M),className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("span",{className:"text-teal-600",children:"Edit"}),(0,s.jsx)("span",{className:"text-gray-400",children:"•"}),(0,s.jsx)("span",{children:"Blog Post"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50",onClick:()=>t.push("/dashboard/blogs"),children:"Cancel"}),(0,s.jsxs)("button",{type:"submit",disabled:q,className:"bg-black text-white px-4 py-2 rounded-md hover:bg-gray-800 disabled:opacity-50 flex items-center gap-2",children:[q&&(0,s.jsx)(c.A,{className:"w-4 h-4 animate-spin"}),q?"Updating...":"Update Post"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Post Details"}),f.blogPost.map(e=>(0,s.jsx)(y,{config:e,register:_,error:I[e.name]?.message},e.name)),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)("div",{children:(0,s.jsx)(b.K,{label:"Categories",placeholder:"Search for categories...",options:N,selectedValues:o,onChange:e=>{d(e),B("categories",e)},allowCustom:!0})}),(0,s.jsx)("div",{children:(0,s.jsx)(b.K,{label:"Tags",placeholder:"Search for tags...",options:[],selectedValues:x,onChange:e=>{j(e),B("tags",e)},allowCustom:!0})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Blog Content"}),(0,s.jsx)(v,{label:"Content",error:I.content?.message,required:!0,children:(0,s.jsx)(m.xI,{name:"content",control:R,render:({field:e})=>(0,s.jsx)(h.A,{onChange:e.onChange,content:e.value,placeholder:"Start writing your blog post content..."})})})]})]})}),(0,s.jsx)("div",{className:"space-y-6"})]})]}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold mb-6",children:"SEO"}),(0,s.jsx)("div",{className:"space-y-4",children:f.seo.map(e=>(0,s.jsx)(y,{config:e,register:_,error:I[e.name]?.message},e.name))})]}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold mb-6",children:"Banner"}),(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("label",{htmlFor:"bannerImage",className:"block text-sm font-medium text-gray-700 jakarta",children:"Banner Image"}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)(l.$,{type:"button",variant:"default",className:"flex items-center gap-2 text-white transition-all duration-300 rounded-md shadow-md",onClick:()=>document.getElementById("bannerImage")?.click(),children:[(0,s.jsx)(p.A,{size:20}),(0,s.jsx)("span",{children:"Choose Image"})]}),(0,s.jsx)("input",{type:"file",id:"bannerImage",accept:"image/*",onChange:e=>{let r=e.target.files?.[0];if(r){if(console.log("Banner image selected:",{name:r.name,size:r.size,type:r.type}),!["image/jpeg","image/jpg","image/png","image/webp","image/gif","image/svg+xml"].includes(r.type))return void g.oR.error(`Invalid file type: ${r.type}. Only images are allowed.`);if(r.size>0xa00000)return void g.oR.error(`File too large: ${(r.size/1024/1024).toFixed(2)}MB. Maximum 10MB allowed.`);if(0===r.size)return void g.oR.error("File is empty");z(r),B("banner.image",URL.createObjectURL(r))}else z(null),B("banner.image",C?.banner.image||"")},className:"hidden"})]}),P?(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsxs)("p",{className:"text-sm text-gray-600 jakarta",children:["Selected: ",P.name," (",(P.size/1024).toFixed(2)," KB)"]}),(0,s.jsx)("img",{src:URL.createObjectURL(P)||"/placeholder.svg",alt:"Preview",className:"mt-2 max-w-[200px] max-h-[200px] rounded-md shadow-lg object-cover"})]}):C?.banner.image?(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 jakarta",children:"Current Image"}),(0,s.jsx)("img",{src:C.banner.image||"/placeholder.svg",alt:"Banner",className:"mt-2 max-w-[200px] max-h-[200px] rounded-md shadow-lg object-cover"})]}):null]})})]})]})})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65536:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Web Studio Nepal\\\\FreeSpin168\\\\freespin\\\\src\\\\app\\\\dashboard\\\\blogs\\\\edit\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\dashboard\\blogs\\edit\\[id]\\page.tsx","default")},66136:e=>{"use strict";e.exports=require("timers")},73024:e=>{"use strict";e.exports=require("node:fs")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80159:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),o=t.n(i),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let d={children:["",{children:["dashboard",{children:["blogs",{children:["edit",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,65536)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\dashboard\\blogs\\edit\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\dashboard\\blogs\\edit\\[id]\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/blogs/edit/[id]/page",pathname:"/dashboard/blogs/edit/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,6944,1658,9733,40,4247,5021,7042,6356,7116,5820,8932],()=>t(80159));module.exports=s})();