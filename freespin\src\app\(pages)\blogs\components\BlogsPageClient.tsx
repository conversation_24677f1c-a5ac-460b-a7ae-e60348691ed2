"use client";

import React, { useState } from "react";
import { NewsSection } from "./NewsSection";
import { BlogsSection } from "./BlogSection";
import { ExploreSection } from "./ExploreSection";
import { PublicBlogPost } from "@/client_apis/api/blog";

interface BlogsPageClientProps {
  initialLatestPosts: PublicBlogPost[];
  explorePosts: PublicBlogPost[];
  topNewsPosts: PublicBlogPost[];
}

export function BlogsPageClient({
  initialLatestPosts,
  explorePosts,
  topNewsPosts,
}: BlogsPageClientProps) {
  const [latestPosts, setLatestPosts] =
    useState<PublicBlogPost[]>(initialLatestPosts);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [currentLatestPage, setCurrentLatestPage] = useState(1);

  const loadMoreLatestPosts = async () => {
    if (isLoadingMore) return;

    setIsLoadingMore(true);
    try {
      const nextPage = currentLatestPage + 1;
      const response = await fetch(
        `/api/public/posts?page=${nextPage}&limit=6`
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data?.posts) {
          const newPosts = data.data.posts;
          setLatestPosts((prev) => [...prev, ...newPosts]);
          setCurrentLatestPage(nextPage);
        }
      }
    } catch (error) {
      console.error("Error loading more posts:", error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-3 md:px-[4rem] pt-24 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          <div className="lg:col-span-2 space-y-6">
            <NewsSection topNewsPosts={topNewsPosts} />
            <ExploreSection pagination posts={explorePosts} postsPerPage={4} />
          </div>
          <div className="lg:col-span-1">
            <BlogsSection
              posts={latestPosts}
              onLoadMore={loadMoreLatestPosts}
              isLoading={isLoadingMore}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
