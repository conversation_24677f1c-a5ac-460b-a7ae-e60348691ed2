"use server";

import { cookies } from "next/headers";
import { revalidateTag, revalidatePath } from "next/cache";
import connectDB from "@/lib/mongodb";
import Post from "@/models/Post";
import Category from "@/models/Category";
import { MinioService } from "@/lib/minio";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

export async function updatePostStatusAction(postId: string, status: string) {
  try {
    // Get cookies directly
    const cookieStore = await cookies();
    const authToken = cookieStore.get("auth-token")?.value;
    const userInfoCookie = cookieStore.get("user-info")?.value;

    let userInfo = null;

    // Try both auth methods
    if (authToken) {
      try {
        userInfo = jwt.verify(authToken, JWT_SECRET) as any;
      } catch (error) {
        console.log("Token verification failed:", error);
      }
    }

    // Fallback to user-info cookie
    if (!userInfo && userInfoCookie) {
      try {
        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));
        if (parsedUserInfo.role === "admin") {
          userInfo = parsedUserInfo;
        }
      } catch (error) {
        console.log("Failed to parse user-info cookie:", error);
      }
    }

    if (!userInfo) {
      return { success: false, error: "Authentication required" };
    }

    // Connect to database
    await connectDB();

    // Find the post first
    const post = await Post.findById(postId);

    if (!post) {
      return { success: false, error: "Post not found" };
    }

    // Update the status and save to trigger middleware
    post.status = status as "draft" | "published" | "archived";

    // Manually handle the published state logic
    if (status === "published") {
      post.isPublished = true;
      if (!post.publishedAt) {
        post.publishedAt = new Date();
      }
    } else {
      post.isPublished = false;
    }

    const updatedPost = await post.save();

    // Revalidate the public posts cache to show updated posts immediately
    revalidateTag("public-posts");

    // Also revalidate the blogs page to show updated posts
    revalidatePath("/en/blogs");
    revalidatePath("/th/blogs");

    // Serialize the post data for client components
    const serializedPost = {
      _id: updatedPost._id.toString(),
      title: updatedPost.title,
      status: updatedPost.status,
      isPublished: updatedPost.isPublished,
      publishedAt: updatedPost.publishedAt?.toISOString(),
      createdAt: updatedPost.createdAt?.toISOString(),
      updatedAt: updatedPost.updatedAt?.toISOString(),
    };

    return {
      success: true,
      data: { post: serializedPost },
    };
  } catch (error) {
    console.error("Error updating post status:", error);
    return {
      success: false,
      error: "Internal server error",
    };
  }
}

export async function getCategoriesAction() {
  try {
    // Get cookies directly
    const cookieStore = await cookies();

    console.log("=== CATEGORIES ACTION DEBUG ===");
    console.log("All cookies:", cookieStore.getAll());

    // Try both auth methods: httpOnly token and user-info cookie
    const authToken = cookieStore.get("auth-token")?.value;
    const userInfoCookie = cookieStore.get("user-info")?.value;

    console.log("Auth token:", authToken ? "Found" : "Not found");
    console.log("User info cookie:", userInfoCookie ? "Found" : "Not found");

    let userInfo = null;

    // Method 1: Try httpOnly token
    if (authToken) {
      try {
        const decoded = jwt.verify(authToken, JWT_SECRET) as any;
        console.log("Token verified successfully for user:", decoded.userId);
        userInfo = decoded;
      } catch (error) {
        console.log("Token verification failed:", error);
      }
    }

    // Method 2: Try user-info cookie (fallback)
    if (!userInfo && userInfoCookie) {
      try {
        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));
        console.log("User info from cookie:", parsedUserInfo);

        // Verify user is admin
        if (parsedUserInfo.role === "admin") {
          userInfo = parsedUserInfo;
          console.log("Admin access granted via user-info cookie");
        } else {
          return { success: false, error: "Admin access required" };
        }
      } catch (error) {
        console.log("Failed to parse user-info cookie:", error);
      }
    }

    if (!userInfo) {
      return { success: false, error: "No valid authentication found" };
    }

    // Connect to database
    await connectDB();

    // Get all categories
    const categories = await Category.find({ isActive: true })
      .sort({ name: 1 })
      .lean();

    // Convert MongoDB documents to plain objects for client components
    const serializedCategories = categories.map((category: any) => ({
      _id: category._id.toString(), // Convert ObjectId to string
      name: category.name,
      description: category.description || "",
      color: category.color || "#6366f1",
      isActive: category.isActive,
      createdAt: category.createdAt?.toISOString() || new Date().toISOString(),
      updatedAt: category.updatedAt?.toISOString() || new Date().toISOString(),
    }));

    return {
      success: true,
      data: { categories: serializedCategories },
    };
  } catch (error) {
    console.error("Error fetching categories:", error);
    return {
      success: false,
      error: "Internal server error",
    };
  }
}

export async function createCategoryAction(data: {
  name: string;
  description: string;
}) {
  try {
    // Get cookies directly
    const cookieStore = await cookies();
    const authToken = cookieStore.get("auth-token")?.value;
    const userInfoCookie = cookieStore.get("user-info")?.value;

    let userInfo = null;

    // Try both auth methods
    if (authToken) {
      try {
        userInfo = jwt.verify(authToken, JWT_SECRET) as any;
      } catch (error) {
        console.log("Token verification failed:", error);
      }
    }

    // Fallback to user-info cookie
    if (!userInfo && userInfoCookie) {
      try {
        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));
        if (parsedUserInfo.role === "admin") {
          userInfo = parsedUserInfo;
        }
      } catch (error) {
        console.log("Failed to parse user-info cookie:", error);
      }
    }

    if (!userInfo) {
      return { success: false, error: "Authentication required" };
    }

    // Connect to database
    await connectDB();

    // Check if category already exists (case-insensitive)
    const existingCategory = await Category.findOne({
      name: { $regex: new RegExp(`^${data.name}$`, "i") },
    });

    if (existingCategory) {
      return { success: false, error: "Category already exists" };
    }

    // Create new category
    const category = new Category(data);
    await category.save();

    // Serialize the category for client components
    const serializedCategory = {
      _id: category._id.toString(),
      name: category.name,
      description: category.description || "",
      color: category.color || "#6366f1",
      isActive: category.isActive,
      createdAt: category.createdAt?.toISOString() || new Date().toISOString(),
      updatedAt: category.updatedAt?.toISOString() || new Date().toISOString(),
    };

    return {
      success: true,
      message: "Category created successfully",
      data: { category: serializedCategory },
    };
  } catch (error) {
    console.error("Error creating category:", error);
    return {
      success: false,
      error: "Internal server error",
    };
  }
}

export async function createPostAction(
  postData: any,
  bannerImageBase64?: string
) {
  console.log("hello");
  try {
    // Get cookies directly
    const cookieStore = await cookies();
    const authToken = cookieStore.get("auth-token")?.value;
    const userInfoCookie = cookieStore.get("user-info")?.value;

    let userInfo = null;

    // Try both auth methods
    if (authToken) {
      try {
        userInfo = jwt.verify(authToken, JWT_SECRET) as any;
      } catch (error) {
        console.log("Token verification failed:", error);
      }
    }

    // Fallback to user-info cookie
    if (!userInfo && userInfoCookie) {
      try {
        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));
        if (parsedUserInfo.role === "admin") {
          userInfo = parsedUserInfo;
        }
      } catch (error) {
        console.log("Failed to parse user-info cookie:", error);
      }
    }

    if (!userInfo) {
      return { success: false, error: "Authentication required" };
    }

    // Connect to database
    await connectDB();

    // Handle banner image upload if provided
    let bannerUrl = "";
    if (bannerImageBase64) {
      try {
        // Extract content type and base64 data
        const matches = bannerImageBase64.match(/^data:(.+);base64,(.+)$/);
        if (!matches || matches.length !== 3) {
          throw new Error("Invalid base64 image format");
        }

        const contentType = matches[1]; // e.g., "image/jpeg", "image/png"
        const base64Data = matches[2];
        const imageBuffer = Buffer.from(base64Data, "base64");

        // Generate filename with correct extension
        const timestamp = Date.now();
        const fileExtension = contentType.split("/")[1] || "jpg";
        const filename = `banner-${timestamp}.${fileExtension}`;

        console.log("Uploading banner image:", {
          contentType,
          filename,
          bufferSize: imageBuffer.length,
        });

        // Upload to MinIO
        bannerUrl = await MinioService.uploadFile(
          imageBuffer,
          filename,
          contentType, // Use detected content type
          "banners" // folder
        );
        console.log("Banner uploaded successfully:", bannerUrl);
      } catch (error) {
        console.error("Error uploading banner:", error);
        // Continue without banner if upload fails
      }
    }

    // Create the post
    const postToCreate = {
      ...postData,
      banner: bannerUrl || postData.banner || "",
      author: userInfo.userId || userInfo._id,
      status: "draft", // Default to draft
      isBlog: true,
      isPublished: false,
    };

    const post = new Post(postToCreate);
    await post.save();

    // Serialize the post for client components
    const serializedPost = {
      _id: post._id.toString(),
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      content: post.content,
      banner: post.banner,
      status: post.status,
      isPublished: post.isPublished,
      publishedAt: post.publishedAt?.toISOString(),
      createdAt: post.createdAt?.toISOString(),
      updatedAt: post.updatedAt?.toISOString(),
    };

    return {
      success: true,
      message: "Post created successfully",
      data: { post: serializedPost },
    };
  } catch (error) {
    console.error("Error creating post:", error);
    return {
      success: false,
      error: "Internal server error",
    };
  }
}

export async function updatePostAction(
  postId: string,
  postData: any,
  bannerImageBase64?: string
) {
  try {
    // Get cookies directly
    const cookieStore = await cookies();
    const authToken = cookieStore.get("auth-token")?.value;
    const userInfoCookie = cookieStore.get("user-info")?.value;

    let userInfo = null;

    // Try both auth methods
    if (authToken) {
      try {
        userInfo = jwt.verify(authToken, JWT_SECRET) as any;
      } catch (error) {
        console.log("Token verification failed:", error);
      }
    }

    // Fallback to user-info cookie
    if (!userInfo && userInfoCookie) {
      try {
        const parsedUserInfo = JSON.parse(decodeURIComponent(userInfoCookie));
        if (parsedUserInfo.role === "admin") {
          userInfo = parsedUserInfo;
        }
      } catch (error) {
        console.log("Failed to parse user-info cookie:", error);
      }
    }

    if (!userInfo) {
      return { success: false, error: "Authentication required" };
    }

    // Connect to database
    await connectDB();

    // Find the post
    const post = await Post.findOne({ slug: postId });
    if (!post) {
      return { success: false, error: "Post not found" };
    }

    // Handle banner image upload if provided
    let bannerUrl =
      postData.banner?.image || post.banner?.image || post.banner || "";
    if (bannerImageBase64) {
      try {
        // Extract content type and base64 data
        const matches = bannerImageBase64.match(/^data:(.+);base64,(.+)$/);
        if (!matches || matches.length !== 3) {
          throw new Error("Invalid base64 image format");
        }

        const contentType = matches[1];
        const base64Data = matches[2];
        const imageBuffer = Buffer.from(base64Data, "base64");

        // Generate filename with correct extension
        const timestamp = Date.now();
        const fileExtension = contentType.split("/")[1] || "jpg";
        const filename = `banner-${timestamp}.${fileExtension}`;

        console.log("Uploading banner image:", {
          contentType,
          filename,
          bufferSize: imageBuffer.length,
        });

        // Upload to MinIO
        bannerUrl = await MinioService.uploadFile(
          imageBuffer,
          filename,
          contentType,
          "banners"
        );
        console.log("Banner uploaded successfully:", bannerUrl);
      } catch (error) {
        console.error("Error uploading banner:", error);
        // Continue without banner if upload fails
      }
    }

    // Clean the postData to avoid circular references
    const cleanPostData = {
      title: postData.title,
      slug: postData.slug, // Include slug field for updates
      content: postData.content,
      categories: postData.categories,
      tags: postData.tags,
      status: postData.status,
      isPublished: postData.isPublished,
      publishedAt: postData.publishedAt,
      metaTitle: postData.metaTitle,
      metaDescription: postData.metaDescription,
      metaKeywords: postData.metaKeywords,
    };

    const updateData = {
      ...cleanPostData,
      banner: bannerUrl, // Store as string, not object
      seoTitle: postData.metaTitle,
      seoDescription: postData.metaDescription,
      metaKeywords: postData.metaKeywords,
      author: userInfo.userId || userInfo._id,
    };

    // Update the post
    const updatedPost = (await Post.findOneAndUpdate(
      { slug: postId },
      updateData,
      {
        new: true,
        runValidators: true,
      }
    )
      .populate("categories", "name description color")
      .lean()) as any; // Add .lean() to get plain objects

    if (!updatedPost) {
      return { success: false, error: "Failed to update post" };
    }

    // Serialize the post for client components (ensure no circular references)
    const serializedPost = {
      _id: updatedPost._id.toString(),
      title: updatedPost.title,
      slug: updatedPost.slug,
      content: updatedPost.content,
      banner: updatedPost.banner,
      categories: Array.isArray(updatedPost.categories)
        ? updatedPost.categories.map((cat: any) => ({
            _id: cat._id?.toString(),
            name: cat.name,
            description: cat.description,
            color: cat.color,
          }))
        : [],
      tags: updatedPost.tags || [],
      seoTitle: updatedPost.seoTitle,
      seoDescription: updatedPost.seoDescription,
      metaKeywords: updatedPost.metaKeywords,
      status: updatedPost.status,
      isPublished: updatedPost.isPublished,
      publishedAt:
        updatedPost.publishedAt?.toISOString?.() || updatedPost.publishedAt,
      createdAt:
        updatedPost.createdAt?.toISOString?.() || updatedPost.createdAt,
      updatedAt:
        updatedPost.updatedAt?.toISOString?.() || updatedPost.updatedAt,
    };

    // Revalidate caches
    revalidateTag("public-posts");
    revalidatePath("/en/blogs");
    revalidatePath("/th/blogs");

    return {
      success: true,
      message: "Post updated successfully",
      data: { post: serializedPost },
    };
  } catch (error) {
    console.error("Error updating post:", error);
    return {
      success: false,
      error: "Internal server error",
    };
  }
}
