import { cookies } from "next/headers";

interface FetchOptions extends RequestInit {
  baseUrl?: string;
  nextOptions?: {
    tags?: string[];
    revalidate?: number | false;
  };
  includeCookies?: boolean;
  customCookies?: Record<string, string>;
}

interface ApiError {
  status: number;
  message: string;
  errors?: Record<string, string[]>;
}

// Enhanced base URL handling
const BASE_URL: string =
  process.env.NEXT_PUBLIC_BASE_URL ||
  (typeof window !== "undefined"
    ? window.location.origin
    : "http://localhost:3000");

export async function customFetch<T>(
  endpoint: string,
  options: FetchOptions = {}
): Promise<T | ApiError> {
  const {
    baseUrl = BASE_URL,
    nextOptions,
    headers,
    includeCookies = true,
    customCookies,
    ...rest
  } = options;

  const url = `${baseUrl}${endpoint}`;
  console.log(url);

  // Prepare headers
  const fetchHeaders: HeadersInit = {
    "Content-Type": "application/json",
    ...headers,
  };

  // Add cookies if we're in a server context and includeCookies is true
  if (includeCookies) {
    try {
      const cookieStore = await cookies();
      const sessionToken = cookieStore.get("session-token");

      if (sessionToken) {
        // Add Authorization header with Bearer token
        (fetchHeaders as Record<string, string>)[
          "Authorization"
        ] = `Bearer ${sessionToken.value}`;
      }

      // Add custom cookies if provided
      if (customCookies) {
        const cookieString = Object.entries(customCookies)
          .map(([key, value]) => `${key}=${value}`)
          .join("; ");

        (fetchHeaders as Record<string, string>).Cookie = cookieString;
      }

      // Or include all cookies as Cookie header (if your API expects it)
      const allCookies = cookieStore.getAll();
      if (allCookies.length > 0) {
        const cookieString = allCookies
          .map((cookie) => `${cookie.name}=${cookie.value}`)
          .join("; ");

        // Only set if not already set by customCookies
        if (!customCookies) {
          (fetchHeaders as Record<string, string>)["Cookie"] = cookieString;
        }
      }
    } catch (error) {
      // We're probably in a client context or cookies are not available
      console.warn("Cookies not available in this context:", error);
    }
  }

  const fetchOptions: RequestInit = {
    ...rest,
    headers: fetchHeaders,
    next: {
      tags: nextOptions?.tags || [],
      revalidate: nextOptions?.revalidate,
    },
  };

  try {
    const res = await fetch(url, fetchOptions);
    console.log("res", res);

    if (!res.ok) {
      // Extract error message from response
      let errorMessage = "An error occurred";
      let errorData: any = {};

      try {
        errorData = await res.json();
        errorMessage =
          errorData.message ||
          errorData.error ||
          `HTTP ${res.status}: ${res.statusText}`;
      } catch {
        // If JSON parsing fails, use status text
        errorMessage = `HTTP ${res.status}: ${res.statusText}`;
      }

      // Return structured error object
      return {
        status: res.status,
        message: errorMessage,
        errors: errorData.errors || undefined,
      } as ApiError;
    }

    return res.json() as Promise<T>;
  } catch (error) {
    // Handle network errors
    return {
      status: 0,
      message:
        error instanceof Error ? error.message : "Network error occurred",
    } as ApiError;
  }
}

// Helper function to check if response is an error
export function isApiError(response: any): response is ApiError {
  return (
    response &&
    typeof response.status === "number" &&
    typeof response.message === "string"
  );
}

// Helper function for authenticated requests
export async function authenticatedFetch<T>(
  endpoint: string,
  options: Omit<FetchOptions, "includeCookies"> = {}
): Promise<T | ApiError> {
  return customFetch<T>(endpoint, {
    ...options,
    includeCookies: true,
  });
}

// Helper function for public requests (no cookies)
export async function publicFetch<T>(
  endpoint: string,
  options: Omit<FetchOptions, "includeCookies"> = {}
): Promise<T | ApiError> {
  return customFetch<T>(endpoint, {
    ...options,
    includeCookies: false,
  });
}
