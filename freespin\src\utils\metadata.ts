import { Metadata } from "next";

interface MetadataConfig {
  title: string;
  description: string;
  keywords?: string[];
  path?: string;
  locale?: string;
  image?: string;
  type?: "website" | "article";
}

export function generateMetadata({
  title,
  description,
  keywords = [],
  path = "",
  locale = "th",
  image = "https://www.freespin168.asia/logo.png",
  type = "website",
}: MetadataConfig): Metadata {
  const baseUrl = "https://www.freespin168.asia";

  // Generate canonical URL (Thai only)
  const canonicalUrl = `${baseUrl}${path}`;

  return {
    title,
    description,
    keywords,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title,
      description,
      url: canonicalUrl,
      siteName: "FreeSpin168",
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: "th_TH",
      type,
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [image],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
    other: {
      language: "Thai",
    },
  };
}
