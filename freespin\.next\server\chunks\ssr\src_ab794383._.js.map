{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 161, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/home/<USER>/BasicHeader.tsx"], "sourcesContent": ["import clsx from \"clsx\";\r\nimport React from \"react\";\r\n\r\nconst BasicHeader: React.FC<{ className?: string; text: string }> = ({\r\n  className,\r\n  text,\r\n}) => {\r\n  return (\r\n    <h2\r\n      className={clsx(\"text-2xl font-bold text-center text-white\", className)}\r\n    >\r\n      {text}\r\n    </h2>\r\n  );\r\n};\r\n\r\nexport default BasicHeader;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,cAA8D,CAAC,EACnE,SAAS,EACT,IAAI,EACL;IACC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,6CAA6C;kBAE5D;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/utils/dateUtils.ts"], "sourcesContent": ["import dayjs from \"dayjs\";\r\n\r\nexport function formatTimeAgo(date: string | Date): string {\r\n  const now = dayjs();\r\n  const postDate = dayjs(date);\r\n  const diffInDays = now.diff(postDate, \"day\");\r\n  \r\n  if (diffInDays === 0) {\r\n    return \"Today\";\r\n  } else if (diffInDays === 1) {\r\n    return \"1 day ago\";\r\n  } else if (diffInDays < 7) {\r\n    return `${diffInDays} days ago`;\r\n  } else if (diffInDays < 30) {\r\n    const weeks = Math.floor(diffInDays / 7);\r\n    return weeks === 1 ? \"1 week ago\" : `${weeks} weeks ago`;\r\n  } else if (diffInDays < 365) {\r\n    const months = Math.floor(diffInDays / 30);\r\n    return months === 1 ? \"1 month ago\" : `${months} months ago`;\r\n  } else {\r\n    const years = Math.floor(diffInDays / 365);\r\n    return years === 1 ? \"1 year ago\" : `${years} years ago`;\r\n  }\r\n}\r\n\r\nexport function formatDate(date: string | Date): string {\r\n  return dayjs(date).format(\"DD/MM/YYYY\");\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,SAAS,cAAc,IAAmB;IAC/C,MAAM,MAAM,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD;IAChB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE;IACvB,MAAM,aAAa,IAAI,IAAI,CAAC,UAAU;IAEtC,IAAI,eAAe,GAAG;QACpB,OAAO;IACT,OAAO,IAAI,eAAe,GAAG;QAC3B,OAAO;IACT,OAAO,IAAI,aAAa,GAAG;QACzB,OAAO,GAAG,WAAW,SAAS,CAAC;IACjC,OAAO,IAAI,aAAa,IAAI;QAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;QACtC,OAAO,UAAU,IAAI,eAAe,GAAG,MAAM,UAAU,CAAC;IAC1D,OAAO,IAAI,aAAa,KAAK;QAC3B,MAAM,SAAS,KAAK,KAAK,CAAC,aAAa;QACvC,OAAO,WAAW,IAAI,gBAAgB,GAAG,OAAO,WAAW,CAAC;IAC9D,OAAO;QACL,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;QACtC,OAAO,UAAU,IAAI,eAAe,GAAG,MAAM,UAAU,CAAC;IAC1D;AACF;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC;AAC5B", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/blogs/components/NewsSection.tsx"], "sourcesContent": ["// import { Card, CardContent } from \"@/components/ui/card\";\r\n// import { <PERSON><PERSON> } from \"@/components/ui/button\";\r\n// import { Badge } from \"@/components/ui/badge\";\r\n// import Link from \"next/link\";\r\n// import BasicHeader from \"../../home/<USER>/BasicHeader\";\r\n// import { PublicBlogPost } from \"@/client_apis/api/blog\";\r\n// import { formatDate } from \"@/utils/dateUtils\";\r\n\r\n// interface NewsSectionProps {\r\n//   topNewsPosts?: PublicBlogPost[];\r\n// }\r\n\r\n// export function NewsSection({ topNewsPosts = [] }: NewsSectionProps) {\r\n//   // Get the first top news post as the featured post\r\n//   const featuredPost = topNewsPosts[0];\r\n\r\n//   if (!featuredPost) {\r\n//     return (\r\n//       <div>\r\n//         <BasicHeader text=\"Top News\" className=\"mb-6 text-start\" />\r\n//         <Card className=\"relative flex flex-col justify-center items-center border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0\">\r\n//           <div className=\"text-center text-gray-400\">\r\n//             <p>No top news available</p>\r\n//             <p className=\"text-sm mt-2\">\r\n//               Mark a blog post as &quot;Top News&quot; to display it here\r\n//             </p>\r\n//           </div>\r\n//         </Card>\r\n//       </div>\r\n//     );\r\n//   }\r\n\r\n//   console.log(`url('${featuredPost.banner || \"/blogs/blg1.png\"}')`);\r\n//   return (\r\n//     <div>\r\n//       <BasicHeader text=\"Top News\" className=\"mb-6 text-start\" />\r\n//       <Link href={`/blogs/${featuredPost._id}`}>\r\n//         <Card className=\"relative flex flex-col justify-end border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0 cursor-pointer hover:scale-[1.02] transition-transform duration-300\">\r\n//           <div\r\n//             className=\"absolute inset-0 z-0 bg-cover bg-center bg-no-repeat\"\r\n//             style={{\r\n//               backgroundImage: `url('${\r\n//                 featuredPost.banner || \"/blogs/blg1.png\"\r\n//               }')`,\r\n//             }}\r\n//           />\r\n//           <CardContent className=\"p-6 relative z-20 h-auto\">\r\n//             <div className=\"absolute inset-0 z-10 backdrop-blur-md bg-black/30\" />\r\n//             <div className=\"relative z-20\">\r\n//               <div className=\"flex items-center gap-2 mb-3\">\r\n//                 <Badge\r\n//                   variant=\"secondary\"\r\n//                   className=\"bg-orange-600 text-white hover:bg-orange-700\"\r\n//                 >\r\n//                   HOT\r\n//                 </Badge>\r\n//                 <span className=\"text-sm text-gray-400\">\r\n//                   {formatDate(\r\n//                     featuredPost.publishedAt || featuredPost.createdAt\r\n//                   )}\r\n//                 </span>\r\n//               </div>\r\n//               <h3 className=\"text-xl font-bold mb-4 text-white leading-tight line-clamp-3\">\r\n//                 {featuredPost.title}\r\n//               </h3>\r\n//               {featuredPost.description && (\r\n//                 <p className=\"text-gray-300 mb-6 leading-relaxed line-clamp-3\">\r\n//                   {featuredPost.description}\r\n//                 </p>\r\n//               )}\r\n//               <Button className=\"bg-[#07BC0C] cursor-pointer text-white hover:bg-[#06A50B] transition-colors\">\r\n//                 Read More\r\n//               </Button>\r\n//             </div>\r\n//           </CardContent>\r\n//         </Card>\r\n//       </Link>\r\n//     </div>\r\n//   );\r\n// }\r\n\r\n// Second\r\n// \"use client\";\r\n\r\n// import { useState } from \"react\";\r\n// import { motion, AnimatePresence } from \"framer-motion\";\r\n// import { Card, CardContent } from \"@/components/ui/card\";\r\n// import { Button } from \"@/components/ui/button\";\r\n// import { Badge } from \"@/components/ui/badge\";\r\n// import Link from \"next/link\";\r\n// import BasicHeader from \"../../home/<USER>/BasicHeader\";\r\n// import type { PublicBlogPost } from \"@/client_apis/api/blog\";\r\n// import { formatDate } from \"@/utils/dateUtils\";\r\n\r\n// interface NewsSectionProps {\r\n//   topNewsPosts?: PublicBlogPost[];\r\n// }\r\n\r\n// const ImageLoader = () => {\r\n//   return (\r\n//     <div className=\"absolute inset-0 z-30 flex items-center justify-center bg-gradient-to-br from-gray-900 via-gray-800 to-black\">\r\n//       {/* Animated background pattern */}\r\n//       <div className=\"absolute inset-0 opacity-10\">\r\n//         <motion.div\r\n//           className=\"absolute inset-0\"\r\n//           animate={{\r\n//             background: [\r\n//               \"radial-gradient(circle at 20% 50%, #3b82f6 0%, transparent 50%)\",\r\n//               \"radial-gradient(circle at 80% 50%, #8b5cf6 0%, transparent 50%)\",\r\n//               \"radial-gradient(circle at 40% 80%, #06b6d4 0%, transparent 50%)\",\r\n//               \"radial-gradient(circle at 20% 50%, #3b82f6 0%, transparent 50%)\",\r\n//             ],\r\n//           }}\r\n//           transition={{\r\n//             duration: 4,\r\n//             repeat: Number.POSITIVE_INFINITY,\r\n//             ease: \"easeInOut\",\r\n//           }}\r\n//         />\r\n//       </div>\r\n\r\n//       {/* Main loader content */}\r\n//       <div className=\"relative z-10 flex flex-col items-center\">\r\n//         {/* Spinning rings */}\r\n//         <div className=\"relative w-20 h-20 mb-6\">\r\n//           <motion.div\r\n//             className=\"absolute inset-0 border-4 border-transparent border-t-blue-500 border-r-purple-500 rounded-full\"\r\n//             animate={{ rotate: 360 }}\r\n//             transition={{\r\n//               duration: 1.5,\r\n//               repeat: Number.POSITIVE_INFINITY,\r\n//               ease: \"linear\",\r\n//             }}\r\n//           />\r\n//           <motion.div\r\n//             className=\"absolute inset-2 border-4 border-transparent border-b-cyan-500 border-l-green-500 rounded-full\"\r\n//             animate={{ rotate: -360 }}\r\n//             transition={{\r\n//               duration: 2,\r\n//               repeat: Number.POSITIVE_INFINITY,\r\n//               ease: \"linear\",\r\n//             }}\r\n//           />\r\n//           <motion.div\r\n//             className=\"absolute inset-4 border-4 border-transparent border-t-orange-500 border-r-pink-500 rounded-full\"\r\n//             animate={{ rotate: 360 }}\r\n//             transition={{\r\n//               duration: 1,\r\n//               repeat: Number.POSITIVE_INFINITY,\r\n//               ease: \"linear\",\r\n//             }}\r\n//           />\r\n//         </div>\r\n\r\n//         {/* Pulsing dots */}\r\n//         <div className=\"flex space-x-2 mb-4\">\r\n//           {[0, 1, 2].map((index) => (\r\n//             <motion.div\r\n//               key={index}\r\n//               className=\"w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full\"\r\n//               animate={{\r\n//                 scale: [1, 1.5, 1],\r\n//                 opacity: [0.5, 1, 0.5],\r\n//               }}\r\n//               transition={{\r\n//                 duration: 1.5,\r\n//                 repeat: Number.POSITIVE_INFINITY,\r\n//                 delay: index * 0.2,\r\n//                 ease: \"easeInOut\",\r\n//               }}\r\n//             />\r\n//           ))}\r\n//         </div>\r\n\r\n//         {/* Loading text */}\r\n//         <motion.div\r\n//           initial={{ opacity: 0, y: 10 }}\r\n//           animate={{ opacity: 1, y: 0 }}\r\n//           transition={{ delay: 0.5 }}\r\n//           className=\"text-center\"\r\n//         >\r\n//           <motion.p\r\n//             className=\"text-white font-medium text-lg mb-2\"\r\n//             animate={{ opacity: [0.5, 1, 0.5] }}\r\n//             transition={{\r\n//               duration: 2,\r\n//               repeat: Number.POSITIVE_INFINITY,\r\n//               ease: \"easeInOut\",\r\n//             }}\r\n//           >\r\n//             Loading News\r\n//           </motion.p>\r\n//           <p className=\"text-gray-400 text-sm\">Preparing your content...</p>\r\n//         </motion.div>\r\n\r\n//         {/* Animated progress bar */}\r\n//         <motion.div\r\n//           className=\"w-32 h-1 bg-gray-700 rounded-full mt-4 overflow-hidden\"\r\n//           initial={{ opacity: 0 }}\r\n//           animate={{ opacity: 1 }}\r\n//           transition={{ delay: 1 }}\r\n//         >\r\n//           <motion.div\r\n//             className=\"h-full bg-gradient-to-r from-blue-500 via-purple-500 to-cyan-500 rounded-full\"\r\n//             animate={{\r\n//               x: [\"-100%\", \"100%\"],\r\n//             }}\r\n//             transition={{\r\n//               duration: 2,\r\n//               repeat: Number.POSITIVE_INFINITY,\r\n//               ease: \"easeInOut\",\r\n//             }}\r\n//           />\r\n//         </motion.div>\r\n//       </div>\r\n\r\n//       {/* Floating particles */}\r\n//       {[...Array(6)].map((_, index) => (\r\n//         <motion.div\r\n//           key={index}\r\n//           className=\"absolute w-2 h-2 bg-white/20 rounded-full\"\r\n//           style={{\r\n//             left: `${Math.random() * 100}%`,\r\n//             top: `${Math.random() * 100}%`,\r\n//           }}\r\n//           animate={{\r\n//             y: [0, -20, 0],\r\n//             opacity: [0, 1, 0],\r\n//             scale: [0.5, 1, 0.5],\r\n//           }}\r\n//           transition={{\r\n//             duration: 3 + Math.random() * 2,\r\n//             repeat: Number.POSITIVE_INFINITY,\r\n//             delay: Math.random() * 2,\r\n//             ease: \"easeInOut\",\r\n//           }}\r\n//         />\r\n//       ))}\r\n//     </div>\r\n//   );\r\n// };\r\n\r\n// export function NewsSection({ topNewsPosts = [] }: NewsSectionProps) {\r\n//   const [imageLoaded, setImageLoaded] = useState(false);\r\n//   const [imageError, setImageError] = useState(false);\r\n\r\n//   // Get the first top news post as the featured post\r\n//   const featuredPost = topNewsPosts[0];\r\n\r\n//   if (!featuredPost) {\r\n//     return (\r\n//       <div>\r\n//         <BasicHeader text=\"Top News\" className=\"mb-6 text-start\" />\r\n//         <Card className=\"relative flex flex-col justify-center items-center border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0\">\r\n//           <div className=\"text-center text-gray-400\">\r\n//             <p>No top news available</p>\r\n//             <p className=\"text-sm mt-2\">\r\n//               Mark a blog post as \"Top News\" to display it here\r\n//             </p>\r\n//           </div>\r\n//         </Card>\r\n//       </div>\r\n//     );\r\n//   }\r\n\r\n//   const imageUrl = featuredPost.banner || \"/blogs/blg1.png\";\r\n\r\n//   return (\r\n//     <div>\r\n//       <BasicHeader text=\"Top News\" className=\"mb-6 text-start\" />\r\n//       <Link href={`/blogs/${featuredPost._id}`}>\r\n//         <Card className=\"relative flex flex-col justify-end border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0 cursor-pointer hover:scale-[1.02] transition-transform duration-300\">\r\n//           {/* Background Image */}\r\n//           <div className=\"absolute inset-0 z-0\">\r\n//             <img\r\n//               src={imageUrl || \"/placeholder.svg\"}\r\n//               alt={featuredPost.title}\r\n//               className={`w-full h-full object-cover transition-opacity duration-500 ${\r\n//                 imageLoaded ? \"opacity-100\" : \"opacity-0\"\r\n//               }`}\r\n//               onLoad={() => setImageLoaded(true)}\r\n//               onError={() => {\r\n//                 setImageError(true);\r\n//                 setImageLoaded(true);\r\n//               }}\r\n//             />\r\n\r\n//             {/* Fallback gradient background */}\r\n//             {imageError && (\r\n//               <div className=\"absolute inset-0 bg-gradient-to-br from-gray-800 via-gray-900 to-black\" />\r\n//             )}\r\n//           </div>\r\n\r\n//           {/* Image Loader */}\r\n//           <AnimatePresence>\r\n//             {!imageLoaded && (\r\n//               <motion.div\r\n//                 initial={{ opacity: 1 }}\r\n//                 exit={{ opacity: 0 }}\r\n//                 transition={{ duration: 0.5 }}\r\n//               >\r\n//                 <ImageLoader />\r\n//               </motion.div>\r\n//             )}\r\n//           </AnimatePresence>\r\n\r\n//           {/* Content Overlay */}\r\n//           <AnimatePresence>\r\n//             {imageLoaded && (\r\n//               <motion.div\r\n//                 initial={{ opacity: 0, y: 20 }}\r\n//                 animate={{ opacity: 1, y: 0 }}\r\n//                 transition={{ duration: 0.6, ease: \"easeOut\" }}\r\n//               >\r\n//                 <CardContent className=\"p-6 relative z-20 h-auto\">\r\n//                   <div className=\"absolute inset-0 z-10 backdrop-blur-md bg-black/30\" />\r\n//                   <div className=\"relative z-20\">\r\n//                     <motion.div\r\n//                       initial={{ opacity: 0, y: 10 }}\r\n//                       animate={{ opacity: 1, y: 0 }}\r\n//                       transition={{ duration: 0.4, delay: 0.2 }}\r\n//                       className=\"flex items-center gap-2 mb-3\"\r\n//                     >\r\n//                       <Badge\r\n//                         variant=\"secondary\"\r\n//                         className=\"bg-orange-600 text-white hover:bg-orange-700\"\r\n//                       >\r\n//                         HOT\r\n//                       </Badge>\r\n//                       <span className=\"text-sm text-gray-400\">\r\n//                         {formatDate(\r\n//                           featuredPost.publishedAt || featuredPost.createdAt\r\n//                         )}\r\n//                       </span>\r\n//                     </motion.div>\r\n\r\n//                     <motion.h3\r\n//                       initial={{ opacity: 0, y: 10 }}\r\n//                       animate={{ opacity: 1, y: 0 }}\r\n//                       transition={{ duration: 0.4, delay: 0.3 }}\r\n//                       className=\"text-xl font-bold mb-4 text-white leading-tight line-clamp-3\"\r\n//                     >\r\n//                       {featuredPost.title}\r\n//                     </motion.h3>\r\n\r\n//                     {featuredPost.description && (\r\n//                       <motion.p\r\n//                         initial={{ opacity: 0, y: 10 }}\r\n//                         animate={{ opacity: 1, y: 0 }}\r\n//                         transition={{ duration: 0.4, delay: 0.4 }}\r\n//                         className=\"text-gray-300 mb-6 leading-relaxed line-clamp-3\"\r\n//                       >\r\n//                         {featuredPost.description}\r\n//                       </motion.p>\r\n//                     )}\r\n\r\n//                     <motion.div\r\n//                       initial={{ opacity: 0, y: 10 }}\r\n//                       animate={{ opacity: 1, y: 0 }}\r\n//                       transition={{ duration: 0.4, delay: 0.5 }}\r\n//                     >\r\n//                       <Button className=\"bg-[#07BC0C] cursor-pointer text-white hover:bg-[#06A50B] transition-colors\">\r\n//                         Read More\r\n//                       </Button>\r\n//                     </motion.div>\r\n//                   </div>\r\n//                 </CardContent>\r\n//               </motion.div>\r\n//             )}\r\n//           </AnimatePresence>\r\n//         </Card>\r\n//       </Link>\r\n//     </div>\r\n//   );\r\n// }\r\n\r\n// Third\r\n\r\n// \"use client\";\r\n\r\n// import { useState, useEffect, useMemo } from \"react\";\r\n// import { motion, AnimatePresence } from \"framer-motion\";\r\n// import { Card, CardContent } from \"@/components/ui/card\";\r\n// import { Button } from \"@/components/ui/button\";\r\n// import { Badge } from \"@/components/ui/badge\";\r\n// import Link from \"next/link\";\r\n// import BasicHeader from \"../../home/<USER>/BasicHeader\";\r\n// import type { PublicBlogPost } from \"@/client_apis/api/blog\";\r\n// import { formatDate } from \"@/utils/dateUtils\";\r\n\r\n// interface NewsSectionProps {\r\n//   topNewsPosts?: PublicBlogPost[];\r\n// }\r\n\r\n// const ImageLoader = () => {\r\n//   const [isClient, setIsClient] = useState(false);\r\n\r\n//   // Generate consistent particle positions\r\n//   const particles = useMemo(() => {\r\n//     return Array.from({ length: 6 }, (_, index) => ({\r\n//       id: index,\r\n//       left: [20, 40, 60, 80, 30, 70][index] || 50, // Fixed positions instead of random\r\n//       top: [10, 30, 50, 70, 20, 80][index] || 50,\r\n//       delay: index * 0.3,\r\n//       duration: 3 + (index % 3),\r\n//     }));\r\n//   }, []);\r\n\r\n//   useEffect(() => {\r\n//     setIsClient(true);\r\n//   }, []);\r\n\r\n//   return (\r\n//     <div className=\"absolute inset-0 z-30 flex items-center justify-center bg-gradient-to-br from-gray-900 via-gray-800 to-black\">\r\n//       {/* Animated background pattern */}\r\n//       <div className=\"absolute inset-0 opacity-10\">\r\n//         <motion.div\r\n//           className=\"absolute inset-0\"\r\n//           animate={{\r\n//             background: [\r\n//               \"radial-gradient(circle at 20% 50%, #3b82f6 0%, transparent 50%)\",\r\n//               \"radial-gradient(circle at 80% 50%, #8b5cf6 0%, transparent 50%)\",\r\n//               \"radial-gradient(circle at 40% 80%, #06b6d4 0%, transparent 50%)\",\r\n//               \"radial-gradient(circle at 20% 50%, #3b82f6 0%, transparent 50%)\",\r\n//             ],\r\n//           }}\r\n//           transition={{\r\n//             duration: 4,\r\n//             repeat: Number.POSITIVE_INFINITY,\r\n//             ease: \"easeInOut\",\r\n//           }}\r\n//         />\r\n//       </div>\r\n\r\n//       {/* Main loader content */}\r\n//       <div className=\"relative z-10 flex flex-col items-center\">\r\n//         {/* Spinning rings */}\r\n//         <div className=\"relative w-20 h-20 mb-6\">\r\n//           <motion.div\r\n//             className=\"absolute inset-0 border-4 border-transparent border-t-blue-500 border-r-purple-500 rounded-full\"\r\n//             animate={{ rotate: 360 }}\r\n//             transition={{\r\n//               duration: 1.5,\r\n//               repeat: Number.POSITIVE_INFINITY,\r\n//               ease: \"linear\",\r\n//             }}\r\n//           />\r\n//           <motion.div\r\n//             className=\"absolute inset-2 border-4 border-transparent border-b-cyan-500 border-l-green-500 rounded-full\"\r\n//             animate={{ rotate: -360 }}\r\n//             transition={{\r\n//               duration: 2,\r\n//               repeat: Number.POSITIVE_INFINITY,\r\n//               ease: \"linear\",\r\n//             }}\r\n//           />\r\n//           <motion.div\r\n//             className=\"absolute inset-4 border-4 border-transparent border-t-orange-500 border-r-pink-500 rounded-full\"\r\n//             animate={{ rotate: 360 }}\r\n//             transition={{\r\n//               duration: 1,\r\n//               repeat: Number.POSITIVE_INFINITY,\r\n//               ease: \"linear\",\r\n//             }}\r\n//           />\r\n//         </div>\r\n\r\n//         {/* Pulsing dots */}\r\n//         <div className=\"flex space-x-2 mb-4\">\r\n//           {[0, 1, 2].map((index) => (\r\n//             <motion.div\r\n//               key={index}\r\n//               className=\"w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full\"\r\n//               animate={{\r\n//                 scale: [1, 1.5, 1],\r\n//                 opacity: [0.5, 1, 0.5],\r\n//               }}\r\n//               transition={{\r\n//                 duration: 1.5,\r\n//                 repeat: Number.POSITIVE_INFINITY,\r\n//                 delay: index * 0.2,\r\n//                 ease: \"easeInOut\",\r\n//               }}\r\n//             />\r\n//           ))}\r\n//         </div>\r\n\r\n//         {/* Loading text */}\r\n//         <motion.div\r\n//           initial={{ opacity: 0, y: 10 }}\r\n//           animate={{ opacity: 1, y: 0 }}\r\n//           transition={{ delay: 0.5 }}\r\n//           className=\"text-center\"\r\n//         >\r\n//           <motion.p\r\n//             className=\"text-white font-medium text-lg mb-2\"\r\n//             animate={{ opacity: [0.5, 1, 0.5] }}\r\n//             transition={{\r\n//               duration: 2,\r\n//               repeat: Number.POSITIVE_INFINITY,\r\n//               ease: \"easeInOut\",\r\n//             }}\r\n//           >\r\n//             Loading News\r\n//           </motion.p>\r\n//           <p className=\"text-gray-400 text-sm\">Preparing your content...</p>\r\n//         </motion.div>\r\n\r\n//         {/* Animated progress bar */}\r\n//         <motion.div\r\n//           className=\"w-32 h-1 bg-gray-700 rounded-full mt-4 overflow-hidden\"\r\n//           initial={{ opacity: 0 }}\r\n//           animate={{ opacity: 1 }}\r\n//           transition={{ delay: 1 }}\r\n//         >\r\n//           <motion.div\r\n//             className=\"h-full bg-gradient-to-r from-blue-500 via-purple-500 to-cyan-500 rounded-full\"\r\n//             animate={{\r\n//               x: [\"-100%\", \"100%\"],\r\n//             }}\r\n//             transition={{\r\n//               duration: 2,\r\n//               repeat: Number.POSITIVE_INFINITY,\r\n//               ease: \"easeInOut\",\r\n//             }}\r\n//           />\r\n//         </motion.div>\r\n//       </div>\r\n\r\n//       {/* Floating particles - only render on client */}\r\n//       {isClient &&\r\n//         particles.map((particle) => (\r\n//           <motion.div\r\n//             key={particle.id}\r\n//             className=\"absolute w-2 h-2 bg-white/20 rounded-full\"\r\n//             style={{\r\n//               left: `${particle.left}%`,\r\n//               top: `${particle.top}%`,\r\n//             }}\r\n//             animate={{\r\n//               y: [0, -20, 0],\r\n//               opacity: [0, 1, 0],\r\n//               scale: [0.5, 1, 0.5],\r\n//             }}\r\n//             transition={{\r\n//               duration: particle.duration,\r\n//               repeat: Number.POSITIVE_INFINITY,\r\n//               delay: particle.delay,\r\n//               ease: \"easeInOut\",\r\n//             }}\r\n//           />\r\n//         ))}\r\n//     </div>\r\n//   );\r\n// };\r\n\r\n// export function NewsSection({ topNewsPosts = [] }: NewsSectionProps) {\r\n//   const [imageLoaded, setImageLoaded] = useState(false);\r\n//   const [imageError, setImageError] = useState(false);\r\n\r\n//   // Get the first top news post as the featured post\r\n//   const featuredPost = topNewsPosts[0];\r\n\r\n//   if (!featuredPost) {\r\n//     return (\r\n//       <div>\r\n//         <BasicHeader text=\"Top News\" className=\"mb-6 text-start\" />\r\n//         <Card className=\"relative flex flex-col justify-center items-center border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0\">\r\n//           <div className=\"text-center text-gray-400\">\r\n//             <p>No top news available</p>\r\n//             <p className=\"text-sm mt-2\">\r\n//               Mark a blog post as \"Top News\" to display it here\r\n//             </p>\r\n//           </div>\r\n//         </Card>\r\n//       </div>\r\n//     );\r\n//   }\r\n\r\n//   const imageUrl = featuredPost.banner || \"/blogs/blg1.png\";\r\n\r\n//   return (\r\n//     <div>\r\n//       <BasicHeader text=\"Top News\" className=\"mb-6 text-start\" />\r\n//       <Link href={`/blogs/${featuredPost._id}`}>\r\n//         <Card className=\"relative flex flex-col justify-end border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0 cursor-pointer hover:scale-[1.02] transition-transform duration-300\">\r\n//           {/* Background Image */}\r\n//           <div className=\"absolute inset-0 z-0\">\r\n//             <img\r\n//               src={imageUrl || \"/placeholder.svg\"}\r\n//               alt={featuredPost.title}\r\n//               className={`w-full h-full object-cover transition-opacity duration-500 ${\r\n//                 imageLoaded ? \"opacity-100\" : \"opacity-0\"\r\n//               }`}\r\n//               onLoad={() => setImageLoaded(true)}\r\n//               onError={() => {\r\n//                 setImageError(true);\r\n//                 setImageLoaded(true);\r\n//               }}\r\n//             />\r\n\r\n//             {/* Fallback gradient background */}\r\n//             {imageError && (\r\n//               <div className=\"absolute inset-0 bg-gradient-to-br from-gray-800 via-gray-900 to-black\" />\r\n//             )}\r\n//           </div>\r\n\r\n//           {/* Image Loader */}\r\n//           <AnimatePresence>\r\n//             {!imageLoaded && (\r\n//               <motion.div\r\n//                 initial={{ opacity: 1 }}\r\n//                 exit={{ opacity: 0 }}\r\n//                 transition={{ duration: 0.5 }}\r\n//               >\r\n//                 <ImageLoader />\r\n//               </motion.div>\r\n//             )}\r\n//           </AnimatePresence>\r\n\r\n//           {/* Content Overlay */}\r\n//           <AnimatePresence>\r\n//             {imageLoaded && (\r\n//               <motion.div\r\n//                 initial={{ opacity: 0, y: 20 }}\r\n//                 animate={{ opacity: 1, y: 0 }}\r\n//                 transition={{ duration: 0.6, ease: \"easeOut\" }}\r\n//               >\r\n//                 <CardContent className=\"p-6 relative z-20 h-auto\">\r\n//                   <div className=\"absolute inset-0 z-10 backdrop-blur-md bg-black/30\" />\r\n//                   <div className=\"relative z-20\">\r\n//                     <motion.div\r\n//                       initial={{ opacity: 0, y: 10 }}\r\n//                       animate={{ opacity: 1, y: 0 }}\r\n//                       transition={{ duration: 0.4, delay: 0.2 }}\r\n//                       className=\"flex items-center gap-2 mb-3\"\r\n//                     >\r\n//                       <Badge\r\n//                         variant=\"secondary\"\r\n//                         className=\"bg-orange-600 text-white hover:bg-orange-700\"\r\n//                       >\r\n//                         HOT\r\n//                       </Badge>\r\n//                       <span className=\"text-sm text-gray-400\">\r\n//                         {formatDate(\r\n//                           featuredPost.publishedAt || featuredPost.createdAt\r\n//                         )}\r\n//                       </span>\r\n//                     </motion.div>\r\n\r\n//                     <motion.h3\r\n//                       initial={{ opacity: 0, y: 10 }}\r\n//                       animate={{ opacity: 1, y: 0 }}\r\n//                       transition={{ duration: 0.4, delay: 0.3 }}\r\n//                       className=\"text-xl font-bold mb-4 text-white leading-tight line-clamp-3\"\r\n//                     >\r\n//                       {featuredPost.title}\r\n//                     </motion.h3>\r\n\r\n//                     {featuredPost.description && (\r\n//                       <motion.p\r\n//                         initial={{ opacity: 0, y: 10 }}\r\n//                         animate={{ opacity: 1, y: 0 }}\r\n//                         transition={{ duration: 0.4, delay: 0.4 }}\r\n//                         className=\"text-gray-300 mb-6 leading-relaxed line-clamp-3\"\r\n//                       >\r\n//                         {featuredPost.description}\r\n//                       </motion.p>\r\n//                     )}\r\n\r\n//                     <motion.div\r\n//                       initial={{ opacity: 0, y: 10 }}\r\n//                       animate={{ opacity: 1, y: 0 }}\r\n//                       transition={{ duration: 0.4, delay: 0.5 }}\r\n//                     >\r\n//                       <Button className=\"bg-[#07BC0C] cursor-pointer text-white hover:bg-[#06A50B] transition-colors\">\r\n//                         Read More\r\n//                       </Button>\r\n//                     </motion.div>\r\n//                   </div>\r\n//                 </CardContent>\r\n//               </motion.div>\r\n//             )}\r\n//           </AnimatePresence>\r\n//         </Card>\r\n//       </Link>\r\n//     </div>\r\n//   );\r\n// }\r\n\r\n// Fourth\r\n\r\n\"use client\";\r\n\r\nimport { useState, useEffect, useMemo } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport Link from \"next/link\";\r\nimport BasicHeader from \"../../home/<USER>/BasicHeader\";\r\nimport type { PublicBlogPost } from \"@/client_apis/api/blog\";\r\nimport { formatDate } from \"@/utils/dateUtils\";\r\n\r\ninterface NewsSectionProps {\r\n  topNewsPosts?: PublicBlogPost[];\r\n}\r\n\r\nconst ImageLoader = () => {\r\n  const [isClient, setIsClient] = useState(false);\r\n\r\n  // Generate consistent particle positions\r\n  const particles = useMemo(() => {\r\n    return Array.from({ length: 6 }, (_, index) => ({\r\n      id: index,\r\n      left: [20, 40, 60, 80, 30, 70][index] || 50, // Fixed positions instead of random\r\n      top: [10, 30, 50, 70, 20, 80][index] || 50,\r\n      delay: index * 0.3,\r\n      duration: 3 + (index % 3),\r\n    }));\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"absolute inset-0 z-30 flex items-center justify-center bg-gradient-to-br from-gray-900 via-gray-800 to-black\">\r\n      {/* Animated background pattern */}\r\n      <div className=\"absolute inset-0 opacity-10\">\r\n        <motion.div\r\n          className=\"absolute inset-0\"\r\n          animate={{\r\n            background: [\r\n              \"radial-gradient(circle at 20% 50%, #3b82f6 0%, transparent 50%)\",\r\n              \"radial-gradient(circle at 80% 50%, #8b5cf6 0%, transparent 50%)\",\r\n              \"radial-gradient(circle at 40% 80%, #06b6d4 0%, transparent 50%)\",\r\n              \"radial-gradient(circle at 20% 50%, #3b82f6 0%, transparent 50%)\",\r\n            ],\r\n          }}\r\n          transition={{\r\n            duration: 4,\r\n            repeat: Number.POSITIVE_INFINITY,\r\n            ease: \"easeInOut\",\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      {/* Main loader content */}\r\n      <div className=\"relative z-10 flex flex-col items-center\">\r\n        {/* Spinning rings */}\r\n        <div className=\"relative w-20 h-20 mb-6\">\r\n          <motion.div\r\n            className=\"absolute inset-0 border-4 border-transparent border-t-blue-500 border-r-purple-500 rounded-full\"\r\n            animate={{ rotate: 360 }}\r\n            transition={{\r\n              duration: 1.5,\r\n              repeat: Number.POSITIVE_INFINITY,\r\n              ease: \"linear\",\r\n            }}\r\n          />\r\n          <motion.div\r\n            className=\"absolute inset-2 border-4 border-transparent border-b-cyan-500 border-l-green-500 rounded-full\"\r\n            animate={{ rotate: -360 }}\r\n            transition={{\r\n              duration: 2,\r\n              repeat: Number.POSITIVE_INFINITY,\r\n              ease: \"linear\",\r\n            }}\r\n          />\r\n          <motion.div\r\n            className=\"absolute inset-4 border-4 border-transparent border-t-orange-500 border-r-pink-500 rounded-full\"\r\n            animate={{ rotate: 360 }}\r\n            transition={{\r\n              duration: 1,\r\n              repeat: Number.POSITIVE_INFINITY,\r\n              ease: \"linear\",\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        {/* Pulsing dots */}\r\n        <div className=\"flex space-x-2 mb-4\">\r\n          {[0, 1, 2].map((index) => (\r\n            <motion.div\r\n              key={index}\r\n              className=\"w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full\"\r\n              animate={{\r\n                scale: [1, 1.5, 1],\r\n                opacity: [0.5, 1, 0.5],\r\n              }}\r\n              transition={{\r\n                duration: 1.5,\r\n                repeat: Number.POSITIVE_INFINITY,\r\n                delay: index * 0.2,\r\n                ease: \"easeInOut\",\r\n              }}\r\n            />\r\n          ))}\r\n        </div>\r\n\r\n        {/* Loading text */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.5 }}\r\n          className=\"text-center\"\r\n        >\r\n          <motion.p\r\n            className=\"text-white font-medium text-lg mb-2\"\r\n            animate={{ opacity: [0.5, 1, 0.5] }}\r\n            transition={{\r\n              duration: 2,\r\n              repeat: Number.POSITIVE_INFINITY,\r\n              ease: \"easeInOut\",\r\n            }}\r\n          >\r\n            Loading News\r\n          </motion.p>\r\n          <p className=\"text-gray-400 text-sm\">Preparing your content...</p>\r\n        </motion.div>\r\n\r\n        {/* Animated progress bar */}\r\n        <motion.div\r\n          className=\"w-32 h-1 bg-gray-700 rounded-full mt-4 overflow-hidden\"\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ delay: 1 }}\r\n        >\r\n          <motion.div\r\n            className=\"h-full bg-gradient-to-r from-blue-500 via-purple-500 to-cyan-500 rounded-full\"\r\n            animate={{\r\n              x: [\"-100%\", \"100%\"],\r\n            }}\r\n            transition={{\r\n              duration: 2,\r\n              repeat: Number.POSITIVE_INFINITY,\r\n              ease: \"easeInOut\",\r\n            }}\r\n          />\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* Floating particles - only render on client */}\r\n      {isClient &&\r\n        particles.map((particle) => (\r\n          <motion.div\r\n            key={particle.id}\r\n            className=\"absolute w-2 h-2 bg-white/20 rounded-full\"\r\n            style={{\r\n              left: `${particle.left}%`,\r\n              top: `${particle.top}%`,\r\n            }}\r\n            animate={{\r\n              y: [0, -20, 0],\r\n              opacity: [0, 1, 0],\r\n              scale: [0.5, 1, 0.5],\r\n            }}\r\n            transition={{\r\n              duration: particle.duration,\r\n              repeat: Number.POSITIVE_INFINITY,\r\n              delay: particle.delay,\r\n              ease: \"easeInOut\",\r\n            }}\r\n          />\r\n        ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport function NewsSection({ topNewsPosts = [] }: NewsSectionProps) {\r\n  const [imageLoaded, setImageLoaded] = useState(false);\r\n  const [imageError, setImageError] = useState(false);\r\n\r\n  // Get the first top news post as the featured post\r\n  const featuredPost = topNewsPosts[0];\r\n\r\n  if (!featuredPost) {\r\n    return (\r\n      <div>\r\n        <BasicHeader text=\"Top News\" className=\"mb-6 text-start\" />\r\n        <Card className=\"relative flex flex-col justify-center items-center border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0\">\r\n          <div className=\"text-center text-gray-400\">\r\n            <p>No top news available</p>\r\n            <p className=\"text-sm mt-2\">\r\n              Mark a blog post as &ldquo;Top News&rdquo; to display it here\r\n            </p>\r\n          </div>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const imageUrl = featuredPost.banner || \"/blogs/blg1.png\";\r\n\r\n  return (\r\n    <div>\r\n      {/* <BasicHeader text=\"Top News\" className=\"mb-6 text-start\" /> */}\r\n\r\n      <h1 className=\" text-lg md:text-xl lg:text-2xl mb-6 text-start\">\r\n        Explore News & Blogs from Freespin168\r\n      </h1>\r\n      <Link href={`/blogs/${featuredPost.slug}`}>\r\n        <Card className=\"relative flex flex-col justify-end border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0 cursor-pointer hover:scale-[1.02] transition-transform duration-300\">\r\n          {/* Background Image */}\r\n          <div className=\"absolute inset-0 z-0\">\r\n            <img\r\n              src={imageUrl || \"/placeholder.svg\"}\r\n              alt={featuredPost.title}\r\n              className={`w-full h-full object-cover transition-opacity duration-500 ${\r\n                imageLoaded ? \"opacity-100\" : \"opacity-0\"\r\n              }`}\r\n              onLoad={() => setImageLoaded(true)}\r\n              onError={() => {\r\n                setImageError(true);\r\n                setImageLoaded(true);\r\n              }}\r\n            />\r\n\r\n            {/* Fallback gradient background */}\r\n            {imageError && (\r\n              <div className=\"absolute inset-0 bg-gradient-to-br from-gray-800 via-gray-900 to-black\" />\r\n            )}\r\n          </div>\r\n\r\n          {/* Image Loader */}\r\n          <AnimatePresence>\r\n            {!imageLoaded && (\r\n              <motion.div\r\n                initial={{ opacity: 1 }}\r\n                exit={{ opacity: 0 }}\r\n                transition={{ duration: 0.5 }}\r\n              >\r\n                <ImageLoader />\r\n              </motion.div>\r\n            )}\r\n          </AnimatePresence>\r\n\r\n          {/* Content Overlay */}\r\n          <AnimatePresence>\r\n            {imageLoaded && (\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, ease: \"easeOut\" }}\r\n              >\r\n                <CardContent className=\"p-6 relative z-20 h-auto\">\r\n                  <div className=\"absolute inset-0 z-10 backdrop-blur-md bg-black/30\" />\r\n                  <div className=\"relative z-20\">\r\n                    <motion.div\r\n                      initial={{ opacity: 0, y: 10 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ duration: 0.4, delay: 0.2 }}\r\n                      className=\"flex items-center gap-2 mb-3\"\r\n                    >\r\n                      <Badge\r\n                        variant=\"secondary\"\r\n                        className=\"bg-orange-600 text-white hover:bg-orange-700\"\r\n                      >\r\n                        HOT\r\n                      </Badge>\r\n                      <span className=\"text-sm text-gray-400\">\r\n                        {formatDate(\r\n                          featuredPost.publishedAt || featuredPost.createdAt\r\n                        )}\r\n                      </span>\r\n                    </motion.div>\r\n\r\n                    <motion.h3\r\n                      initial={{ opacity: 0, y: 10 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ duration: 0.4, delay: 0.3 }}\r\n                      className=\"text-xl font-bold mb-4 text-white leading-tight line-clamp-3\"\r\n                    >\r\n                      {featuredPost.title}\r\n                    </motion.h3>\r\n\r\n                    {/* {featuredPost.description && (\r\n                      <motion.p\r\n                        initial={{ opacity: 0, y: 10 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        transition={{ duration: 0.4, delay: 0.4 }}\r\n                        className=\"text-gray-300 mb-6 leading-relaxed line-clamp-3\"\r\n                      >\r\n                        {featuredPost.description}\r\n                      </motion.p>\r\n                    )} */}\r\n\r\n                    <motion.div\r\n                      initial={{ opacity: 0, y: 10 }}\r\n                      animate={{ opacity: 1, y: 0 }}\r\n                      transition={{ duration: 0.4, delay: 0.5 }}\r\n                    >\r\n                      <Button className=\"bg-[#07BC0C] cursor-pointer text-white hover:bg-[#06A50B] transition-colors\">\r\n                        Read More\r\n                      </Button>\r\n                    </motion.div>\r\n                  </div>\r\n                </CardContent>\r\n              </motion.div>\r\n            )}\r\n          </AnimatePresence>\r\n        </Card>\r\n      </Link>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,mDAAmD;AACnD,iDAAiD;AACjD,gCAAgC;AAChC,+DAA+D;AAC/D,2DAA2D;AAC3D,kDAAkD;AAElD,+BAA+B;AAC/B,qCAAqC;AACrC,IAAI;AAEJ,yEAAyE;AACzE,wDAAwD;AACxD,0CAA0C;AAE1C,yBAAyB;AACzB,eAAe;AACf,cAAc;AACd,sEAAsE;AACtE,iJAAiJ;AACjJ,wDAAwD;AACxD,2CAA2C;AAC3C,2CAA2C;AAC3C,4EAA4E;AAC5E,mBAAmB;AACnB,mBAAmB;AACnB,kBAAkB;AAClB,eAAe;AACf,SAAS;AACT,MAAM;AAEN,uEAAuE;AACvE,aAAa;AACb,YAAY;AACZ,oEAAoE;AACpE,mDAAmD;AACnD,qMAAqM;AACrM,iBAAiB;AACjB,+EAA+E;AAC/E,uBAAuB;AACvB,0CAA0C;AAC1C,2DAA2D;AAC3D,sBAAsB;AACtB,iBAAiB;AACjB,eAAe;AACf,+DAA+D;AAC/D,qFAAqF;AACrF,8CAA8C;AAC9C,+DAA+D;AAC/D,yBAAyB;AACzB,wCAAwC;AACxC,6EAA6E;AAC7E,oBAAoB;AACpB,wBAAwB;AACxB,2BAA2B;AAC3B,2DAA2D;AAC3D,iCAAiC;AACjC,yEAAyE;AACzE,uBAAuB;AACvB,0BAA0B;AAC1B,uBAAuB;AACvB,8FAA8F;AAC9F,uCAAuC;AACvC,sBAAsB;AACtB,+CAA+C;AAC/C,kFAAkF;AAClF,+CAA+C;AAC/C,uBAAuB;AACvB,mBAAmB;AACnB,iHAAiH;AACjH,4BAA4B;AAC5B,0BAA0B;AAC1B,qBAAqB;AACrB,2BAA2B;AAC3B,kBAAkB;AAClB,gBAAgB;AAChB,aAAa;AACb,OAAO;AACP,IAAI;AAEJ,SAAS;AACT,gBAAgB;AAEhB,oCAAoC;AACpC,2DAA2D;AAC3D,4DAA4D;AAC5D,mDAAmD;AACnD,iDAAiD;AACjD,gCAAgC;AAChC,+DAA+D;AAC/D,gEAAgE;AAChE,kDAAkD;AAElD,+BAA+B;AAC/B,qCAAqC;AACrC,IAAI;AAEJ,8BAA8B;AAC9B,aAAa;AACb,qIAAqI;AACrI,4CAA4C;AAC5C,sDAAsD;AACtD,sBAAsB;AACtB,yCAAyC;AACzC,uBAAuB;AACvB,4BAA4B;AAC5B,mFAAmF;AACnF,mFAAmF;AACnF,mFAAmF;AACnF,mFAAmF;AACnF,iBAAiB;AACjB,eAAe;AACf,0BAA0B;AAC1B,2BAA2B;AAC3B,gDAAgD;AAChD,iCAAiC;AACjC,eAAe;AACf,aAAa;AACb,eAAe;AAEf,oCAAoC;AACpC,mEAAmE;AACnE,iCAAiC;AACjC,oDAAoD;AACpD,wBAAwB;AACxB,0HAA0H;AAC1H,wCAAwC;AACxC,4BAA4B;AAC5B,+BAA+B;AAC/B,kDAAkD;AAClD,gCAAgC;AAChC,iBAAiB;AACjB,eAAe;AACf,wBAAwB;AACxB,yHAAyH;AACzH,yCAAyC;AACzC,4BAA4B;AAC5B,6BAA6B;AAC7B,kDAAkD;AAClD,gCAAgC;AAChC,iBAAiB;AACjB,eAAe;AACf,wBAAwB;AACxB,0HAA0H;AAC1H,wCAAwC;AACxC,4BAA4B;AAC5B,6BAA6B;AAC7B,kDAAkD;AAClD,gCAAgC;AAChC,iBAAiB;AACjB,eAAe;AACf,iBAAiB;AAEjB,+BAA+B;AAC/B,gDAAgD;AAChD,wCAAwC;AACxC,0BAA0B;AAC1B,4BAA4B;AAC5B,8FAA8F;AAC9F,2BAA2B;AAC3B,sCAAsC;AACtC,0CAA0C;AAC1C,mBAAmB;AACnB,8BAA8B;AAC9B,iCAAiC;AACjC,oDAAoD;AACpD,sCAAsC;AACtC,qCAAqC;AACrC,mBAAmB;AACnB,iBAAiB;AACjB,gBAAgB;AAChB,iBAAiB;AAEjB,+BAA+B;AAC/B,sBAAsB;AACtB,4CAA4C;AAC5C,2CAA2C;AAC3C,wCAAwC;AACxC,oCAAoC;AACpC,YAAY;AACZ,sBAAsB;AACtB,8DAA8D;AAC9D,mDAAmD;AACnD,4BAA4B;AAC5B,6BAA6B;AAC7B,kDAAkD;AAClD,mCAAmC;AACnC,iBAAiB;AACjB,cAAc;AACd,2BAA2B;AAC3B,wBAAwB;AACxB,+EAA+E;AAC/E,wBAAwB;AAExB,wCAAwC;AACxC,sBAAsB;AACtB,+EAA+E;AAC/E,qCAAqC;AACrC,qCAAqC;AACrC,sCAAsC;AACtC,YAAY;AACZ,wBAAwB;AACxB,wGAAwG;AACxG,yBAAyB;AACzB,sCAAsC;AACtC,iBAAiB;AACjB,4BAA4B;AAC5B,6BAA6B;AAC7B,kDAAkD;AAClD,mCAAmC;AACnC,iBAAiB;AACjB,eAAe;AACf,wBAAwB;AACxB,eAAe;AAEf,mCAAmC;AACnC,2CAA2C;AAC3C,sBAAsB;AACtB,wBAAwB;AACxB,kEAAkE;AAClE,qBAAqB;AACrB,+CAA+C;AAC/C,8CAA8C;AAC9C,eAAe;AACf,uBAAuB;AACvB,8BAA8B;AAC9B,kCAAkC;AAClC,oCAAoC;AACpC,eAAe;AACf,0BAA0B;AAC1B,+CAA+C;AAC/C,gDAAgD;AAChD,wCAAwC;AACxC,iCAAiC;AACjC,eAAe;AACf,aAAa;AACb,YAAY;AACZ,aAAa;AACb,OAAO;AACP,KAAK;AAEL,yEAAyE;AACzE,2DAA2D;AAC3D,yDAAyD;AAEzD,wDAAwD;AACxD,0CAA0C;AAE1C,yBAAyB;AACzB,eAAe;AACf,cAAc;AACd,sEAAsE;AACtE,iJAAiJ;AACjJ,wDAAwD;AACxD,2CAA2C;AAC3C,2CAA2C;AAC3C,kEAAkE;AAClE,mBAAmB;AACnB,mBAAmB;AACnB,kBAAkB;AAClB,eAAe;AACf,SAAS;AACT,MAAM;AAEN,+DAA+D;AAE/D,aAAa;AACb,YAAY;AACZ,oEAAoE;AACpE,mDAAmD;AACnD,qMAAqM;AACrM,qCAAqC;AACrC,mDAAmD;AACnD,mBAAmB;AACnB,qDAAqD;AACrD,yCAAyC;AACzC,0FAA0F;AAC1F,4DAA4D;AAC5D,oBAAoB;AACpB,oDAAoD;AACpD,iCAAiC;AACjC,uCAAuC;AACvC,wCAAwC;AACxC,mBAAmB;AACnB,iBAAiB;AAEjB,mDAAmD;AACnD,+BAA+B;AAC/B,2GAA2G;AAC3G,iBAAiB;AACjB,mBAAmB;AAEnB,iCAAiC;AACjC,8BAA8B;AAC9B,iCAAiC;AACjC,4BAA4B;AAC5B,2CAA2C;AAC3C,wCAAwC;AACxC,iDAAiD;AACjD,kBAAkB;AAClB,kCAAkC;AAClC,8BAA8B;AAC9B,iBAAiB;AACjB,+BAA+B;AAE/B,oCAAoC;AACpC,8BAA8B;AAC9B,gCAAgC;AAChC,4BAA4B;AAC5B,kDAAkD;AAClD,iDAAiD;AACjD,kEAAkE;AAClE,kBAAkB;AAClB,qEAAqE;AACrE,2FAA2F;AAC3F,oDAAoD;AACpD,kCAAkC;AAClC,wDAAwD;AACxD,uDAAuD;AACvD,mEAAmE;AACnE,iEAAiE;AACjE,wBAAwB;AACxB,+BAA+B;AAC/B,8CAA8C;AAC9C,mFAAmF;AACnF,0BAA0B;AAC1B,8BAA8B;AAC9B,iCAAiC;AACjC,iEAAiE;AACjE,uCAAuC;AACvC,+EAA+E;AAC/E,6BAA6B;AAC7B,gCAAgC;AAChC,oCAAoC;AAEpC,iCAAiC;AACjC,wDAAwD;AACxD,uDAAuD;AACvD,mEAAmE;AACnE,iGAAiG;AACjG,wBAAwB;AACxB,6CAA6C;AAC7C,mCAAmC;AAEnC,qDAAqD;AACrD,kCAAkC;AAClC,0DAA0D;AAC1D,yDAAyD;AACzD,qEAAqE;AACrE,sFAAsF;AACtF,0BAA0B;AAC1B,qDAAqD;AACrD,oCAAoC;AACpC,yBAAyB;AAEzB,kCAAkC;AAClC,wDAAwD;AACxD,uDAAuD;AACvD,mEAAmE;AACnE,wBAAwB;AACxB,yHAAyH;AACzH,oCAAoC;AACpC,kCAAkC;AAClC,oCAAoC;AACpC,2BAA2B;AAC3B,iCAAiC;AACjC,8BAA8B;AAC9B,iBAAiB;AACjB,+BAA+B;AAC/B,kBAAkB;AAClB,gBAAgB;AAChB,aAAa;AACb,OAAO;AACP,IAAI;AAEJ,QAAQ;AAER,gBAAgB;AAEhB,wDAAwD;AACxD,2DAA2D;AAC3D,4DAA4D;AAC5D,mDAAmD;AACnD,iDAAiD;AACjD,gCAAgC;AAChC,+DAA+D;AAC/D,gEAAgE;AAChE,kDAAkD;AAElD,+BAA+B;AAC/B,qCAAqC;AACrC,IAAI;AAEJ,8BAA8B;AAC9B,qDAAqD;AAErD,8CAA8C;AAC9C,sCAAsC;AACtC,wDAAwD;AACxD,mBAAmB;AACnB,0FAA0F;AAC1F,oDAAoD;AACpD,4BAA4B;AAC5B,mCAAmC;AACnC,WAAW;AACX,YAAY;AAEZ,sBAAsB;AACtB,yBAAyB;AACzB,YAAY;AAEZ,aAAa;AACb,qIAAqI;AACrI,4CAA4C;AAC5C,sDAAsD;AACtD,sBAAsB;AACtB,yCAAyC;AACzC,uBAAuB;AACvB,4BAA4B;AAC5B,mFAAmF;AACnF,mFAAmF;AACnF,mFAAmF;AACnF,mFAAmF;AACnF,iBAAiB;AACjB,eAAe;AACf,0BAA0B;AAC1B,2BAA2B;AAC3B,gDAAgD;AAChD,iCAAiC;AACjC,eAAe;AACf,aAAa;AACb,eAAe;AAEf,oCAAoC;AACpC,mEAAmE;AACnE,iCAAiC;AACjC,oDAAoD;AACpD,wBAAwB;AACxB,0HAA0H;AAC1H,wCAAwC;AACxC,4BAA4B;AAC5B,+BAA+B;AAC/B,kDAAkD;AAClD,gCAAgC;AAChC,iBAAiB;AACjB,eAAe;AACf,wBAAwB;AACxB,yHAAyH;AACzH,yCAAyC;AACzC,4BAA4B;AAC5B,6BAA6B;AAC7B,kDAAkD;AAClD,gCAAgC;AAChC,iBAAiB;AACjB,eAAe;AACf,wBAAwB;AACxB,0HAA0H;AAC1H,wCAAwC;AACxC,4BAA4B;AAC5B,6BAA6B;AAC7B,kDAAkD;AAClD,gCAAgC;AAChC,iBAAiB;AACjB,eAAe;AACf,iBAAiB;AAEjB,+BAA+B;AAC/B,gDAAgD;AAChD,wCAAwC;AACxC,0BAA0B;AAC1B,4BAA4B;AAC5B,8FAA8F;AAC9F,2BAA2B;AAC3B,sCAAsC;AACtC,0CAA0C;AAC1C,mBAAmB;AACnB,8BAA8B;AAC9B,iCAAiC;AACjC,oDAAoD;AACpD,sCAAsC;AACtC,qCAAqC;AACrC,mBAAmB;AACnB,iBAAiB;AACjB,gBAAgB;AAChB,iBAAiB;AAEjB,+BAA+B;AAC/B,sBAAsB;AACtB,4CAA4C;AAC5C,2CAA2C;AAC3C,wCAAwC;AACxC,oCAAoC;AACpC,YAAY;AACZ,sBAAsB;AACtB,8DAA8D;AAC9D,mDAAmD;AACnD,4BAA4B;AAC5B,6BAA6B;AAC7B,kDAAkD;AAClD,mCAAmC;AACnC,iBAAiB;AACjB,cAAc;AACd,2BAA2B;AAC3B,wBAAwB;AACxB,+EAA+E;AAC/E,wBAAwB;AAExB,wCAAwC;AACxC,sBAAsB;AACtB,+EAA+E;AAC/E,qCAAqC;AACrC,qCAAqC;AACrC,sCAAsC;AACtC,YAAY;AACZ,wBAAwB;AACxB,wGAAwG;AACxG,yBAAyB;AACzB,sCAAsC;AACtC,iBAAiB;AACjB,4BAA4B;AAC5B,6BAA6B;AAC7B,kDAAkD;AAClD,mCAAmC;AACnC,iBAAiB;AACjB,eAAe;AACf,wBAAwB;AACxB,eAAe;AAEf,2DAA2D;AAC3D,qBAAqB;AACrB,wCAAwC;AACxC,wBAAwB;AACxB,gCAAgC;AAChC,oEAAoE;AACpE,uBAAuB;AACvB,2CAA2C;AAC3C,yCAAyC;AACzC,iBAAiB;AACjB,yBAAyB;AACzB,gCAAgC;AAChC,oCAAoC;AACpC,sCAAsC;AACtC,iBAAiB;AACjB,4BAA4B;AAC5B,6CAA6C;AAC7C,kDAAkD;AAClD,uCAAuC;AACvC,mCAAmC;AACnC,iBAAiB;AACjB,eAAe;AACf,cAAc;AACd,aAAa;AACb,OAAO;AACP,KAAK;AAEL,yEAAyE;AACzE,2DAA2D;AAC3D,yDAAyD;AAEzD,wDAAwD;AACxD,0CAA0C;AAE1C,yBAAyB;AACzB,eAAe;AACf,cAAc;AACd,sEAAsE;AACtE,iJAAiJ;AACjJ,wDAAwD;AACxD,2CAA2C;AAC3C,2CAA2C;AAC3C,kEAAkE;AAClE,mBAAmB;AACnB,mBAAmB;AACnB,kBAAkB;AAClB,eAAe;AACf,SAAS;AACT,MAAM;AAEN,+DAA+D;AAE/D,aAAa;AACb,YAAY;AACZ,oEAAoE;AACpE,mDAAmD;AACnD,qMAAqM;AACrM,qCAAqC;AACrC,mDAAmD;AACnD,mBAAmB;AACnB,qDAAqD;AACrD,yCAAyC;AACzC,0FAA0F;AAC1F,4DAA4D;AAC5D,oBAAoB;AACpB,oDAAoD;AACpD,iCAAiC;AACjC,uCAAuC;AACvC,wCAAwC;AACxC,mBAAmB;AACnB,iBAAiB;AAEjB,mDAAmD;AACnD,+BAA+B;AAC/B,2GAA2G;AAC3G,iBAAiB;AACjB,mBAAmB;AAEnB,iCAAiC;AACjC,8BAA8B;AAC9B,iCAAiC;AACjC,4BAA4B;AAC5B,2CAA2C;AAC3C,wCAAwC;AACxC,iDAAiD;AACjD,kBAAkB;AAClB,kCAAkC;AAClC,8BAA8B;AAC9B,iBAAiB;AACjB,+BAA+B;AAE/B,oCAAoC;AACpC,8BAA8B;AAC9B,gCAAgC;AAChC,4BAA4B;AAC5B,kDAAkD;AAClD,iDAAiD;AACjD,kEAAkE;AAClE,kBAAkB;AAClB,qEAAqE;AACrE,2FAA2F;AAC3F,oDAAoD;AACpD,kCAAkC;AAClC,wDAAwD;AACxD,uDAAuD;AACvD,mEAAmE;AACnE,iEAAiE;AACjE,wBAAwB;AACxB,+BAA+B;AAC/B,8CAA8C;AAC9C,mFAAmF;AACnF,0BAA0B;AAC1B,8BAA8B;AAC9B,iCAAiC;AACjC,iEAAiE;AACjE,uCAAuC;AACvC,+EAA+E;AAC/E,6BAA6B;AAC7B,gCAAgC;AAChC,oCAAoC;AAEpC,iCAAiC;AACjC,wDAAwD;AACxD,uDAAuD;AACvD,mEAAmE;AACnE,iGAAiG;AACjG,wBAAwB;AACxB,6CAA6C;AAC7C,mCAAmC;AAEnC,qDAAqD;AACrD,kCAAkC;AAClC,0DAA0D;AAC1D,yDAAyD;AACzD,qEAAqE;AACrE,sFAAsF;AACtF,0BAA0B;AAC1B,qDAAqD;AACrD,oCAAoC;AACpC,yBAAyB;AAEzB,kCAAkC;AAClC,wDAAwD;AACxD,uDAAuD;AACvD,mEAAmE;AACnE,wBAAwB;AACxB,yHAAyH;AACzH,oCAAoC;AACpC,kCAAkC;AAClC,oCAAoC;AACpC,2BAA2B;AAC3B,iCAAiC;AACjC,8BAA8B;AAC9B,iBAAiB;AACjB,+BAA+B;AAC/B,kBAAkB;AAClB,gBAAgB;AAChB,aAAa;AACb,OAAO;AACP,IAAI;AAEJ,SAAS;;;;;AAIT;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AAVA;;;;;;;;;;AAgBA,MAAM,cAAc;IAClB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,yCAAyC;IACzC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,QAAU,CAAC;gBAC9C,IAAI;gBACJ,MAAM;oBAAC;oBAAI;oBAAI;oBAAI;oBAAI;oBAAI;iBAAG,CAAC,MAAM,IAAI;gBACzC,KAAK;oBAAC;oBAAI;oBAAI;oBAAI;oBAAI;oBAAI;iBAAG,CAAC,MAAM,IAAI;gBACxC,OAAO,QAAQ;gBACf,UAAU,IAAK,QAAQ;YACzB,CAAC;IACH,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBACP,YAAY;4BACV;4BACA;4BACA;4BACA;yBACD;oBACH;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ,OAAO,iBAAiB;wBAChC,MAAM;oBACR;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,QAAQ;gCAAI;gCACvB,YAAY;oCACV,UAAU;oCACV,QAAQ,OAAO,iBAAiB;oCAChC,MAAM;gCACR;;;;;;0CAEF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,QAAQ,CAAC;gCAAI;gCACxB,YAAY;oCACV,UAAU;oCACV,QAAQ,OAAO,iBAAiB;oCAChC,MAAM;gCACR;;;;;;0CAEF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,QAAQ;gCAAI;gCACvB,YAAY;oCACV,UAAU;oCACV,QAAQ,OAAO,iBAAiB;oCAChC,MAAM;gCACR;;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,SAAS;oCACP,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;oCAClB,SAAS;wCAAC;wCAAK;wCAAG;qCAAI;gCACxB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ,OAAO,iBAAiB;oCAChC,OAAO,QAAQ;oCACf,MAAM;gCACR;+BAXK;;;;;;;;;;kCAiBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,WAAU;gCACV,SAAS;oCAAE,SAAS;wCAAC;wCAAK;wCAAG;qCAAI;gCAAC;gCAClC,YAAY;oCACV,UAAU;oCACV,QAAQ,OAAO,iBAAiB;oCAChC,MAAM;gCACR;0CACD;;;;;;0CAGD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAE;kCAEvB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCACP,GAAG;oCAAC;oCAAS;iCAAO;4BACtB;4BACA,YAAY;gCACV,UAAU;gCACV,QAAQ,OAAO,iBAAiB;gCAChC,MAAM;4BACR;;;;;;;;;;;;;;;;;YAML,YACC,UAAU,GAAG,CAAC,CAAC,yBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,OAAO;wBACL,MAAM,GAAG,SAAS,IAAI,CAAC,CAAC,CAAC;wBACzB,KAAK,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC;oBACzB;oBACA,SAAS;wBACP,GAAG;4BAAC;4BAAG,CAAC;4BAAI;yBAAE;wBACd,SAAS;4BAAC;4BAAG;4BAAG;yBAAE;wBAClB,OAAO;4BAAC;4BAAK;4BAAG;yBAAI;oBACtB;oBACA,YAAY;wBACV,UAAU,SAAS,QAAQ;wBAC3B,QAAQ,OAAO,iBAAiB;wBAChC,OAAO,SAAS,KAAK;wBACrB,MAAM;oBACR;mBAhBK,SAAS,EAAE;;;;;;;;;;;AAqB5B;AAEO,SAAS,YAAY,EAAE,eAAe,EAAE,EAAoB;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,mDAAmD;IACnD,MAAM,eAAe,YAAY,CAAC,EAAE;IAEpC,IAAI,CAAC,cAAc;QACjB,qBACE,8OAAC;;8BACC,8OAAC,6JAAA,CAAA,UAAW;oBAAC,MAAK;oBAAW,WAAU;;;;;;8BACvC,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAE;;;;;;0CACH,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;;;;;;;;;;;;IAOtC;IAEA,MAAM,WAAW,aAAa,MAAM,IAAI;IAExC,qBACE,8OAAC;;0BAGC,8OAAC;gBAAG,WAAU;0BAAkD;;;;;;0BAGhE,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAM,CAAC,OAAO,EAAE,aAAa,IAAI,EAAE;0BACvC,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCAEd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,KAAK,YAAY;oCACjB,KAAK,aAAa,KAAK;oCACvB,WAAW,CAAC,2DAA2D,EACrE,cAAc,gBAAgB,aAC9B;oCACF,QAAQ,IAAM,eAAe;oCAC7B,SAAS;wCACP,cAAc;wCACd,eAAe;oCACjB;;;;;;gCAID,4BACC,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAKnB,8OAAC,yLAAA,CAAA,kBAAe;sCACb,CAAC,6BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;gCAAE;gCACtB,MAAM;oCAAE,SAAS;gCAAE;gCACnB,YAAY;oCAAE,UAAU;gCAAI;0CAE5B,cAAA,8OAAC;;;;;;;;;;;;;;;sCAMP,8OAAC,yLAAA,CAAA,kBAAe;sCACb,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,MAAM;gCAAU;0CAE7C,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,UAAU;wDAAK,OAAO;oDAAI;oDACxC,WAAU;;sEAEV,8OAAC,iIAAA,CAAA,QAAK;4DACJ,SAAQ;4DACR,WAAU;sEACX;;;;;;sEAGD,8OAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EACR,aAAa,WAAW,IAAI,aAAa,SAAS;;;;;;;;;;;;8DAKxD,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oDACR,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,UAAU;wDAAK,OAAO;oDAAI;oDACxC,WAAU;8DAET,aAAa,KAAK;;;;;;8DAcrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,UAAU;wDAAK,OAAO;oDAAI;8DAExC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;kEAA8E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAatH", "debugId": null}}, {"offset": {"line": 1498, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/blogs/components/BlogSection.tsx"], "sourcesContent": ["import { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport Image from \"next/image\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { PublicBlogPost } from \"@/client_apis/api/blog\";\r\nimport { formatTimeAgo } from \"@/utils/dateUtils\";\r\nimport Link from \"next/link\";\r\n\r\ninterface BlogsSectionProps {\r\n  posts: PublicBlogPost[];\r\n  onLoadMore?: () => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nexport function BlogsSection({\r\n  posts,\r\n  onLoadMore,\r\n  isLoading,\r\n}: BlogsSectionProps) {\r\n  console.log(\"posts\", posts);\r\n  return (\r\n    <div>\r\n      <h2 className=\"text-2xl font-bold mb-6 text-white\">Latest Blogs</h2>\r\n      <div className=\"flex flex-col  gap-4 w-full\">\r\n        {posts.length > 0 ? (\r\n          posts.map((post) => (\r\n            <Link key={post._id} href={`/blogs/${post.slug}`}>\r\n              <Card className=\"bg-gray-800 border-gray-700 hover:bg-gray-750 p-0 transition-colors cursor-pointer\">\r\n                <CardContent className=\"flex p-0\">\r\n                  <div className=\"relative size-24 flex-shrink-0\">\r\n                    <Image\r\n                      src={post.banner || \"/blogs/latest1.png\"}\r\n                      alt={post.title}\r\n                      fill\r\n                      className=\"object-cover rounded-l-lg\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"flex-1 flex flex-col\">\r\n                    <div className=\"p-4 pb-2\">\r\n                      <h3 className=\"font-semibold text-white text-sm leading-tight mb-2 line-clamp-2\">\r\n                        {post.title}\r\n                      </h3>\r\n                    </div>\r\n                    {/* Border line with gap from edges */}\r\n                    <div className=\"mx-4 h-[1px]  bg-[#4F5259] \"></div>\r\n                    <div className=\"p-4 pt-2\">\r\n                      <div className=\"flex items-center gap-3 justify-between\">\r\n                        <span className=\"text-xs text-gray-400\">\r\n                          {formatTimeAgo(post.publishedAt || post.createdAt)}\r\n                        </span>\r\n                        <Badge\r\n                          variant=\"outline\"\r\n                          className=\"text-xs border-orange-600 text-orange-400\"\r\n                        >\r\n                          {post.categories[0] || \"BLOG\"}\r\n                        </Badge>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            </Link>\r\n          ))\r\n        ) : (\r\n          <div className=\"text-center text-gray-400 py-8\">\r\n            {isLoading ? \"Loading posts...\" : \"No blog posts available.\"}\r\n          </div>\r\n        )}\r\n        {onLoadMore && (\r\n          <div className=\"flex items-center justify-center mt-2\">\r\n            <Button\r\n              className=\"bg-[#07BC0C] cursor-pointer text-white hover:bg-[#06A50B] transition-colors\"\r\n              onClick={onLoadMore}\r\n              disabled={isLoading}\r\n            >\r\n              {isLoading ? \"Loading...\" : \"Load More\"}\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;AAQO,SAAS,aAAa,EAC3B,KAAK,EACL,UAAU,EACV,SAAS,EACS;IAClB,QAAQ,GAAG,CAAC,SAAS;IACrB,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BAAqC;;;;;;0BACnD,8OAAC;gBAAI,WAAU;;oBACZ,MAAM,MAAM,GAAG,IACd,MAAM,GAAG,CAAC,CAAC,qBACT,8OAAC,4JAAA,CAAA,UAAI;4BAAgB,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;sCAC9C,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,MAAM,IAAI;gDACpB,KAAK,KAAK,KAAK;gDACf,IAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;;;;;;8DAIf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,WAAW,IAAI,KAAK,SAAS;;;;;;0EAEnD,8OAAC,iIAAA,CAAA,QAAK;gEACJ,SAAQ;gEACR,WAAU;0EAET,KAAK,UAAU,CAAC,EAAE,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA5B1B,KAAK,GAAG;;;;kDAsCrB,8OAAC;wBAAI,WAAU;kCACZ,YAAY,qBAAqB;;;;;;oBAGrC,4BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,WAAU;4BACV,SAAS;4BACT,UAAU;sCAET,YAAY,eAAe;;;;;;;;;;;;;;;;;;;;;;;AAO1C", "debugId": null}}, {"offset": {"line": 1677, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/blogs/components/ExploreSection.tsx"], "sourcesContent": ["import { Card, CardContent } from \"@/components/ui/card\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { ChevronLeft, ChevronRight } from \"lucide-react\";\r\nimport BasicHeader from \"../../home/<USER>/BasicHeader\";\r\nimport { PublicBlogPost } from \"@/client_apis/api/blog\";\r\nimport { formatDate } from \"@/utils/dateUtils\";\r\nimport { useState } from \"react\";\r\n\r\ninterface ExploreSectionProps {\r\n  text?: string;\r\n  pagination?: boolean;\r\n  posts: PublicBlogPost[];\r\n  postsPerPage?: number;\r\n}\r\n\r\nexport function ExploreSection({\r\n  text = \"Explore\",\r\n  pagination = false,\r\n  posts,\r\n  postsPerPage = 4,\r\n}: ExploreSectionProps) {\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n\r\n  // Calculate pagination\r\n  const totalPages = Math.ceil(posts.length / postsPerPage);\r\n  const startIndex = (currentPage - 1) * postsPerPage;\r\n  const endIndex = startIndex + postsPerPage;\r\n  const currentPosts = posts.slice(startIndex, endIndex);\r\n\r\n  const handlePageChange = (page: number) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    if (currentPage > 1) {\r\n      setCurrentPage(currentPage - 1);\r\n    }\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (currentPage < totalPages) {\r\n      setCurrentPage(currentPage + 1);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <BasicHeader text={text} className=\"text-start mb-6\" />\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n        {currentPosts.length > 0 ? (\r\n          currentPosts.map((post) => (\r\n            <Link key={post._id} href={`/blogs/${post.slug}`}>\r\n              <Card className=\"bg-gray-800 border-gray-700 py-0 overflow-hidden hover:bg-gray-750 transition-colors cursor-pointer\">\r\n                <div className=\"relative h-48 w-full\">\r\n                  <Image\r\n                    src={post.banner || \"/placeholder.svg\"}\r\n                    alt={post.title}\r\n                    fill\r\n                    className=\"object-cover\"\r\n                  />\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\" />\r\n                </div>\r\n                <CardContent className=\"px-6 py-2\">\r\n                  <div className=\"flex items-center gap-2 mb-3\">\r\n                    <span className=\"text-sm text-gray-400\">\r\n                      {formatDate(post.publishedAt || post.createdAt)}\r\n                    </span>\r\n                  </div>\r\n                  <h3 className=\"text-lg font-bold mb-3 text-white leading-tight\">\r\n                    {post.title}\r\n                  </h3>\r\n                  {/* <p className=\"text-gray-300 mb-4 text-sm leading-relaxed\">\r\n                    {post.description}\r\n                  </p>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    className=\"border-green-600 text-green-400 hover:bg-green-600 hover:text-white transition-colors\"\r\n                  >\r\n                    Read More\r\n                  </Button> */}\r\n                </CardContent>\r\n              </Card>\r\n            </Link>\r\n          ))\r\n        ) : (\r\n          <div className=\"col-span-2 text-center text-gray-400 py-8\">\r\n            No posts available.\r\n          </div>\r\n        )}\r\n      </div>\r\n      {pagination && (\r\n        <div className=\"mt-8 flex items-center justify-center\">\r\n          <div className=\"flex items-center gap-2\">\r\n            {/* Previous Button */}\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              onClick={handlePrevious}\r\n              disabled={currentPage <= 1}\r\n              className={\r\n                currentPage <= 1\r\n                  ? \"bg-[#131925] text-gray-500 cursor-not-allowed rounded-full w-10 h-10 opacity-50\"\r\n                  : \"bg-[#131925] text-white hover:bg-[#A945F1] rounded-full w-10 h-10 transition-all duration-200\"\r\n              }\r\n            >\r\n              <ChevronLeft className=\"h-5 w-5\" />\r\n            </Button>\r\n\r\n            {/* Page Numbers */}\r\n            <div className=\"flex items-center gap-2 mx-4\">\r\n              {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {\r\n                const pageNum = i + 1;\r\n                const isActive = pageNum === currentPage;\r\n\r\n                return (\r\n                  <Button\r\n                    key={pageNum}\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={() => handlePageChange(pageNum)}\r\n                    className={\r\n                      isActive\r\n                        ? \"bg-[#A945F1] text-[#310267] hover:bg-[#8B37C7] rounded-full w-10 h-10 font-semibold\"\r\n                        : \"bg-[#131925] text-white hover:bg-[#A945F1] hover:text-[#310267] transition-all duration-200 rounded-full w-10 h-10\"\r\n                    }\r\n                  >\r\n                    {pageNum}\r\n                  </Button>\r\n                );\r\n              })}\r\n\r\n              {/* Ellipsis for more pages */}\r\n              {totalPages > 7 && (\r\n                <span className=\"text-gray-400 px-2\">...</span>\r\n              )}\r\n            </div>\r\n\r\n            {/* Next Button */}\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              onClick={handleNext}\r\n              disabled={currentPage >= totalPages}\r\n              className={\r\n                currentPage >= totalPages\r\n                  ? \"bg-[#131925] text-gray-500 cursor-not-allowed rounded-full w-10 h-10 opacity-50\"\r\n                  : \"bg-[#131925] text-white hover:bg-[#A945F1] rounded-full w-10 h-10 transition-all duration-200\"\r\n              }\r\n            >\r\n              <ChevronRight className=\"h-5 w-5\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;;;;;;;;;;AASO,SAAS,eAAe,EAC7B,OAAO,SAAS,EAChB,aAAa,KAAK,EAClB,KAAK,EACL,eAAe,CAAC,EACI;IACpB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,uBAAuB;IACvB,MAAM,aAAa,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG;IAC5C,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI;IACvC,MAAM,WAAW,aAAa;IAC9B,MAAM,eAAe,MAAM,KAAK,CAAC,YAAY;IAE7C,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,YAAY;YAC5B,eAAe,cAAc;QAC/B;IACF;IAEA,qBACE,8OAAC;;0BACC,8OAAC,6JAAA,CAAA,UAAW;gBAAC,MAAM;gBAAM,WAAU;;;;;;0BACnC,8OAAC;gBAAI,WAAU;0BACZ,aAAa,MAAM,GAAG,IACrB,aAAa,GAAG,CAAC,CAAC,qBAChB,8OAAC,4JAAA,CAAA,UAAI;wBAAgB,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;kCAC9C,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,KAAK,MAAM,IAAI;4CACpB,KAAK,KAAK,KAAK;4CACf,IAAI;4CACJ,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,WAAW,IAAI,KAAK,SAAS;;;;;;;;;;;sDAGlD,8OAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;;;;;;;;;;;;;uBAlBR,KAAK,GAAG;;;;8CAkCrB,8OAAC;oBAAI,WAAU;8BAA4C;;;;;;;;;;;YAK9D,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU,eAAe;4BACzB,WACE,eAAe,IACX,oFACA;sCAGN,cAAA,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAIzB,8OAAC;4BAAI,WAAU;;gCACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ,KAAK,GAAG,CAAC,YAAY;gCAAG,GAAG,CAAC,GAAG;oCACnD,MAAM,UAAU,IAAI;oCACpB,MAAM,WAAW,YAAY;oCAE7B,qBACE,8OAAC,kIAAA,CAAA,SAAM;wCAEL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,iBAAiB;wCAChC,WACE,WACI,wFACA;kDAGL;uCAVI;;;;;gCAaX;gCAGC,aAAa,mBACZ,8OAAC;oCAAK,WAAU;8CAAqB;;;;;;;;;;;;sCAKzC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU,eAAe;4BACzB,WACE,eAAe,aACX,oFACA;sCAGN,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC", "debugId": null}}, {"offset": {"line": 1916, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/blogs/components/BlogsPageClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport { NewsSection } from \"./NewsSection\";\r\nimport { BlogsSection } from \"./BlogSection\";\r\nimport { ExploreSection } from \"./ExploreSection\";\r\nimport { PublicBlogPost } from \"@/client_apis/api/blog\";\r\n\r\ninterface BlogsPageClientProps {\r\n  initialLatestPosts: PublicBlogPost[];\r\n  explorePosts: PublicBlogPost[];\r\n  topNewsPosts: PublicBlogPost[];\r\n}\r\n\r\nexport function BlogsPageClient({\r\n  initialLatestPosts,\r\n  explorePosts,\r\n  topNewsPosts,\r\n}: BlogsPageClientProps) {\r\n  const [latestPosts, setLatestPosts] =\r\n    useState<PublicBlogPost[]>(initialLatestPosts);\r\n  const [isLoadingMore, setIsLoadingMore] = useState(false);\r\n  const [currentLatestPage, setCurrentLatestPage] = useState(1);\r\n\r\n  const loadMoreLatestPosts = async () => {\r\n    if (isLoadingMore) return;\r\n\r\n    setIsLoadingMore(true);\r\n    try {\r\n      const nextPage = currentLatestPage + 1;\r\n      const response = await fetch(\r\n        `/api/public/posts?page=${nextPage}&limit=6`\r\n      );\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        if (data.success && data.data?.posts) {\r\n          const newPosts = data.data.posts;\r\n          setLatestPosts((prev) => [...prev, ...newPosts]);\r\n          setCurrentLatestPage(nextPage);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error loading more posts:\", error);\r\n    } finally {\r\n      setIsLoadingMore(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-black text-white\">\r\n      <div className=\"container mx-auto px-3 md:px-[4rem] pt-24 py-8\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12\">\r\n          <div className=\"lg:col-span-2 space-y-6\">\r\n            <NewsSection topNewsPosts={topNewsPosts} />\r\n            <ExploreSection pagination posts={explorePosts} postsPerPage={4} />\r\n          </div>\r\n          <div className=\"lg:col-span-1\">\r\n            <BlogsSection\r\n              posts={latestPosts}\r\n              onLoadMore={loadMoreLatestPosts}\r\n              isLoading={isLoadingMore}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAcO,SAAS,gBAAgB,EAC9B,kBAAkB,EAClB,YAAY,EACZ,YAAY,EACS;IACrB,MAAM,CAAC,aAAa,eAAe,GACjC,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,sBAAsB;QAC1B,IAAI,eAAe;QAEnB,iBAAiB;QACjB,IAAI;YACF,MAAM,WAAW,oBAAoB;YACrC,MAAM,WAAW,MAAM,MACrB,CAAC,uBAAuB,EAAE,SAAS,QAAQ,CAAC;YAG9C,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE,OAAO;oBACpC,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK;oBAChC,eAAe,CAAC,OAAS;+BAAI;+BAAS;yBAAS;oBAC/C,qBAAqB;gBACvB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8JAAA,CAAA,cAAW;gCAAC,cAAc;;;;;;0CAC3B,8OAAC,iKAAA,CAAA,iBAAc;gCAAC,UAAU;gCAAC,OAAO;gCAAc,cAAc;;;;;;;;;;;;kCAEhE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,8JAAA,CAAA,eAAY;4BACX,OAAO;4BACP,YAAY;4BACZ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB", "debugId": null}}]}