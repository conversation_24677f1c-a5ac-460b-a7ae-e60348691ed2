import { NextRequest, NextResponse } from "next/server";
import connectDB from "@/lib/mongodb";
import Post from "@/models/Post";
import { verifyAuth } from "@/lib/auth";
import { isValidObjectId } from "mongoose";

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Validate ObjectId
    if (!isValidObjectId(id)) {
      return NextResponse.json(
        { success: false, message: "Invalid post ID" },
        { status: 400 }
      );
    }

    // Verify authentication
    const authResult = await verifyAuth(request);
    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          message: authResult.error || "Authentication required",
        },
        { status: 401 }
      );
    }

    // Check if user has admin role (optional - you can remove this if any authenticated user can toggle top news)
    if (authResult.user?.role !== "admin") {
      return NextResponse.json(
        { success: false, message: "Admin access required" },
        { status: 403 }
      );
    }

    // Connect to database
    await connectDB();

    // Find the post
    const post = await Post.findById(id);
    if (!post) {
      return NextResponse.json(
        { success: false, message: "Post not found" },
        { status: 404 }
      );
    }

    // Toggle the isTopNews status
    post.isTopNews = !post.isTopNews;
    await post.save();

    return NextResponse.json({
      success: true,
      message: `Post ${post.isTopNews ? "marked as" : "removed from"} top news`,
      data: {
        postId: post._id,
        isTopNews: post.isTopNews,
      },
    });
  } catch (error) {
    console.error("Error toggling top news status:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
