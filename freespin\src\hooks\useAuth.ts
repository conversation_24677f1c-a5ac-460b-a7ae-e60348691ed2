"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";

export interface User {
  id: string;
  email: string;
  username: string;
  role: string;
  name?: string;
}

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isAdmin: boolean;
}

export function useAuth(): AuthState {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const checkAuth = useCallback(async () => {
    try {
      setIsLoading(true);

      // Check if user info exists in cookies (client-side accessible)
      const userInfo = getCookie("user-info");

      if (!userInfo) {
        setUser(null);
        setIsLoading(false);
        return;
      }

      // Parse user info from cookie
      let parsedUser;
      try {
        parsedUser = JSON.parse(userInfo);
      } catch (parseError) {
        console.error("Failed to parse user-info cookie:", parseError);
        console.log("Raw cookie value:", userInfo);
        setUser(null);
        clearAuthCookies();
        setIsLoading(false);
        return;
      }

      // For now, let's trust the cookie and skip server verification
      // This will help us identify if the issue is with the verification API
      setUser({
        id: parsedUser.id,
        email: parsedUser.email,
        username: parsedUser.username,
        role: parsedUser.role,
        name: parsedUser.name,
      });

      // TODO: Re-enable server verification once basic auth is working
      // const response = await fetch("/api/auth/verify", {
      //   method: "GET",
      //   credentials: "include",
      //   headers: {
      //     "Content-Type": "application/json",
      //   },
      // });

      // if (response.ok) {
      //   const result = await response.json();
      //   if (result.success && result.user) {
      //     setUser({
      //       id: result.user.userId || parsedUser.id,
      //       email: result.user.email || parsedUser.email,
      //       username: result.user.username || parsedUser.username,
      //       role: result.user.role || parsedUser.role,
      //       name: parsedUser.name,
      //     });
      //   } else {
      //     setUser(null);
      //     clearAuthCookies();
      //   }
      // } else {
      //   setUser(null);
      //   clearAuthCookies();
      // }
    } catch (error) {
      console.error("Auth check failed:", error);
      setUser(null);
      clearAuthCookies();
    } finally {
      setIsLoading(false);
    }
  }, []);
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  const getCookie = (name: string): string | null => {
    if (typeof document === "undefined") return null;

    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      const cookieValue = parts.pop()?.split(";").shift() || null;
      // URL decode the cookie value
      return cookieValue ? decodeURIComponent(cookieValue) : null;
    }
    return null;
  };

  const clearAuthCookies = () => {
    if (typeof document !== "undefined") {
      // Only clear the client-accessible cookie
      // The httpOnly session-token cookie will be cleared by the server
      document.cookie =
        "user-info=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    }
  };

  return {
    user,
    isLoading,
    isAuthenticated: !!user,
    isAdmin: user?.role === "admin",
  };
}

export function useRequireAuth(requiredRole?: string) {
  const auth = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!auth.isLoading) {
      if (!auth.isAuthenticated) {
        router.push("/auth");
        return;
      }

      if (requiredRole && auth.user?.role !== requiredRole) {
        router.push("/unauthorized");
        return;
      }
    }
  }, [
    auth.isLoading,
    auth.isAuthenticated,
    auth.user?.role,
    requiredRole,
    router,
  ]);

  return auth;
}

export function useRequireAdmin() {
  return useRequireAuth("admin");
}
