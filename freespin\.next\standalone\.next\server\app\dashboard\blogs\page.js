(()=>{var e={};e.id=1431,e.ids=[1431],e.modules={658:(e,t,r)=>{e.exports=r(41547)(r(85718),"Map")},1264:(e,t,r)=>{"use strict";r.d(t,{default:()=>C});var s=r(60687),a=r(6475);let n=(0,a.createServerReference)("60cdb4e58939c5ebe2ad11f732ca2e9f4afd0da297",a.callServer,void 0,a.findSourceMapURL,"updatePostStatusAction");var o=r(85668),i=r.n(o),c=r(96834),l=r(29523),u=r(44493),d=r(89667),p=r(43210),h=r(4780);function f({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,h.cn)("w-full caption-bottom text-sm",e),...t})})}function g({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,h.cn)("[&_tr]:border-b",e),...t})}function x({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,h.cn)("[&_tr:last-child]:border-0",e),...t})}function v({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,h.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function b({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,h.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function m({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,h.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}var y=r(96474),w=r(99270),j=r(62688);let $=(0,j.A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),_=(0,j.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),N=(0,j.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),S=(0,j.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);var D=r(88233),T=r(16189),k=r(93853),M=r(40491),A=r.n(M);let O={published:"bg-green-100 text-green-800 hover:bg-green-200",draft:"bg-yellow-100 text-yellow-800 hover:bg-yellow-200",archived:"bg-gray-100 text-gray-800 hover:bg-gray-200"};function C({posts:e,isLoading:t=!1,className:r=""}){let a=(0,T.useRouter)(),[o,h]=(0,p.useState)("all"),[j,M]=(0,p.useState)(""),C=e.filter(e=>{let t="all"===o||e.status===o,r=""===j||e.title.toLowerCase().includes(j.toLowerCase())||e.categories.some(e=>e.toLowerCase().includes(j.toLowerCase()))||e.tags.some(e=>e.toLowerCase().includes(j.toLowerCase()));return t&&r}),P=e=>{if(!e)return"Not published";try{let t=i()(e);if(!t.isValid())return"Invalid date";return t.format("MMMM DD, YYYY")}catch(e){return"Invalid date"}},E=e=>`${O[e]} border-0 font-medium`,F=async e=>{try{let t=await fetch(`/api/posts/${e}`,{method:"DELETE",headers:{"Content-Type":"application/json"},credentials:"include"}),r=await t.json();if(!t.ok||!r.success)return void k.oR.error(r.error||r.message||"Failed to delete post");k.oR.success(r.message||"Post deleted successfully"),window.location.reload()}catch(e){console.error("Delete error:",e),k.oR.error("Failed to delete post")}},q=async e=>{try{console.log("Toggling post status for:",e._id,"from",e.status);let t="published"===e.status?"draft":"published";console.log("New status will be:",t);let r=await n(e._id,t);if(console.log("API response:",r),!r.success)return void k.oR.error(r.error||"Failed to update post status");k.oR.success(`Post ${t} successfully`),a.refresh()}catch(e){console.error("Error toggling post status:",e),k.oR.error("Failed to update post status")}},z=async e=>{try{let t=await fetch(`/api/posts/${e._id}/toggle-top-news`,{method:"PATCH",headers:{"Content-Type":"application/json"},credentials:"include"}),r=await t.json();if(!t.ok||!r.success)return void k.oR.error(r.message||"Failed to toggle top news status");k.oR.success(r.message),window.location.reload()}catch(e){console.error("Error toggling top news:",e),k.oR.error("Failed to toggle top news status")}};return(0,s.jsxs)("div",{className:`space-y-6 ${r}`,children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Blog Management"}),(0,s.jsxs)(l.$,{onClick:()=>a.push("/dashboard/blogs/add"),className:"bg-black hover:bg-gray-800",children:[(0,s.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Create Blog"]})]}),(0,s.jsxs)(u.Zp,{children:[(0,s.jsx)(u.aR,{className:"pb-4",children:(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"relative w-80",children:[(0,s.jsx)(w.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)(d.p,{placeholder:"Search...",value:j,onChange:e=>M(e.target.value),className:"pl-10 bg-gray-50 border-gray-200 focus:bg-white"})]})})}),(0,s.jsx)(u.Wu,{className:"p-0",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)(f,{children:[(0,s.jsx)(g,{children:(0,s.jsxs)(v,{className:"border-gray-200",children:[(0,s.jsx)(b,{className:"font-semibold text-gray-900",children:"Title"}),(0,s.jsx)(b,{className:"font-semibold text-gray-900",children:"Categories"}),(0,s.jsx)(b,{className:"font-semibold text-gray-900",children:"Tags"}),(0,s.jsx)(b,{className:"font-semibold text-gray-900",children:"Status"}),(0,s.jsx)(b,{className:"font-semibold text-gray-900",children:"Top News"}),(0,s.jsx)(b,{className:"font-semibold text-gray-900",children:"Published"}),(0,s.jsx)(b,{className:"font-semibold text-gray-900 text-right",children:"Actions"})]})}),(0,s.jsx)(x,{children:t?Array.from({length:3}).map((e,t)=>(0,s.jsxs)(v,{children:[(0,s.jsx)(m,{children:(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"})}),(0,s.jsx)(m,{children:(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-16"})}),(0,s.jsx)(m,{children:(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-16"})}),(0,s.jsx)(m,{children:(0,s.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse w-16"})}),(0,s.jsx)(m,{children:(0,s.jsx)("div",{className:"h-6 bg-gray-200 rounded animate-pulse w-16"})}),(0,s.jsx)(m,{children:(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse w-20"})}),(0,s.jsx)(m,{children:(0,s.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,s.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded animate-pulse"}),(0,s.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded animate-pulse"}),(0,s.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded animate-pulse"})]})})]},t)):0===C.length?(0,s.jsx)(v,{children:(0,s.jsx)(m,{colSpan:8,className:"text-center py-8 text-gray-500",children:j?"No posts found matching your search.":"No posts found."})}):C.map(e=>(0,s.jsxs)(v,{className:"hover:bg-gray-50",children:[(0,s.jsx)(m,{className:"font-medium max-w-xs",children:(0,s.jsx)("div",{className:"truncate",title:e.title,children:e.title})}),(0,s.jsx)(m,{children:e.categories.length>0?(0,s.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.categories.slice(0,2).map((e,t)=>(0,s.jsx)(c.E,{variant:"outline",className:"text-xs",children:A()(e,"name","N/A")},t)),e.categories.length>2&&(0,s.jsxs)(c.E,{variant:"outline",className:"text-xs",children:["+",e.categories.length-2]})]}):(0,s.jsx)("span",{className:"text-gray-400",children:"-"})}),(0,s.jsx)(m,{children:e.tags.length>0?(0,s.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.tags.slice(0,2).map((e,t)=>(0,s.jsx)(c.E,{variant:"secondary",className:"text-xs",children:e},t)),e.tags.length>2&&(0,s.jsxs)(c.E,{variant:"secondary",className:"text-xs",children:["+",e.tags.length-2]})]}):(0,s.jsx)("span",{className:"text-gray-400",children:"-"})}),(0,s.jsx)(m,{children:(0,s.jsx)(c.E,{className:E(e.status),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,s.jsx)(m,{children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)($,{className:`h-4 w-4 ${e.isTopNews?"text-yellow-500 fill-yellow-500":"text-gray-300"}`}),(0,s.jsx)("span",{className:"ml-2 text-sm text-gray-600",children:e.isTopNews?"Yes":"No"})]})}),(0,s.jsx)(m,{className:"text-gray-600",children:P(e.createdAt)}),(0,s.jsx)(m,{children:(0,s.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,s.jsxs)(l.$,{type:"button",variant:"outline",size:"sm",onClick:t=>{t.preventDefault(),t.stopPropagation(),z(e)},className:`h-8 px-3 ${e.isTopNews?"text-yellow-600 hover:text-yellow-700 hover:bg-yellow-50":"text-gray-600 hover:text-gray-700 hover:bg-gray-50"}`,title:e.isTopNews?"Remove from Top News":"Mark as Top News",children:[(0,s.jsx)($,{className:`h-4 w-4 mr-1 ${e.isTopNews?"fill-current":""}`}),e.isTopNews?"Remove":"Top News"]}),(0,s.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:t=>{t.preventDefault(),t.stopPropagation(),q(e)},className:`h-8 px-3 ${"published"===e.status?"text-orange-600 hover:text-orange-700 hover:bg-orange-50":"text-green-600 hover:text-green-700 hover:bg-green-50"}`,children:"published"===e.status?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(_,{className:"h-4 w-4 mr-1"}),"Unpublish"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(N,{className:"h-4 w-4 mr-1"}),"Publish"]})}),(0,s.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>a.push(`/dashboard/blogs/edit/${e.slug}`),className:"h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50",children:(0,s.jsx)(S,{className:"h-4 w-4"})}),(0,s.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:t=>{t.preventDefault(),t.stopPropagation(),F(e._id)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50",children:(0,s.jsx)(D.A,{className:"h-4 w-4"})})]})})]},e._id))})]})})})]})]})}},1707:(e,t,r)=>{var s=r(35142),a=r(46436);e.exports=function(e,t){t=s(t,e);for(var r=0,n=t.length;null!=e&&r<n;)e=e[a(t[r++])];return r&&r==n?e:void 0}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5231:(e,t,r)=>{var s=r(29395),a=r(55048);e.exports=function(e){if(!a(e))return!1;var t=s(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},8336:(e,t,r)=>{var s=r(45803);e.exports=function(e,t){var r=e.__data__;return s(t)?r["string"==typeof t?"string":"hash"]:r.map}},10663:e=>{e.exports="object"==typeof global&&global&&global.Object===Object&&global},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12233:(e,t,r)=>{Promise.resolve().then(r.bind(r,1264))},12290:e=>{var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},15909:(e,t,r)=>{var s=r(87506),a=r(66930),n=r(658);e.exports=function(){this.size=0,this.__data__={hash:new s,map:new(n||a),string:new s}}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19976:(e,t,r)=>{var s=r(8336);e.exports=function(e,t){var r=s(this,e),a=r.size;return r.set(e,t),this.size+=+(r.size!=a),this}},21367:e=>{e.exports=function(e,t){for(var r=-1,s=null==e?0:e.length,a=Array(s);++r<s;)a[r]=t(e[r],r,e);return a}},27467:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},27669:e=>{e.exports=function(){this.__data__=[],this.size=0}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28837:(e,t,r)=>{var s=r(57797),a=Array.prototype.splice;e.exports=function(e){var t=this.__data__,r=s(t,e);return!(r<0)&&(r==t.length-1?t.pop():a.call(t,r,1),--this.size,!0)}},29021:e=>{"use strict";e.exports=require("fs")},29205:(e,t,r)=>{var s=r(8336);e.exports=function(e){var t=s(this,e).delete(e);return this.size-=!!t,t}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(e,t,r)=>{var s=r(79474),a=r(70222),n=r(84713),o=s?s.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":o&&o in Object(e)?a(e):n(e)}},29508:(e,t,r)=>{var s=r(8336);e.exports=function(e){return s(this,e).get(e)}},33873:e=>{"use strict";e.exports=require("path")},35142:(e,t,r)=>{var s=r(40542),a=r(67619),n=r(51449),o=r(42403);e.exports=function(e,t){return s(e)?e:a(e,t)?[e]:n(o(e))}},35800:(e,t,r)=>{var s=r(57797);e.exports=function(e){return s(this.__data__,e)>-1}},39153:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"0026ed18766329cb38cbf036b1b58cc4e238af4b63":()=>s.G8,"00bd484230662a391368281e6d8bac1469f7b246f1":()=>s.Ui,"00d6b6044cac2bc7b8d62ef179e12bb8d427e56b1a":()=>s.K5,"4028b030361181c413d3fc466747cf702ccff9b189":()=>s.nt,"4038792a88358da73fac1c13a273a76c89f53d3e2e":()=>s.QY,"407c1d9494e79c02f37662fbc2619c5ae3c2bbded2":()=>s.dc,"408de5c0c7bb7fdff5408d6db4a77d8acb05b282d4":()=>s.ys,"40db3833dad112ff1faa13e6562a2aeee0dc76a47a":()=>s.pD,"60775d7582a533479853fd0ab1fbe6dd7b1300fc9b":()=>s.qG});var s=r(67328)},39672:(e,t,r)=>{var s=r(58141);e.exports=function(e,t){var r=this.__data__;return this.size+=+!this.has(e),r[e]=s&&void 0===t?"__lodash_hash_undefined__":t,this}},40491:(e,t,r)=>{var s=r(1707);e.exports=function(e,t,r){var a=null==e?void 0:s(e,t);return void 0===a?r:a}},40542:e=>{e.exports=Array.isArray},41204:e=>{"use strict";e.exports=require("string_decoder")},41547:(e,t,r)=>{var s=r(61548),a=r(90851);e.exports=function(e,t){var r=a(e,t);return s(r)?r:void 0}},42403:(e,t,r)=>{var s=r(80195);e.exports=function(e){return null==e?"":s(e)}},44493:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>i,Zp:()=>n,aR:()=>o});var s=r(60687);r(43210);var a=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},45803:e=>{e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},46436:(e,t,r)=>{var s=r(49227),a=1/0;e.exports=function(e){if("string"==typeof e||s(e))return e;var t=e+"";return"0"==t&&1/e==-a?"-0":t}},47081:(e,t,r)=>{Promise.resolve().then(r.bind(r,77488))},49227:(e,t,r)=>{var s=r(29395),a=r(27467);e.exports=function(e){return"symbol"==typeof e||a(e)&&"[object Symbol]"==s(e)}},51449:(e,t,r)=>{var s=r(85745),a=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,n=/\\(\\)?/g;e.exports=s(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(a,function(e,r,s,a){t.push(s?a.replace(n,"$1"):r||e)}),t})},52823:(e,t,r)=>{var s=r(85406),a=function(){var e=/[^.]+$/.exec(s&&s.keys&&s.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();e.exports=function(e){return!!a&&a in e}},55048:e=>{e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},57797:(e,t,r)=>{var s=r(67009);e.exports=function(e,t){for(var r=e.length;r--;)if(s(e[r][0],t))return r;return -1}},58141:(e,t,r)=>{e.exports=r(41547)(Object,"create")},58744:(e,t,r)=>{var s=r(57797);e.exports=function(e,t){var r=this.__data__,a=s(r,e);return a<0?(++this.size,r.push([e,t])):r[a][1]=t,this}},61320:(e,t,r)=>{var s=r(8336);e.exports=function(e){return s(this,e).has(e)}},61548:(e,t,r)=>{var s=r(5231),a=r(52823),n=r(55048),o=r(12290),i=/^\[object .+?Constructor\]$/,c=Object.prototype,l=Function.prototype.toString,u=c.hasOwnProperty,d=RegExp("^"+l.call(u).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!n(e)||a(e))&&(s(e)?d:i).test(o(e))}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66136:e=>{"use strict";e.exports=require("timers")},66837:(e,t,r)=>{var s=r(58141);e.exports=function(){this.__data__=s?s(null):{},this.size=0}},66930:(e,t,r)=>{var s=r(27669),a=r(28837),n=r(94388),o=r(35800),i=r(58744);function c(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var s=e[t];this.set(s[0],s[1])}}c.prototype.clear=s,c.prototype.delete=a,c.prototype.get=n,c.prototype.has=o,c.prototype.set=i,e.exports=c},67009:e=>{e.exports=function(e,t){return e===t||e!=e&&t!=t}},67328:(e,t,r)=>{"use strict";r.d(t,{nt:()=>x,pD:()=>y,QY:()=>v,ys:()=>w,G8:()=>g,K5:()=>m,Ui:()=>f,dc:()=>$,qG:()=>j});var s=r(67218);r(79130);var a=r(62351),n=r(44999);async function o(e,t={}){let{baseUrl:r="https://www.freespin168.asia",nextOptions:s,headers:a,includeCookies:i=!0,customCookies:c,...l}=t,u=`${r}${e}`;console.log(u);let d={"Content-Type":"application/json",...a};if(i)try{let e=await (0,n.UL)(),t=e.get("session-token");t&&(d.Authorization=`Bearer ${t.value}`),c&&(d.Cookie=Object.entries(c).map(([e,t])=>`${e}=${t}`).join("; "));let r=e.getAll();if(r.length>0){let e=r.map(e=>`${e.name}=${e.value}`).join("; ");c||(d.Cookie=e)}}catch(e){console.warn("Cookies not available in this context:",e)}let p={...l,headers:d,next:{tags:s?.tags||[],revalidate:s?.revalidate}};try{let e=await fetch(u,p);if(console.log("res",e),!e.ok){let t="An error occurred",r={};try{t=(r=await e.json()).message||r.error||`HTTP ${e.status}: ${e.statusText}`}catch{t=`HTTP ${e.status}: ${e.statusText}`}return{status:e.status,message:t,errors:r.errors||void 0}}return e.json()}catch(e){return{status:0,message:e instanceof Error?e.message:"Network error occurred"}}}function i(e){return e&&"number"==typeof e.status&&"string"==typeof e.message}async function c(e,t={}){return o(e,{...t,includeCookies:!0})}async function l(e,t={}){return o(e,{...t,includeCookies:!1})}let u=e=>e?"?"+new URLSearchParams(e).toString():"",d=({endpoint:e,tags:t,queryParams:r,revalidate:s,requireAuth:n=!0})=>{let o=n?c:l;return{async getAll(){let a=await o(`${e}${u(r)}`,{nextOptions:{tags:[e,...t],revalidate:s}});return i(a)?{success:!1,message:a.message,status:a.status}:a},async getById(r){let s=await o(`${e}/${r}`,{nextOptions:{tags:[`${e}-${r}`,...t]}});return i(s)?{success:!1,message:s.message,status:s.status}:s},async createData(r){let s=r instanceof FormData?{"Content-Type":"multipart/form-data"}:{"Content-Type":"application/json"},n=await o(e,{method:"POST",body:r instanceof FormData?r:JSON.stringify(r),headers:s});return i(n)?{success:!1,message:n.message,status:n.status}:(t.forEach(e=>(0,a.revalidateTag)(e)),(0,a.revalidateTag)(e),n)},async updateData(r,s){let n=s instanceof FormData?void 0:{"Content-Type":"application/json"},c=await o(`${e}/${r}`,{method:"PATCH",body:s instanceof FormData?s:JSON.stringify(s),headers:n});return i(c)?{success:!1,message:c.message,status:c.status}:(t.forEach(e=>(0,a.revalidateTag)(e)),(0,a.revalidateTag)(e),(0,a.revalidateTag)(`${e}-${r}`),c)},async deleteDataById(r){let s=await o(`${e}/${r}`,{method:"DELETE"});return i(s)?{success:!1,message:s.message,status:s.status}:(t.forEach(e=>(0,a.revalidateTag)(e)),(0,a.revalidateTag)(e),(0,a.revalidateTag)(`${e}-${r}`),s)}}};var p=r(17478);let h=d({endpoint:"/api/categories",tags:["categories"],requireAuth:!0});async function f(){try{let e=await fetch("https://freespin168.asia/api/categories",{method:"GET",headers:{"Content-Type":"application/json"},credentials:"include"});if(!e.ok){let t=await e.text();throw Error(`HTTP error! status: ${e.status}, body: ${t}`)}return await e.json()}catch(e){throw console.error("Error fetching categories:",e),e}}async function g(){return f()}async function x(e){return await h.createData(e)}async function v(e){return await h.deleteDataById(e)}let b=d({endpoint:"/api/posts",tags:["posts"],requireAuth:!0});async function m(){return await b.getAll()}async function y(e){return await b.createData(e)}async function w(e){return await b.deleteDataById(e)}async function j(e,t){try{let r=await fetch(`https://freespin168.asia/api/posts/${e}/status`,{method:"PATCH",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({status:t})});if(!r.ok){let e=await r.text();throw Error(`HTTP error! status: ${r.status}, body: ${e}`)}return await r.json()}catch(e){return console.error("Error updating post status:",e),{success:!1,error:e instanceof Error?e.message:"Failed to update post status"}}}async function $(e){let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.category&&t.append("category",e.category),e?.tag&&t.append("tag",e.tag),e?.search&&t.append("search",e.search);let r=t.toString(),s="https://freespin168.asia",a=r?`${s}/api/public/posts?${r}`:`${s}/api/public/posts`;try{let e=await fetch(a,{method:"GET",headers:{"Content-Type":"application/json"},next:{tags:["public-posts"],revalidate:300}});if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);return await e.json()}catch(e){return console.error("Error fetching public posts:",e),{success:!1,error:"Failed to fetch posts"}}}(0,p.D)([f,g,x,v,m,y,w,j,$]),(0,s.A)(f,"00bd484230662a391368281e6d8bac1469f7b246f1",null),(0,s.A)(g,"0026ed18766329cb38cbf036b1b58cc4e238af4b63",null),(0,s.A)(x,"4028b030361181c413d3fc466747cf702ccff9b189",null),(0,s.A)(v,"4038792a88358da73fac1c13a273a76c89f53d3e2e",null),(0,s.A)(m,"00d6b6044cac2bc7b8d62ef179e12bb8d427e56b1a",null),(0,s.A)(y,"40db3833dad112ff1faa13e6562a2aeee0dc76a47a",null),(0,s.A)(w,"408de5c0c7bb7fdff5408d6db4a77d8acb05b282d4",null),(0,s.A)(j,"60775d7582a533479853fd0ab1fbe6dd7b1300fc9b",null),(0,s.A)($,"407c1d9494e79c02f37662fbc2619c5ae3c2bbded2",null)},67619:(e,t,r)=>{var s=r(40542),a=r(49227),n=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,o=/^\w*$/;e.exports=function(e,t){if(s(e))return!1;var r=typeof e;return!!("number"==r||"symbol"==r||"boolean"==r||null==e||a(e))||o.test(e)||!n.test(e)||null!=t&&e in Object(t)}},70222:(e,t,r)=>{var s=r(79474),a=Object.prototype,n=a.hasOwnProperty,o=a.toString,i=s?s.toStringTag:void 0;e.exports=function(e){var t=n.call(e,i),r=e[i];try{e[i]=void 0;var s=!0}catch(e){}var a=o.call(e);return s&&(t?e[i]=r:delete e[i]),a}},73024:e=>{"use strict";e.exports=require("node:fs")},75557:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>l});var s=r(65239),a=r(48088),n=r(88170),o=r.n(n),i=r(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(t,c);let l={children:["",{children:["dashboard",{children:["blogs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,86349)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\dashboard\\blogs\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,63144)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\dashboard\\blogs\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/blogs/page",pathname:"/dashboard/blogs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},77488:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Web Studio Nepal\\\\FreeSpin168\\\\freespin\\\\src\\\\app\\\\dashboard\\\\blogs\\\\components\\\\BlogManagement.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\dashboard\\blogs\\components\\BlogManagement.tsx","default")},79428:e=>{"use strict";e.exports=require("buffer")},79474:(e,t,r)=>{e.exports=r(85718).Symbol},79551:e=>{"use strict";e.exports=require("url")},80195:(e,t,r)=>{var s=r(79474),a=r(21367),n=r(40542),o=r(49227),i=1/0,c=s?s.prototype:void 0,l=c?c.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(n(t))return a(t,e)+"";if(o(t))return l?l.call(t):"";var r=t+"";return"0"==r&&1/t==-i?"-0":r}},81630:e=>{"use strict";e.exports=require("http")},84261:e=>{e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=!!t,t}},84713:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},85406:(e,t,r)=>{e.exports=r(85718)["__core-js_shared__"]},85668:function(e){e.exports=function(){"use strict";var e="millisecond",t="second",r="minute",s="hour",a="week",n="month",o="quarter",i="year",c="date",l="Invalid Date",u=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,d=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,p=function(e,t,r){var s=String(e);return!s||s.length>=t?e:""+Array(t+1-s.length).join(r)+e},h="en",f={};f[h]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||t[0])+"]"}};var g="$isDayjsObject",x=function(e){return e instanceof y||!(!e||!e[g])},v=function e(t,r,s){var a;if(!t)return h;if("string"==typeof t){var n=t.toLowerCase();f[n]&&(a=n),r&&(f[n]=r,a=n);var o=t.split("-");if(!a&&o.length>1)return e(o[0])}else{var i=t.name;f[i]=t,a=i}return!s&&a&&(h=a),a||!s&&h},b=function(e,t){if(x(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new y(r)},m={s:p,z:function(e){var t=-e.utcOffset(),r=Math.abs(t);return(t<=0?"+":"-")+p(Math.floor(r/60),2,"0")+":"+p(r%60,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var s=12*(r.year()-t.year())+(r.month()-t.month()),a=t.clone().add(s,n),o=r-a<0,i=t.clone().add(s+(o?-1:1),n);return+(-(s+(r-a)/(o?a-i:i-a))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(l){return({M:n,y:i,w:a,d:"day",D:c,h:s,m:r,s:t,ms:e,Q:o})[l]||String(l||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};m.l=v,m.i=x,m.w=function(e,t){return b(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var y=function(){function p(e){this.$L=v(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[g]=!0}var h=p.prototype;return h.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if(m.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var s=t.match(u);if(s){var a=s[2]-1||0,n=(s[7]||"0").substring(0,3);return r?new Date(Date.UTC(s[1],a,s[3]||1,s[4]||0,s[5]||0,s[6]||0,n)):new Date(s[1],a,s[3]||1,s[4]||0,s[5]||0,s[6]||0,n)}}return new Date(t)}(e),this.init()},h.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},h.$utils=function(){return m},h.isValid=function(){return this.$d.toString()!==l},h.isSame=function(e,t){var r=b(e);return this.startOf(t)<=r&&r<=this.endOf(t)},h.isAfter=function(e,t){return b(e)<this.startOf(t)},h.isBefore=function(e,t){return this.endOf(t)<b(e)},h.$g=function(e,t,r){return m.u(e)?this[t]:this.set(r,e)},h.unix=function(){return Math.floor(this.valueOf()/1e3)},h.valueOf=function(){return this.$d.getTime()},h.startOf=function(e,o){var l=this,u=!!m.u(o)||o,d=m.p(e),p=function(e,t){var r=m.w(l.$u?Date.UTC(l.$y,t,e):new Date(l.$y,t,e),l);return u?r:r.endOf("day")},h=function(e,t){return m.w(l.toDate()[e].apply(l.toDate("s"),(u?[0,0,0,0]:[23,59,59,999]).slice(t)),l)},f=this.$W,g=this.$M,x=this.$D,v="set"+(this.$u?"UTC":"");switch(d){case i:return u?p(1,0):p(31,11);case n:return u?p(1,g):p(0,g+1);case a:var b=this.$locale().weekStart||0,y=(f<b?f+7:f)-b;return p(u?x-y:x+(6-y),g);case"day":case c:return h(v+"Hours",0);case s:return h(v+"Minutes",1);case r:return h(v+"Seconds",2);case t:return h(v+"Milliseconds",3);default:return this.clone()}},h.endOf=function(e){return this.startOf(e,!1)},h.$set=function(a,o){var l,u=m.p(a),d="set"+(this.$u?"UTC":""),p=((l={}).day=d+"Date",l[c]=d+"Date",l[n]=d+"Month",l[i]=d+"FullYear",l[s]=d+"Hours",l[r]=d+"Minutes",l[t]=d+"Seconds",l[e]=d+"Milliseconds",l)[u],h="day"===u?this.$D+(o-this.$W):o;if(u===n||u===i){var f=this.clone().set(c,1);f.$d[p](h),f.init(),this.$d=f.set(c,Math.min(this.$D,f.daysInMonth())).$d}else p&&this.$d[p](h);return this.init(),this},h.set=function(e,t){return this.clone().$set(e,t)},h.get=function(e){return this[m.p(e)]()},h.add=function(e,o){var c,l=this;e=Number(e);var u=m.p(o),d=function(t){var r=b(l);return m.w(r.date(r.date()+Math.round(t*e)),l)};if(u===n)return this.set(n,this.$M+e);if(u===i)return this.set(i,this.$y+e);if("day"===u)return d(1);if(u===a)return d(7);var p=((c={})[r]=6e4,c[s]=36e5,c[t]=1e3,c)[u]||1,h=this.$d.getTime()+e*p;return m.w(h,this)},h.subtract=function(e,t){return this.add(-1*e,t)},h.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||l;var s=e||"YYYY-MM-DDTHH:mm:ssZ",a=m.z(this),n=this.$H,o=this.$m,i=this.$M,c=r.weekdays,u=r.months,p=r.meridiem,h=function(e,r,a,n){return e&&(e[r]||e(t,s))||a[r].slice(0,n)},f=function(e){return m.s(n%12||12,e,"0")},g=p||function(e,t,r){var s=e<12?"AM":"PM";return r?s.toLowerCase():s};return s.replace(d,function(e,s){return s||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return m.s(t.$y,4,"0");case"M":return i+1;case"MM":return m.s(i+1,2,"0");case"MMM":return h(r.monthsShort,i,u,3);case"MMMM":return h(u,i);case"D":return t.$D;case"DD":return m.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return h(r.weekdaysMin,t.$W,c,2);case"ddd":return h(r.weekdaysShort,t.$W,c,3);case"dddd":return c[t.$W];case"H":return String(n);case"HH":return m.s(n,2,"0");case"h":return f(1);case"hh":return f(2);case"a":return g(n,o,!0);case"A":return g(n,o,!1);case"m":return String(o);case"mm":return m.s(o,2,"0");case"s":return String(t.$s);case"ss":return m.s(t.$s,2,"0");case"SSS":return m.s(t.$ms,3,"0");case"Z":return a}return null}(e)||a.replace(":","")})},h.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},h.diff=function(e,c,l){var u,d=this,p=m.p(c),h=b(e),f=(h.utcOffset()-this.utcOffset())*6e4,g=this-h,x=function(){return m.m(d,h)};switch(p){case i:u=x()/12;break;case n:u=x();break;case o:u=x()/3;break;case a:u=(g-f)/6048e5;break;case"day":u=(g-f)/864e5;break;case s:u=g/36e5;break;case r:u=g/6e4;break;case t:u=g/1e3;break;default:u=g}return l?u:m.a(u)},h.daysInMonth=function(){return this.endOf(n).$D},h.$locale=function(){return f[this.$L]},h.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),s=v(e,t,!0);return s&&(r.$L=s),r},h.clone=function(){return m.w(this.$d,this)},h.toDate=function(){return new Date(this.valueOf())},h.toJSON=function(){return this.isValid()?this.toISOString():null},h.toISOString=function(){return this.$d.toISOString()},h.toString=function(){return this.$d.toUTCString()},p}(),w=y.prototype;return b.prototype=w,[["$ms",e],["$s",t],["$m",r],["$H",s],["$W","day"],["$M",n],["$y",i],["$D",c]].forEach(function(e){w[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),b.extend=function(e,t){return e.$i||(e(t,y,b),e.$i=!0),b},b.locale=v,b.isDayjs=x,b.unix=function(e){return b(1e3*e)},b.en=f[h],b.Ls=f,b.p={},b}()},85718:(e,t,r)=>{var s=r(10663),a="object"==typeof self&&self&&self.Object===Object&&self;e.exports=s||a||Function("return this")()},85745:(e,t,r)=>{var s=r(86451);e.exports=function(e){var t=s(e,function(e){return 500===r.size&&r.clear(),e}),r=t.cache;return t}},86349:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(37413),a=r(67328),n=r(77488),o=r(34661),i=r.n(o);async function c(){let e=await (0,a.K5)(),t=i()(e,"data.posts",[]);return console.log(t,"POSTS DSDA"),(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsx)(n.default,{posts:t})})}},86451:(e,t,r)=>{var s=r(95746);function a(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw TypeError("Expected a function");var r=function(){var s=arguments,a=t?t.apply(this,s):s[0],n=r.cache;if(n.has(a))return n.get(a);var o=e.apply(this,s);return r.cache=n.set(a,o)||n,o};return r.cache=new(a.Cache||s),r}a.Cache=s,e.exports=a},87506:(e,t,r)=>{var s=r(66837),a=r(84261),n=r(89492),o=r(90200),i=r(39672);function c(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var s=e[t];this.set(s[0],s[1])}}c.prototype.clear=s,c.prototype.delete=a,c.prototype.get=n,c.prototype.has=o,c.prototype.set=i,e.exports=c},89492:(e,t,r)=>{var s=r(58141),a=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(s){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return a.call(t,e)?t[e]:void 0}},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(60687);r(43210);var a=r(4780);function n({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},90200:(e,t,r)=>{var s=r(58141),a=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return s?void 0!==t[e]:a.call(t,e)}},90851:e=>{e.exports=function(e,t){return null==e?void 0:e[t]}},94388:(e,t,r)=>{var s=r(57797);e.exports=function(e){var t=this.__data__,r=s(t,e);return r<0?void 0:t[r][1]}},94735:e=>{"use strict";e.exports=require("events")},95746:(e,t,r)=>{var s=r(15909),a=r(29205),n=r(29508),o=r(61320),i=r(19976);function c(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var s=e[t];this.set(s[0],s[1])}}c.prototype.clear=s,c.prototype.delete=a,c.prototype.get=n,c.prototype.has=o,c.prototype.set=i,e.exports=c},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>c});var s=r(60687);r(43210);var a=r(8730),n=r(24224),o=r(4780);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:t,asChild:r=!1,...n}){let c=r?a.DX:"span";return(0,s.jsx)(c,{"data-slot":"badge",className:(0,o.cn)(i({variant:t}),e),...n})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,6944,1658,9733,40,5021,7042,1997,7116,5820],()=>r(75557));module.exports=s})();