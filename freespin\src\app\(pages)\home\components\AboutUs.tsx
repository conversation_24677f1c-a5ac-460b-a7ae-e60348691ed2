import React from "react";
import Image from "next/image";
import BasicHeader from "./BasicHeader";
;

const AboutUs = () => {
  

  return (
    <section className="bg-black text-white">
      <div className="container mx-auto px-3 md:px-[4rem]  py-6 grid grid-cols-1 md:grid-cols-2">
        <div className="flex justify-center flex-col">
          <BasicHeader text="เกี่ยวกับเรา" className="text-start" />
          {/* <h3 className="font-bold  md:text-4xl sm:text-3xl text-2xl">
            เกี่ยวกับเรา
          </h3> */}
          <div className="my-6 text-[#A9A7B0] space-y-3 text-justify">
            <p>
              จุดหมายปลายทางการพนันและการเดิมพันออนไลน์ชั้นนำของประเทศไทย
              Freespin168
              เป็นหนึ่งในแพลตฟอร์มการพนันออนไลน์ที่ได้รับการจัดอันดับสูงสุดและน่าเชื่อถือที่สุดของประเทศไทย
              ตั้งแต่ปี 2013 เราเป็นผู้นำด้านการเดิมพันคาสิโนและกีฬา
              ด้วยประสบการณ์ความเป็นเลิศกว่า 8 ปี
              ผู้เล่นมากมายทั่วประเทศไทยต่างยกย่อง Freespin168
              ให้เป็นคาสิโนออนไลน์ชั้นนำในประเทศไทยและเป็นจุดหมายปลายทางสำหรับการเดิมพันออนไลน์ที่ปลอดภัย
              น่าตื่นเต้น และเชื่อถือได้
            </p>
            <p>
              แพลตฟอร์มของเรามีเกมคาสิโนให้เลือกมากมาย ทั้งเกมสล็อต แบล็คแจ็ค
              บาคาร่า เกมตกปลา และอื่นๆ อีกมากมาย
              มอบประสบการณ์การเล่นเกมที่เหนือชั้นสำหรับผู้เล่นทุกประเภท
            </p>
            <p>
              เรามีความภูมิใจที่ได้ร่วมมือกับแบรนด์ที่ได้รับการยอมรับมากที่สุดในอุตสาหกรรม
              เช่น SBOBET, 918KISS, MEGA888, BETWOS, WWBET, GCLUB, PUSSY888 และ
              JOKER เพื่อมอบประสบการณ์การเล่นเกมออนไลน์ที่ดีที่สุดให้กับคุณ
            </p>
          </div>
        </div>
        <div className="flex items-center justify-center">
          <Image
            src="/homepage/about.png"
            alt="about"
            width={500}
            height={500}
            className="border-transparent "
          />
        </div>
      </div>
    </section>
  );
};

export default AboutUs;
