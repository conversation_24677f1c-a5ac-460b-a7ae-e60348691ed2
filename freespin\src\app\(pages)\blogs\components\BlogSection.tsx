import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { PublicBlogPost } from "@/client_apis/api/blog";
import { formatTimeAgo } from "@/utils/dateUtils";
import Link from "next/link";

interface BlogsSectionProps {
  posts: PublicBlogPost[];
  onLoadMore?: () => void;
  isLoading?: boolean;
}

export function BlogsSection({
  posts,
  onLoadMore,
  isLoading,
}: BlogsSectionProps) {
  console.log("posts", posts);
  return (
    <div>
      <h2 className="text-2xl font-bold mb-6 text-white">Latest Blogs</h2>
      <div className="flex flex-col  gap-4 w-full">
        {posts.length > 0 ? (
          posts.map((post) => (
            <Link key={post._id} href={`/blogs/${post.slug}`}>
              <Card className="bg-gray-800 border-gray-700 hover:bg-gray-750 p-0 transition-colors cursor-pointer">
                <CardContent className="flex p-0">
                  <div className="relative size-24 flex-shrink-0">
                    <Image
                      src={post.banner || "/blogs/latest1.png"}
                      alt={post.title}
                      fill
                      className="object-cover rounded-l-lg"
                    />
                  </div>
                  <div className="flex-1 flex flex-col">
                    <div className="p-4 pb-2">
                      <h3 className="font-semibold text-white text-sm leading-tight mb-2 line-clamp-2">
                        {post.title}
                      </h3>
                    </div>
                    {/* Border line with gap from edges */}
                    <div className="mx-4 h-[1px]  bg-[#4F5259] "></div>
                    <div className="p-4 pt-2">
                      <div className="flex items-center gap-3 justify-between">
                        <span className="text-xs text-gray-400">
                          {formatTimeAgo(post.publishedAt || post.createdAt)}
                        </span>
                        <Badge
                          variant="outline"
                          className="text-xs border-orange-600 text-orange-400"
                        >
                          {post.categories[0] || "BLOG"}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))
        ) : (
          <div className="text-center text-gray-400 py-8">
            {isLoading ? "Loading posts..." : "No blog posts available."}
          </div>
        )}
        {onLoadMore && (
          <div className="flex items-center justify-center mt-2">
            <Button
              className="bg-[#07BC0C] cursor-pointer text-white hover:bg-[#06A50B] transition-colors"
              onClick={onLoadMore}
              disabled={isLoading}
            >
              {isLoading ? "Loading..." : "Load More"}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
