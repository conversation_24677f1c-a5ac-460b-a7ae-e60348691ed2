import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";
import { ChevronLeft, ChevronRight } from "lucide-react";
import BasicHeader from "../../home/<USER>/BasicHeader";
import { PublicBlogPost } from "@/client_apis/api/blog";
import { formatDate } from "@/utils/dateUtils";
import { useState } from "react";

interface ExploreSectionProps {
  text?: string;
  pagination?: boolean;
  posts: PublicBlogPost[];
  postsPerPage?: number;
}

export function ExploreSection({
  text = "Explore",
  pagination = false,
  posts,
  postsPerPage = 4,
}: ExploreSectionProps) {
  const [currentPage, setCurrentPage] = useState(1);

  // Calculate pagination
  const totalPages = Math.ceil(posts.length / postsPerPage);
  const startIndex = (currentPage - 1) * postsPerPage;
  const endIndex = startIndex + postsPerPage;
  const currentPosts = posts.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  return (
    <div>
      <BasicHeader text={text} className="text-start mb-6" />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {currentPosts.length > 0 ? (
          currentPosts.map((post) => (
            <Link key={post._id} href={`/blogs/${post.slug}`}>
              <Card className="bg-gray-800 border-gray-700 py-0 overflow-hidden hover:bg-gray-750 transition-colors cursor-pointer">
                <div className="relative h-48 w-full">
                  <Image
                    src={post.banner || "/placeholder.svg"}
                    alt={post.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                </div>
                <CardContent className="px-6 py-2">
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-sm text-gray-400">
                      {formatDate(post.publishedAt || post.createdAt)}
                    </span>
                  </div>
                  <h3 className="text-lg font-bold mb-3 text-white leading-tight">
                    {post.title}
                  </h3>
                  {/* <p className="text-gray-300 mb-4 text-sm leading-relaxed">
                    {post.description}
                  </p>
                  <Button
                    variant="outline"
                    className="border-green-600 text-green-400 hover:bg-green-600 hover:text-white transition-colors"
                  >
                    Read More
                  </Button> */}
                </CardContent>
              </Card>
            </Link>
          ))
        ) : (
          <div className="col-span-2 text-center text-gray-400 py-8">
            No posts available.
          </div>
        )}
      </div>
      {pagination && (
        <div className="mt-8 flex items-center justify-center">
          <div className="flex items-center gap-2">
            {/* Previous Button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={handlePrevious}
              disabled={currentPage <= 1}
              className={
                currentPage <= 1
                  ? "bg-[#131925] text-gray-500 cursor-not-allowed rounded-full w-10 h-10 opacity-50"
                  : "bg-[#131925] text-white hover:bg-[#A945F1] rounded-full w-10 h-10 transition-all duration-200"
              }
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>

            {/* Page Numbers */}
            <div className="flex items-center gap-2 mx-4">
              {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                const pageNum = i + 1;
                const isActive = pageNum === currentPage;

                return (
                  <Button
                    key={pageNum}
                    variant="ghost"
                    size="icon"
                    onClick={() => handlePageChange(pageNum)}
                    className={
                      isActive
                        ? "bg-[#A945F1] text-[#310267] hover:bg-[#8B37C7] rounded-full w-10 h-10 font-semibold"
                        : "bg-[#131925] text-white hover:bg-[#A945F1] hover:text-[#310267] transition-all duration-200 rounded-full w-10 h-10"
                    }
                  >
                    {pageNum}
                  </Button>
                );
              })}

              {/* Ellipsis for more pages */}
              {totalPages > 7 && (
                <span className="text-gray-400 px-2">...</span>
              )}
            </div>

            {/* Next Button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={handleNext}
              disabled={currentPage >= totalPages}
              className={
                currentPage >= totalPages
                  ? "bg-[#131925] text-gray-500 cursor-not-allowed rounded-full w-10 h-10 opacity-50"
                  : "bg-[#131925] text-white hover:bg-[#A945F1] rounded-full w-10 h-10 transition-all duration-200"
              }
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
