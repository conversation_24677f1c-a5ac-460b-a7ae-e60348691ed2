import { type NextRequest, NextResponse } from "next/server";
import jwt from "jsonwebtoken";
import connectDB from "@/lib/mongodb";
import User from "@/models/User";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

export interface AuthenticatedRequest extends NextRequest {
  user?: {
    userId: string;
    email: string;
    username: string;
    role: string;
  };
}

// Helper function to get client IP address
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get("x-forwarded-for");
  const realIP = request.headers.get("x-real-ip");
  const cfConnectingIP = request.headers.get("cf-connecting-ip");

  if (forwarded) {
    return forwarded.split(",")[0].trim();
  }
  if (realIP) {
    return realIP;
  }
  if (cfConnectingIP) {
    return cfConnectingIP;
  }

  return "unknown";
}

// Higher-order function that wraps API route handlers with authentication
export function withAuth(
  handler: (
    request: AuthenticatedRequest,
    context: any
  ) => Promise<NextResponse>
) {
  return async (request: NextRequest, context: any) => {
    try {
      // Get token from Authorization header or cookie
      const authHeader = request.headers.get("authorization");
      const token =
        authHeader?.replace("Bearer ", "") ||
        request.cookies.get("session-token")?.value;

      if (!token) {
        return NextResponse.json(
          { success: false, error: "Authentication required" },
          { status: 401 }
        );
      }

      // Verify JWT token
      const decoded = jwt.verify(token, JWT_SECRET) as any;

      // Optional: Verify user still exists in database
      await connectDB();
      const user = await User.findById(decoded.userId).select(
        "_id email username role isActive"
      );

      if (!user || !user.isActive) {
        return NextResponse.json(
          { success: false, error: "User not found or inactive" },
          { status: 401 }
        );
      }
      // Add user info to request object
      (request as AuthenticatedRequest).user = {
        userId: user._id.toString(),
        email: user.email,
        username: user.username,
        role: user.role,
      };

      // Call the original handler with authenticated request
      return handler(request as AuthenticatedRequest, context);
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        return NextResponse.json(
          { success: false, error: "Invalid authentication token" },
          { status: 401 }
        );
      }

      console.error("Authentication middleware error:", error);
      return NextResponse.json(
        { success: false, error: "Authentication failed" },
        { status: 500 }
      );
    }
  };
}

// Higher-order function that wraps API route handlers with role-based authorization
export function withRole(allowedRoles: string[]) {
  return (
    handler: (
      request: AuthenticatedRequest,
      context: any
    ) => Promise<NextResponse>
  ) =>
    withAuth(async (request: AuthenticatedRequest, context: any) => {
      const user = request.user!;

      if (!allowedRoles.includes(user.role)) {
        return NextResponse.json(
          {
            success: false,
            error: `Access denied. Required roles: ${allowedRoles.join(
              ", "
            )}. Your role: ${user.role}`,
          },
          { status: 403 }
        );
      }

      // User has required role, proceed with handler
      return handler(request, context);
    });
}

// Middleware for admin-only routes
export const withAdmin = withRole(["admin"]);

// Middleware for admin and editor routes
export const withEditor = withRole(["admin", "editor"]);

// Middleware for admin, editor, and author routes
export const withAuthor = withRole(["admin", "editor", "author"]);

// Optional: Rate limiting middleware
export function withRateLimit(
  maxRequests = 100,
  windowMs: number = 15 * 60 * 1000
) {
  const requests = new Map<string, { count: number; resetTime: number }>();

  return (
      handler: (request: NextRequest, context: any) => Promise<NextResponse>
    ) =>
    async (request: NextRequest, context: any) => {
      const ip = getClientIP(request);
      const now = Date.now();

      // Clean up old entries
      for (const [key, value] of requests.entries()) {
        if (now > value.resetTime) {
          requests.delete(key);
        }
      }

      // Get or create request count for this IP
      const requestData = requests.get(ip) || {
        count: 0,
        resetTime: now + windowMs,
      };

      if (now > requestData.resetTime) {
        // Reset window
        requestData.count = 1;
        requestData.resetTime = now + windowMs;
      } else {
        requestData.count++;
      }

      requests.set(ip, requestData);

      // Check if rate limit exceeded
      if (requestData.count > maxRequests) {
        return NextResponse.json(
          {
            success: false,
            error: "Rate limit exceeded. Please try again later.",
            retryAfter: Math.ceil((requestData.resetTime - now) / 1000),
          },
          { status: 429 }
        );
      }

      return handler(request, context);
    };
}

// Logging middleware
export function withLogging(
  handler: (request: NextRequest, context: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context: any) => {
    const start = Date.now();
    const method = request.method;
    const url = request.url;

    console.log(`[${new Date().toISOString()}] ${method} ${url} - Started`);

    try {
      const response = await handler(request, context);
      const duration = Date.now() - start;

      console.log(
        `[${new Date().toISOString()}] ${method} ${url} - ${
          response.status
        } (${duration}ms)`
      );

      return response;
    } catch (error) {
      const duration = Date.now() - start;
      console.error(
        `[${new Date().toISOString()}] ${method} ${url} - Error (${duration}ms):`,
        error
      );
      throw error;
    }
  };
}
