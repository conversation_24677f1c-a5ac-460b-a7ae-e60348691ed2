"use client";

import { motion } from "framer-motion";
import React from "react";

const PulseCircle: React.FC = () => {
  const numCircles = 10;
  const duration = 6;

  const pulseVariants = {
    animate: (i: number) => ({
      scale: [0, 4],
      opacity: [1, 0],
      transition: {
        duration,
        ease: "easeInOut",
        repeat: Infinity,
        repeatType: "loop",
        delay: (i * duration) / numCircles,
      },
    }),
  };

  return (
    <div className="relative w-full h-full flex items-center justify-center overflow-hidden">
      {[...Array(numCircles)].map((_, i) => (
        <motion.div
          key={i}
          custom={i}
          // @ts-expect-error: Framer Motion's custom prop type is not compatible with React's FC type
          variants={pulseVariants}
          animate="animate"
          className="absolute rounded-full"
          style={{
            width: 100,
            height: 100,
            transformOrigin: "center",
            transform: "translate(-50%, -50%)",
            boxShadow: "0 0 0px 1px rgba(169, 69, 241)",
            willChange: "transform, opacity",
          }}
        />
      ))}

      {/* Center static image */}
      <div
        className="z-10 bg-cover bg-no-repeat bg-center"
        style={{
          width: 350,
          height: 350,
          backgroundImage: "url(/homepage/games.png)",
        }}
      />
    </div>
  );
};

export default PulseCircle;
