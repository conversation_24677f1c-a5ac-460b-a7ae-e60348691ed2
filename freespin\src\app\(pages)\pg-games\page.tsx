import Banner from "@/app/shared/Banner";
import Image from "next/image";
import React from "react";
import BasicHeader from "../home/<USER>/BasicHeader";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "เล่น PG Online Games | สล็อตและเกมคาสิโนชั้นนำ – FreeSpin168",
  alternates: {
    canonical: "https://www.freespin168.asia/pg-games",
  },
  description:
    "เพลิดเพลินกับเกม PG online ที่ดีที่สุด รวมถึงสล็อตยอดนิยมและเกมคาสิโนสด เล่นตอนนี้เพื่อโบนัสที่น่าตื่นเต้นและชัยชนะครั้งใหญ่ที่ FreeSpin168.asia",
  keywords: ["pg online games", "เกม PG", "สล็อตออนไลน์"],
};

const images = [
  "/pg/img1.png",
  "/pg/img2.png",
  "/pg/img3.png",
  "/pg/img4.png",
  "/pg/img5.png",
  "/pg/img6.png",
  "/pg/img7.png",
  "/pg/img8.png",
  "/pg/img9.png",
  "/pg/img10.png",
];

const Page = () => {
  return (
    <main className="bg-black ">
      <Banner
        url="/jili/background.png"
        secondText="เกม PG"
        secondTitleText="เกม"
      />
      <div className=" h-full w-full ">
        <div
          className="bg-cover bg-center py-10"
          style={{
            backgroundImage: "url('/jili/background2.png')",
          }}
        >
          <div className=" container mx-auto px-3 md:px-[4rem] place-items-center grid grid-cols-1 md:grid-cols-3 pb-[8rem] gap-4 md:gap-4 lg:gap-8 lg:grid-cols-5 ">
            {images.map((item, index) => (
              <Image
                key={index}
                src={item}
                alt={`เกม PG ${index + 1}`}
                height={200}
                width={300}
              />
            ))}
          </div>
        </div>
      </div>
      <div className="container mx-auto px-3 md:px-[4rem] pb-[4rem]  gap-[4rem]  grid grid-cols-1 md:grid-cols-2">
        <div>
          <div className="pb-4">
            <BasicHeader
              text="ดำดิ่งสู่โลกแห่งเกม PG"
              className="text-start pb-4 "
            />
            <p className="jakarta text-sm md:text-base text-[#A4A3A3]">
              ก้าวเข้าสู่อาณาจักรแห่งความตื่นเต้นและความบันเทิงที่ไม่มีที่สิ้นสุดกับเกม
              PG ที่ FreeSpin168 ด้วยกราฟิกที่น่าทึ่ง ธีมที่น่าดึงดูด
              และโอกาสในการชนะรางวัลใหญ่
              เกมเหล่านี้ได้รับการออกแบบมาเพื่อมอบประสบการณ์การเล่นเกมระดับพรีเมียมให้กับผู้เล่นทุกระดับ
            </p>
          </div>
          <div className="pb-4">
            <BasicHeader
              text="ทำไมต้องเลือกเกม PG"
              className="text-start pb-4 "
            />
            <p className="jakarta text-sm md:text-base text-[#A4A3A3]">
              การเลือก FreeSpin168 สำหรับการผจญภัยในเกม PG
              ของคุณหมายถึงการเข้าถึงคอลเลกชันเกมที่ได้รับการคัดสรรอย่างพิถีพิถันซึ่งมอบทั้งความบันเทิงและโอกาสในการชนะรางวัลใหญ่
              เรามุ่งมั่นที่จะมอบประสบการณ์การเล่นเกมที่ปลอดภัย ยุติธรรม
              และน่าตื่นเต้นให้กับผู้เล่นทุกคน
            </p>
            <ul className="jakarta text-sm md:text-base text-[#A4A3A3] list-disc pl-4">
              <li>เกมที่สวยงามตระการตา</li>
              <li>แพลตฟอร์มที่ปลอดภัยและน่าเชื่อถือ</li>
              <li>การเล่นที่ยุติธรรมรับประกัน</li>
              <li>ความตื่นเต้นอย่างต่อเนื่อง</li>
            </ul>
          </div>
          <div className="pb-4">
            <BasicHeader
              text="หมุนด้วยความได้เปรียบ"
              className="text-start pb-4 "
            />
            <p className="jakarta text-sm md:text-base text-[#A4A3A3]">
              พร้อมที่จะยกระดับประสบการณ์การเล่นเกมของคุณหรือยัง? สมัครกับ
              FreeSpin168
              วันนี้และรับโบนัสต้อนรับพิเศษเมื่อคุณเริ่มการเดินทางในเกม PG
              ของคุณ ด้วยโปรโมชั่นประจำ การสนับสนุนลูกค้าตลอด 24 ชั่วโมงทุกวัน
              และการอัปเดตเกมใหม่อย่างสม่ำเสมอ
              เราพร้อมที่จะทำให้คุณเพลิดเพลินไปกับประสบการณ์การเล่นเกมที่ดีที่สุด
            </p>
          </div>
        </div>
        <Image
          src="/pg/play.png"
          alt="ภาพการเล่นเกม"
          height={500}
          width={700}
        />
      </div>
    </main>
  );
};

export default Page;
