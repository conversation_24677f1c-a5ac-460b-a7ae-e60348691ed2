"use client";
import { motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";

const ProximityText = () => {
  const textRef = useRef<HTMLDivElement>(null);
  const [isClose, setIsClose] = useState(false);
  const radius = 150;

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!textRef.current) return;

      const rect = textRef.current.getBoundingClientRect();
      const textX = rect.left + rect.width / 2;
      const textY = rect.top + rect.height / 2;

      const dist = Math.hypot(e.clientX - textX, e.clientY - textY);
      setIsClose(dist < radius);
    };

    window.addEventListener("mousemove", handleMouseMove);
    return () => window.removeEventListener("mousemove", handleMouseMove);
  }, []);

  return (
    <motion.div
      ref={textRef}
      animate={{ color: isClose ? "#ffffff" : "#555555" }}
      transition={{ duration: 0.2 }}
      style={{
        fontSize: "3rem",
        fontWeight: "bold",
        fontFamily: "sans-serif",
        textAlign: "center",
        height: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        background: "#111",
      }}
    >
      Hover Me Lorem ipsum dolor sit amet consectetur adipisicing elit.
      Repudiandae facilis iure nihil eaque neque vel consequuntur error quaerat
      numquam facere aut necessitatibus harum fugiat, officia autem nisi
      voluptate enim praesentium?
    </motion.div>
  );
};

export default ProximityText;
