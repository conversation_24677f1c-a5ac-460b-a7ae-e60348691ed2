(()=>{var e={};e.id=1946,e.ids=[1946],e.modules={163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,t,r)=>{let{createProxy:n}=r(39844);e.exports=n("D:\\Web Studio Nepal\\FreeSpin168\\freespin\\node_modules\\next\\dist\\client\\app-dir\\link.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(75986),i=r(8974);function s(...e){return(0,i.QP)((0,n.$)(e))}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31718:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.t.bind(r,49603,23))},33361:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(37413),i=r(75986);r(61120);let s=({className:e,text:t})=>(0,n.jsx)("h2",{className:(0,i.A)("text-2xl font-bold text-center text-white",e),children:t})},33873:e=>{"use strict";e.exports=require("path")},45573:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>f,tree:()=>d});var n=r(65239),i=r(48088),s=r(88170),a=r.n(s),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["(pages)",{children:["blogs",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,90029)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\(pages)\\blogs\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,35299)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\(pages)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\(pages)\\blogs\\[id]\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(pages)/blogs/[id]/page",pathname:"/blogs/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},48976:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},55530:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(56037),i=r.n(n);let s=new n.Schema({title:{type:String,required:[!0,"Post title is required"],trim:!0,maxlength:[1e3,"Title cannot exceed 200 characters"]},slug:{type:String,trim:!0,lowercase:!0},canonicalUrl:{type:String,trim:!0,validate:{validator:e=>!e||/^https?:\/\/.+/.test(e),message:"Canonical URL must be a valid URL"}},existingUrl:{type:Boolean,default:!1},content:{type:String,required:[!0,"Post content is required"]},excerpt:{type:String,trim:!0,maxlength:[1e3,"Excerpt cannot exceed 300 characters"]},description:{type:String},author:{type:String,trim:!0},isBlog:{type:Boolean,default:!0},categories:[{type:i().Schema.Types.ObjectId,ref:"Category"}],tags:[{type:String,trim:!0,lowercase:!0}],metaTitle:{type:String,required:[!0,"Meta title is required"],maxlength:[1e3,"Meta title cannot exceed 60 characters"]},metaDescription:{type:String,required:[!0,"Meta description is required"],maxlength:[1e3,"Meta description cannot exceed 160 characters"]},metaKeywords:{type:String,required:[!0,"Meta keywords are required"],maxlength:[1e3,"Meta keywords cannot exceed 200 characters"]},banner:{type:String,required:[!0,"Banner image is required"],trim:!0},status:{type:String,enum:["draft","published","archived"],default:"draft"},isPublished:{type:Boolean,default:!1},publishedAt:{type:Date,default:null},views:{type:Number,default:0},readTime:{type:Number,default:1},isTopNews:{type:Boolean,default:!1}},{timestamps:!0});s.pre("save",function(e){if(this.isModified("title")&&!this.slug&&(this.slug=this.title.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,"")),this.isModified("content")){let e=(this.content||"").split(/\s+/).length;this.readTime=Math.ceil(e/200)}this.isModified("status")&&"published"===this.status&&!this.publishedAt&&(this.publishedAt=new Date,this.isPublished=!0),e()}),s.index({categories:1}),s.index({tags:1}),s.index({status:1}),s.index({isPublished:1}),s.index({publishedAt:-1}),s.index({"banner.title":1}),s.index({title:"text",content:"text",metaTitle:"text",metaDescription:"text"});let a=i().models.Post||i().model("Post",s)},56037:e=>{"use strict";e.exports=require("mongoose")},59406:function(e){e.exports=function(){"use strict";var e="millisecond",t="second",r="minute",n="hour",i="week",s="month",a="quarter",o="year",l="date",d="Invalid Date",u=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,c=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,f=function(e,t,r){var n=String(e);return!n||n.length>=t?e:""+Array(t+1-n.length).join(r)+e},p="en",h={};h[p]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||t[0])+"]"}};var g="$isDayjsObject",m=function(e){return e instanceof y||!(!e||!e[g])},b=function e(t,r,n){var i;if(!t)return p;if("string"==typeof t){var s=t.toLowerCase();h[s]&&(i=s),r&&(h[s]=r,i=s);var a=t.split("-");if(!i&&a.length>1)return e(a[0])}else{var o=t.name;h[o]=t,i=o}return!n&&i&&(p=i),i||!n&&p},v=function(e,t){if(m(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new y(r)},x={s:f,z:function(e){var t=-e.utcOffset(),r=Math.abs(t);return(t<=0?"+":"-")+f(Math.floor(r/60),2,"0")+":"+f(r%60,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var n=12*(r.year()-t.year())+(r.month()-t.month()),i=t.clone().add(n,s),a=r-i<0,o=t.clone().add(n+(a?-1:1),s);return+(-(n+(r-i)/(a?i-o:o-i))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(d){return({M:s,y:o,w:i,d:"day",D:l,h:n,m:r,s:t,ms:e,Q:a})[d]||String(d||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};x.l=b,x.i=m,x.w=function(e,t){return v(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var y=function(){function f(e){this.$L=b(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[g]=!0}var p=f.prototype;return p.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if(x.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var n=t.match(u);if(n){var i=n[2]-1||0,s=(n[7]||"0").substring(0,3);return r?new Date(Date.UTC(n[1],i,n[3]||1,n[4]||0,n[5]||0,n[6]||0,s)):new Date(n[1],i,n[3]||1,n[4]||0,n[5]||0,n[6]||0,s)}}return new Date(t)}(e),this.init()},p.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},p.$utils=function(){return x},p.isValid=function(){return this.$d.toString()!==d},p.isSame=function(e,t){var r=v(e);return this.startOf(t)<=r&&r<=this.endOf(t)},p.isAfter=function(e,t){return v(e)<this.startOf(t)},p.isBefore=function(e,t){return this.endOf(t)<v(e)},p.$g=function(e,t,r){return x.u(e)?this[t]:this.set(r,e)},p.unix=function(){return Math.floor(this.valueOf()/1e3)},p.valueOf=function(){return this.$d.getTime()},p.startOf=function(e,a){var d=this,u=!!x.u(a)||a,c=x.p(e),f=function(e,t){var r=x.w(d.$u?Date.UTC(d.$y,t,e):new Date(d.$y,t,e),d);return u?r:r.endOf("day")},p=function(e,t){return x.w(d.toDate()[e].apply(d.toDate("s"),(u?[0,0,0,0]:[23,59,59,999]).slice(t)),d)},h=this.$W,g=this.$M,m=this.$D,b="set"+(this.$u?"UTC":"");switch(c){case o:return u?f(1,0):f(31,11);case s:return u?f(1,g):f(0,g+1);case i:var v=this.$locale().weekStart||0,y=(h<v?h+7:h)-v;return f(u?m-y:m+(6-y),g);case"day":case l:return p(b+"Hours",0);case n:return p(b+"Minutes",1);case r:return p(b+"Seconds",2);case t:return p(b+"Milliseconds",3);default:return this.clone()}},p.endOf=function(e){return this.startOf(e,!1)},p.$set=function(i,a){var d,u=x.p(i),c="set"+(this.$u?"UTC":""),f=((d={}).day=c+"Date",d[l]=c+"Date",d[s]=c+"Month",d[o]=c+"FullYear",d[n]=c+"Hours",d[r]=c+"Minutes",d[t]=c+"Seconds",d[e]=c+"Milliseconds",d)[u],p="day"===u?this.$D+(a-this.$W):a;if(u===s||u===o){var h=this.clone().set(l,1);h.$d[f](p),h.init(),this.$d=h.set(l,Math.min(this.$D,h.daysInMonth())).$d}else f&&this.$d[f](p);return this.init(),this},p.set=function(e,t){return this.clone().$set(e,t)},p.get=function(e){return this[x.p(e)]()},p.add=function(e,a){var l,d=this;e=Number(e);var u=x.p(a),c=function(t){var r=v(d);return x.w(r.date(r.date()+Math.round(t*e)),d)};if(u===s)return this.set(s,this.$M+e);if(u===o)return this.set(o,this.$y+e);if("day"===u)return c(1);if(u===i)return c(7);var f=((l={})[r]=6e4,l[n]=36e5,l[t]=1e3,l)[u]||1,p=this.$d.getTime()+e*f;return x.w(p,this)},p.subtract=function(e,t){return this.add(-1*e,t)},p.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||d;var n=e||"YYYY-MM-DDTHH:mm:ssZ",i=x.z(this),s=this.$H,a=this.$m,o=this.$M,l=r.weekdays,u=r.months,f=r.meridiem,p=function(e,r,i,s){return e&&(e[r]||e(t,n))||i[r].slice(0,s)},h=function(e){return x.s(s%12||12,e,"0")},g=f||function(e,t,r){var n=e<12?"AM":"PM";return r?n.toLowerCase():n};return n.replace(c,function(e,n){return n||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return x.s(t.$y,4,"0");case"M":return o+1;case"MM":return x.s(o+1,2,"0");case"MMM":return p(r.monthsShort,o,u,3);case"MMMM":return p(u,o);case"D":return t.$D;case"DD":return x.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return p(r.weekdaysMin,t.$W,l,2);case"ddd":return p(r.weekdaysShort,t.$W,l,3);case"dddd":return l[t.$W];case"H":return String(s);case"HH":return x.s(s,2,"0");case"h":return h(1);case"hh":return h(2);case"a":return g(s,a,!0);case"A":return g(s,a,!1);case"m":return String(a);case"mm":return x.s(a,2,"0");case"s":return String(t.$s);case"ss":return x.s(t.$s,2,"0");case"SSS":return x.s(t.$ms,3,"0");case"Z":return i}return null}(e)||i.replace(":","")})},p.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},p.diff=function(e,l,d){var u,c=this,f=x.p(l),p=v(e),h=(p.utcOffset()-this.utcOffset())*6e4,g=this-p,m=function(){return x.m(c,p)};switch(f){case o:u=m()/12;break;case s:u=m();break;case a:u=m()/3;break;case i:u=(g-h)/6048e5;break;case"day":u=(g-h)/864e5;break;case n:u=g/36e5;break;case r:u=g/6e4;break;case t:u=g/1e3;break;default:u=g}return d?u:x.a(u)},p.daysInMonth=function(){return this.endOf(s).$D},p.$locale=function(){return h[this.$L]},p.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),n=b(e,t,!0);return n&&(r.$L=n),r},p.clone=function(){return x.w(this.$d,this)},p.toDate=function(){return new Date(this.valueOf())},p.toJSON=function(){return this.isValid()?this.toISOString():null},p.toISOString=function(){return this.$d.toISOString()},p.toString=function(){return this.$d.toUTCString()},f}(),_=y.prototype;return v.prototype=_,[["$ms",e],["$s",t],["$m",r],["$H",n],["$W","day"],["$M",s],["$y",o],["$D",l]].forEach(function(e){_[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),v.extend=function(e,t){return e.$i||(e(t,y,v),e.$i=!0),v},v.locale=b,v.isDayjs=m,v.unix=function(e){return v(1e3*e)},v.en=h[p],v.Ls=h,v.p={},v}()},62765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return i}});let n=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function i(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70899:(e,t,r)=>{"use strict";function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,a.isNextRouterError)(t)||(0,s.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,o.isDynamicPostpone)(t)||(0,i.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(68388),i=r(52637),s=r(51846),a=r(31162),o=r(84971),l=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(56037),i=r.n(n);let s=process.env.MONGODB_URI;if(!s)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global?.mongoose;a||(a=global.mongoose={conn:null,promise:null});let o=async function(){if(a.conn)return a.conn;a.promise||(a.promise=i().connect(s,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},78963:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>a,Zp:()=>s});var n=r(37413);r(61120);var i=r(10974);function s({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function a({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",e),...t})}},79551:e=>{"use strict";e.exports=require("url")},86897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return a},getRedirectStatusCodeFromError:function(){return c},getRedirectTypeFromError:function(){return u},getURLFromRedirectError:function(){return d},permanentRedirect:function(){return l},redirect:function(){return o}});let n=r(52836),i=r(49026),s=r(19121).actionAsyncStorage;function a(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let s=Object.defineProperty(Error(i.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return s.digest=i.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",s}function o(e,t){var r;throw null!=t||(t=(null==s||null==(r=s.getStore())?void 0:r.isAction)?i.RedirectType.push:i.RedirectType.replace),a(e,t,n.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=i.RedirectType.replace),a(e,t,n.RedirectStatusCode.PermanentRedirect)}function d(e){return(0,i.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function u(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function c(e){if(!(0,i.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},90029:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>D,generateMetadata:()=>E});var n=r(37413),i=r(61120),s=r(53384),a=r(97576);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var l=function(e){let t=function(e){let t=i.forwardRef((e,t)=>{let{children:r,...n}=e;if(i.isValidElement(r)){var s;let e,a,l=(s=r,(a=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(a=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),d=function(e,t){let r={...t};for(let n in t){let i=e[n],s=t[n];/^on[A-Z]/.test(n)?i&&s?r[n]=(...e)=>{let t=s(...e);return i(...e),t}:i&&(r[n]=i):"style"===n?r[n]={...i,...s}:"className"===n&&(r[n]=[i,s].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==i.Fragment&&(d.ref=t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}(t,l):l),i.cloneElement(r,d)}return i.Children.count(r)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=i.forwardRef((e,r)=>{let{children:s,...a}=e,o=i.Children.toArray(s),l=o.find(u);if(l){let e=l.props.children,s=o.map(t=>t!==l?t:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...a,ref:r,children:i.isValidElement(e)?i.cloneElement(e,void 0,s):null})}return(0,n.jsx)(t,{...a,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}("Slot"),d=Symbol("radix.slottable");function u(e){return i.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}var c=r(75986);let f=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,p=c.$,h=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return p(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:s}=t,a=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==s?void 0:s[e];if(null===t)return null;let a=f(t)||f(n);return i[e][a]}),o=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return p(e,a,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...o}[t]):({...s,...o})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)};var g=r(10974);let m=h("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function b({className:e,variant:t,asChild:r=!1,...i}){return(0,n.jsx)(r?l:"span",{"data-slot":"badge",className:(0,g.cn)(m({variant:t}),e),...i})}var v=r(78963);let x=h("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function y({className:e,variant:t,size:r,asChild:i=!1,...s}){return(0,n.jsx)(i?l:"button",{"data-slot":"button",className:(0,g.cn)(x({variant:t,size:r,className:e})),...s})}var _=r(59406),j=r.n(_);function w(e){return j()(e).format("DD/MM/YYYY")}var O=r(4536),$=r.n(O);function R({posts:e,onLoadMore:t,isLoading:r}){return console.log("posts",e),(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"text-2xl font-bold mb-6 text-white",children:"Latest Blogs"}),(0,n.jsxs)("div",{className:"flex flex-col  gap-4 w-full",children:[e.length>0?e.map(e=>(0,n.jsx)($(),{href:`/blogs/${e.slug}`,children:(0,n.jsx)(v.Zp,{className:"bg-gray-800 border-gray-700 hover:bg-gray-750 p-0 transition-colors cursor-pointer",children:(0,n.jsxs)(v.Wu,{className:"flex p-0",children:[(0,n.jsx)("div",{className:"relative size-24 flex-shrink-0",children:(0,n.jsx)(s.default,{src:e.banner||"/blogs/latest1.png",alt:e.title,fill:!0,className:"object-cover rounded-l-lg"})}),(0,n.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,n.jsx)("div",{className:"p-4 pb-2",children:(0,n.jsx)("h3",{className:"font-semibold text-white text-sm leading-tight mb-2 line-clamp-2",children:e.title})}),(0,n.jsx)("div",{className:"mx-4 h-[1px]  bg-[#4F5259] "}),(0,n.jsx)("div",{className:"p-4 pt-2",children:(0,n.jsxs)("div",{className:"flex items-center gap-3 justify-between",children:[(0,n.jsx)("span",{className:"text-xs text-gray-400",children:function(e){let t=j()(),r=j()(e),n=t.diff(r,"day");if(0===n)return"Today";if(1===n)return"1 day ago";if(n<7)return`${n} days ago`;if(n<30){let e=Math.floor(n/7);return 1===e?"1 week ago":`${e} weeks ago`}if(n<365){let e=Math.floor(n/30);return 1===e?"1 month ago":`${e} months ago`}else{let e=Math.floor(n/365);return 1===e?"1 year ago":`${e} years ago`}}(e.publishedAt||e.createdAt)}),(0,n.jsx)(b,{variant:"outline",className:"text-xs border-orange-600 text-orange-400",children:e.categories[0]||"BLOG"})]})})]})]})})},e._id)):(0,n.jsx)("div",{className:"text-center text-gray-400 py-8",children:r?"Loading posts...":"No blog posts available."}),t&&(0,n.jsx)("div",{className:"flex items-center justify-center mt-2",children:(0,n.jsx)(y,{className:"bg-[#07BC0C] cursor-pointer text-white hover:bg-[#06A50B] transition-colors",onClick:t,disabled:r,children:r?"Loading...":"Load More"})})]})]})}var N=r(33361);function S({posts:e,text:t="Related Blogs"}){return(0,n.jsxs)("div",{children:[(0,n.jsx)(N.A,{text:t}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6",children:e.length>0?e.map(e=>(0,n.jsx)($(),{href:`/blogs/${e._id}`,children:(0,n.jsxs)(v.Zp,{className:"bg-gray-800 border-gray-700 overflow-hidden hover:bg-gray-750 transition-colors cursor-pointer group flex flex-col h-full p-0",children:[(0,n.jsxs)("div",{className:"relative flex-1 w-full min-h-[250px]",children:[(0,n.jsx)(s.default,{src:e.banner||"/placeholder.svg",alt:e.title,fill:!0,className:"object-cover"}),(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"})]}),(0,n.jsxs)("div",{className:"p-4 mt-auto",children:[(0,n.jsx)("div",{className:"mb-2",children:(0,n.jsx)("time",{className:"text-sm text-gray-400 font-medium",children:w(e.publishedAt||e.createdAt)})}),(0,n.jsx)("h3",{className:"text-lg font-bold text-white leading-tight line-clamp-2 group-hover:text-gray-200 transition-colors",children:e.title}),e.categories&&e.categories.length>0&&(0,n.jsx)("div",{className:"mt-2",children:(0,n.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-orange-600/20 text-orange-400 rounded-md",children:e.categories[0]})})]})]})},e._id)):(0,n.jsx)("div",{className:"col-span-2 text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-400",children:"No related blogs found in this category."})})})]})}var M=r(75745),P=r(55530);async function E({params:e}){let{id:t}=await e,r=`https://www.freespin168.asia/blogs/${t}`;try{await (0,M.A)();let e=await P.A.findOne({slug:t,status:"published",isPublished:!0,isBlog:!0}).lean();if(!e)return{title:"Blog Post Not Found",alternates:{canonical:r},description:"The requested blog post could not be found."};return{title:e.metaTitle||e.title,description:e.metaDescription||e.excerpt||e.description,alternates:{canonical:r},openGraph:{title:e.metaTitle||e.title,description:e.metaDescription||e.excerpt||e.description,images:e.banner?[{url:e.banner}]:[],type:"article",publishedTime:e.publishedAt?.toISOString()},twitter:{card:"summary_large_image",title:e.metaTitle||e.title,description:e.metaDescription||e.excerpt||e.description,images:e.banner?[e.banner]:[]}}}catch(e){return console.error("Error generating metadata:",e),{title:"Blog Post",description:"Read our latest blog post.",alternates:{canonical:r}}}}let D=async({params:e})=>{let{id:t}=await e;try{await (0,M.A)();let e=await P.A.findOne({slug:t,status:"published",isPublished:!0,isBlog:!0}).populate("categories","name description").lean();e||(0,a.notFound)();let r=await P.A.find({slug:{$ne:t},status:"published",isPublished:!0,isBlog:!0,categories:{$in:e.categories||[]}}).populate("categories","name description").sort({publishedAt:-1,createdAt:-1}).limit(6).lean();console.log("=== RELATED POSTS DEBUG ==="),console.log("Current post categories:",e.categories),console.log("Related posts found:",r.length);let i=await P.A.find({slug:{$ne:t},status:"published",isPublished:!0,isBlog:!0}).populate("categories","name description").sort({publishedAt:-1,createdAt:-1}).limit(6).lean(),o=e=>e.map(e=>({_id:e._id.toString(),title:e.title,description:e.excerpt||e.description||e.content?.substring(0,200)||"",banner:e.banner||"",slug:e.slug||"",categories:e.categories?.map(e=>e.name||e)||[],tags:e.tags||[],publishedAt:e.publishedAt?.toISOString()||e.createdAt?.toISOString()||"",createdAt:e.createdAt?.toISOString()||"",readTime:e.readTime||5,views:e.views||0,metaTitle:e.metaTitle||e.title||"",metaDescription:e.metaDescription||e.excerpt||e.description||""})),l=o(r),d=o(i),u={...e,categories:e.categories?.map(e=>({name:e.name||e,description:e.description||""}))||[]};return(0,n.jsx)("div",{className:"min-h-screen bg-black text-white",children:(0,n.jsx)("div",{className:"container mx-auto px-4 pt-24 py-8",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12",children:[(0,n.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,n.jsxs)("div",{className:"relative w-full space-y-4",children:[u.banner&&(0,n.jsx)("div",{className:"h-[400px] rounded-3xl w-full z-0 overflow-hidden",children:(0,n.jsx)(s.default,{src:u.banner,alt:u.title,width:800,height:400,className:"w-full h-full object-cover",priority:!0})}),(0,n.jsxs)("article",{className:"prose prose-invert prose-lg max-w-none",children:[(0,n.jsx)("div",{className:"mb-6",children:(0,n.jsx)("time",{className:"text-gray-400 text-sm font-medium",children:w(u.publishedAt?.toISOString()||u.createdAt?.toISOString())})}),(0,n.jsx)("h1",{className:"text-3xl font-bold mb-4 text-white leading-tight",children:u.title}),u.categories&&u.categories.length>0&&(0,n.jsx)("div",{className:"flex items-center gap-2 mb-6",children:u.categories.map((e,t)=>(0,n.jsx)(b,{variant:"outline",className:"border-orange-600 text-orange-400",children:e.name},t))}),(0,n.jsx)("div",{className:"prose prose-invert prose-lg max-w-none text-gray-300 leading-relaxed text-justify",dangerouslySetInnerHTML:{__html:u.content||u.description||""}}),u.tags&&u.tags.length>0&&(0,n.jsxs)("div",{className:"mt-8 pt-8 border-t border-gray-700",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Tags"}),(0,n.jsx)("div",{className:"flex flex-wrap gap-2",children:u.tags.map((e,t)=>(0,n.jsxs)(b,{variant:"secondary",className:"bg-gray-700 text-gray-300",children:["#",e]},t))})]})]})]}),(0,n.jsx)("div",{className:"w-full h-[1px] bg-gray-400"}),(0,n.jsx)(S,{text:"Related Blogs",posts:l})]}),(0,n.jsx)("div",{className:"lg:col-span-1",children:(0,n.jsx)(R,{posts:d})})]})})})}catch(e){console.error("Error fetching blog post:",e),(0,a.notFound)()}}},92398:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.t.bind(r,46533,23))},97576:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return u},RedirectType:function(){return i.RedirectType},forbidden:function(){return a.forbidden},notFound:function(){return s.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return o.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let n=r(86897),i=r(49026),s=r(62765),a=r(48976),o=r(70899),l=r(163);class d extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class u extends URLSearchParams{append(){throw new d}delete(){throw new d}set(){throw new d}sort(){throw new d}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4447,6944,1658,9733,5036,6343,8974,8378],()=>r(45573));module.exports=n})();