{"name": "freespin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint", "sitemap": "next-sitemap", "remove-intl": "node scripts/remove-intl.js"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@lexical/code": "^0.32.1", "@lexical/history": "^0.32.1", "@lexical/html": "^0.32.1", "@lexical/link": "^0.32.1", "@lexical/list": "^0.32.1", "@lexical/markdown": "^0.32.1", "@lexical/plain-text": "^0.32.1", "@lexical/react": "^0.32.1", "@lexical/rich-text": "^0.32.1", "@lexical/table": "^0.32.1", "@lexical/utils": "^0.32.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-visually-hidden": "^1.2.3", "@uiw/react-md-editor": "^4.0.7", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "framer-motion": "^12.16.0", "jsonwebtoken": "^9.0.2", "lexical": "^0.32.1", "lodash": "^4.17.21", "lucide-react": "^0.514.0", "minio": "^8.0.5", "mongoose": "^8.15.2", "next": "15.3.3", "next-sitemap": "^4.2.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-slick": "^0.30.3", "react-toastify": "^11.0.5", "slick-carousel": "^1.8.1", "slugify": "^1.6.6", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^3.25.64"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.17", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-slick": "^0.23.13", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}