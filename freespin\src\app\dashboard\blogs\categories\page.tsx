import { getCategoriesAction, createCategoryAction } from "../actions";
import { get } from "lodash";
import CategoryList from "./components/CategoryList";
import { revalidatePath } from "next/cache";

const page = async () => {
  // Use server action for authenticated admin access
  const response = await getCategoriesAction();
  console.log("resposne for category", response);
  const categoriesData = get(response, "data.categories", []);

  // Transform the data to match the expected interface
  const transformedCategories = categoriesData.map((category: any) => ({
    _id: category._id,
    name: category.name,
    slug: category.name.toLowerCase().replace(/\s+/g, "-"), // Generate slug from name
    description: category.description || "",
  }));

  return <CategoryList categories={transformedCategories} />;
};

export default page;
