{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|.*\\.).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|.*\\.).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "FrNNN+HBdWeLONyYct2yC9KZ/WQp8bWC8ijse9tJFWk=", "__NEXT_PREVIEW_MODE_ID": "df7c34b8aea0ed10b333b24525077b50", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7f112e9738a8d7df3f23a3610374262ac227aa4126a32d832b0ef94fac66f6c6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e6007d86de565a80d2d738077a5455e462f7328e6a7fb5c46b387109a910f633"}}}, "instrumentation": null, "functions": {}}