{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|.*\\.).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|.*\\.).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "FrNNN+HBdWeLONyYct2yC9KZ/WQp8bWC8ijse9tJFWk=", "__NEXT_PREVIEW_MODE_ID": "5cbc5349f45509b4360efc121077cab3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1eeb015fa3320237f3c1b05e404dcde891b678f4e94dd5eab7480df657061831", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "29436355bed756dc1ed04bf6297325792e962e17d3f46922995174d692b734e8"}}}, "instrumentation": null, "functions": {}}