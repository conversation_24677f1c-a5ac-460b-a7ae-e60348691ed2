{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|.*\\.).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|.*\\.).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "FrNNN+HBdWeLONyYct2yC9KZ/WQp8bWC8ijse9tJFWk=", "__NEXT_PREVIEW_MODE_ID": "8eb7793ed33fa2769617f5b2e6d2574d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5c8a2a95ce338e84f8badad533df58159c995b1150e794e919264ab086049b89", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "427d21d729f084d4a86fc161d26086dd37f648a0cee0d2bb3b174a784b06dda3"}}}, "instrumentation": null, "functions": {}}