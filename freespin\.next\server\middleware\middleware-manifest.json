{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|.*\\.).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|.*\\.).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "FrNNN+HBdWeLONyYct2yC9KZ/WQp8bWC8ijse9tJFWk=", "__NEXT_PREVIEW_MODE_ID": "749c0ae8af686254a807e0d7ccc29622", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ea077f320b83e6fc49511eda0a4b3e40b2f990242ef5d84c7abed0f10e9162bd", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d783efbcfc766b1bf1089843ff5fdaa2e6656846dc695001d4f2b6b825d247ff"}}}, "instrumentation": null, "functions": {}}