import mongoose, { type Document, Mongoose, Schema, Types } from "mongoose";

export interface IPost extends Document {
  // Blog Post fields
  title: string;
  slug?: string;
  canonicalUrl?: string;
  existingUrl: boolean;
  content: string; // Main content field
  excerpt?: string; // Short description/excerpt
  description: string; // For backward compatibility
  isBlog: boolean;
  categories: Types.ObjectId[];
  tags: string[];
  author?: string; // Author reference

  // SEO fields
  metaTitle: string;
  metaDescription: string;
  metaKeywords: string;

  // FAQ fields
  // faqs: {
  //   question: string;
  //   answer: string;
  //   index: number;
  // }[];

  // Banner fields
  banner: string;

  // Additional blog functionality fields
  status: "draft" | "published" | "archived";
  isPublished: boolean;
  publishedAt?: Date;
  views: number;
  readTime: number;
  isTopNews: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const PostSchema = new Schema<IPost>(
  {
    // Blog Post fields
    title: {
      type: String,
      required: [true, "Post title is required"],
      trim: true,
      maxlength: [1000, "Title cannot exceed 200 characters"],
    },

    slug: {
      type: String,
      trim: true,
      lowercase: true,
    },

    canonicalUrl: {
      type: String,
      trim: true,
      validate: {
        validator: (v: string) => {
          if (!v) return true; // Allow empty string
          return /^https?:\/\/.+/.test(v);
        },
        message: "Canonical URL must be a valid URL",
      },
    },
    existingUrl: {
      type: Boolean,
      default: false,
    },

    content: {
      type: String,
      required: [true, "Post content is required"],
    },

    excerpt: {
      type: String,
      trim: true,
      maxlength: [1000, "Excerpt cannot exceed 300 characters"],
    },

    description: {
      type: String,
      // Not required anymore since we have content field
    },

    author: {
      type: String,
      trim: true,
    },
    isBlog: {
      type: Boolean,
      default: true,
    },
    categories: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Category",
      },
    ],
    tags: [
      {
        type: String,
        trim: true,
        lowercase: true,
      },
    ],

    // SEO fields
    metaTitle: {
      type: String,
      required: [true, "Meta title is required"],
      maxlength: [1000, "Meta title cannot exceed 60 characters"],
    },
    metaDescription: {
      type: String,
      required: [true, "Meta description is required"],
      maxlength: [1000, "Meta description cannot exceed 160 characters"],
    },
    metaKeywords: {
      type: String,
      required: [true, "Meta keywords are required"],
      maxlength: [1000, "Meta keywords cannot exceed 200 characters"],
    },

    // Banner fields
    banner: {
      type: String,
      required: [true, "Banner image is required"],
      trim: true,
    },

    // Additional blog functionality fields
    status: {
      type: String,
      enum: ["draft", "published", "archived"],
      default: "draft",
    },
    isPublished: {
      type: Boolean,
      default: false,
    },
    publishedAt: {
      type: Date,
      default: null,
    },
    views: {
      type: Number,
      default: 0,
    },
    readTime: {
      type: Number,
      default: 1,
    },
    isTopNews: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  }
);

// Pre-save middleware
PostSchema.pre("save", function (next) {
  // Generate slug from title if not provided
  if (this.isModified("title") && !this.slug) {
    this.slug = this.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-+|-+$/g, "");
  }

  // Calculate read time (average 200 words per minute)
  if (this.isModified("content")) {
    const wordCount = (this.content || "").split(/\s+/).length;
    this.readTime = Math.ceil(wordCount / 200);
  }

  // Set published date when status changes to published
  if (
    this.isModified("status") &&
    this.status === "published" &&
    !this.publishedAt
  ) {
    this.publishedAt = new Date();
    this.isPublished = true;
  }

  next();
});

// Create indexes for better query performance
PostSchema.index({ categories: 1 });
PostSchema.index({ tags: 1 });
PostSchema.index({ status: 1 });
PostSchema.index({ isPublished: 1 });
PostSchema.index({ publishedAt: -1 });
PostSchema.index({ "banner.title": 1 });
PostSchema.index({
  title: "text",
  content: "text",
  metaTitle: "text",
  metaDescription: "text",
}); // Text search index

export default mongoose.models.Post ||
  mongoose.model<IPost>("Post", PostSchema);
