self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"0083a550d7711b950e0380eaf183245f5ff600cd2d\": {\n      \"workers\": {\n        \"app/dashboard/blogs/add/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/add/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/dashboard/blogs/categories/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/categories/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/dashboard/blogs/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/blogs/add/page\": \"action-browser\",\n        \"app/dashboard/blogs/categories/page\": \"action-browser\",\n        \"app/dashboard/blogs/page\": \"action-browser\"\n      }\n    },\n    \"00218888a992dbe0f0f59c40eb1d6da396b1f8bef9\": {\n      \"workers\": {\n        \"app/dashboard/blogs/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/blogs/page\": \"rsc\"\n      }\n    },\n    \"007ad306e150b2c52c5f587da7ab326f3381065acb\": {\n      \"workers\": {\n        \"app/dashboard/blogs/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/blogs/page\": \"rsc\"\n      }\n    },\n    \"00aeb9467f3b81d270edac5fc42d30e46753429f0b\": {\n      \"workers\": {\n        \"app/dashboard/blogs/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/blogs/page\": \"rsc\"\n      }\n    },\n    \"405237e0d0b1c045da7fe8974a3c8d45bf183207cb\": {\n      \"workers\": {\n        \"app/dashboard/blogs/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/blogs/page\": \"rsc\"\n      }\n    },\n    \"4090ffc861781390c33bd2a02d2bf879db9096df8f\": {\n      \"workers\": {\n        \"app/dashboard/blogs/categories/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/categories/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/dashboard/blogs/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/blogs/categories/page\": \"action-browser\",\n        \"app/dashboard/blogs/page\": \"rsc\"\n      }\n    },\n    \"40ed9ac3739b2b25e2a2e38921fe3a197384a45c9a\": {\n      \"workers\": {\n        \"app/dashboard/blogs/categories/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/categories/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/dashboard/blogs/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/blogs/categories/page\": \"action-browser\",\n        \"app/dashboard/blogs/page\": \"rsc\"\n      }\n    },\n    \"40ef6a94fd31e53e44bb335ccf220a53fa42e6ef7f\": {\n      \"workers\": {\n        \"app/dashboard/blogs/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/blogs/page\": \"rsc\"\n      }\n    },\n    \"40efe57005355e473756f81a95740baf067a03c492\": {\n      \"workers\": {\n        \"app/dashboard/blogs/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/blogs/page\": \"rsc\"\n      }\n    },\n    \"60a97fb412b406df9fa77ff920f3eb804ebce20bc1\": {\n      \"workers\": {\n        \"app/dashboard/blogs/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/blogs/page\": \"rsc\"\n      }\n    },\n    \"607c10ea8628475a8cbfb06ec6fe9153598d2e744a\": {\n      \"workers\": {\n        \"app/dashboard/blogs/categories/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/categories/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/dashboard/blogs/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/blogs/categories/page\": \"rsc\",\n        \"app/dashboard/blogs/page\": \"action-browser\"\n      }\n    },\n    \"00aaf3229ad66ce8c0e05f422008f0d7c0448605e4\": {\n      \"workers\": {\n        \"app/dashboard/blogs/add/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/add/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/dashboard/blogs/categories/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/categories/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/blogs/add/page\": \"action-browser\",\n        \"app/dashboard/blogs/categories/page\": \"rsc\"\n      }\n    },\n    \"60e82a0f1ab060d924d48393e65f78bcf87b8015d6\": {\n      \"workers\": {\n        \"app/dashboard/blogs/add/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/add/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        },\n        \"app/dashboard/blogs/categories/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/categories/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/blogs/add/page\": \"action-browser\",\n        \"app/dashboard/blogs/categories/page\": \"rsc\"\n      }\n    },\n    \"409d162b3b4f864115692081373dbef024bdcff963\": {\n      \"workers\": {\n        \"app/dashboard/blogs/categories/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/categories/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/blogs/categories/page\": \"rsc\"\n      }\n    },\n    \"70ca012623c33fa3ea1bb858d64dc26cce9ffda769\": {\n      \"workers\": {\n        \"app/dashboard/blogs/categories/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/blogs/categories/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/client_apis/auth.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/app/dashboard/blogs/actions.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE2 => \\\"[project]/src/client_apis/api/blog.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/blogs/categories/page\": \"rsc\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"FrNNN+HBdWeLONyYct2yC9KZ/WQp8bWC8ijse9tJFWk=\"\n}"