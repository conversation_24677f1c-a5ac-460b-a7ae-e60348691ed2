#!/usr/bin/env node

const fs = require('fs');
const path = require('path');



// Files that need hook fixes
const files = [
  'src/app/[locale]/shared/Marquee.tsx',
  'src/app/[locale]/shared/MarqueeText.tsx',
  'src/app/[locale]/shared/TextHoverEffect.tsx',
  'src/utils/translation.ts'
];

function fixHookIssues() {
  console.log('🔧 Fixing React Hook placement issues...');
  
  // Fix Marquee.tsx
  try {
    const marqueePath = path.join(process.cwd(), 'src/app/[locale]/shared/Marquee.tsx');
    let content = fs.readFileSync(marqueePath, 'utf8');
    
    // Remove hook call outside component
    content = content.replace(/const t = useTranslations\("shared"\);\n/, '');
    
    // Add hook inside component
    content = content.replace(
      /const Marquee = \(\) => \{/,
      'const Marquee = () => {\n  const t = useTranslations("shared");'
    );
    
    fs.writeFileSync(marqueePath, content);
    console.log('✅ Fixed: Marquee.tsx');
  } catch (error) {
    console.log('❌ Error fixing Marquee.tsx:', error.message);
  }
  
  // Fix MarqueeText.tsx
  try {
    const marqueeTextPath = path.join(process.cwd(), 'src/app/[locale]/shared/MarqueeText.tsx');
    let content = fs.readFileSync(marqueeTextPath, 'utf8');
    
    // Remove hook call outside component
    content = content.replace(/const t = useTranslations\("shared"\);\n/, '');
    
    // Add hook inside component
    content = content.replace(
      /const MarqueeText = \(\) => \{/,
      'const MarqueeText = () => {\n  const t = useTranslations("shared");'
    );
    
    // Remove any stray expressions
    content = content.replace(/^\s*t\(".*"\);\s*$/gm, '');
    
    fs.writeFileSync(marqueeTextPath, content);
    console.log('✅ Fixed: MarqueeText.tsx');
  } catch (error) {
    console.log('❌ Error fixing MarqueeText.tsx:', error.message);
  }
  
  // Fix TextHoverEffect.tsx
  try {
    const textHoverPath = path.join(process.cwd(), 'src/app/[locale]/shared/TextHoverEffect.tsx');
    let content = fs.readFileSync(textHoverPath, 'utf8');
    
    // Remove hook call outside component
    content = content.replace(/const t = useTranslations\("shared"\);\n/, '');
    
    // Add hook inside component
    content = content.replace(
      /export const TextHoverEffect = \(\{/,
      'export const TextHoverEffect = ({\n  // const t = useTranslations("shared"); // Add this if needed'
    );
    
    fs.writeFileSync(textHoverPath, content);
    console.log('✅ Fixed: TextHoverEffect.tsx');
  } catch (error) {
    console.log('❌ Error fixing TextHoverEffect.tsx:', error.message);
  }
  
  // Fix translation.ts
  try {
    const translationPath = path.join(process.cwd(), 'src/utils/translation.ts');
    let content = fs.readFileSync(translationPath, 'utf8');
    
    // Comment out the hook usage since this is a utility file
    content = content.replace(
      /const t = useTranslations\(\);/,
      '// const t = useTranslations(); // Cannot use hooks in utility functions'
    );
    
    content = content.replace(
      /return t\(key\);/,
      'return key; // Return key as fallback since hooks cannot be used here'
    );
    
    fs.writeFileSync(translationPath, content);
    console.log('✅ Fixed: translation.ts');
  } catch (error) {
    console.log('❌ Error fixing translation.ts:', error.message);
  }
  
  console.log('🎉 Hook fixes completed!');
}

fixHookIssues();
