const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

// Function to recursively process files in a directory
function processDirectory(directory) {
  const files = fs.readdirSync(directory);

  files.forEach((file) => {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);

    if (stats.isDirectory()) {
      // Skip node_modules and .next directories
      if (file !== "node_modules" && file !== ".next") {
        processDirectory(filePath);
      }
    } else if (
      stats.isFile() &&
      (file.endsWith(".tsx") || file.endsWith(".ts"))
    ) {
      processFile(filePath);
    }
  });
}

// Function to process a single file
function processFile(filePath) {
  console.log(`Processing ${filePath}`);
  let content = fs.readFileSync(filePath, "utf8");

  // Remove next-intl imports
  content = content.replace(/import\s+.*\s+from\s+['"]next-intl.*['"]/g, "");
  content = content.replace(
    /import\s+{\s*useTranslations.*}\s+from\s+['"]next-intl.*['"]/g,
    ""
  );

  // Remove useTranslations hooks
  content = content.replace(/const\s+t\s*=\s*useTranslations\([^)]*\);?/g, "");

  // Remove NextIntlClientProvider
  content = content.replace(
    /<NextIntlClientProvider[^>]*>([\s\S]*?)<\/NextIntlClientProvider>/g,
    "$1"
  );

  fs.writeFileSync(filePath, content);
}

// Main function
function main() {
  console.log("Starting next-intl removal process...");

  // Process source files
  processDirectory(path.join(__dirname, "..", "src"));

  // Remove i18n directory
  const i18nDir = path.join(__dirname, "..", "src", "i18n");
  if (fs.existsSync(i18nDir)) {
    console.log("Removing i18n directory...");
    fs.rmSync(i18nDir, { recursive: true, force: true });
  }

  // Remove messages directory
  const messagesDir = path.join(__dirname, "..", "messages");
  if (fs.existsSync(messagesDir)) {
    console.log("Removing messages directory...");
    fs.rmSync(messagesDir, { recursive: true, force: true });
  }

  console.log("Uninstalling next-intl package...");
  try {
    execSync("npm uninstall next-intl", { stdio: "inherit" });
  } catch (error) {
    console.error("Failed to uninstall next-intl:", error);
  }

  console.log("next-intl removal process completed!");
}

main();
