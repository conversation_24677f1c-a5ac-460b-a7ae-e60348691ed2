{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/blogs/components/BlogsPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogsPageClient = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogsPageClient() from the server but BlogsPageClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(pages)/blogs/components/BlogsPageClient.tsx <module evaluation>\",\n    \"BlogsPageClient\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,sFACA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/blogs/components/BlogsPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogsPageClient = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogsPageClient() from the server but BlogsPageClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(pages)/blogs/components/BlogsPageClient.tsx\",\n    \"BlogsPageClient\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,kEACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from \"mongoose\";\r\n\r\nconst MONGODB_URI = process.env.MONGODB_URI!;\r\n\r\nif (!MONGODB_URI) {\r\n  throw new Error(\r\n    \"Please define the MONGODB_URI environment variable inside .env.local\"\r\n  );\r\n}\r\n\r\n/**\r\n * Global is used here to maintain a cached connection across hot reloads\r\n * in development. This prevents connections growing exponentially\r\n * during API Route usage.\r\n */\r\n// @ts-expect-error //global can have type any\r\nlet cached = global?.mongoose;\r\n\r\nif (!cached) {\r\n  // @ts-expect-error //global mongoose can have type any\r\n  cached = global.mongoose = { conn: null, promise: null };\r\n}\r\n\r\nasync function connectDB() {\r\n  if (cached.conn) {\r\n    return cached.conn;\r\n  }\r\n\r\n  if (!cached.promise) {\r\n    const opts = {\r\n      bufferCommands: false,\r\n    };\r\n\r\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\r\n      return mongoose;\r\n    });\r\n  }\r\n\r\n  try {\r\n    cached.conn = await cached.promise;\r\n  } catch (e) {\r\n    cached.promise = null;\r\n    throw e;\r\n  }\r\n\r\n  return cached.conn;\r\n}\r\n\r\nexport default connectDB;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MACR;AAEJ;AAEA;;;;CAIC,GACD,8CAA8C;AAC9C,IAAI,SAAS,QAAQ;AAErB,IAAI,CAAC,QAAQ;IACX,uDAAuD;IACvD,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/models/Post.ts"], "sourcesContent": ["import mongoose, { type Document, Mongoose, Schema, Types } from \"mongoose\";\r\n\r\nexport interface IPost extends Document {\r\n  // Blog Post fields\r\n  title: string;\r\n  slug?: string;\r\n  canonicalUrl?: string;\r\n  existingUrl: boolean;\r\n  content: string; // Main content field\r\n  excerpt?: string; // Short description/excerpt\r\n  description: string; // For backward compatibility\r\n  isBlog: boolean;\r\n  categories: Types.ObjectId[];\r\n  tags: string[];\r\n  author?: string; // Author reference\r\n\r\n  // SEO fields\r\n  metaTitle: string;\r\n  metaDescription: string;\r\n  metaKeywords: string;\r\n\r\n  // FAQ fields\r\n  // faqs: {\r\n  //   question: string;\r\n  //   answer: string;\r\n  //   index: number;\r\n  // }[];\r\n\r\n  // Banner fields\r\n  banner: string;\r\n\r\n  // Additional blog functionality fields\r\n  status: \"draft\" | \"published\" | \"archived\";\r\n  isPublished: boolean;\r\n  publishedAt?: Date;\r\n  views: number;\r\n  readTime: number;\r\n  isTopNews: boolean;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\nconst PostSchema = new Schema<IPost>(\r\n  {\r\n    // Blog Post fields\r\n    title: {\r\n      type: String,\r\n      required: [true, \"Post title is required\"],\r\n      trim: true,\r\n      maxlength: [1000, \"Title cannot exceed 200 characters\"],\r\n    },\r\n\r\n    slug: {\r\n      type: String,\r\n      trim: true,\r\n      lowercase: true,\r\n    },\r\n\r\n    canonicalUrl: {\r\n      type: String,\r\n      trim: true,\r\n      validate: {\r\n        validator: (v: string) => {\r\n          if (!v) return true; // Allow empty string\r\n          return /^https?:\\/\\/.+/.test(v);\r\n        },\r\n        message: \"Canonical URL must be a valid URL\",\r\n      },\r\n    },\r\n    existingUrl: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n\r\n    content: {\r\n      type: String,\r\n      required: [true, \"Post content is required\"],\r\n    },\r\n\r\n    excerpt: {\r\n      type: String,\r\n      trim: true,\r\n      maxlength: [1000, \"Excerpt cannot exceed 300 characters\"],\r\n    },\r\n\r\n    description: {\r\n      type: String,\r\n      // Not required anymore since we have content field\r\n    },\r\n\r\n    author: {\r\n      type: String,\r\n      trim: true,\r\n    },\r\n    isBlog: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    categories: [\r\n      {\r\n        type: mongoose.Schema.Types.ObjectId,\r\n        ref: \"Category\",\r\n      },\r\n    ],\r\n    \r\n    tags: [\r\n      {\r\n        type: String,\r\n        trim: true,\r\n        lowercase: true,\r\n      },\r\n    ],\r\n\r\n    // SEO fields\r\n    metaTitle: {\r\n      type: String,\r\n      required: [true, \"Meta title is required\"],\r\n      maxlength: [1000, \"Meta title cannot exceed 60 characters\"],\r\n    },\r\n    metaDescription: {\r\n      type: String,\r\n      required: [true, \"Meta description is required\"],\r\n      maxlength: [1000, \"Meta description cannot exceed 160 characters\"],\r\n    },\r\n    metaKeywords: {\r\n      type: String,\r\n      required: [true, \"Meta keywords are required\"],\r\n      maxlength: [1000, \"Meta keywords cannot exceed 200 characters\"],\r\n    },\r\n\r\n    // Banner fields\r\n    banner: {\r\n      type: String,\r\n      required: [true, \"Banner image is required\"],\r\n      trim: true,\r\n    },\r\n\r\n    // Additional blog functionality fields\r\n    status: {\r\n      type: String,\r\n      enum: [\"draft\", \"published\", \"archived\"],\r\n      default: \"draft\",\r\n    },\r\n    isPublished: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    publishedAt: {\r\n      type: Date,\r\n      default: null,\r\n    },\r\n    views: {\r\n      type: Number,\r\n      default: 0,\r\n    },\r\n    readTime: {\r\n      type: Number,\r\n      default: 1,\r\n    },\r\n    isTopNews: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  {\r\n    timestamps: true,\r\n  }\r\n);\r\n\r\n// Pre-save middleware\r\nPostSchema.pre(\"save\", function (next) {\r\n  // Generate slug from title if not provided\r\n  if (this.isModified(\"title\") && !this.slug) {\r\n    this.slug = this.title\r\n      .toLowerCase()\r\n      .replace(/[^a-z0-9]+/g, \"-\")\r\n      .replace(/^-+|-+$/g, \"\");\r\n  }\r\n\r\n  // Calculate read time (average 200 words per minute)\r\n  if (this.isModified(\"content\")) {\r\n    const wordCount = (this.content || \"\").split(/\\s+/).length;\r\n    this.readTime = Math.ceil(wordCount / 200);\r\n  }\r\n\r\n  // Set published date when status changes to published\r\n  if (\r\n    this.isModified(\"status\") &&\r\n    this.status === \"published\" &&\r\n    !this.publishedAt\r\n  ) {\r\n    this.publishedAt = new Date();\r\n    this.isPublished = true;\r\n  }\r\n\r\n  next();\r\n});\r\n\r\n// Create indexes for better query performance\r\nPostSchema.index({ categories: 1 });\r\nPostSchema.index({ tags: 1 });\r\nPostSchema.index({ status: 1 });\r\nPostSchema.index({ isPublished: 1 });\r\nPostSchema.index({ publishedAt: -1 });\r\nPostSchema.index({ \"banner.title\": 1 });\r\nPostSchema.index({\r\n  title: \"text\",\r\n  content: \"text\",\r\n  metaTitle: \"text\",\r\n  metaDescription: \"text\",\r\n}); // Text search index\r\n\r\nexport default mongoose.models.Post ||\r\n  mongoose.model<IPost>(\"Post\", PostSchema);\r\n"], "names": [], "mappings": ";;;AAAA;;AA0CA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAC3B;IACE,mBAAmB;IACnB,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,MAAM;QACN,WAAW;YAAC;YAAM;SAAqC;IACzD;IAEA,MAAM;QACJ,MAAM;QACN,MAAM;QACN,WAAW;IACb;IAEA,cAAc;QACZ,MAAM;QACN,MAAM;QACN,UAAU;YACR,WAAW,CAAC;gBACV,IAAI,CAAC,GAAG,OAAO,MAAM,qBAAqB;gBAC1C,OAAO,iBAAiB,IAAI,CAAC;YAC/B;YACA,SAAS;QACX;IACF;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IAEA,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;IAC9C;IAEA,SAAS;QACP,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAAuC;IAC3D;IAEA,aAAa;QACX,MAAM;IAER;IAEA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,YAAY;QACV;YACE,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;YACpC,KAAK;QACP;KACD;IAED,MAAM;QACJ;YACE,MAAM;YACN,MAAM;YACN,WAAW;QACb;KACD;IAED,aAAa;IACb,WAAW;QACT,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,WAAW;YAAC;YAAM;SAAyC;IAC7D;IACA,iBAAiB;QACf,MAAM;QACN,UAAU;YAAC;YAAM;SAA+B;QAChD,WAAW;YAAC;YAAM;SAAgD;IACpE;IACA,cAAc;QACZ,MAAM;QACN,UAAU;YAAC;YAAM;SAA6B;QAC9C,WAAW;YAAC;YAAM;SAA6C;IACjE;IAEA,gBAAgB;IAChB,QAAQ;QACN,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,MAAM;IACR;IAEA,uCAAuC;IACvC,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAS;YAAa;SAAW;QACxC,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IACA,OAAO;QACL,MAAM;QACN,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,SAAS;IACX;AACF,GACA;IACE,YAAY;AACd;AAGF,sBAAsB;AACtB,WAAW,GAAG,CAAC,QAAQ,SAAU,IAAI;IACnC,2CAA2C;IAC3C,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE;QAC1C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CACnB,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;IACzB;IAEA,qDAAqD;IACrD,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY;QAC9B,MAAM,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,EAAE,KAAK,CAAC,OAAO,MAAM;QAC1D,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,YAAY;IACxC;IAEA,sDAAsD;IACtD,IACE,IAAI,CAAC,UAAU,CAAC,aAChB,IAAI,CAAC,MAAM,KAAK,eAChB,CAAC,IAAI,CAAC,WAAW,EACjB;QACA,IAAI,CAAC,WAAW,GAAG,IAAI;QACvB,IAAI,CAAC,WAAW,GAAG;IACrB;IAEA;AACF;AAEA,8CAA8C;AAC9C,WAAW,KAAK,CAAC;IAAE,YAAY;AAAE;AACjC,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC7B,WAAW,KAAK,CAAC;IAAE,aAAa;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,aAAa,CAAC;AAAE;AACnC,WAAW,KAAK,CAAC;IAAE,gBAAgB;AAAE;AACrC,WAAW,KAAK,CAAC;IACf,OAAO;IACP,SAAS;IACT,WAAW;IACX,iBAAiB;AACnB,IAAI,oBAAoB;uCAET,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IACjC,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/blogs/page.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { BlogsPageClient } from \"./components/BlogsPageClient\";\r\nimport { PublicBlogPost } from \"@/client_apis/api/blog\";\r\nimport connectDB from \"@/lib/mongodb\";\r\nimport Post from \"@/models/Post\";\r\nimport Category from \"@/models/Category\";\r\nimport { Metadata } from \"next\";\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"Casino News & Gaming Blog | Latest Updates & Reviews – FreeSpin168\",\r\n  alternates: {\r\n    canonical: \"https://www.freespin168.asia/blogs\",\r\n  },\r\n  description:\r\n    \"Stay updated with the latest casino news, game reviews, and industry insights. Read expert analysis and tips from FreeSpin168's gaming blog.\",\r\n  keywords: [\r\n    \"casino news\",\r\n    \"gaming blog\",\r\n    \"casino reviews\",\r\n    \"online casino updates\",\r\n  ],\r\n};\r\n\r\ninterface BlogsPageProps {\r\n  searchParams: Promise<{\r\n    page?: string;\r\n  }>;\r\n}\r\n\r\nconst page = async ({ searchParams }: BlogsPageProps) => {\r\n  try {\r\n    // Connect to database directly\r\n    await connectDB();\r\n\r\n    // Debug: Check all posts first\r\n    const allPosts = await Post.find({ isBlog: true }).lean();\r\n    console.log(\"=== BLOGS PAGE DEBUG ===\");\r\n    console.log(\"Total blog posts in database:\", allPosts.length);\r\n    console.log(\r\n      \"Posts by status:\",\r\n      allPosts.reduce((acc, post) => {\r\n        acc[post.status] = (acc[post.status] || 0) + 1;\r\n        return acc;\r\n      }, {} as Record<string, number>)\r\n    );\r\n    console.log(\r\n      \"Published posts:\",\r\n      allPosts.filter((p) => p.status === \"published\" && p.isPublished).length\r\n    );\r\n\r\n    // Fetch latest posts directly from database (initial 6 posts)\r\n    const latestPostsData = await Post.find({\r\n      status: \"published\",\r\n      isPublished: true,\r\n      isBlog: true,\r\n    })\r\n      .populate(\"categories\", \"name description\")\r\n      .sort({ publishedAt: -1, createdAt: -1 })\r\n      .limit(6)\r\n      .lean();\r\n\r\n    console.log(\"Latest posts found:\", latestPostsData.length);\r\n\r\n    // Fetch all explore posts (client-side pagination will handle the rest)\r\n    const explorePostsData = await Post.find({\r\n      status: \"published\",\r\n      isPublished: true,\r\n      isBlog: true,\r\n    })\r\n      .sort({ publishedAt: -1, createdAt: -1 })\r\n      .lean();\r\n\r\n    // Fetch top news posts\r\n    const topNewsPostsData = await Post.find({\r\n      status: \"published\",\r\n      isPublished: true,\r\n      isBlog: true,\r\n      isTopNews: true,\r\n    })\r\n      .sort({ publishedAt: -1, createdAt: -1 })\r\n      .limit(5) // Limit to 5 top news posts\r\n      .lean();\r\n\r\n    // Serialize posts for client components\r\n    const serializePosts = (posts: any[]): PublicBlogPost[] => {\r\n      return posts.map((post) => ({\r\n        _id: post._id.toString(),\r\n        title: post.title,\r\n        description:\r\n          post.excerpt ||\r\n          post.description ||\r\n          post.content?.substring(0, 200) ||\r\n          \"\",\r\n        banner: post.banner || \"\",\r\n        slug: post?.slug || post?._id,\r\n        // categories: post.categories || [], // categories is already an array of strings\r\n        categories: post.categories?.map((cat: any) => cat.name) || [],\r\n        tags: post.tags || [],\r\n        publishedAt:\r\n          post.publishedAt?.toISOString() ||\r\n          post.createdAt?.toISOString() ||\r\n          \"\",\r\n        createdAt: post.createdAt?.toISOString() || \"\",\r\n        readTime: post.readTime || 5,\r\n        views: post.views || 0,\r\n\r\n        metaTitle: post.metaTitle || post.title || \"\",\r\n        metaDescription:\r\n          post.metaDescription || post.excerpt || post.description || \"\",\r\n      }));\r\n    };\r\n\r\n    const latestPosts = serializePosts(latestPostsData);\r\n    const explorePosts = serializePosts(explorePostsData);\r\n    const topNewsPosts = serializePosts(topNewsPostsData);\r\n\r\n    return (\r\n      <BlogsPageClient\r\n        initialLatestPosts={latestPosts}\r\n        explorePosts={explorePosts}\r\n        topNewsPosts={topNewsPosts}\r\n      />\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching posts:\", error);\r\n\r\n    // Return empty state on error\r\n    return (\r\n      <BlogsPageClient\r\n        initialLatestPosts={[]}\r\n        explorePosts={[]}\r\n        topNewsPosts={[]}\r\n      />\r\n    );\r\n  }\r\n};\r\n\r\nexport default page;\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;AACA;;;;;AAIO,MAAM,WAAqB;IAChC,OAAO;IACP,YAAY;QACV,WAAW;IACb;IACA,aACE;IACF,UAAU;QACR;QACA;QACA;QACA;KACD;AACH;AAQA,MAAM,OAAO,OAAO,EAAE,YAAY,EAAkB;IAClD,IAAI;QACF,+BAA+B;QAC/B,MAAM,CAAA,GAAA,qHAAA,CAAA,UAAS,AAAD;QAEd,+BAA+B;QAC/B,MAAM,WAAW,MAAM,qHAAA,CAAA,UAAI,CAAC,IAAI,CAAC;YAAE,QAAQ;QAAK,GAAG,IAAI;QACvD,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,iCAAiC,SAAS,MAAM;QAC5D,QAAQ,GAAG,CACT,oBACA,SAAS,MAAM,CAAC,CAAC,KAAK;YACpB,GAAG,CAAC,KAAK,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI;YAC7C,OAAO;QACT,GAAG,CAAC;QAEN,QAAQ,GAAG,CACT,oBACA,SAAS,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,eAAe,EAAE,WAAW,EAAE,MAAM;QAG1E,8DAA8D;QAC9D,MAAM,kBAAkB,MAAM,qHAAA,CAAA,UAAI,CAAC,IAAI,CAAC;YACtC,QAAQ;YACR,aAAa;YACb,QAAQ;QACV,GACG,QAAQ,CAAC,cAAc,oBACvB,IAAI,CAAC;YAAE,aAAa,CAAC;YAAG,WAAW,CAAC;QAAE,GACtC,KAAK,CAAC,GACN,IAAI;QAEP,QAAQ,GAAG,CAAC,uBAAuB,gBAAgB,MAAM;QAEzD,wEAAwE;QACxE,MAAM,mBAAmB,MAAM,qHAAA,CAAA,UAAI,CAAC,IAAI,CAAC;YACvC,QAAQ;YACR,aAAa;YACb,QAAQ;QACV,GACG,IAAI,CAAC;YAAE,aAAa,CAAC;YAAG,WAAW,CAAC;QAAE,GACtC,IAAI;QAEP,uBAAuB;QACvB,MAAM,mBAAmB,MAAM,qHAAA,CAAA,UAAI,CAAC,IAAI,CAAC;YACvC,QAAQ;YACR,aAAa;YACb,QAAQ;YACR,WAAW;QACb,GACG,IAAI,CAAC;YAAE,aAAa,CAAC;YAAG,WAAW,CAAC;QAAE,GACtC,KAAK,CAAC,GAAG,4BAA4B;SACrC,IAAI;QAEP,wCAAwC;QACxC,MAAM,iBAAiB,CAAC;YACtB,OAAO,MAAM,GAAG,CAAC,CAAC,OAAS,CAAC;oBAC1B,KAAK,KAAK,GAAG,CAAC,QAAQ;oBACtB,OAAO,KAAK,KAAK;oBACjB,aACE,KAAK,OAAO,IACZ,KAAK,WAAW,IAChB,KAAK,OAAO,EAAE,UAAU,GAAG,QAC3B;oBACF,QAAQ,KAAK,MAAM,IAAI;oBACvB,MAAM,MAAM,QAAQ,MAAM;oBAC1B,kFAAkF;oBAClF,YAAY,KAAK,UAAU,EAAE,IAAI,CAAC,MAAa,IAAI,IAAI,KAAK,EAAE;oBAC9D,MAAM,KAAK,IAAI,IAAI,EAAE;oBACrB,aACE,KAAK,WAAW,EAAE,iBAClB,KAAK,SAAS,EAAE,iBAChB;oBACF,WAAW,KAAK,SAAS,EAAE,iBAAiB;oBAC5C,UAAU,KAAK,QAAQ,IAAI;oBAC3B,OAAO,KAAK,KAAK,IAAI;oBAErB,WAAW,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI;oBAC3C,iBACE,KAAK,eAAe,IAAI,KAAK,OAAO,IAAI,KAAK,WAAW,IAAI;gBAChE,CAAC;QACH;QAEA,MAAM,cAAc,eAAe;QACnC,MAAM,eAAe,eAAe;QACpC,MAAM,eAAe,eAAe;QAEpC,qBACE,8OAAC,kKAAA,CAAA,kBAAe;YACd,oBAAoB;YACpB,cAAc;YACd,cAAc;;;;;;IAGpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,8BAA8B;QAC9B,qBACE,8OAAC,kKAAA,CAAA,kBAAe;YACd,oBAAoB,EAAE;YACtB,cAAc,EAAE;YAChB,cAAc,EAAE;;;;;;IAGtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAapBM,WAAWC;AAGb,cAAc,0CAA0C;AAExD,4DAA4D;;;;;;;;;;;;;;;;QAGxDG,MAAMZ,UAAUa,QAAQ,yCAAA;QACxBC,EAAAA,IAAM;QACNC,KAAAA;IAAAA;IAAU;gBACV,IAAA;YAAA;YAAA,2BAA2C;wBAC3CC,IAAAA;oBAAAA,OAAY;oBAAA;gCACZC,IAAAA;4BAAAA,KAAU;4BAAA;iCACVC,UAAU,EAAE;kCACd,QAAA,CAAA;gCAAA,QAAA;oCAAA,IAAA;oCAAA;iCAAA;;2BACAC,UAAU;;yBACRC,YAAYnB;0BACd,QAAA,CAAA;oBAAA;iBAAA;YACF;YAAE", "ignoreList": [0], "debugId": null}}]}