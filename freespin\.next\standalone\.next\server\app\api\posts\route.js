(()=>{var e={};e.id=4607,e.ids=[4607],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36847:(e,t,r)=>{"use strict";r.d(t,{Kd:()=>u});var s=r(67288),o=r(27910);let i={endPoint:process.env.MINIO_ENDPOINT?.replace(/^https?:\/\//,"")||"localhost",port:process.env.MINIO_PORT?Number.parseInt(process.env.MINIO_PORT):9e3,useSSL:"true"===process.env.MINIO_USE_SSL,accessKey:process.env.MINIO_ACCESS_KEY||"3uiq5emitjasdfghyjui",secretKey:process.env.MINIO_SECRET_KEY||"TYo1ruP1PqbOx3fONapGwawKQGqCDQsdfadsdfgh"},n=process.env.MINIO_BUCKET_NAME||"spinfree",a=new s.Kj(i),l=async()=>{try{if(console.log("Checking MinIO connection..."),console.log("MinIO config:",{endPoint:i.endPoint,port:i.port,useSSL:i.useSSL,bucket:n}),await a.bucketExists(n))console.log(`Bucket '${n}' already exists`);else{await a.makeBucket(n,"us-east-1"),console.log(`Bucket '${n}' created successfully`);let e={Version:"2012-10-17",Statement:[{Effect:"Allow",Principal:{AWS:["*"]},Action:["s3:GetObject"],Resource:[`arn:aws:s3:::${n}/*`]}]};await a.setBucketPolicy(n,JSON.stringify(e)),console.log(`Bucket policy set for '${n}'`)}}catch(e){console.error("Error initializing MinIO bucket:",e)}},c=e=>{if(137===e[0]&&80===e[1]&&78===e[2]&&71===e[3])return"image/png";if(255===e[0]&&216===e[1]&&255===e[2])return"image/jpeg";let t=e.toString("ascii",0,100).toLowerCase();return t.includes("<svg")||t.includes("<?xml")?"image/svg+xml":null};class u{static async uploadFile(e,t,r,s="posts"){try{console.log("Starting file upload to MinIO...");try{await a.bucketExists(n),console.log("MinIO connection verified")}catch(e){throw console.error("MinIO connection failed:",e),Error("MinIO service is unavailable. Please try again later.")}await l();let i=r;if("application/octet-stream"===r){let t=c(e);if(!t)throw Error("Invalid or unsupported image format");i=t}let u=new Date,p="image/svg+xml"===i?"svg":"image/png"===i?"png":"jpg",d=`${Date.now()}-${t.replace(/\.[^/.]+$/,"")}.${p}`,g=[s,u.getFullYear().toString(),(u.getMonth()+1).toString().padStart(2,"0"),d].join("/");console.log("Uploading to path:",g);let f=new o.Readable;f.push(e),f.push(null),await a.putObject(n,g,f,e.length,{"Content-Type":i});let m="https://freespin168.asia",h=`${m}/api/uploads/${g}`;return console.log("File uploaded to MinIO successfully!"),console.log(`- MinIO path: ${g}`),console.log(`- Proxy URL: ${h}`),console.log(`- Base URL: ${m}`),h}catch(a){console.error("Error uploading file to MinIO:",a),console.error("MinIO config:",{endPoint:i.endPoint,port:i.port,useSSL:i.useSSL,bucket:n}),console.error("Upload details:",{fileName:t,contentType:r,folder:s,bufferSize:e.length});let o=`/uploads/${s}/${Date.now()}-${t}`;return console.log("Upload failed, using fallback URL:",o),o}}static async deleteFile(e){try{let t=new URL(e),r=t.pathname.substring(t.pathname.indexOf("/",1)+1);await a.removeObject(n,r),console.log(`File deleted from MinIO: ${r}`)}catch(e){throw console.error("Error deleting file from MinIO:",e),Error("Failed to delete file")}}static async listFiles(e="posts",t=100){try{let r=[],s=a.listObjects(n,`${e}/`,!0);return new Promise((e,o)=>{s.on("data",e=>{if(r.length<t){let t=i.useSSL?"https":"http",s=i.port!==(i.useSSL?443:80)?`:${i.port}`:"",o=`${t}://${i.endPoint}${s}/${n}/${e.name}`;r.push({name:e.name,size:e.size,lastModified:e.lastModified,url:o,fileName:e.name?.split("/").pop()||"",folder:e.name?.split("/").slice(0,-1).join("/")||""})}}),s.on("end",()=>e(r)),s.on("error",e=>o(e))})}catch(e){throw console.error("Error listing files:",e),Error("Failed to list files")}}static async getPresignedUrl(e,t=3600){try{return await a.presignedGetObject(n,e,t)}catch(e){throw console.error("Error generating presigned URL:",e),Error("Failed to generate presigned URL")}}static async getFileStream(e){try{let t=await a.getObject(n,e),r=[];return new Promise((e,s)=>{t.on("data",e=>r.push(e)),t.on("end",()=>e(Buffer.concat(r))),t.on("error",s)})}catch(e){throw console.error("Error getting file stream from MinIO:",e),Error("Failed to get file from MinIO")}}}l().catch(e=>{console.error("Critical error during MinIO initialization:",e)})},41204:e=>{"use strict";e.exports=require("string_decoder")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45158:(e,t,r)=>{var s=r(79428),o=s.Buffer;function i(e,t){for(var r in e)t[r]=e[r]}function n(e,t,r){return o(e,t,r)}o.from&&o.alloc&&o.allocUnsafe&&o.allocUnsafeSlow?e.exports=s:(i(s,t),t.Buffer=n),n.prototype=Object.create(o.prototype),i(o,n),n.from=function(e,t,r){if("number"==typeof e)throw TypeError("Argument must not be a number");return o(e,t,r)},n.alloc=function(e,t,r){if("number"!=typeof e)throw TypeError("Argument must be a number");var s=o(e);return void 0!==t?"string"==typeof r?s.fill(t,r):s.fill(t):s.fill(0),s},n.allocUnsafe=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return o(e)},n.allocUnsafeSlow=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return s.SlowBuffer(e)}},55511:e=>{"use strict";e.exports=require("crypto")},55530:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(56037),o=r.n(s);let i=new s.Schema({title:{type:String,required:[!0,"Post title is required"],trim:!0,maxlength:[1e3,"Title cannot exceed 200 characters"]},slug:{type:String,trim:!0,lowercase:!0},canonicalUrl:{type:String,trim:!0,validate:{validator:e=>!e||/^https?:\/\/.+/.test(e),message:"Canonical URL must be a valid URL"}},existingUrl:{type:Boolean,default:!1},content:{type:String,required:[!0,"Post content is required"]},excerpt:{type:String,trim:!0,maxlength:[1e3,"Excerpt cannot exceed 300 characters"]},description:{type:String},author:{type:String,trim:!0},isBlog:{type:Boolean,default:!0},categories:[{type:o().Schema.Types.ObjectId,ref:"Category"}],tags:[{type:String,trim:!0,lowercase:!0}],metaTitle:{type:String,required:[!0,"Meta title is required"],maxlength:[1e3,"Meta title cannot exceed 60 characters"]},metaDescription:{type:String,required:[!0,"Meta description is required"],maxlength:[1e3,"Meta description cannot exceed 160 characters"]},metaKeywords:{type:String,required:[!0,"Meta keywords are required"],maxlength:[1e3,"Meta keywords cannot exceed 200 characters"]},banner:{type:String,required:[!0,"Banner image is required"],trim:!0},status:{type:String,enum:["draft","published","archived"],default:"draft"},isPublished:{type:Boolean,default:!1},publishedAt:{type:Date,default:null},views:{type:Number,default:0},readTime:{type:Number,default:1},isTopNews:{type:Boolean,default:!1}},{timestamps:!0});i.pre("save",function(e){if(this.isModified("title")&&!this.slug&&(this.slug=this.title.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,"")),this.isModified("content")){let e=(this.content||"").split(/\s+/).length;this.readTime=Math.ceil(e/200)}this.isModified("status")&&"published"===this.status&&!this.publishedAt&&(this.publishedAt=new Date,this.isPublished=!0),e()}),i.index({categories:1}),i.index({tags:1}),i.index({status:1}),i.index({isPublished:1}),i.index({publishedAt:-1}),i.index({"banner.title":1}),i.index({title:"text",content:"text",metaTitle:"text",metaDescription:"text"});let n=o().models.Post||o().model("Post",i)},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66136:e=>{"use strict";e.exports=require("timers")},67323:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>h,serverHooks:()=>b,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>f,POST:()=>m,dynamic:()=>d,revalidate:()=>g});var o=r(96559),i=r(48088),n=r(37719),a=r(32190),l=r(45697),c=r(75745),u=r(55530),p=r(36847);let d="force-dynamic",g=0;async function f(e){try{await (0,c.A)();let e=await u.A.find().populate("categories","name description isActive").lean();console.log("data",e);let t=a.NextResponse.json({success:!0,data:{posts:e}});return t.headers.set("Cache-Control","no-store, no-cache, must-revalidate, proxy-revalidate"),t.headers.set("Pragma","no-cache"),t.headers.set("Expires","0"),t}catch(e){return console.error("Error fetching posts:",e),a.NextResponse.json({success:!1,error:"Failed to fetch posts"},{status:500})}}async function m(e){try{let t=e.headers.get("content-type")||"";if(console.log("createingn, creating"),!t.includes("application/json"))return a.NextResponse.json({success:!1,error:"Unsupported Content-Type"},{status:415});let{data:r,bannerImageBase64:s}=await e.json();if(!r||"object"!=typeof r)return a.NextResponse.json({success:!1,error:"Missing or invalid 'data' field"},{status:400});let o=null,i="";if(s&&"string"==typeof s){let e=s.match(/^data:(.+);base64,(.+)$/);if(!e||3!==e.length)return a.NextResponse.json({success:!1,error:"Invalid base64 image format"},{status:400});i=e[1];let t=e[2];o=Buffer.from(t,"base64"),function(e,t=5){if(e.length>1024*t*1024)throw Error(`File size exceeds ${t}MB`)}(o);let n=i.split("/")[1],l=`banner-${Date.now()}.${n}`,c=await p.Kd.uploadFile(o,l,i,"banners");console.log("imageUrl",c),r.banner=c}console.log("validatedData",r),await (0,c.A)();let n=await u.A.create(r);return a.NextResponse.json({success:!0,message:"Post created successfully",post:n},{status:201})}catch(e){return console.error("Error in POST /api/posts:",e),a.NextResponse.json({success:!1,error:e?.message||"Internal server error"},{status:500})}}l.z.object({title:l.z.string().min(1),description:l.z.string().min(1)});let h=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/posts/route",pathname:"/api/posts",filename:"route",bundlePath:"app/api/posts/route"},resolvedPagePath:"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\api\\posts\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:y,workUnitAsyncStorage:x,serverHooks:b}=h;function w(){return(0,n.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:x})}},73024:e=>{"use strict";e.exports=require("node:fs")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(56037),o=r.n(s);let i=process.env.MONGODB_URI;if(!i)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let n=global?.mongoose;n||(n=global.mongoose={conn:null,promise:null});let a=async function(){if(n.conn)return n.conn;n.promise||(n.promise=o().connect(i,{bufferCommands:!1}).then(e=>e));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,5697,7288],()=>r(67323));module.exports=s})();