// import { Card, CardContent } from "@/components/ui/card";
// import { <PERSON><PERSON> } from "@/components/ui/button";
// import { Badge } from "@/components/ui/badge";
// import Link from "next/link";
// import BasicHeader from "../../home/<USER>/BasicHeader";
// import { PublicBlogPost } from "@/client_apis/api/blog";
// import { formatDate } from "@/utils/dateUtils";

// interface NewsSectionProps {
//   topNewsPosts?: PublicBlogPost[];
// }

// export function NewsSection({ topNewsPosts = [] }: NewsSectionProps) {
//   // Get the first top news post as the featured post
//   const featuredPost = topNewsPosts[0];

//   if (!featuredPost) {
//     return (
//       <div>
//         <BasicHeader text="Top News" className="mb-6 text-start" />
//         <Card className="relative flex flex-col justify-center items-center border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0">
//           <div className="text-center text-gray-400">
//             <p>No top news available</p>
//             <p className="text-sm mt-2">
//               Mark a blog post as &quot;Top News&quot; to display it here
//             </p>
//           </div>
//         </Card>
//       </div>
//     );
//   }

//   console.log(`url('${featuredPost.banner || "/blogs/blg1.png"}')`);
//   return (
//     <div>
//       <BasicHeader text="Top News" className="mb-6 text-start" />
//       <Link href={`/blogs/${featuredPost._id}`}>
//         <Card className="relative flex flex-col justify-end border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0 cursor-pointer hover:scale-[1.02] transition-transform duration-300">
//           <div
//             className="absolute inset-0 z-0 bg-cover bg-center bg-no-repeat"
//             style={{
//               backgroundImage: `url('${
//                 featuredPost.banner || "/blogs/blg1.png"
//               }')`,
//             }}
//           />
//           <CardContent className="p-6 relative z-20 h-auto">
//             <div className="absolute inset-0 z-10 backdrop-blur-md bg-black/30" />
//             <div className="relative z-20">
//               <div className="flex items-center gap-2 mb-3">
//                 <Badge
//                   variant="secondary"
//                   className="bg-orange-600 text-white hover:bg-orange-700"
//                 >
//                   HOT
//                 </Badge>
//                 <span className="text-sm text-gray-400">
//                   {formatDate(
//                     featuredPost.publishedAt || featuredPost.createdAt
//                   )}
//                 </span>
//               </div>
//               <h3 className="text-xl font-bold mb-4 text-white leading-tight line-clamp-3">
//                 {featuredPost.title}
//               </h3>
//               {featuredPost.description && (
//                 <p className="text-gray-300 mb-6 leading-relaxed line-clamp-3">
//                   {featuredPost.description}
//                 </p>
//               )}
//               <Button className="bg-[#07BC0C] cursor-pointer text-white hover:bg-[#06A50B] transition-colors">
//                 Read More
//               </Button>
//             </div>
//           </CardContent>
//         </Card>
//       </Link>
//     </div>
//   );
// }

// Second
// "use client";

// import { useState } from "react";
// import { motion, AnimatePresence } from "framer-motion";
// import { Card, CardContent } from "@/components/ui/card";
// import { Button } from "@/components/ui/button";
// import { Badge } from "@/components/ui/badge";
// import Link from "next/link";
// import BasicHeader from "../../home/<USER>/BasicHeader";
// import type { PublicBlogPost } from "@/client_apis/api/blog";
// import { formatDate } from "@/utils/dateUtils";

// interface NewsSectionProps {
//   topNewsPosts?: PublicBlogPost[];
// }

// const ImageLoader = () => {
//   return (
//     <div className="absolute inset-0 z-30 flex items-center justify-center bg-gradient-to-br from-gray-900 via-gray-800 to-black">
//       {/* Animated background pattern */}
//       <div className="absolute inset-0 opacity-10">
//         <motion.div
//           className="absolute inset-0"
//           animate={{
//             background: [
//               "radial-gradient(circle at 20% 50%, #3b82f6 0%, transparent 50%)",
//               "radial-gradient(circle at 80% 50%, #8b5cf6 0%, transparent 50%)",
//               "radial-gradient(circle at 40% 80%, #06b6d4 0%, transparent 50%)",
//               "radial-gradient(circle at 20% 50%, #3b82f6 0%, transparent 50%)",
//             ],
//           }}
//           transition={{
//             duration: 4,
//             repeat: Number.POSITIVE_INFINITY,
//             ease: "easeInOut",
//           }}
//         />
//       </div>

//       {/* Main loader content */}
//       <div className="relative z-10 flex flex-col items-center">
//         {/* Spinning rings */}
//         <div className="relative w-20 h-20 mb-6">
//           <motion.div
//             className="absolute inset-0 border-4 border-transparent border-t-blue-500 border-r-purple-500 rounded-full"
//             animate={{ rotate: 360 }}
//             transition={{
//               duration: 1.5,
//               repeat: Number.POSITIVE_INFINITY,
//               ease: "linear",
//             }}
//           />
//           <motion.div
//             className="absolute inset-2 border-4 border-transparent border-b-cyan-500 border-l-green-500 rounded-full"
//             animate={{ rotate: -360 }}
//             transition={{
//               duration: 2,
//               repeat: Number.POSITIVE_INFINITY,
//               ease: "linear",
//             }}
//           />
//           <motion.div
//             className="absolute inset-4 border-4 border-transparent border-t-orange-500 border-r-pink-500 rounded-full"
//             animate={{ rotate: 360 }}
//             transition={{
//               duration: 1,
//               repeat: Number.POSITIVE_INFINITY,
//               ease: "linear",
//             }}
//           />
//         </div>

//         {/* Pulsing dots */}
//         <div className="flex space-x-2 mb-4">
//           {[0, 1, 2].map((index) => (
//             <motion.div
//               key={index}
//               className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
//               animate={{
//                 scale: [1, 1.5, 1],
//                 opacity: [0.5, 1, 0.5],
//               }}
//               transition={{
//                 duration: 1.5,
//                 repeat: Number.POSITIVE_INFINITY,
//                 delay: index * 0.2,
//                 ease: "easeInOut",
//               }}
//             />
//           ))}
//         </div>

//         {/* Loading text */}
//         <motion.div
//           initial={{ opacity: 0, y: 10 }}
//           animate={{ opacity: 1, y: 0 }}
//           transition={{ delay: 0.5 }}
//           className="text-center"
//         >
//           <motion.p
//             className="text-white font-medium text-lg mb-2"
//             animate={{ opacity: [0.5, 1, 0.5] }}
//             transition={{
//               duration: 2,
//               repeat: Number.POSITIVE_INFINITY,
//               ease: "easeInOut",
//             }}
//           >
//             Loading News
//           </motion.p>
//           <p className="text-gray-400 text-sm">Preparing your content...</p>
//         </motion.div>

//         {/* Animated progress bar */}
//         <motion.div
//           className="w-32 h-1 bg-gray-700 rounded-full mt-4 overflow-hidden"
//           initial={{ opacity: 0 }}
//           animate={{ opacity: 1 }}
//           transition={{ delay: 1 }}
//         >
//           <motion.div
//             className="h-full bg-gradient-to-r from-blue-500 via-purple-500 to-cyan-500 rounded-full"
//             animate={{
//               x: ["-100%", "100%"],
//             }}
//             transition={{
//               duration: 2,
//               repeat: Number.POSITIVE_INFINITY,
//               ease: "easeInOut",
//             }}
//           />
//         </motion.div>
//       </div>

//       {/* Floating particles */}
//       {[...Array(6)].map((_, index) => (
//         <motion.div
//           key={index}
//           className="absolute w-2 h-2 bg-white/20 rounded-full"
//           style={{
//             left: `${Math.random() * 100}%`,
//             top: `${Math.random() * 100}%`,
//           }}
//           animate={{
//             y: [0, -20, 0],
//             opacity: [0, 1, 0],
//             scale: [0.5, 1, 0.5],
//           }}
//           transition={{
//             duration: 3 + Math.random() * 2,
//             repeat: Number.POSITIVE_INFINITY,
//             delay: Math.random() * 2,
//             ease: "easeInOut",
//           }}
//         />
//       ))}
//     </div>
//   );
// };

// export function NewsSection({ topNewsPosts = [] }: NewsSectionProps) {
//   const [imageLoaded, setImageLoaded] = useState(false);
//   const [imageError, setImageError] = useState(false);

//   // Get the first top news post as the featured post
//   const featuredPost = topNewsPosts[0];

//   if (!featuredPost) {
//     return (
//       <div>
//         <BasicHeader text="Top News" className="mb-6 text-start" />
//         <Card className="relative flex flex-col justify-center items-center border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0">
//           <div className="text-center text-gray-400">
//             <p>No top news available</p>
//             <p className="text-sm mt-2">
//               Mark a blog post as "Top News" to display it here
//             </p>
//           </div>
//         </Card>
//       </div>
//     );
//   }

//   const imageUrl = featuredPost.banner || "/blogs/blg1.png";

//   return (
//     <div>
//       <BasicHeader text="Top News" className="mb-6 text-start" />
//       <Link href={`/blogs/${featuredPost._id}`}>
//         <Card className="relative flex flex-col justify-end border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0 cursor-pointer hover:scale-[1.02] transition-transform duration-300">
//           {/* Background Image */}
//           <div className="absolute inset-0 z-0">
//             <img
//               src={imageUrl || "/placeholder.svg"}
//               alt={featuredPost.title}
//               className={`w-full h-full object-cover transition-opacity duration-500 ${
//                 imageLoaded ? "opacity-100" : "opacity-0"
//               }`}
//               onLoad={() => setImageLoaded(true)}
//               onError={() => {
//                 setImageError(true);
//                 setImageLoaded(true);
//               }}
//             />

//             {/* Fallback gradient background */}
//             {imageError && (
//               <div className="absolute inset-0 bg-gradient-to-br from-gray-800 via-gray-900 to-black" />
//             )}
//           </div>

//           {/* Image Loader */}
//           <AnimatePresence>
//             {!imageLoaded && (
//               <motion.div
//                 initial={{ opacity: 1 }}
//                 exit={{ opacity: 0 }}
//                 transition={{ duration: 0.5 }}
//               >
//                 <ImageLoader />
//               </motion.div>
//             )}
//           </AnimatePresence>

//           {/* Content Overlay */}
//           <AnimatePresence>
//             {imageLoaded && (
//               <motion.div
//                 initial={{ opacity: 0, y: 20 }}
//                 animate={{ opacity: 1, y: 0 }}
//                 transition={{ duration: 0.6, ease: "easeOut" }}
//               >
//                 <CardContent className="p-6 relative z-20 h-auto">
//                   <div className="absolute inset-0 z-10 backdrop-blur-md bg-black/30" />
//                   <div className="relative z-20">
//                     <motion.div
//                       initial={{ opacity: 0, y: 10 }}
//                       animate={{ opacity: 1, y: 0 }}
//                       transition={{ duration: 0.4, delay: 0.2 }}
//                       className="flex items-center gap-2 mb-3"
//                     >
//                       <Badge
//                         variant="secondary"
//                         className="bg-orange-600 text-white hover:bg-orange-700"
//                       >
//                         HOT
//                       </Badge>
//                       <span className="text-sm text-gray-400">
//                         {formatDate(
//                           featuredPost.publishedAt || featuredPost.createdAt
//                         )}
//                       </span>
//                     </motion.div>

//                     <motion.h3
//                       initial={{ opacity: 0, y: 10 }}
//                       animate={{ opacity: 1, y: 0 }}
//                       transition={{ duration: 0.4, delay: 0.3 }}
//                       className="text-xl font-bold mb-4 text-white leading-tight line-clamp-3"
//                     >
//                       {featuredPost.title}
//                     </motion.h3>

//                     {featuredPost.description && (
//                       <motion.p
//                         initial={{ opacity: 0, y: 10 }}
//                         animate={{ opacity: 1, y: 0 }}
//                         transition={{ duration: 0.4, delay: 0.4 }}
//                         className="text-gray-300 mb-6 leading-relaxed line-clamp-3"
//                       >
//                         {featuredPost.description}
//                       </motion.p>
//                     )}

//                     <motion.div
//                       initial={{ opacity: 0, y: 10 }}
//                       animate={{ opacity: 1, y: 0 }}
//                       transition={{ duration: 0.4, delay: 0.5 }}
//                     >
//                       <Button className="bg-[#07BC0C] cursor-pointer text-white hover:bg-[#06A50B] transition-colors">
//                         Read More
//                       </Button>
//                     </motion.div>
//                   </div>
//                 </CardContent>
//               </motion.div>
//             )}
//           </AnimatePresence>
//         </Card>
//       </Link>
//     </div>
//   );
// }

// Third

// "use client";

// import { useState, useEffect, useMemo } from "react";
// import { motion, AnimatePresence } from "framer-motion";
// import { Card, CardContent } from "@/components/ui/card";
// import { Button } from "@/components/ui/button";
// import { Badge } from "@/components/ui/badge";
// import Link from "next/link";
// import BasicHeader from "../../home/<USER>/BasicHeader";
// import type { PublicBlogPost } from "@/client_apis/api/blog";
// import { formatDate } from "@/utils/dateUtils";

// interface NewsSectionProps {
//   topNewsPosts?: PublicBlogPost[];
// }

// const ImageLoader = () => {
//   const [isClient, setIsClient] = useState(false);

//   // Generate consistent particle positions
//   const particles = useMemo(() => {
//     return Array.from({ length: 6 }, (_, index) => ({
//       id: index,
//       left: [20, 40, 60, 80, 30, 70][index] || 50, // Fixed positions instead of random
//       top: [10, 30, 50, 70, 20, 80][index] || 50,
//       delay: index * 0.3,
//       duration: 3 + (index % 3),
//     }));
//   }, []);

//   useEffect(() => {
//     setIsClient(true);
//   }, []);

//   return (
//     <div className="absolute inset-0 z-30 flex items-center justify-center bg-gradient-to-br from-gray-900 via-gray-800 to-black">
//       {/* Animated background pattern */}
//       <div className="absolute inset-0 opacity-10">
//         <motion.div
//           className="absolute inset-0"
//           animate={{
//             background: [
//               "radial-gradient(circle at 20% 50%, #3b82f6 0%, transparent 50%)",
//               "radial-gradient(circle at 80% 50%, #8b5cf6 0%, transparent 50%)",
//               "radial-gradient(circle at 40% 80%, #06b6d4 0%, transparent 50%)",
//               "radial-gradient(circle at 20% 50%, #3b82f6 0%, transparent 50%)",
//             ],
//           }}
//           transition={{
//             duration: 4,
//             repeat: Number.POSITIVE_INFINITY,
//             ease: "easeInOut",
//           }}
//         />
//       </div>

//       {/* Main loader content */}
//       <div className="relative z-10 flex flex-col items-center">
//         {/* Spinning rings */}
//         <div className="relative w-20 h-20 mb-6">
//           <motion.div
//             className="absolute inset-0 border-4 border-transparent border-t-blue-500 border-r-purple-500 rounded-full"
//             animate={{ rotate: 360 }}
//             transition={{
//               duration: 1.5,
//               repeat: Number.POSITIVE_INFINITY,
//               ease: "linear",
//             }}
//           />
//           <motion.div
//             className="absolute inset-2 border-4 border-transparent border-b-cyan-500 border-l-green-500 rounded-full"
//             animate={{ rotate: -360 }}
//             transition={{
//               duration: 2,
//               repeat: Number.POSITIVE_INFINITY,
//               ease: "linear",
//             }}
//           />
//           <motion.div
//             className="absolute inset-4 border-4 border-transparent border-t-orange-500 border-r-pink-500 rounded-full"
//             animate={{ rotate: 360 }}
//             transition={{
//               duration: 1,
//               repeat: Number.POSITIVE_INFINITY,
//               ease: "linear",
//             }}
//           />
//         </div>

//         {/* Pulsing dots */}
//         <div className="flex space-x-2 mb-4">
//           {[0, 1, 2].map((index) => (
//             <motion.div
//               key={index}
//               className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
//               animate={{
//                 scale: [1, 1.5, 1],
//                 opacity: [0.5, 1, 0.5],
//               }}
//               transition={{
//                 duration: 1.5,
//                 repeat: Number.POSITIVE_INFINITY,
//                 delay: index * 0.2,
//                 ease: "easeInOut",
//               }}
//             />
//           ))}
//         </div>

//         {/* Loading text */}
//         <motion.div
//           initial={{ opacity: 0, y: 10 }}
//           animate={{ opacity: 1, y: 0 }}
//           transition={{ delay: 0.5 }}
//           className="text-center"
//         >
//           <motion.p
//             className="text-white font-medium text-lg mb-2"
//             animate={{ opacity: [0.5, 1, 0.5] }}
//             transition={{
//               duration: 2,
//               repeat: Number.POSITIVE_INFINITY,
//               ease: "easeInOut",
//             }}
//           >
//             Loading News
//           </motion.p>
//           <p className="text-gray-400 text-sm">Preparing your content...</p>
//         </motion.div>

//         {/* Animated progress bar */}
//         <motion.div
//           className="w-32 h-1 bg-gray-700 rounded-full mt-4 overflow-hidden"
//           initial={{ opacity: 0 }}
//           animate={{ opacity: 1 }}
//           transition={{ delay: 1 }}
//         >
//           <motion.div
//             className="h-full bg-gradient-to-r from-blue-500 via-purple-500 to-cyan-500 rounded-full"
//             animate={{
//               x: ["-100%", "100%"],
//             }}
//             transition={{
//               duration: 2,
//               repeat: Number.POSITIVE_INFINITY,
//               ease: "easeInOut",
//             }}
//           />
//         </motion.div>
//       </div>

//       {/* Floating particles - only render on client */}
//       {isClient &&
//         particles.map((particle) => (
//           <motion.div
//             key={particle.id}
//             className="absolute w-2 h-2 bg-white/20 rounded-full"
//             style={{
//               left: `${particle.left}%`,
//               top: `${particle.top}%`,
//             }}
//             animate={{
//               y: [0, -20, 0],
//               opacity: [0, 1, 0],
//               scale: [0.5, 1, 0.5],
//             }}
//             transition={{
//               duration: particle.duration,
//               repeat: Number.POSITIVE_INFINITY,
//               delay: particle.delay,
//               ease: "easeInOut",
//             }}
//           />
//         ))}
//     </div>
//   );
// };

// export function NewsSection({ topNewsPosts = [] }: NewsSectionProps) {
//   const [imageLoaded, setImageLoaded] = useState(false);
//   const [imageError, setImageError] = useState(false);

//   // Get the first top news post as the featured post
//   const featuredPost = topNewsPosts[0];

//   if (!featuredPost) {
//     return (
//       <div>
//         <BasicHeader text="Top News" className="mb-6 text-start" />
//         <Card className="relative flex flex-col justify-center items-center border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0">
//           <div className="text-center text-gray-400">
//             <p>No top news available</p>
//             <p className="text-sm mt-2">
//               Mark a blog post as "Top News" to display it here
//             </p>
//           </div>
//         </Card>
//       </div>
//     );
//   }

//   const imageUrl = featuredPost.banner || "/blogs/blg1.png";

//   return (
//     <div>
//       <BasicHeader text="Top News" className="mb-6 text-start" />
//       <Link href={`/blogs/${featuredPost._id}`}>
//         <Card className="relative flex flex-col justify-end border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0 cursor-pointer hover:scale-[1.02] transition-transform duration-300">
//           {/* Background Image */}
//           <div className="absolute inset-0 z-0">
//             <img
//               src={imageUrl || "/placeholder.svg"}
//               alt={featuredPost.title}
//               className={`w-full h-full object-cover transition-opacity duration-500 ${
//                 imageLoaded ? "opacity-100" : "opacity-0"
//               }`}
//               onLoad={() => setImageLoaded(true)}
//               onError={() => {
//                 setImageError(true);
//                 setImageLoaded(true);
//               }}
//             />

//             {/* Fallback gradient background */}
//             {imageError && (
//               <div className="absolute inset-0 bg-gradient-to-br from-gray-800 via-gray-900 to-black" />
//             )}
//           </div>

//           {/* Image Loader */}
//           <AnimatePresence>
//             {!imageLoaded && (
//               <motion.div
//                 initial={{ opacity: 1 }}
//                 exit={{ opacity: 0 }}
//                 transition={{ duration: 0.5 }}
//               >
//                 <ImageLoader />
//               </motion.div>
//             )}
//           </AnimatePresence>

//           {/* Content Overlay */}
//           <AnimatePresence>
//             {imageLoaded && (
//               <motion.div
//                 initial={{ opacity: 0, y: 20 }}
//                 animate={{ opacity: 1, y: 0 }}
//                 transition={{ duration: 0.6, ease: "easeOut" }}
//               >
//                 <CardContent className="p-6 relative z-20 h-auto">
//                   <div className="absolute inset-0 z-10 backdrop-blur-md bg-black/30" />
//                   <div className="relative z-20">
//                     <motion.div
//                       initial={{ opacity: 0, y: 10 }}
//                       animate={{ opacity: 1, y: 0 }}
//                       transition={{ duration: 0.4, delay: 0.2 }}
//                       className="flex items-center gap-2 mb-3"
//                     >
//                       <Badge
//                         variant="secondary"
//                         className="bg-orange-600 text-white hover:bg-orange-700"
//                       >
//                         HOT
//                       </Badge>
//                       <span className="text-sm text-gray-400">
//                         {formatDate(
//                           featuredPost.publishedAt || featuredPost.createdAt
//                         )}
//                       </span>
//                     </motion.div>

//                     <motion.h3
//                       initial={{ opacity: 0, y: 10 }}
//                       animate={{ opacity: 1, y: 0 }}
//                       transition={{ duration: 0.4, delay: 0.3 }}
//                       className="text-xl font-bold mb-4 text-white leading-tight line-clamp-3"
//                     >
//                       {featuredPost.title}
//                     </motion.h3>

//                     {featuredPost.description && (
//                       <motion.p
//                         initial={{ opacity: 0, y: 10 }}
//                         animate={{ opacity: 1, y: 0 }}
//                         transition={{ duration: 0.4, delay: 0.4 }}
//                         className="text-gray-300 mb-6 leading-relaxed line-clamp-3"
//                       >
//                         {featuredPost.description}
//                       </motion.p>
//                     )}

//                     <motion.div
//                       initial={{ opacity: 0, y: 10 }}
//                       animate={{ opacity: 1, y: 0 }}
//                       transition={{ duration: 0.4, delay: 0.5 }}
//                     >
//                       <Button className="bg-[#07BC0C] cursor-pointer text-white hover:bg-[#06A50B] transition-colors">
//                         Read More
//                       </Button>
//                     </motion.div>
//                   </div>
//                 </CardContent>
//               </motion.div>
//             )}
//           </AnimatePresence>
//         </Card>
//       </Link>
//     </div>
//   );
// }

// Fourth

"use client";

import { useState, useEffect, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import BasicHeader from "../../home/<USER>/BasicHeader";
import type { PublicBlogPost } from "@/client_apis/api/blog";
import { formatDate } from "@/utils/dateUtils";

interface NewsSectionProps {
  topNewsPosts?: PublicBlogPost[];
}

const ImageLoader = () => {
  const [isClient, setIsClient] = useState(false);

  // Generate consistent particle positions
  const particles = useMemo(() => {
    return Array.from({ length: 6 }, (_, index) => ({
      id: index,
      left: [20, 40, 60, 80, 30, 70][index] || 50, // Fixed positions instead of random
      top: [10, 30, 50, 70, 20, 80][index] || 50,
      delay: index * 0.3,
      duration: 3 + (index % 3),
    }));
  }, []);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div className="absolute inset-0 z-30 flex items-center justify-center bg-gradient-to-br from-gray-900 via-gray-800 to-black">
      {/* Animated background pattern */}
      <div className="absolute inset-0 opacity-10">
        <motion.div
          className="absolute inset-0"
          animate={{
            background: [
              "radial-gradient(circle at 20% 50%, #3b82f6 0%, transparent 50%)",
              "radial-gradient(circle at 80% 50%, #8b5cf6 0%, transparent 50%)",
              "radial-gradient(circle at 40% 80%, #06b6d4 0%, transparent 50%)",
              "radial-gradient(circle at 20% 50%, #3b82f6 0%, transparent 50%)",
            ],
          }}
          transition={{
            duration: 4,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        />
      </div>

      {/* Main loader content */}
      <div className="relative z-10 flex flex-col items-center">
        {/* Spinning rings */}
        <div className="relative w-20 h-20 mb-6">
          <motion.div
            className="absolute inset-0 border-4 border-transparent border-t-blue-500 border-r-purple-500 rounded-full"
            animate={{ rotate: 360 }}
            transition={{
              duration: 1.5,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
          />
          <motion.div
            className="absolute inset-2 border-4 border-transparent border-b-cyan-500 border-l-green-500 rounded-full"
            animate={{ rotate: -360 }}
            transition={{
              duration: 2,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
          />
          <motion.div
            className="absolute inset-4 border-4 border-transparent border-t-orange-500 border-r-pink-500 rounded-full"
            animate={{ rotate: 360 }}
            transition={{
              duration: 1,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
          />
        </div>

        {/* Pulsing dots */}
        <div className="flex space-x-2 mb-4">
          {[0, 1, 2].map((index) => (
            <motion.div
              key={index}
              className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Number.POSITIVE_INFINITY,
                delay: index * 0.2,
                ease: "easeInOut",
              }}
            />
          ))}
        </div>

        {/* Loading text */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="text-center"
        >
          <motion.p
            className="text-white font-medium text-lg mb-2"
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{
              duration: 2,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut",
            }}
          >
            Loading News
          </motion.p>
          <p className="text-gray-400 text-sm">Preparing your content...</p>
        </motion.div>

        {/* Animated progress bar */}
        <motion.div
          className="w-32 h-1 bg-gray-700 rounded-full mt-4 overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
        >
          <motion.div
            className="h-full bg-gradient-to-r from-blue-500 via-purple-500 to-cyan-500 rounded-full"
            animate={{
              x: ["-100%", "100%"],
            }}
            transition={{
              duration: 2,
              repeat: Number.POSITIVE_INFINITY,
              ease: "easeInOut",
            }}
          />
        </motion.div>
      </div>

      {/* Floating particles - only render on client */}
      {isClient &&
        particles.map((particle) => (
          <motion.div
            key={particle.id}
            className="absolute w-2 h-2 bg-white/20 rounded-full"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0, 1, 0],
              scale: [0.5, 1, 0.5],
            }}
            transition={{
              duration: particle.duration,
              repeat: Number.POSITIVE_INFINITY,
              delay: particle.delay,
              ease: "easeInOut",
            }}
          />
        ))}
    </div>
  );
};

export function NewsSection({ topNewsPosts = [] }: NewsSectionProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Get the first top news post as the featured post
  const featuredPost = topNewsPosts[0];

  if (!featuredPost) {
    return (
      <div>
        <BasicHeader text="Top News" className="mb-6 text-start" />
        <Card className="relative flex flex-col justify-center items-center border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0">
          <div className="text-center text-gray-400">
            <p>No top news available</p>
            <p className="text-sm mt-2">
              Mark a blog post as &ldquo;Top News&rdquo; to display it here
            </p>
          </div>
        </Card>
      </div>
    );
  }

  const imageUrl = featuredPost.banner || "/blogs/blg1.png";

  return (
    <div>
      <BasicHeader text="Top News" className="mb-6 text-start" />
      <Link href={`/blogs/${featuredPost.slug}`}>
        <Card className="relative flex flex-col justify-end border-gray-700 rounded-3xl h-[400px] w-full overflow-hidden p-0 cursor-pointer hover:scale-[1.02] transition-transform duration-300">
          {/* Background Image */}
          <div className="absolute inset-0 z-0">
            <img
              src={imageUrl || "/placeholder.svg"}
              alt={featuredPost.title}
              className={`w-full h-full object-cover transition-opacity duration-500 ${
                imageLoaded ? "opacity-100" : "opacity-0"
              }`}
              onLoad={() => setImageLoaded(true)}
              onError={() => {
                setImageError(true);
                setImageLoaded(true);
              }}
            />

            {/* Fallback gradient background */}
            {imageError && (
              <div className="absolute inset-0 bg-gradient-to-br from-gray-800 via-gray-900 to-black" />
            )}
          </div>

          {/* Image Loader */}
          <AnimatePresence>
            {!imageLoaded && (
              <motion.div
                initial={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              >
                <ImageLoader />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Content Overlay */}
          <AnimatePresence>
            {imageLoaded && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
              >
                <CardContent className="p-6 relative z-20 h-auto">
                  <div className="absolute inset-0 z-10 backdrop-blur-md bg-black/30" />
                  <div className="relative z-20">
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: 0.2 }}
                      className="flex items-center gap-2 mb-3"
                    >
                      <Badge
                        variant="secondary"
                        className="bg-orange-600 text-white hover:bg-orange-700"
                      >
                        HOT
                      </Badge>
                      <span className="text-sm text-gray-400">
                        {formatDate(
                          featuredPost.publishedAt || featuredPost.createdAt
                        )}
                      </span>
                    </motion.div>

                    <motion.h3
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: 0.3 }}
                      className="text-xl font-bold mb-4 text-white leading-tight line-clamp-3"
                    >
                      {featuredPost.title}
                    </motion.h3>

                    {/* {featuredPost.description && (
                      <motion.p
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: 0.4 }}
                        className="text-gray-300 mb-6 leading-relaxed line-clamp-3"
                      >
                        {featuredPost.description}
                      </motion.p>
                    )} */}

                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: 0.5 }}
                    >
                      <Button className="bg-[#07BC0C] cursor-pointer text-white hover:bg-[#06A50B] transition-colors">
                        Read More
                      </Button>
                    </motion.div>
                  </div>
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>
        </Card>
      </Link>
    </div>
  );
}
