// "use client";
// import { createBlogCategory, deleteBlogCategory } from "@/client_apis/api/blog";
// import { Button } from "@/components/ui/button";
// import {
//   Dialog,
//   DialogContent,
//   DialogHeader,
//   DialogTitle,
//   DialogTrigger,
// } from "@/components/ui/dialog";
// import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";
// import { Textarea } from "@/components/ui/textarea";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { Info, Plus, Trash2 } from "lucide-react";
// import React, { useState } from "react";
// import { useForm } from "react-hook-form";
// import { toast } from "react-toastify";
// import { z } from "zod";

// // Zod schema for form validation
// const categorySchema = z.object({
//   name: z.string().min(1, "Category name is required"),
//   slug: z.string().min(1, "Category slug is required"),
//   image: z.string().url("Must be a valid URL").optional().or(z.literal("")),
//   description: z.string().min(1, "Category description is required"),
// });

// type CategoryFormData = z.infer<typeof categorySchema>;

// const FormField = ({
//   label,
//   error,
//   children,
//   required = false,
//   tooltip = false,
//   htmlFor,
// }: {
//   label: string;
//   error?: string;
//   children: React.ReactNode;
//   required?: boolean;
//   tooltip?: boolean;
//   htmlFor?: string;
// }) => (
//   <div className="space-y-2">
//     <div className="flex items-center gap-2">
//       <Label htmlFor={htmlFor}>{label}</Label>
//       {tooltip && <Info className="w-4 h-4 text-gray-400" />}
//     </div>
//     {children}
//     {error && <p className="text-sm text-red-500">{error}</p>}
//   </div>
// );

// interface AddCategoryDialogProps {
//   trigger?: React.ReactNode;
//   onSubmit?: (data: CategoryFormData) => void;
// }

// const AddCategoryDialog: React.FC<AddCategoryDialogProps> = ({ trigger }) => {
//   const [open, setOpen] = useState(false);

//   const {
//     register,
//     handleSubmit,
//     formState: { errors, isSubmitting },
//     reset,
//     watch,
//   } = useForm<CategoryFormData>({
//     resolver: zodResolver(categorySchema),
//     defaultValues: {
//       name: "",
//       slug: "",
//       description: "",
//     },
//   });

//   const onFormSubmit = async (data: CategoryFormData) => {
//     const res = await createBlogCategory(data);
//     if (!res.success) toast.error(res.message);
//     toast.success(res.message);
//   };

//   const handleOpenChange = (newOpen: boolean) => {
//     setOpen(newOpen);
//     if (!newOpen) {
//       reset();
//     }
//   };

//   return (
//     <Dialog open={open} onOpenChange={handleOpenChange}>
//       <DialogTrigger onClick={() => setOpen(true)} asChild>
//         {trigger || (
//           <Button className="flex items-center gap-2">
//             <Plus className="w-4 h-4" />
//             Add New Category
//           </Button>
//         )}
//       </DialogTrigger>

//       <DialogContent>
//         <DialogHeader>
//           <DialogTitle>Add New Category</DialogTitle>
//         </DialogHeader>

//         <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
//           <div className="space-y-4">
//             <FormField
//               label="Name"
//               error={errors.name?.message}
//               tooltip
//               htmlFor="category-name"
//             >
//               <Input
//                 id="category-name"
//                 placeholder="Enter the name of the category"
//                 {...register("name")}
//               />
//             </FormField>

//             <FormField
//               label="Slug"
//               error={errors.slug?.message}
//               tooltip
//               htmlFor="category-slug"
//             >
//               <Input
//                 id="category-slug"
//                 placeholder="Enter the slug of the category"
//                 {...register("slug")}
//               />
//             </FormField>

//             <FormField
//               label="Description"
//               error={errors.description?.message}
//               tooltip
//               htmlFor="category-description"
//             >
//               <Textarea
//                 id="category-description"
//                 placeholder="Enter the description of the category"
//                 rows={4}
//                 {...register("description")}
//               />
//             </FormField>
//           </div>

//           <div className="flex justify-end">
//             <Button
//               type="submit"
//               disabled={isSubmitting}
//               onClick={handleSubmit(onFormSubmit)}
//             >
//               {isSubmitting ? "Creating..." : "Create Category"}
//             </Button>
//           </div>
//         </form>
//       </DialogContent>
//     </Dialog>
//   );
// };

// // Demo component showing usage
// interface IProps {
//   categories: {
//     _id: string;
//     name: string;
//     slug: string;
//     description: string;
//   }[];
// }
// export default function CategoryList({ categories }: IProps) {
//   const handleDelete = async (id: string) => {
//     const res = await deleteBlogCategory(id);
//     console.log("res", res);
//     if (!res.success) toast.error(res.message);
//     else toast.success(res.message);
//   };
//   return (
//     <div className="p-8 pt-12 max-w-4xl mx-auto">
//       <div className="mb-8">
//         <h1 className="text-2xl font-bold mb-4">Category Management</h1>
//         <AddCategoryDialog />
//       </div>

//       {categories.length > 0 && (
//         <div className="mt-8">
//           <h2 className="text-lg font-semibold mb-4">Created Categories</h2>
//           <div className="grid gap-4">
//             {categories.map((category, index) => (
//               <div
//                 key={index}
//                 className="p-4 flex items-center justify-between border rounded-lg bg-gray-50"
//               >
//                 <div>
//                   <h3 className="font-medium">{category.name}</h3>
//                   {/* <p className="text-sm text-gray-600">Slug: {category.slug}</p> */}
//                   <p className="text-sm text-gray-600 mt-1">
//                     {category.description}
//                   </p>
//                 </div>
//                 <Button
//                   onClick={() => handleDelete(category._id)}
//                   variant="destructive"
//                   size="sm"
//                 >
//                   Del
//                 </Button>
//               </div>
//             ))}
//           </div>
//         </div>
//       )}
//     </div>
//   );
// }
"use client";

import { createBlogCategory, deleteBlogCategory } from "@/client_apis/api/blog";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { Info, Plus, Search, Trash2 } from "lucide-react";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { z } from "zod";

// Zod schema for form validation
const categorySchema = z.object({
  name: z.string().min(1, "Category name is required"),
  // slug: z.string().min(1, "Category slug is required"),
  image: z.string().url("Must be a valid URL").optional().or(z.literal("")),
  description: z.string().min(1, "Category description is required"),
});

type CategoryFormData = z.infer<typeof categorySchema>;

const FormField = ({
  label,
  error,
  children,
  required = false,
  tooltip = false,
  htmlFor,
}: {
  label: string;
  error?: string;
  children: React.ReactNode;
  required?: boolean;
  tooltip?: boolean;
  htmlFor?: string;
}) => (
  <div className="space-y-2">
    <div className="flex items-center gap-2">
      <Label htmlFor={htmlFor} className="text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500">*</span>}
      </Label>
      {tooltip && (
        <Info
          className="w-4 h-4 text-gray-400 cursor-help"
          aria-label="Tooltip for additional information"
        />
      )}
    </div>
    {children}
    {error && <p className="text-sm text-red-500">{error}</p>}
  </div>
);

interface AddCategoryDialogProps {
  trigger?: React.ReactNode;
  onSubmit?: (data: CategoryFormData) => void;
}

const AddCategoryDialog: React.FC<AddCategoryDialogProps> = ({ trigger }) => {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<CategoryFormData>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: "",

      description: "",
    },
  });

  const onFormSubmit = async (data: CategoryFormData) => {
    setIsLoading(true);
    try {
      const res = await createBlogCategory(data);
      if (!res.success) {
        toast.error(res.message);
      } else {
        toast.success(res.message);
        setOpen(false);
        reset();
      }
    } catch (error) {
      toast.error("Failed to create category");
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      reset();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || (
          <Button
            className="flex items-center gap-2 bg-black hover:bg-gray-800 transition-colors"
            onClick={() => setOpen(true)}
          >
            <Plus className="w-4 h-4" />
            Add New Category
          </Button>
        )}
      </DialogTrigger>

      <DialogContent className="sm:max-w-md bg-white rounded-lg shadow-xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-gray-900">
            Add New Category
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
          <div className="space-y-4">
            <FormField
              label="Name"
              error={errors.name?.message}
              tooltip
              htmlFor="category-name"
              required
            >
              <Input
                id="category-name"
                placeholder="Enter category name"
                className="border-gray-200 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500"
                {...register("name")}
              />
            </FormField>

            {/* <FormField
              label="Slug"
              error={errors.slug?.message}
              tooltip
              htmlFor="category-slug"
              required
            > */}
            {/* <Input
                id="category-slug"
                placeholder="Enter category slug"
                className="border-gray-200 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500"
                {...register("slug")}
              /> */}
            {/* </FormField> */}

            <FormField
              label="Description"
              error={errors.description?.message}
              tooltip
              htmlFor="category-description"
              required
            >
              <Textarea
                id="category-description"
                placeholder="Enter category description"
                rows={4}
                className="border-gray-200 bg-gray-50 focus:bg-white focus:ring-2 focus:ring-blue-500"
                {...register("description")}
              />
            </FormField>
          </div>

          <div className="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              className="border-gray-200 text-gray-700 hover:bg-gray-100"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || isLoading}
              className="bg-black hover:bg-gray-800 transition-colors"
            >
              {isSubmitting || isLoading ? (
                <span className="flex items-center gap-2">
                  <svg
                    className="animate-spin h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8v8h8a8 8 0 01-8 8 8 8 0 01-8-8z"
                    />
                  </svg>
                  Creating...
                </span>
              ) : (
                "Create Category"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

interface IProps {
  categories: {
    _id: string;
    name: string;
    slug: string;
    description: string;
  }[];
}

export default function CategoryList({ categories }: IProps) {
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  const handleDelete = async (id: string) => {
    setIsDeleting(id);
    try {
      const res = await deleteBlogCategory(id);
      if (!res.success) {
        toast.error(res.message);
      } else {
        toast.success(res.message);
      }
    } catch (error) {
      toast.error("Failed to delete category");
    } finally {
      setIsDeleting(null);
    }
  };

  // Filter categories based on search query
  const filteredCategories = categories.filter(
    (category) =>
      searchQuery === "" ||
      category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (category.slug &&
        category.slug.toLowerCase().includes(searchQuery.toLowerCase())) ||
      category.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6 p-8 pt-12 max-w-4xl mx-auto border border-gray-200 rounded-lg">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">
          Category Management
        </h1>
        <AddCategoryDialog />
      </div>

      {/* Search */}
      <div className="relative w-80">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          placeholder="Search categories..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 bg-gray-50 border-gray-200 focus:bg-white"
        />
      </div>

      {/* Categories List */}
      {filteredCategories.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          {searchQuery
            ? "No categories found matching your search."
            : "No categories found."}
        </div>
      ) : (
        <div className="mt-8">
          <h2 className="text-lg font-semibold mb-4 text-gray-900">
            Created Categories
          </h2>
          <div className="grid gap-4">
            {filteredCategories.map((category) => (
              <div
                key={category._id}
                className="p-4 flex items-center justify-between border border-gray-200 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors"
              >
                <div>
                  <h3 className="font-medium text-gray-900">{category.name}</h3>
                  {/* <p className="text-sm text-gray-600">
                    Slug: {category.slug || "-"}
                  </p> */}
                  <p className="text-sm text-gray-600 mt-1">
                    {category.description}
                  </p>
                </div>
                <Button
                  onClick={() => handleDelete(category._id)}
                  variant="outline"
                  size="sm"
                  disabled={isDeleting === category._id}
                  className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  {isDeleting === category._id ? (
                    <svg
                      className="animate-spin h-5 w-5 text-red-600"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      />
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8v8h8a8 8 0 01-8 8 8 8 0 01-8-8z"
                      />
                    </svg>
                  ) : (
                    <Trash2 className="h-4 w-4" />
                  )}
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
