import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import connectDB from "@/lib/mongodb";
import Post from "@/models/Post";
import Category from "@/models/Category";
import { verifyAuth } from "@/lib/auth";
import { MinioService } from "@/lib/minio";
import { CardDescription } from "@/components/ui/card";

// File validation constants
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_MIME_TYPES = new Set([
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp",
  "image/gif",
  "image/svg+xml",
]);

// Validation schema
const postSchema = z.object({
  title: z.string().min(1),
  description: z.string().min(1),
});

function validateFileBuffer(buffer: Buffer, maxSizeMB = 5) {
  if (buffer.length > maxSizeMB * 1024 * 1024) {
    throw new Error(`File size exceeds ${maxSizeMB}MB`);
  }
}
// Helper function to validate file
const validateFile = (file: File): void => {
  if (!ALLOWED_MIME_TYPES.has(file.type)) {
    throw new Error(
      `Invalid file type: ${file.type}. Only images are allowed.`
    );
  }

  if (file.size > MAX_FILE_SIZE) {
    throw new Error(
      `File size too large: ${(file.size / 1024 / 1024).toFixed(
        2
      )}MB. Maximum 10MB allowed.`
    );
  }
};

// Helper function to process base64 images in content
async function processContentImages(content: string): Promise<string> {
  const base64ImageRegex =
    /<img[^>]+src="data:image\/([^;]+);base64,([^"]+)"[^>]*>/g;
  let processedContent = content;
  const matches = [...content.matchAll(base64ImageRegex)];

  for (const match of matches) {
    try {
      const [fullMatch, imageType, base64Data] = match;
      const buffer = Buffer.from(base64Data, "base64");
      const fileName = `content-image-${Date.now()}.${imageType}`;
      const contentType = `image/${imageType}`;

      // Upload to MinIO
      const imageUrl = await MinioService.uploadFile(
        buffer,
        fileName,
        contentType,
        "content"
      );

      // Replace base64 image with uploaded URL
      processedContent = processedContent.replace(
        fullMatch,
        fullMatch.replace(/src="[^"]*"/, `src="${imageUrl}"`)
      );
    } catch (error) {
      console.error("Error processing content image:", error);
      // Continue with other images if one fails
    }
  }

  return processedContent;
}

// GET /api/posts - Get all posts with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const data = await Post.find()
      .populate("categories", "name description isActive")
      .lean();

    // const { searchParams } = new URL(request.url);
    // const page = Number.parseInt(searchParams.get("page") || "1");
    // const limit = Number.parseInt(searchParams.get("limit") || "10");
    // const category = searchParams.get("category");
    // const tag = searchParams.get("tag");
    // const search = searchParams.get("search");
    // const status = searchParams.get("status") || "published";
    // const author = searchParams.get("author");
    // const isBlog = searchParams.get("isBlog");

    // // Build query
    // const query: any = {};

    // if (status) query.status = status;
    // if (category) query.categories = { $in: [category] };
    // if (tag) query.tags = { $in: [tag] };
    // if (author) query.author = author;
    // if (isBlog !== null) query.isBlog = isBlog === "true";

    // if (search) {
    //   query.$or = [
    //     { title: { $regex: search, $options: "i" } },
    //     { content: { $regex: search, $options: "i" } },
    //     { metaTitle: { $regex: search, $options: "i" } },
    //     { metaDescription: { $regex: search, $options: "i" } },
    //   ];
    // }

    // const skip = (page - 1) * limit;

    // const posts = await Post.find(query)
    //   .sort({ publishedAt: -1, createdAt: -1 })
    //   .skip(skip)
    //   .limit(limit)
    //   .lean();

    // const total = await Post.countDocuments(query);

    // return NextResponse.json({
    //   success: true,
    //   data: {
    //     posts,
    //     pagination: {
    //       page,
    //       limit,
    //       total,
    //       pages: Math.ceil(total / limit),
    //       hasNext: page < Math.ceil(total / limit),
    //       hasPrev: page > 1,
    //     },
    //   },
    console.log("data", data);
    // });

    return NextResponse.json({
      success: true,
      data: {
        posts: data,
      },
    });
  } catch (error) {
    console.error("Error fetching posts:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch posts" },
      { status: 500 }
    );
  }
}

// POST /api/posts - Create a new post with image handling
// POST /api/posts
export async function POST(req: NextRequest) {
  try {
    const contentType = req.headers.get("content-type") || "";
    console.log("createingn, creating");
    if (!contentType.includes("application/json")) {
      return NextResponse.json(
        { success: false, error: "Unsupported Content-Type" },
        { status: 415 }
      );
    }

    const body = await req.json();

    const { data, bannerImageBase64 } = body;

    if (!data || typeof data !== "object") {
      return NextResponse.json(
        { success: false, error: "Missing or invalid 'data' field" },
        { status: 400 }
      );
    }

    // Validate post data
    const validatedData = data;

    // Convert and validate base64 image
    let bannerImageBuffer: Buffer | null = null;
    let mimeType = "";

    if (bannerImageBase64 && typeof bannerImageBase64 === "string") {
      const matches = bannerImageBase64.match(/^data:(.+);base64,(.+)$/);
      if (!matches || matches.length !== 3) {
        return NextResponse.json(
          { success: false, error: "Invalid base64 image format" },
          { status: 400 }
        );
      }

      mimeType = matches[1];
      const base64Data = matches[2];
      bannerImageBuffer = Buffer.from(base64Data, "base64");

      // Validate file size
      validateFileBuffer(bannerImageBuffer);

      // Determine file extension
      const fileExtension = mimeType.split("/")[1];
      const fileName = `banner-${Date.now()}.${fileExtension}`;

      // Upload to MinIO
      const imageUrl = await MinioService.uploadFile(
        bannerImageBuffer,
        fileName,
        mimeType,
        "banners"
      );
      console.log("imageUrl", imageUrl);

      validatedData.banner = imageUrl;
    }
    console.log("validatedData", validatedData);
    await connectDB();
    const newPost = await Post.create(validatedData);

    return NextResponse.json(
      {
        success: true,
        message: "Post created successfully",
        post: newPost,
      },
      { status: 201 }
    );
  } catch (error: any) {
    console.error("Error in POST /api/posts:", error);
    return NextResponse.json(
      {
        success: false,
        error: error?.message || "Internal server error",
      },
      { status: 500 }
    );
  }
}
