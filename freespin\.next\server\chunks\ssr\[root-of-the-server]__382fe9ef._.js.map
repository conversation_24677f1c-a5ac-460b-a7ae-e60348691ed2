{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/utils/dateUtils.ts"], "sourcesContent": ["import dayjs from \"dayjs\";\r\n\r\nexport function formatTimeAgo(date: string | Date): string {\r\n  const now = dayjs();\r\n  const postDate = dayjs(date);\r\n  const diffInDays = now.diff(postDate, \"day\");\r\n  \r\n  if (diffInDays === 0) {\r\n    return \"Today\";\r\n  } else if (diffInDays === 1) {\r\n    return \"1 day ago\";\r\n  } else if (diffInDays < 7) {\r\n    return `${diffInDays} days ago`;\r\n  } else if (diffInDays < 30) {\r\n    const weeks = Math.floor(diffInDays / 7);\r\n    return weeks === 1 ? \"1 week ago\" : `${weeks} weeks ago`;\r\n  } else if (diffInDays < 365) {\r\n    const months = Math.floor(diffInDays / 30);\r\n    return months === 1 ? \"1 month ago\" : `${months} months ago`;\r\n  } else {\r\n    const years = Math.floor(diffInDays / 365);\r\n    return years === 1 ? \"1 year ago\" : `${years} years ago`;\r\n  }\r\n}\r\n\r\nexport function formatDate(date: string | Date): string {\r\n  return dayjs(date).format(\"DD/MM/YYYY\");\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,SAAS,cAAc,IAAmB;IAC/C,MAAM,MAAM,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD;IAChB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE;IACvB,MAAM,aAAa,IAAI,IAAI,CAAC,UAAU;IAEtC,IAAI,eAAe,GAAG;QACpB,OAAO;IACT,OAAO,IAAI,eAAe,GAAG;QAC3B,OAAO;IACT,OAAO,IAAI,aAAa,GAAG;QACzB,OAAO,GAAG,WAAW,SAAS,CAAC;IACjC,OAAO,IAAI,aAAa,IAAI;QAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;QACtC,OAAO,UAAU,IAAI,eAAe,GAAG,MAAM,UAAU,CAAC;IAC1D,OAAO,IAAI,aAAa,KAAK;QAC3B,MAAM,SAAS,KAAK,KAAK,CAAC,aAAa;QACvC,OAAO,WAAW,IAAI,gBAAgB,GAAG,OAAO,WAAW,CAAC;IAC9D,OAAO;QACL,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;QACtC,OAAO,UAAU,IAAI,eAAe,GAAG,MAAM,UAAU,CAAC;IAC1D;AACF;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,CAAA,GAAA,qIAAA,CAAA,UAAK,AAAD,EAAE,MAAM,MAAM,CAAC;AAC5B", "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/blogs/components/BlogSection.tsx"], "sourcesContent": ["import { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport Image from \"next/image\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { PublicBlogPost } from \"@/client_apis/api/blog\";\r\nimport { formatTimeAgo } from \"@/utils/dateUtils\";\r\nimport Link from \"next/link\";\r\n\r\ninterface BlogsSectionProps {\r\n  posts: PublicBlogPost[];\r\n  onLoadMore?: () => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nexport function BlogsSection({\r\n  posts,\r\n  onLoadMore,\r\n  isLoading,\r\n}: BlogsSectionProps) {\r\n  console.log(\"posts\", posts);\r\n  return (\r\n    <div>\r\n      <h2 className=\"text-2xl font-bold mb-6 text-white\">Latest Blogs</h2>\r\n      <div className=\"flex flex-col  gap-4 w-full\">\r\n        {posts.length > 0 ? (\r\n          posts.map((post) => (\r\n            <Link key={post._id} href={`/blogs/${post.slug}`}>\r\n              <Card className=\"bg-gray-800 border-gray-700 hover:bg-gray-750 p-0 transition-colors cursor-pointer\">\r\n                <CardContent className=\"flex p-0\">\r\n                  <div className=\"relative size-24 flex-shrink-0\">\r\n                    <Image\r\n                      src={post.banner || \"/blogs/latest1.png\"}\r\n                      alt={post.title}\r\n                      fill\r\n                      className=\"object-cover rounded-l-lg\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"flex-1 flex flex-col\">\r\n                    <div className=\"p-4 pb-2\">\r\n                      <h3 className=\"font-semibold text-white text-sm leading-tight mb-2 line-clamp-2\">\r\n                        {post.title}\r\n                      </h3>\r\n                    </div>\r\n                    {/* Border line with gap from edges */}\r\n                    <div className=\"mx-4 h-[1px]  bg-[#4F5259] \"></div>\r\n                    <div className=\"p-4 pt-2\">\r\n                      <div className=\"flex items-center gap-3 justify-between\">\r\n                        <span className=\"text-xs text-gray-400\">\r\n                          {formatTimeAgo(post.publishedAt || post.createdAt)}\r\n                        </span>\r\n                        <Badge\r\n                          variant=\"outline\"\r\n                          className=\"text-xs border-orange-600 text-orange-400\"\r\n                        >\r\n                          {post.categories[0] || \"BLOG\"}\r\n                        </Badge>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            </Link>\r\n          ))\r\n        ) : (\r\n          <div className=\"text-center text-gray-400 py-8\">\r\n            {isLoading ? \"Loading posts...\" : \"No blog posts available.\"}\r\n          </div>\r\n        )}\r\n        {onLoadMore && (\r\n          <div className=\"flex items-center justify-center mt-2\">\r\n            <Button\r\n              className=\"bg-[#07BC0C] cursor-pointer text-white hover:bg-[#06A50B] transition-colors\"\r\n              onClick={onLoadMore}\r\n              disabled={isLoading}\r\n            >\r\n              {isLoading ? \"Loading...\" : \"Load More\"}\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;AAQO,SAAS,aAAa,EAC3B,KAAK,EACL,UAAU,EACV,SAAS,EACS;IAClB,QAAQ,GAAG,CAAC,SAAS;IACrB,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BAAqC;;;;;;0BACnD,8OAAC;gBAAI,WAAU;;oBACZ,MAAM,MAAM,GAAG,IACd,MAAM,GAAG,CAAC,CAAC,qBACT,8OAAC,4JAAA,CAAA,UAAI;4BAAgB,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,EAAE;sCAC9C,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,MAAM,IAAI;gDACpB,KAAK,KAAK,KAAK;gDACf,IAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;;;;;;8DAIf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,WAAW,IAAI,KAAK,SAAS;;;;;;0EAEnD,8OAAC,iIAAA,CAAA,QAAK;gEACJ,SAAQ;gEACR,WAAU;0EAET,KAAK,UAAU,CAAC,EAAE,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA5B1B,KAAK,GAAG;;;;kDAsCrB,8OAAC;wBAAI,WAAU;kCACZ,YAAY,qBAAqB;;;;;;oBAGrC,4BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,WAAU;4BACV,SAAS;4BACT,UAAU;sCAET,YAAY,eAAe;;;;;;;;;;;;;;;;;;;;;;;AAO1C", "debugId": null}}, {"offset": {"line": 461, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/home/<USER>/BasicHeader.tsx"], "sourcesContent": ["import clsx from \"clsx\";\r\nimport React from \"react\";\r\n\r\nconst BasicHeader: React.FC<{ className?: string; text: string }> = ({\r\n  className,\r\n  text,\r\n}) => {\r\n  return (\r\n    <h2\r\n      className={clsx(\"text-2xl font-bold text-center text-white\", className)}\r\n    >\r\n      {text}\r\n    </h2>\r\n  );\r\n};\r\n\r\nexport default BasicHeader;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAGA,MAAM,cAA8D,CAAC,EACnE,SAAS,EACT,IAAI,EACL;IACC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,qIAAA,CAAA,UAAI,AAAD,EAAE,6CAA6C;kBAE5D;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/blogs/%5Bid%5D/components/RelatedBlogsSection.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Card } from \"@/components/ui/card\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { PublicBlogPost } from \"@/client_apis/api/blog\";\r\nimport { formatDate } from \"@/utils/dateUtils\";\r\nimport BasicHeader from \"../../../home/<USER>/BasicHeader\";\r\n\r\ninterface RelatedBlogsSectionProps {\r\n  posts: PublicBlogPost[];\r\n  text?: string;\r\n}\r\n\r\nexport function RelatedBlogsSection({\r\n  posts,\r\n  text = \"Related Blogs\",\r\n}: RelatedBlogsSectionProps) {\r\n  return (\r\n    <div>\r\n      <BasicHeader text={text} />\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\">\r\n        {posts.length > 0 ? (\r\n          posts.map((post) => (\r\n            <Link key={post._id} href={`/blogs/${post._id}`}>\r\n              <Card className=\"bg-gray-800 border-gray-700 overflow-hidden hover:bg-gray-750 transition-colors cursor-pointer group flex flex-col h-full p-0\">\r\n                {/* Banner Image - Takes most space */}\r\n                <div className=\"relative flex-1 w-full min-h-[250px]\">\r\n                  <Image\r\n                    src={post.banner || \"/placeholder.svg\"}\r\n                    alt={post.title}\r\n                    fill\r\n                    className=\"object-cover\"\r\n                  />\r\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent\" />\r\n                </div>\r\n\r\n                {/* Content at bottom */}\r\n                <div className=\"p-4 mt-auto\">\r\n                  {/* Date */}\r\n                  <div className=\"mb-2\">\r\n                    <time className=\"text-sm text-gray-400 font-medium\">\r\n                      {formatDate(post.publishedAt || post.createdAt)}\r\n                    </time>\r\n                  </div>\r\n\r\n                  {/* Title */}\r\n                  <h3 className=\"text-lg font-bold text-white leading-tight line-clamp-2 group-hover:text-gray-200 transition-colors\">\r\n                    {post.title}\r\n                  </h3>\r\n\r\n                  {/* Categories (optional) */}\r\n                  {post.categories && post.categories.length > 0 && (\r\n                    <div className=\"mt-2\">\r\n                      <span className=\"inline-block px-2 py-1 text-xs bg-orange-600/20 text-orange-400 rounded-md\">\r\n                        {post.categories[0]}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </Card>\r\n            </Link>\r\n          ))\r\n        ) : (\r\n          <div className=\"col-span-2 text-center py-8\">\r\n            <p className=\"text-gray-400\">\r\n              No related blogs found in this category.\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAEA;AACA;;;;;;;AAOO,SAAS,oBAAoB,EAClC,KAAK,EACL,OAAO,eAAe,EACG;IACzB,qBACE,8OAAC;;0BACC,8OAAC,6JAAA,CAAA,UAAW;gBAAC,MAAM;;;;;;0BACnB,8OAAC;gBAAI,WAAU;0BACZ,MAAM,MAAM,GAAG,IACd,MAAM,GAAG,CAAC,CAAC,qBACT,8OAAC,4JAAA,CAAA,UAAI;wBAAgB,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;kCAC7C,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CAEd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,KAAK,MAAM,IAAI;4CACpB,KAAK,KAAK,KAAK;4CACf,IAAI;4CACJ,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,WAAW,IAAI,KAAK,SAAS;;;;;;;;;;;sDAKlD,8OAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;wCAIZ,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,MAAM,GAAG,mBAC3C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DACb,KAAK,UAAU,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;uBA/BpB,KAAK,GAAG;;;;8CAwCrB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAQzC", "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from \"mongoose\";\r\n\r\nconst MONGODB_URI = process.env.MONGODB_URI!;\r\n\r\nif (!MONGODB_URI) {\r\n  throw new Error(\r\n    \"Please define the MONGODB_URI environment variable inside .env.local\"\r\n  );\r\n}\r\n\r\n/**\r\n * Global is used here to maintain a cached connection across hot reloads\r\n * in development. This prevents connections growing exponentially\r\n * during API Route usage.\r\n */\r\n// @ts-expect-error //global can have type any\r\nlet cached = global?.mongoose;\r\n\r\nif (!cached) {\r\n  // @ts-expect-error //global mongoose can have type any\r\n  cached = global.mongoose = { conn: null, promise: null };\r\n}\r\n\r\nasync function connectDB() {\r\n  if (cached.conn) {\r\n    return cached.conn;\r\n  }\r\n\r\n  if (!cached.promise) {\r\n    const opts = {\r\n      bufferCommands: false,\r\n    };\r\n\r\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\r\n      return mongoose;\r\n    });\r\n  }\r\n\r\n  try {\r\n    cached.conn = await cached.promise;\r\n  } catch (e) {\r\n    cached.promise = null;\r\n    throw e;\r\n  }\r\n\r\n  return cached.conn;\r\n}\r\n\r\nexport default connectDB;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MACR;AAEJ;AAEA;;;;CAIC,GACD,8CAA8C;AAC9C,IAAI,SAAS,QAAQ;AAErB,IAAI,CAAC,QAAQ;IACX,uDAAuD;IACvD,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/models/Post.ts"], "sourcesContent": ["import mongoose, { type Document, Mongoose, Schema, Types } from \"mongoose\";\r\n\r\nexport interface IPost extends Document {\r\n  // Blog Post fields\r\n  title: string;\r\n  slug?: string;\r\n  canonicalUrl?: string;\r\n  existingUrl: boolean;\r\n  content: string; // Main content field\r\n  excerpt?: string; // Short description/excerpt\r\n  description: string; // For backward compatibility\r\n  isBlog: boolean;\r\n  categories: Types.ObjectId[];\r\n  tags: string[];\r\n  author?: string; // Author reference\r\n\r\n  // SEO fields\r\n  metaTitle: string;\r\n  metaDescription: string;\r\n  metaKeywords: string;\r\n\r\n  // FAQ fields\r\n  // faqs: {\r\n  //   question: string;\r\n  //   answer: string;\r\n  //   index: number;\r\n  // }[];\r\n\r\n  // Banner fields\r\n  banner: string;\r\n\r\n  // Additional blog functionality fields\r\n  status: \"draft\" | \"published\" | \"archived\";\r\n  isPublished: boolean;\r\n  publishedAt?: Date;\r\n  views: number;\r\n  readTime: number;\r\n  isTopNews: boolean;\r\n  createdAt: Date;\r\n  updatedAt: Date;\r\n}\r\n\r\nconst PostSchema = new Schema<IPost>(\r\n  {\r\n    // Blog Post fields\r\n    title: {\r\n      type: String,\r\n      required: [true, \"Post title is required\"],\r\n      trim: true,\r\n      maxlength: [1000, \"Title cannot exceed 200 characters\"],\r\n    },\r\n\r\n    slug: {\r\n      type: String,\r\n      trim: true,\r\n      lowercase: true,\r\n    },\r\n\r\n    canonicalUrl: {\r\n      type: String,\r\n      trim: true,\r\n      validate: {\r\n        validator: (v: string) => {\r\n          if (!v) return true; // Allow empty string\r\n          return /^https?:\\/\\/.+/.test(v);\r\n        },\r\n        message: \"Canonical URL must be a valid URL\",\r\n      },\r\n    },\r\n    existingUrl: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n\r\n    content: {\r\n      type: String,\r\n      required: [true, \"Post content is required\"],\r\n    },\r\n\r\n    excerpt: {\r\n      type: String,\r\n      trim: true,\r\n      maxlength: [1000, \"Excerpt cannot exceed 300 characters\"],\r\n    },\r\n\r\n    description: {\r\n      type: String,\r\n      // Not required anymore since we have content field\r\n    },\r\n\r\n    author: {\r\n      type: String,\r\n      trim: true,\r\n    },\r\n    isBlog: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    categories: [\r\n      {\r\n        type: mongoose.Schema.Types.ObjectId,\r\n        ref: \"Category\",\r\n      },\r\n    ],\r\n    tags: [\r\n      {\r\n        type: String,\r\n        trim: true,\r\n        lowercase: true,\r\n      },\r\n    ],\r\n\r\n    // SEO fields\r\n    metaTitle: {\r\n      type: String,\r\n      required: [true, \"Meta title is required\"],\r\n      maxlength: [1000, \"Meta title cannot exceed 60 characters\"],\r\n    },\r\n    metaDescription: {\r\n      type: String,\r\n      required: [true, \"Meta description is required\"],\r\n      maxlength: [1000, \"Meta description cannot exceed 160 characters\"],\r\n    },\r\n    metaKeywords: {\r\n      type: String,\r\n      required: [true, \"Meta keywords are required\"],\r\n      maxlength: [1000, \"Meta keywords cannot exceed 200 characters\"],\r\n    },\r\n\r\n    // Banner fields\r\n    banner: {\r\n      type: String,\r\n      required: [true, \"Banner image is required\"],\r\n      trim: true,\r\n    },\r\n\r\n    // Additional blog functionality fields\r\n    status: {\r\n      type: String,\r\n      enum: [\"draft\", \"published\", \"archived\"],\r\n      default: \"draft\",\r\n    },\r\n    isPublished: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    publishedAt: {\r\n      type: Date,\r\n      default: null,\r\n    },\r\n    views: {\r\n      type: Number,\r\n      default: 0,\r\n    },\r\n    readTime: {\r\n      type: Number,\r\n      default: 1,\r\n    },\r\n    isTopNews: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  {\r\n    timestamps: true,\r\n  }\r\n);\r\n\r\n// Pre-save middleware\r\nPostSchema.pre(\"save\", function (next) {\r\n  // Generate slug from title if not provided\r\n  if (this.isModified(\"title\") && !this.slug) {\r\n    this.slug = this.title\r\n      .toLowerCase()\r\n      .replace(/[^a-z0-9]+/g, \"-\")\r\n      .replace(/^-+|-+$/g, \"\");\r\n  }\r\n\r\n  // Calculate read time (average 200 words per minute)\r\n  if (this.isModified(\"content\")) {\r\n    const wordCount = (this.content || \"\").split(/\\s+/).length;\r\n    this.readTime = Math.ceil(wordCount / 200);\r\n  }\r\n\r\n  // Set published date when status changes to published\r\n  if (\r\n    this.isModified(\"status\") &&\r\n    this.status === \"published\" &&\r\n    !this.publishedAt\r\n  ) {\r\n    this.publishedAt = new Date();\r\n    this.isPublished = true;\r\n  }\r\n\r\n  next();\r\n});\r\n\r\n// Create indexes for better query performance\r\nPostSchema.index({ categories: 1 });\r\nPostSchema.index({ tags: 1 });\r\nPostSchema.index({ status: 1 });\r\nPostSchema.index({ isPublished: 1 });\r\nPostSchema.index({ publishedAt: -1 });\r\nPostSchema.index({ \"banner.title\": 1 });\r\nPostSchema.index({\r\n  title: \"text\",\r\n  content: \"text\",\r\n  metaTitle: \"text\",\r\n  metaDescription: \"text\",\r\n}); // Text search index\r\n\r\nexport default mongoose.models.Post ||\r\n  mongoose.model<IPost>(\"Post\", PostSchema);\r\n"], "names": [], "mappings": ";;;AAAA;;AA0CA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAC3B;IACE,mBAAmB;IACnB,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,MAAM;QACN,WAAW;YAAC;YAAM;SAAqC;IACzD;IAEA,MAAM;QACJ,MAAM;QACN,MAAM;QACN,WAAW;IACb;IAEA,cAAc;QACZ,MAAM;QACN,MAAM;QACN,UAAU;YACR,WAAW,CAAC;gBACV,IAAI,CAAC,GAAG,OAAO,MAAM,qBAAqB;gBAC1C,OAAO,iBAAiB,IAAI,CAAC;YAC/B;YACA,SAAS;QACX;IACF;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IAEA,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;IAC9C;IAEA,SAAS;QACP,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAAuC;IAC3D;IAEA,aAAa;QACX,MAAM;IAER;IAEA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,SAAS;IACX;IACA,YAAY;QACV;YACE,MAAM,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ;YACpC,KAAK;QACP;KACD;IACD,MAAM;QACJ;YACE,MAAM;YACN,MAAM;YACN,WAAW;QACb;KACD;IAED,aAAa;IACb,WAAW;QACT,MAAM;QACN,UAAU;YAAC;YAAM;SAAyB;QAC1C,WAAW;YAAC;YAAM;SAAyC;IAC7D;IACA,iBAAiB;QACf,MAAM;QACN,UAAU;YAAC;YAAM;SAA+B;QAChD,WAAW;YAAC;YAAM;SAAgD;IACpE;IACA,cAAc;QACZ,MAAM;QACN,UAAU;YAAC;YAAM;SAA6B;QAC9C,WAAW;YAAC;YAAM;SAA6C;IACjE;IAEA,gBAAgB;IAChB,QAAQ;QACN,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,MAAM;IACR;IAEA,uCAAuC;IACvC,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAS;YAAa;SAAW;QACxC,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,SAAS;IACX;IACA,OAAO;QACL,MAAM;QACN,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,SAAS;IACX;AACF,GACA;IACE,YAAY;AACd;AAGF,sBAAsB;AACtB,WAAW,GAAG,CAAC,QAAQ,SAAU,IAAI;IACnC,2CAA2C;IAC3C,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE;QAC1C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CACnB,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;IACzB;IAEA,qDAAqD;IACrD,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY;QAC9B,MAAM,YAAY,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,EAAE,KAAK,CAAC,OAAO,MAAM;QAC1D,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,YAAY;IACxC;IAEA,sDAAsD;IACtD,IACE,IAAI,CAAC,UAAU,CAAC,aAChB,IAAI,CAAC,MAAM,KAAK,eAChB,CAAC,IAAI,CAAC,WAAW,EACjB;QACA,IAAI,CAAC,WAAW,GAAG,IAAI;QACvB,IAAI,CAAC,WAAW,GAAG;IACrB;IAEA;AACF;AAEA,8CAA8C;AAC9C,WAAW,KAAK,CAAC;IAAE,YAAY;AAAE;AACjC,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,QAAQ;AAAE;AAC7B,WAAW,KAAK,CAAC;IAAE,aAAa;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,aAAa,CAAC;AAAE;AACnC,WAAW,KAAK,CAAC;IAAE,gBAAgB;AAAE;AACrC,WAAW,KAAK,CAAC;IACf,OAAO;IACP,SAAS;IACT,WAAW;IACX,iBAAiB;AACnB,IAAI,oBAAoB;uCAET,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IACjC,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 894, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Web%20Studio%20Nepal/FreeSpin168/freespin/src/app/%28pages%29/blogs/%5Bid%5D/page.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Image from \"next/image\";\r\nimport { notFound } from \"next/navigation\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { BlogsSection } from \"../components/BlogSection\";\r\nimport { RelatedBlogsSection } from \"./components/RelatedBlogsSection\";\r\nimport connectDB from \"@/lib/mongodb\";\r\nimport Post from \"@/models/Post\";\r\nimport Category from \"@/models/Category\";\r\nimport { formatDate } from \"@/utils/dateUtils\";\r\nimport { PublicBlogPost } from \"@/client_apis/api/blog\";\r\nimport { isValidObjectId } from \"mongoose\";\r\nimport { Metadata } from \"next\";\r\n\r\ninterface BlogPageProps {\r\n  params: Promise<{\r\n    locale: string;\r\n    id: string;\r\n  }>;\r\n}\r\nexport async function generateMetadata({\r\n  params,\r\n}: BlogPageProps): Promise<Metadata> {\r\n  const { id, locale } = await params;\r\n  const canonicalUrl = `https://www.freespin168.asia/${locale}/blogs/${id}`;\r\n\r\n  try {\r\n    await connectDB();\r\n\r\n    const post = await Post.findOne({\r\n      slug: id,\r\n      status: \"published\",\r\n      isPublished: true,\r\n      isBlog: true,\r\n    }).lean();\r\n\r\n    if (!post) {\r\n      return {\r\n        title: \"Blog Post Not Found\",\r\n        alternates: {\r\n          canonical: canonicalUrl,\r\n        },\r\n        description: \"The requested blog post could not be found.\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      title: (post as any).metaTitle || (post as any).title,\r\n      description:\r\n        (post as any).metaDescription ||\r\n        (post as any).excerpt ||\r\n        (post as any).description,\r\n      alternates: {\r\n        canonical: canonicalUrl,\r\n      },\r\n      openGraph: {\r\n        title: (post as any).metaTitle || (post as any).title,\r\n        description:\r\n          (post as any).metaDescription ||\r\n          (post as any).excerpt ||\r\n          (post as any).description,\r\n        images: (post as any).banner ? [{ url: (post as any).banner }] : [],\r\n        type: \"article\",\r\n        publishedTime: (post as any).publishedAt?.toISOString(),\r\n      },\r\n      twitter: {\r\n        card: \"summary_large_image\",\r\n        title: (post as any).metaTitle || (post as any).title,\r\n        description:\r\n          (post as any).metaDescription ||\r\n          (post as any).excerpt ||\r\n          (post as any).description,\r\n        images: (post as any).banner ? [(post as any).banner] : [],\r\n      },\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error generating metadata:\", error);\r\n    return {\r\n      title: \"Blog Post\",\r\n      description: \"Read our latest blog post.\",\r\n      alternates: {\r\n        canonical: canonicalUrl,\r\n      },\r\n    };\r\n  }\r\n}\r\nconst page = async ({ params }: BlogPageProps) => {\r\n  const { locale, id } = await params;\r\n\r\n  try {\r\n    // Connect to database\r\n    await connectDB();\r\n\r\n    // Find the blog post by ID\r\n    const post = await Post.findOne({\r\n      slug: id,\r\n      status: \"published\",\r\n      isPublished: true,\r\n      isBlog: true,\r\n    })\r\n      .populate(\"categories\", \"name description\")\r\n      .lean();\r\n\r\n    if (!post) {\r\n      notFound();\r\n    }\r\n\r\n    // Type cast for better TypeScript support\r\n    const typedPost = post as any;\r\n\r\n    // Get related posts (same categories, excluding current post)\r\n    const relatedPosts = await Post.find({\r\n      slug: { $ne: id },\r\n      status: \"published\",\r\n      isPublished: true,\r\n      isBlog: true,\r\n      categories: { $in: typedPost.categories || [] },\r\n    })\r\n      .populate(\"categories\", \"name description\")\r\n      .sort({ publishedAt: -1, createdAt: -1 })\r\n      .limit(6) // Increased to show more related posts\r\n      .lean();\r\n\r\n    console.log(\"=== RELATED POSTS DEBUG ===\");\r\n    console.log(\"Current post categories:\", typedPost.categories);\r\n    console.log(\"Related posts found:\", relatedPosts.length);\r\n\r\n    // Get latest posts for sidebar\r\n    const latestPosts = await Post.find({\r\n      slug: { $ne: id },\r\n      status: \"published\",\r\n      isPublished: true,\r\n      isBlog: true,\r\n    })\r\n      .populate(\"categories\", \"name description\")\r\n      .sort({ publishedAt: -1, createdAt: -1 })\r\n      .limit(6)\r\n      .lean();\r\n\r\n    // Serialize posts for client components\r\n    const serializePosts = (posts: any[]): PublicBlogPost[] => {\r\n      return posts.map((p) => ({\r\n        _id: p._id.toString(),\r\n        title: p.title,\r\n        description:\r\n          p.excerpt || p.description || p.content?.substring(0, 200) || \"\",\r\n        banner: p.banner || \"\",\r\n        slug: p.slug || \"\",\r\n        categories: p.categories?.map((cat: any) => cat.name || cat) || [],\r\n        tags: p.tags || [],\r\n        publishedAt:\r\n          p.publishedAt?.toISOString() || p.createdAt?.toISOString() || \"\",\r\n        createdAt: p.createdAt?.toISOString() || \"\",\r\n        readTime: p.readTime || 5,\r\n        views: p.views || 0,\r\n        metaTitle: p.metaTitle || p.title || \"\",\r\n        metaDescription: p.metaDescription || p.excerpt || p.description || \"\",\r\n      }));\r\n    };\r\n\r\n    const serializedRelatedPosts = serializePosts(relatedPosts);\r\n    const serializedLatestPosts = serializePosts(latestPosts);\r\n\r\n    // Serialize the main post categories for safe client-side usage\r\n    const serializedMainPost = {\r\n      ...typedPost,\r\n      categories:\r\n        typedPost.categories?.map((cat: any) => ({\r\n          name: cat.name || cat,\r\n          description: cat.description || \"\",\r\n        })) || [],\r\n    };\r\n\r\n    return (\r\n      <div className=\"min-h-screen bg-black text-white\">\r\n        <div className=\"container mx-auto px-4 pt-24 py-8\">\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12\">\r\n            <div className=\"lg:col-span-2 space-y-6\">\r\n              <div className=\"relative w-full space-y-4\">\r\n                {/* Banner Image */}\r\n                {serializedMainPost.banner && (\r\n                  <div className=\"h-[400px] rounded-3xl w-full z-0 overflow-hidden\">\r\n                    <Image\r\n                      src={serializedMainPost.banner}\r\n                      alt={serializedMainPost.title}\r\n                      width={800}\r\n                      height={400}\r\n                      className=\"w-full h-full object-cover\"\r\n                      priority\r\n                    />\r\n                  </div>\r\n                )}\r\n\r\n                {/* Article Content */}\r\n                <article className=\"prose prose-invert prose-lg max-w-none\">\r\n                  {/* Date */}\r\n                  <div className=\"mb-6\">\r\n                    <time className=\"text-gray-400 text-sm font-medium\">\r\n                      {formatDate(\r\n                        serializedMainPost.publishedAt?.toISOString() ||\r\n                          serializedMainPost.createdAt?.toISOString()\r\n                      )}\r\n                    </time>\r\n                  </div>\r\n\r\n                  {/* Title */}\r\n                  <h1 className=\"text-3xl font-bold mb-4 text-white leading-tight\">\r\n                    {serializedMainPost.title}\r\n                  </h1>\r\n\r\n                  {/* Categories */}\r\n                  {serializedMainPost.categories &&\r\n                    serializedMainPost.categories.length > 0 && (\r\n                      <div className=\"flex items-center gap-2 mb-6\">\r\n                        {serializedMainPost.categories.map(\r\n                          (\r\n                            category: { name: string; description: string },\r\n                            index: number\r\n                          ) => (\r\n                            <Badge\r\n                              key={index}\r\n                              variant=\"outline\"\r\n                              className=\"border-orange-600 text-orange-400\"\r\n                            >\r\n                              {category.name}\r\n                            </Badge>\r\n                          )\r\n                        )}\r\n                      </div>\r\n                    )}\r\n\r\n                  {/* Content */}\r\n                  <div\r\n                    className=\"prose prose-invert prose-lg max-w-none text-gray-300 leading-relaxed text-justify\"\r\n                    dangerouslySetInnerHTML={{\r\n                      __html:\r\n                        serializedMainPost.content ||\r\n                        serializedMainPost.description ||\r\n                        \"\",\r\n                    }}\r\n                  />\r\n\r\n                  {/* Tags */}\r\n                  {serializedMainPost.tags &&\r\n                    serializedMainPost.tags.length > 0 && (\r\n                      <div className=\"mt-8 pt-8 border-t border-gray-700\">\r\n                        <h3 className=\"text-lg font-semibold mb-4\">Tags</h3>\r\n                        <div className=\"flex flex-wrap gap-2\">\r\n                          {serializedMainPost.tags.map(\r\n                            (tag: string, index: number) => (\r\n                              <Badge\r\n                                key={index}\r\n                                variant=\"secondary\"\r\n                                className=\"bg-gray-700 text-gray-300\"\r\n                              >\r\n                                #{tag}\r\n                              </Badge>\r\n                            )\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                </article>\r\n              </div>\r\n              <div className=\"w-full h-[1px] bg-gray-400\" />\r\n              <RelatedBlogsSection\r\n                text=\"Related Blogs\"\r\n                posts={serializedRelatedPosts}\r\n              />\r\n            </div>\r\n            <div className=\"lg:col-span-1\">\r\n              <BlogsSection posts={serializedLatestPosts} />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  } catch (error) {\r\n    console.error(\"Error fetching blog post:\", error);\r\n    notFound();\r\n  }\r\n};\r\n\r\nexport default page;\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;AAWO,eAAe,iBAAiB,EACrC,MAAM,EACQ;IACd,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,MAAM;IAC7B,MAAM,eAAe,CAAC,6BAA6B,EAAE,OAAO,OAAO,EAAE,IAAI;IAEzE,IAAI;QACF,MAAM,CAAA,GAAA,qHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,OAAO,MAAM,qHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAC9B,MAAM;YACN,QAAQ;YACR,aAAa;YACb,QAAQ;QACV,GAAG,IAAI;QAEP,IAAI,CAAC,MAAM;YACT,OAAO;gBACL,OAAO;gBACP,YAAY;oBACV,WAAW;gBACb;gBACA,aAAa;YACf;QACF;QAEA,OAAO;YACL,OAAO,AAAC,KAAa,SAAS,IAAI,AAAC,KAAa,KAAK;YACrD,aACE,AAAC,KAAa,eAAe,IAC7B,AAAC,KAAa,OAAO,IACrB,AAAC,KAAa,WAAW;YAC3B,YAAY;gBACV,WAAW;YACb;YACA,WAAW;gBACT,OAAO,AAAC,KAAa,SAAS,IAAI,AAAC,KAAa,KAAK;gBACrD,aACE,AAAC,KAAa,eAAe,IAC7B,AAAC,KAAa,OAAO,IACrB,AAAC,KAAa,WAAW;gBAC3B,QAAQ,AAAC,KAAa,MAAM,GAAG;oBAAC;wBAAE,KAAK,AAAC,KAAa,MAAM;oBAAC;iBAAE,GAAG,EAAE;gBACnE,MAAM;gBACN,eAAe,AAAC,KAAa,WAAW,EAAE;YAC5C;YACA,SAAS;gBACP,MAAM;gBACN,OAAO,AAAC,KAAa,SAAS,IAAI,AAAC,KAAa,KAAK;gBACrD,aACE,AAAC,KAAa,eAAe,IAC7B,AAAC,KAAa,OAAO,IACrB,AAAC,KAAa,WAAW;gBAC3B,QAAQ,AAAC,KAAa,MAAM,GAAG;oBAAE,KAAa,MAAM;iBAAC,GAAG,EAAE;YAC5D;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YACL,OAAO;YACP,aAAa;YACb,YAAY;gBACV,WAAW;YACb;QACF;IACF;AACF;AACA,MAAM,OAAO,OAAO,EAAE,MAAM,EAAiB;IAC3C,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;IAE7B,IAAI;QACF,sBAAsB;QACtB,MAAM,CAAA,GAAA,qHAAA,CAAA,UAAS,AAAD;QAEd,2BAA2B;QAC3B,MAAM,OAAO,MAAM,qHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAC9B,MAAM;YACN,QAAQ;YACR,aAAa;YACb,QAAQ;QACV,GACG,QAAQ,CAAC,cAAc,oBACvB,IAAI;QAEP,IAAI,CAAC,MAAM;YACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;QACT;QAEA,0CAA0C;QAC1C,MAAM,YAAY;QAElB,8DAA8D;QAC9D,MAAM,eAAe,MAAM,qHAAA,CAAA,UAAI,CAAC,IAAI,CAAC;YACnC,MAAM;gBAAE,KAAK;YAAG;YAChB,QAAQ;YACR,aAAa;YACb,QAAQ;YACR,YAAY;gBAAE,KAAK,UAAU,UAAU,IAAI,EAAE;YAAC;QAChD,GACG,QAAQ,CAAC,cAAc,oBACvB,IAAI,CAAC;YAAE,aAAa,CAAC;YAAG,WAAW,CAAC;QAAE,GACtC,KAAK,CAAC,GAAG,uCAAuC;SAChD,IAAI;QAEP,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,4BAA4B,UAAU,UAAU;QAC5D,QAAQ,GAAG,CAAC,wBAAwB,aAAa,MAAM;QAEvD,+BAA+B;QAC/B,MAAM,cAAc,MAAM,qHAAA,CAAA,UAAI,CAAC,IAAI,CAAC;YAClC,MAAM;gBAAE,KAAK;YAAG;YAChB,QAAQ;YACR,aAAa;YACb,QAAQ;QACV,GACG,QAAQ,CAAC,cAAc,oBACvB,IAAI,CAAC;YAAE,aAAa,CAAC;YAAG,WAAW,CAAC;QAAE,GACtC,KAAK,CAAC,GACN,IAAI;QAEP,wCAAwC;QACxC,MAAM,iBAAiB,CAAC;YACtB,OAAO,MAAM,GAAG,CAAC,CAAC,IAAM,CAAC;oBACvB,KAAK,EAAE,GAAG,CAAC,QAAQ;oBACnB,OAAO,EAAE,KAAK;oBACd,aACE,EAAE,OAAO,IAAI,EAAE,WAAW,IAAI,EAAE,OAAO,EAAE,UAAU,GAAG,QAAQ;oBAChE,QAAQ,EAAE,MAAM,IAAI;oBACpB,MAAM,EAAE,IAAI,IAAI;oBAChB,YAAY,EAAE,UAAU,EAAE,IAAI,CAAC,MAAa,IAAI,IAAI,IAAI,QAAQ,EAAE;oBAClE,MAAM,EAAE,IAAI,IAAI,EAAE;oBAClB,aACE,EAAE,WAAW,EAAE,iBAAiB,EAAE,SAAS,EAAE,iBAAiB;oBAChE,WAAW,EAAE,SAAS,EAAE,iBAAiB;oBACzC,UAAU,EAAE,QAAQ,IAAI;oBACxB,OAAO,EAAE,KAAK,IAAI;oBAClB,WAAW,EAAE,SAAS,IAAI,EAAE,KAAK,IAAI;oBACrC,iBAAiB,EAAE,eAAe,IAAI,EAAE,OAAO,IAAI,EAAE,WAAW,IAAI;gBACtE,CAAC;QACH;QAEA,MAAM,yBAAyB,eAAe;QAC9C,MAAM,wBAAwB,eAAe;QAE7C,gEAAgE;QAChE,MAAM,qBAAqB;YACzB,GAAG,SAAS;YACZ,YACE,UAAU,UAAU,EAAE,IAAI,CAAC,MAAa,CAAC;oBACvC,MAAM,IAAI,IAAI,IAAI;oBAClB,aAAa,IAAI,WAAW,IAAI;gBAClC,CAAC,MAAM,EAAE;QACb;QAEA,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCAEZ,mBAAmB,MAAM,kBACxB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,mBAAmB,MAAM;gDAC9B,KAAK,mBAAmB,KAAK;gDAC7B,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,QAAQ;;;;;;;;;;;sDAMd,8OAAC;4CAAQ,WAAU;;8DAEjB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,yHAAA,CAAA,aAAU,AAAD,EACR,mBAAmB,WAAW,EAAE,iBAC9B,mBAAmB,SAAS,EAAE;;;;;;;;;;;8DAMtC,8OAAC;oDAAG,WAAU;8DACX,mBAAmB,KAAK;;;;;;gDAI1B,mBAAmB,UAAU,IAC5B,mBAAmB,UAAU,CAAC,MAAM,GAAG,mBACrC,8OAAC;oDAAI,WAAU;8DACZ,mBAAmB,UAAU,CAAC,GAAG,CAChC,CACE,UACA,sBAEA,8OAAC,iIAAA,CAAA,QAAK;4DAEJ,SAAQ;4DACR,WAAU;sEAET,SAAS,IAAI;2DAJT;;;;;;;;;;8DAYjB,8OAAC;oDACC,WAAU;oDACV,yBAAyB;wDACvB,QACE,mBAAmB,OAAO,IAC1B,mBAAmB,WAAW,IAC9B;oDACJ;;;;;;gDAID,mBAAmB,IAAI,IACtB,mBAAmB,IAAI,CAAC,MAAM,GAAG,mBAC/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,8OAAC;4DAAI,WAAU;sEACZ,mBAAmB,IAAI,CAAC,GAAG,CAC1B,CAAC,KAAa,sBACZ,8OAAC,iIAAA,CAAA,QAAK;oEAEJ,SAAQ;oEACR,WAAU;;wEACX;wEACG;;mEAJG;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAavB,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC,gLAAA,CAAA,sBAAmB;oCAClB,MAAK;oCACL,OAAO;;;;;;;;;;;;sCAGX,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,8JAAA,CAAA,eAAY;gCAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;AACF;uCAEe", "debugId": null}}]}