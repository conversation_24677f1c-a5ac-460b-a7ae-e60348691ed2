import MainHeader from "../(pages)/aboutus/components/MainHeader";
import { Card, CardContent } from "../../components/ui/card";
import Image from "next/image";

const defaultContent: string[] = [
  `Take a visual journey through our SA Gaming collection with captivating game screenshots that highlight the premium live casino experience. From crystal-clear HD streaming to sleek interfaces and professional live dealers, SA Gaming delivers immersive gameplay at its finest.`,
  `They say a picture is worth a thousand words—and our gallery speaks volumes. Explore our curated visuals showcasing elegant tables, real-time action, and high-quality graphics that bring each game to life.`,
  `Available in multiple languages including English, Thai, Chinese, and more—SA Gaming ensures players around the world enjoy a smooth, localized, and world-class gaming experience.`,
];
const GamingScreenShot: React.FC<{
  header: string;
  imgList: string[];
  contents?: string[];
  className?: string;
}> = ({
  header,
  imgList,
  contents = defaultContent,
  className = `lg:grid-cols-3 grid sm:grid-cols-2 grid-cols-1 gap-4`,
}) => {
  return (
    <section className="text-white px-2 lg:px-0 py-12">
      <MainHeader text={header} className="text-center mb-10" />
      {/* <div className="container mx-auto "> */}
      <div>
        <div className={className}>
          {imgList?.map((item, index) => (
            <Card key={index} className="w-full h-full bg-[#FFFFFF48]">
              <CardContent>
                <Image src={item} alt="image" width={500} height={500} />
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="space-y-2 py-8 text-[#A9A7B0]">
          {contents?.map((item, index) => (
            <p key={index} className="text-base text-justify">
              {item}
            </p>
          ))}
        </div>
      </div>
    </section>
  );
};

export default GamingScreenShot;
