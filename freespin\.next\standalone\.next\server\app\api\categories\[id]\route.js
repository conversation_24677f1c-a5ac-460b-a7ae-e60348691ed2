(()=>{var e={};e.id=4096,e.ids=[4096],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17063:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(56037),i=r.n(s),a=r(85663);let n=new s.Schema({username:{type:String,required:[!0,"Username is required"],unique:!0,trim:!0,minlength:[3,"Username must be at least 3 characters"],maxlength:[30,"Username cannot exceed 30 characters"]},email:{type:String,required:[!0,"Email is required"],unique:!0,lowercase:!0,trim:!0,match:[/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,"Please enter a valid email"]},password:{type:String,required:[!0,"Password is required"],minlength:[6,"Password must be at least 6 characters"],select:!1},firstName:{type:String,trim:!0,maxlength:[50,"First name cannot exceed 50 characters"]},lastName:{type:String,trim:!0,maxlength:[50,"Last name cannot exceed 50 characters"]},bio:{type:String,maxlength:[500,"Bio cannot exceed 500 characters"]},avatar:{type:String,default:null},role:{type:String,enum:["user","admin","moderator"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1}},{timestamps:!0});n.pre("save",async function(e){if(!this.isModified("password"))return e();try{let t=await a.Ay.genSalt(12);this.password=await a.Ay.hash(this.password,t),e()}catch(t){e(t)}}),n.methods.comparePassword=async function(e){return a.Ay.compare(e,this.password)};let o=i().models.User||i().model("User",n)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55530:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(56037),i=r.n(s);let a=new s.Schema({title:{type:String,required:[!0,"Post title is required"],trim:!0,maxlength:[1e3,"Title cannot exceed 200 characters"]},slug:{type:String,trim:!0,lowercase:!0},canonicalUrl:{type:String,trim:!0,validate:{validator:e=>!e||/^https?:\/\/.+/.test(e),message:"Canonical URL must be a valid URL"}},existingUrl:{type:Boolean,default:!1},content:{type:String,required:[!0,"Post content is required"]},excerpt:{type:String,trim:!0,maxlength:[1e3,"Excerpt cannot exceed 300 characters"]},description:{type:String},author:{type:String,trim:!0},isBlog:{type:Boolean,default:!0},categories:[{type:i().Schema.Types.ObjectId,ref:"Category"}],tags:[{type:String,trim:!0,lowercase:!0}],metaTitle:{type:String,required:[!0,"Meta title is required"],maxlength:[1e3,"Meta title cannot exceed 60 characters"]},metaDescription:{type:String,required:[!0,"Meta description is required"],maxlength:[1e3,"Meta description cannot exceed 160 characters"]},metaKeywords:{type:String,required:[!0,"Meta keywords are required"],maxlength:[1e3,"Meta keywords cannot exceed 200 characters"]},banner:{type:String,required:[!0,"Banner image is required"],trim:!0},status:{type:String,enum:["draft","published","archived"],default:"draft"},isPublished:{type:Boolean,default:!1},publishedAt:{type:Date,default:null},views:{type:Number,default:0},readTime:{type:Number,default:1},isTopNews:{type:Boolean,default:!1}},{timestamps:!0});a.pre("save",function(e){if(this.isModified("title")&&!this.slug&&(this.slug=this.title.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,"")),this.isModified("content")){let e=(this.content||"").split(/\s+/).length;this.readTime=Math.ceil(e/200)}this.isModified("status")&&"published"===this.status&&!this.publishedAt&&(this.publishedAt=new Date,this.isPublished=!0),e()}),a.index({categories:1}),a.index({tags:1}),a.index({status:1}),a.index({isPublished:1}),a.index({publishedAt:-1}),a.index({"banner.title":1}),a.index({title:"text",content:"text",metaTitle:"text",metaDescription:"text"});let n=i().models.Post||i().model("Post",a)},56037:e=>{"use strict";e.exports=require("mongoose")},57595:(e,t,r)=>{"use strict";r.d(t,{eh:()=>p,m6:()=>d,ru:()=>l});var s=r(32190),i=r(43205),a=r.n(i),n=r(75745),o=r(17063);let c=process.env.JWT_SECRET||"your-secret-key";function l(e){return async(t,r)=>{try{let i=t.headers.get("authorization"),l=i?.replace("Bearer ","")||t.cookies.get("session-token")?.value;if(!l)return s.NextResponse.json({success:!1,error:"Authentication required"},{status:401});let u=a().verify(l,c);await (0,n.A)();let d=await o.A.findById(u.userId).select("_id email username role isActive");if(!d||!d.isActive)return s.NextResponse.json({success:!1,error:"User not found or inactive"},{status:401});return t.user={userId:d._id.toString(),email:d.email,username:d.username,role:d.role},e(t,r)}catch(e){if(e instanceof a().JsonWebTokenError)return s.NextResponse.json({success:!1,error:"Invalid authentication token"},{status:401});return console.error("Authentication middleware error:",e),s.NextResponse.json({success:!1,error:"Authentication failed"},{status:500})}}}function u(e){return t=>l(async(r,i)=>{let a=r.user;return e.includes(a.role)?t(r,i):s.NextResponse.json({success:!1,error:`Access denied. Required roles: ${e.join(", ")}. Your role: ${a.role}`},{status:403})})}let d=u(["admin"]);function p(e){return async(t,r)=>{let s=Date.now(),i=t.method,a=t.url;console.log(`[${new Date().toISOString()}] ${i} ${a} - Started`);try{let n=await e(t,r),o=Date.now()-s;return console.log(`[${new Date().toISOString()}] ${i} ${a} - ${n.status} (${o}ms)`),n}catch(t){let e=Date.now()-s;throw console.error(`[${new Date().toISOString()}] ${i} ${a} - Error (${e}ms):`,t),t}}}u(["admin","editor"]),u(["admin","editor","author"])},62071:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>g,serverHooks:()=>f,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>y});var s={};r.r(s),r.d(s,{DELETE:()=>m});var i=r(96559),a=r(48088),n=r(37719),o=r(32190),c=r(45697),l=r(75745),u=r(73944),d=r(55530),p=r(57595);c.z.object({name:c.z.string().min(1,"Name is required").max(50,"Name too long").trim().optional(),description:c.z.string().max(200,"Description too long").optional(),color:c.z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,"Invalid color format").optional(),isActive:c.z.boolean().optional()});let m=(0,p.eh)((0,p.m6)(async(e,{params:t})=>{console.log("\uD83D\uDD25 DELETE CATEGORY ROUTE HIT - ID:",t.id),console.log("\uD83D\uDD25 Request method:",e.method),console.log("\uD83D\uDD25 Request URL:",e.url);try{if(await (0,l.A)(),!t.id.match(/^[0-9a-fA-F]{24}$/))return console.log("❌ Invalid ObjectId format:",t.id),o.NextResponse.json({success:!1,error:"Invalid category ID format"},{status:400});let r=await u.A.findById(t.id);if(!r)return console.log("❌ Category not found for deletion:",t.id),o.NextResponse.json({success:!1,error:"Category not found"},{status:404});console.log("✅ Found category to delete:",r.name);let s=await d.A.countDocuments({category:t.id});if(s>0)return console.log("❌ Category has posts, cannot delete:",s),o.NextResponse.json({success:!1,error:`Cannot delete category. It has ${s} associated posts. Please reassign or delete the posts first.`},{status:400});return await u.A.findByIdAndDelete(t.id),console.log("✅ Successfully deleted category:",r.name),o.NextResponse.json({success:!0,message:"Category deleted successfully",deletedBy:e.user.username})}catch(e){return console.error("\uD83D\uDCA5 Error deleting category:",e),o.NextResponse.json({success:!1,error:"Failed to delete category"},{status:500})}})),g=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/categories/[id]/route",pathname:"/api/categories/[id]",filename:"route",bundlePath:"app/api/categories/[id]/route"},resolvedPagePath:"D:\\Web Studio Nepal\\FreeSpin168\\freespin\\src\\app\\api\\categories\\[id]\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:y,serverHooks:f}=g;function x(){return(0,n.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:y})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73944:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(56037),i=r.n(s);let a=new s.Schema({name:{type:String,required:[!0,"Category name is required"],unique:!0,trim:!0,maxlength:[50,"Category name cannot exceed 50 characters"]},description:{type:String,maxlength:[200,"Description cannot exceed 200 characters"]},color:{type:String,default:"#6366f1",match:[/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,"Please enter a valid hex color"]},isActive:{type:Boolean,default:!0}},{timestamps:!0});a.index({isActive:1});let n=i().models.Category||i().model("Category",a)},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(56037),i=r.n(s);let a=process.env.MONGODB_URI;if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let n=global?.mongoose;n||(n=global.mongoose={conn:null,promise:null});let o=async function(){if(n.conn)return n.conn;n.promise||(n.promise=i().connect(a,{bufferCommands:!1}).then(e=>e));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,3205,5697,5663],()=>r(62071));module.exports=s})();