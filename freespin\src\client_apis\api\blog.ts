"use server";
import { createApiConfig } from "../api-config";

const blog = createApiConfig({
  endpoint: "/api/categories",
  tags: ["categories"],
  requireAuth: true, // Login doesn't require existing auth
});

// Public categories function for frontend use (no auth required)
export async function getPublicCategories() {
  const baseUrl =
    typeof window !== "undefined"
      ? window.location.origin
      : process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3001";

  try {
    const response = await fetch(`${baseUrl}/api/categories`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `HTTP error! status: ${response.status}, body: ${errorText}`
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching categories:", error);
    throw error;
  }
}

// Legacy function - now redirects to server action for admin use
export async function getBlogsCategories() {
  // This function is deprecated for admin use
  // Use getCategoriesAction server action instead for authenticated admin access
  return getPublicCategories();
}

export async function createBlogCategory(data: any) {
  return await blog.createData(data);
}

export async function deleteBlogCategory(id: any) {
  return await blog.deleteDataById(id);
}

const posts = createApiConfig({
  endpoint: "/api/posts",
  tags: ["posts"],
  requireAuth: true,
});

export async function getPosts() {
  return await posts.getAll();
}
export async function createPost(data: any) {
  return await posts.createData(data);
}
export async function deletePost(id: any) {
  return await posts.deleteDataById(id);
}

export async function updatePostStatus(
  id: string,
  status: "draft" | "published" | "archived"
) {
  try {
    const baseUrl =
      typeof window !== "undefined"
        ? window.location.origin
        : process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3001";

    // Headers for the request
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    const response = await fetch(`${baseUrl}/api/posts/${id}/status`, {
      method: "PATCH",
      headers,
      credentials: "include",
      body: JSON.stringify({ status }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `HTTP error! status: ${response.status}, body: ${errorText}`
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating post status:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to update post status",
    };
  }
}

// Public posts API (no auth required)
export async function getPublicPosts(params?: {
  page?: number;
  limit?: number;
  category?: string;
  tag?: string;
  search?: string;
}) {
  const queryParams = new URLSearchParams();
  if (params?.page) queryParams.append("page", params.page.toString());
  if (params?.limit) queryParams.append("limit", params.limit.toString());
  if (params?.category) queryParams.append("category", params.category);
  if (params?.tag) queryParams.append("tag", params.tag);
  if (params?.search) queryParams.append("search", params.search);

  const queryString = queryParams.toString();
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3001";
  const endpoint = queryString
    ? `${baseUrl}/api/public/posts?${queryString}`
    : `${baseUrl}/api/public/posts`;

  try {
    const response = await fetch(endpoint, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
      next: {
        tags: ["public-posts"],
        revalidate: 300, // Cache for 5 minutes
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching public posts:", error);
    return {
      success: false,
      error: "Failed to fetch posts",
    };
  }
}

// Blog post interface for public consumption
export interface PublicBlogPost {
  _id: string;
  title: string;
  description: string;
  banner: string;
  categories: string[];
  tags: string[];
  slug: string;
  publishedAt: string;
  createdAt: string;
  readTime: number;
  views: number;
  metaTitle: string;
  metaDescription: string;
}
